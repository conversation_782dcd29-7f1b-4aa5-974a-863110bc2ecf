package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.entity.ESignUserinfoEntity;
import com.foxconn.ipebg.basics.dao.ESignUserinfoDao;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.AuditConnonUser;
import com.foxconn.ipebg.system.entity.Dict;
import com.foxconn.ipebg.system.entity.TQhChargepathEntity;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WorkFlowEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.system.service.DictService;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 簽核者信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-12-25 14:52:53
 */
@Service
@Transactional(readOnly=true)
public class  ESignUserinfoService extends BaseBusinessService<ESignUserinfoEntity, String>{
    @Autowired
    private ESignUserinfoDao eSignUserinfoDao;
    @Autowired
    private UserServiceUtil serviceUtil;
    @Autowired
    private DictService dictService;

    @Override
    public HibernateDao<ESignUserinfoEntity, String> getEntityDao() {
        return eSignUserinfoDao;
    }

    // TODO: 這裡需要判斷簽核人員是否有效
    public ESignUserinfoEntity findByeserIdTypNodeName(String eserId,String eserTyp,String eserNodeName) {
    	 StringBuffer sb = new StringBuffer();
         sb.append(" select * from E_SIGN_USERINFO t " )
         .append(" WHERE 1=1 ");
         if (eserId != null&&!"".equals(eserId)) {
             sb.append(" AND t.eser_id = '"+eserId+"'");
         }
         if (eserTyp != null&&!"".equals(eserTyp)) {
        	 sb.append(" AND t.eser_typ = '"+eserTyp+"'");
         }
         if (eserNodeName != null&&!"".equals(eserNodeName)) {
        	 sb.append(" AND t.eser_node_name = '"+eserNodeName+"'");
         }

         List<ESignUserinfoEntity> list = this.eSignUserinfoDao.createSQLQuery(sb.toString()).addEntity(ESignUserinfoEntity.class).list();
        return list.get(0);
    }
	/**
	 * 方法描述: 獲取審核節點信息
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019/01/01 下午 01:39
	 * @Return
	 **/
    public List<AuditConnonUser> findAuditUser(String eserTyp,String eserNodeName) {
        List<AuditConnonUser> userList = new ArrayList<AuditConnonUser>();
        AuditConnonUser user = null;
        StringBuffer sb = new StringBuffer();
        sb.append(" select *  from E_SIGN_USERINFO t " )
        .append(" WHERE 1=1 and t.yn_va='Y' ");

        if (eserTyp != null&&!"".equals(eserTyp)) {
       	 sb.append(" AND t.eser_typ = '"+eserTyp+"'");
        }
        if (eserNodeName != null&&!"".equals(eserNodeName)) {
       	 sb.append(" AND t.eser_node_name = '"+eserNodeName+"'");
        }
        
        List<ESignUserinfoEntity> list = this.eSignUserinfoDao.createSQLQuery(sb.toString()).addEntity(ESignUserinfoEntity.class).list();

        if (list.size() > 0) {
            for (int i=0;i<list.size(); i++) {
                if (list.get(i) != null&&list.get(i)!=null) {
                    user = new AuditConnonUser();
                    user.setEmpno(list.get(i).getEserId());
                    user.setUsername(list.get(i).getEserNam());
                    userList.add(user);
                }
            }
        }
        return userList;
    }

}
