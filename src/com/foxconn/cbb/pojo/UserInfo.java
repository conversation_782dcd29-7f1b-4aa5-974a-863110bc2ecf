package com.foxconn.cbb.pojo;


import java.io.Serializable;
import java.util.Date;

/**
 * 用戶信息類
 * <AUTHOR>
 *
 */
public class UserInfo implements Serializable {

	private static final long serialVersionUID = 1L;
	private String userId;
	private String userName;
	private String userPwd;
	private String userMail;
	private String phone;
	private String userType;
	private Date createDate;
	private String loginIp;
	private String imgUrl;
	//是否C次集團員工
	private String isInc;

	private String sex;
	private String birthday;
	private String deptCostNo;
	private String idStatus;
	private String deptName;
	private String psnId;
	private String levelTypeName;


	/*****************餐飲增加字段*******************/
	private String empNo;
	private String deptNo;
	private String levelType;
	private String factoryId;
	private String inDate;
	private String workTime;
	private String levelName;
	private String isManager;//是否有管理職位，有管理職位填寫管理職位，否則寫無或不填
	private String schoolDesc;
	private String mobile;
	private String isValid;
	private String creator;
	private String modor;
	private Date modifyTime;
	private String isFirstLogin;//是否為第一次登陸0：第一次登陸
	private String isSetSecureInfo;//是否設置過賬戶安全問題
	/*****************餐飲增加字段*******************/


	private String roleName;//用戶權限分配查詢字段
	private String remarks;//備註
	private String emailSet;//是否定時發送郵件 0及時發送 1定時發送
	private String timingSend;//定時發送郵件時間 5.7.9.11.13.15.17.19

	public String getLevelTypeName() {
		return levelTypeName;
	}
	public void setLevelTypeName(String levelTypeName) {
		this.levelTypeName = levelTypeName;
	}
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getUserPwd() {
		return userPwd;
	}
	public void setUserPwd(String userPwd) {
		this.userPwd = userPwd;
	}
	public String getUserMail() {
		return userMail;
	}
	public void setUserMail(String userMail) {
		this.userMail = userMail;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public String getLoginIp() {
		return loginIp;
	}
	public void setLoginIp(String loginIp) {
		this.loginIp = loginIp;
	}
	public String getImgUrl() {
		return imgUrl;
	}
	public void setImgUrl(String imgUrl) {
		this.imgUrl = imgUrl;
	}
	public String getEmpNo() {
		return empNo;
	}
	public void setEmpNo(String empNo) {
		this.empNo = empNo;
	}
	public String getDeptNo() {
		return deptNo;
	}
	public void setDeptNo(String deptNo) {
		this.deptNo = deptNo;
	}
	public String getLevelType() {
		return levelType;
	}
	public void setLevelType(String levelType) {
		this.levelType = levelType;
	}
	public String getFactoryId() {
		return factoryId;
	}
	public void setFactoryId(String factoryId) {
		this.factoryId = factoryId;
	}
	public String getInDate() {
		return inDate;
	}
	public void setInDate(String inDate) {
		this.inDate = inDate;
	}
	public String getWorkTime() {
		return workTime;
	}
	public void setWorkTime(String workTime) {
		this.workTime = workTime;
	}
	public String getLevelName() {
		return levelName;
	}
	public void setLevelName(String levelName) {
		this.levelName = levelName;
	}
	public String getIsManager() {
		return isManager;
	}
	public void setIsManager(String isManager) {
		this.isManager = isManager;
	}
	public String getSchoolDesc() {
		return schoolDesc;
	}
	public void setSchoolDesc(String schoolDesc) {
		this.schoolDesc = schoolDesc;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getCreator() {
		return creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}
	public String getModor() {
		return modor;
	}
	public void setModor(String modor) {
		this.modor = modor;
	}
	public Date getModifyTime() {
		return modifyTime;
	}
	public void setModifyTime(Date modifyTime) {
		this.modifyTime = modifyTime;
	}
	public String getUserType() {
		return userType;
	}
	public void setUserType(String userType) {
		this.userType = userType;
	}
	public String getIsValid() {
		return isValid;
	}
	public void setIsValid(String isValid) {
		this.isValid = isValid;
	}
	public String getIsFirstLogin() {
		return isFirstLogin;
	}
	public void setIsFirstLogin(String isFirstLogin) {
		this.isFirstLogin = isFirstLogin;
	}
	public String getIsSetSecureInfo() {
		return isSetSecureInfo;
	}
	public void setIsSetSecureInfo(String isSetSecureInfo) {
		this.isSetSecureInfo = isSetSecureInfo;
	}
	public String getIsInc() {
		return isInc;
	}
	public void setIsInc(String isInc) {
		this.isInc = isInc;
	}
	public String getSex() {
		return sex;
	}
	public void setSex(String sex) {
		this.sex = sex;
	}
	public String getBirthday() {
		return birthday;
	}
	public void setBirthday(String birthday) {
		this.birthday = birthday;
	}
	public String getDeptCostNo() {
		return deptCostNo;
	}
	public void setDeptCostNo(String deptCostNo) {
		this.deptCostNo = deptCostNo;
	}
	public String getIdStatus() {
		return idStatus;
	}
	public void setIdStatus(String idStatus) {
		this.idStatus = idStatus;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public String getPsnId() {
		return psnId;
	}
	public void setPsnId(String psnId) {
		this.psnId = psnId;
	}
	public String getRoleName() {
		return roleName;
	}
	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}
	public String getRemarks() {
		return remarks;
	}
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

    public String getEmailSet() {
        return emailSet;
    }

    public void setEmailSet(String emailSet) {
        this.emailSet = emailSet;
    }

    public String getTimingSend() {
        return timingSend;
    }

    public void setTimingSend(String timingSend) {
        this.timingSend = timingSend;
    }
}
