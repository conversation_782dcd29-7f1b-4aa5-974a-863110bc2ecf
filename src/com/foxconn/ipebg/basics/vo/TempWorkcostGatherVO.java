package com.foxconn.ipebg.basics.vo;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;


/**
 * 警衛費用結賬明細表匯總
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-16 10:36:12
 */
public class TempWorkcostGatherVO {

    //事業群名稱
    private String buNam;
    //法人
    private String legalId;
    //費用代碼
    private String costId;

    //常規費用（RMB）
    private Integer normalCost;
    //加班費用（RMB）
    private Integer otCost;
    //總費用（RMB）
    private Integer totalCost;
    //工作年月（yyyy-MM）
    private String workMonth;
    //臨時勤務費用（RMB分）
    private Integer tsCost;

    @Column(name = "BU_NAM", nullable = false, length = 20)
    public String getBuNam() {
        return buNam;
    }

    public void setBuNam(String buNam) {
        this.buNam = buNam;
    }

    /**
     * 设置：法人
     */
    public void setLegalId(String legalId) {
        this.legalId = legalId;
    }

    /**
     * 获取：法人
     */

    @Column(name = "LEGAL_ID", nullable = false, length = 20)
    public String getLegalId() {
        return legalId;
    }


    /**
     * 设置：費用代碼
     */
    public void setCostId(String costId) {
        this.costId = costId;
    }

    /**
     * 获取：費用代碼
     */

    @Column(name = "COST_ID", nullable = false, length = 20)
    public String getCostId() {
        return costId;
    }

    /**
     * 设置：常規費用（RMB分）
     */
    public void setNormalCost(Integer normalCost) {
        this.normalCost = normalCost;
    }

    /**
     * 获取：常規費用（RMB分）
     */

    @Column(name = "NORMAL_COST", nullable = false, length = 20)
    public Integer getNormalCost() {
        if (normalCost == null) return 0;
        return normalCost;
    }

    /**
     * 设置：加班費用（RMB分）
     */
    public void setOtCost(Integer otCost) {
        this.otCost = otCost;
    }

    /**
     * 获取：加班費用（RMB分）
     */

    @Column(name = "OT_COST", nullable = false, length = 20)
    public Integer getOtCost() {
        if (otCost == null) return 0;
        return otCost;
    }

    /**
     * 设置：總費用（RMB分）
     */
    public void setTotalCost(Integer totalCost) {
        this.totalCost = totalCost;
    }

    /**
     * 获取：總費用（RMB分）
     */

    @Column(name = "TOTAL_COST", nullable = false, length = 20)
    public Integer getTotalCost() {
        if (totalCost == null) return 0;
        return totalCost;
    }


    /**
     * 设置：工作年月（YYYY-MM）
     */
    public void setWorkMonth(String workMonth) {
        this.workMonth = workMonth;
    }

    /**
     * 获取：工作年月（YYYY-MM）
     */

    @Column(name = "WORK_MONTH", nullable = false, length = 20)
    public String getWorkMonth() {
        return workMonth;
    }

    /**
     * 设置：臨時勤務費用（RMB分）
     */
    public void setTsCost(Integer tsCost) {
        this.tsCost = tsCost;
    }

    /**
     * 获取：臨時勤務費用（RMB分）
     */

    @Column(name = "TS_COST", nullable = false, length = 20)
    public Integer getTsCost() {
        if (tsCost == null) return 0;
        return tsCost;
    }

}
