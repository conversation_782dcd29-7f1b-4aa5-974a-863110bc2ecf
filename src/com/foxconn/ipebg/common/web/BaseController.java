package com.foxconn.ipebg.common.web;

import java.beans.PropertyEditorSupport;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.foxconn.ipebg.common.exception.BusinessException;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.utils.DateUtils;
import com.foxconn.ipebg.common.utils.Result;
import com.foxconn.ipebg.common.utils.ResultEnum;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.system.entity.User;
import com.foxconn.ipebg.system.utils.UserUtil;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.shiro.SecurityUtils;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.ResponseBody;


/**
 * 基础控制器 
 * 其他控制器继承此控制器获得日期字段类型转换和防止XSS攻击的功能
 * @description 
 * <AUTHOR>
 * @date 2014年3月19日
 */
@ControllerAdvice
public class BaseController {
	/*通用日誌記錄器，可以再子類中直接使用*/
	public org.slf4j.Logger logger= LoggerFactory.getLogger(getClass().getName());
	@InitBinder
	public void initBinder(WebDataBinder binder) {
		// String类型转换，将所有传递进来的String进行HTML编码，防止XSS攻击
		binder.registerCustomEditor(String.class, new PropertyEditorSupport() {
			@Override
			public void setAsText(String text) {
				setValue(text == null ? null : StringEscapeUtils.escapeHtml4(text.trim()));
			}
			@Override
			public String getAsText() {
				Object value = getValue();
				return value != null ? value.toString() : "";
			}
		});
		
		// Date 类型转换
		binder.registerCustomEditor(Date.class, new PropertyEditorSupport() {
			@Override
			public void setAsText(String text) {
				setValue(DateUtils.parseDate(text));
			}
		});
		
		// Timestamp 类型转换
		binder.registerCustomEditor(Timestamp.class, new PropertyEditorSupport() {
			@Override
			public void setAsText(String text) {
				Date date = DateUtils.parseDate(text);
				setValue(date==null?null:new Timestamp(date.getTime()));
			}
		});
	}
	
	/**
	 * 获取page对象
	 * @param request
	 * @return page对象
	 */
	public <T> Page<T> getPage(HttpServletRequest request){
		int pageNo=1;	//当前页码
		int pageSize=Page.MAXSIZE;	//每页行数
		String orderBy="id";	//排序字段
		String order="asc";	//排序顺序
		if(StringUtils.isNotEmpty(request.getParameter("page")))
			pageNo=Integer.valueOf(request.getParameter("page"));
		if(StringUtils.isNotEmpty(request.getParameter("rows")))
			pageSize=Integer.valueOf(request.getParameter("rows"));
		if(StringUtils.isNotEmpty(request.getParameter("sort")))
			orderBy=request.getParameter("sort").toString();
		if(StringUtils.isNotEmpty(request.getParameter("order")))
			order=request.getParameter("order").toString();
		if(StringUtils.isNotEmpty(request.getParameter("orderBy")))
			orderBy=request.getParameter("orderBy").toString();
		return new Page<T>(pageNo, pageSize, orderBy, order);
	}

	/**
	 * 获取page对象
	 * @param params
	 * @return page对象
	 */
	public Page getPage(Map<String,String> params){
		int pageNo=1;	//当前页码
		int pageSize=20;	//每页行数
		String orderBy="id";	//排序字段
		String order="desc";	//排序顺序
		if(params.get("page")!=null &&StringUtils.isNotBlank(params.get("page").toString()))
			pageNo=Integer.valueOf(params.get("page").toString());
		if(params.get("rows")!=null &&StringUtils.isNotBlank(params.get("rows").toString()))
			pageSize=Integer.valueOf(params.get("rows").toString());
		if(params.get("sort")!=null &&StringUtils.isNotBlank(params.get("sort").toString()))
			orderBy=params.get("sort").toString();
		if(params.get("order")!=null &&StringUtils.isNotBlank(params.get("order").toString()))
			order=params.get("order").toString();
		return new Page(pageNo, pageSize, orderBy, order);
	}
	
	/**
	 * 获取easyui分页数据
	 * @param page
	 * @return map对象
	 */
	public <T> Map<String, Object> getEasyUIData(Page<T> page){
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("rows", page.getResult());
		map.put("total", page.getTotalCount());
		return map;
	}

	public <T> Map<String, Object> getEasyUIData(List<T> list) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("rows", list);
		map.put("total", list.size());
		return map;
	}
	
	/**
	 * 用来获取当前登录用户
	 * @return 当前登录用户
	 */
	public User getCurUser() {
		
		//Object = null;
		User curUser = UserUtil.getCurrentUser();
		return curUser;
	}

	/** 基于@ExceptionHandler异常处理 *//*
	@ExceptionHandler
	@ResponseBody
	public Map<String, Object>  handleAndReturnData(HttpServletRequest request, HttpServletResponse response, Exception ex) {

		Map<String, Object> data = new HashMap<String, Object>();
		if(ex instanceof BusinessException) {
			this.logger.error("出錯了",ex.getStackTrace());
			BusinessException e = (BusinessException)ex;
			data.put("code", e.getCode());
			data.put("msg", ex.getMessage());
		}else{
			data.put("code", "100");
			data.put("msg", "系統錯誤");
		}
		ex.printStackTrace();
		data.put("success", false);
		data.put("data", null);
		return data;
	}*/
	/**
	 * http回调成功
	 * @param object
	 * @return
	 */
	public static Result success(Object object){
		Result result = new Result();
		result.setCode(ResultEnum.SUCCESS.getCode());
		result.setMsg(ResultEnum.SUCCESS.getMsg());
		result.setData(object);
		return result;
	}
	/**
	 * 无object返回
	 * @return
	 */
	public static Result success(){
		return success(null);
	}
	/**
	 * http回调错误
	 * @return
	 */
	public static Result error(){
		Result result = new Result();
		result.setCode(ResultEnum.FAIL.getCode());
		result.setMsg(ResultEnum.FAIL.getMsg());
		return result;
	}

}
