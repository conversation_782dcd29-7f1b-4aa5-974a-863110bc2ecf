package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.entity.ESignUserLineEntity;
import com.foxconn.ipebg.basics.entity.ESignUserinfoEntity;
import com.foxconn.ipebg.basics.dao.ESignUserLineDao;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.Dict;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WorkFlowEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.system.service.DictService;

import java.util.Date;
import java.util.List;

/**
 * 默認簽核線記錄表
 *
 * <AUTHOR>
 * @email <EMAIL> 
 * @date 2019-09-23 11:36:24
 */
@Service
@Transactional(readOnly=true)
public class  ESignUserLineService extends BaseBusinessService<ESignUserLineEntity, String>{
    @Autowired
    private ESignUserLineDao eSignUserLineDao;

    @Autowired
    private UserServiceUtil serviceUtil;
    @Autowired
    private DictService dictService;

    @Override
    public HibernateDao<ESignUserLineEntity, String> getEntityDao() {
        return eSignUserLineDao;
    }

    public ESignUserLineEntity findBySerialno(String serialno) {
        return this.eSignUserLineDao.findUniqueBy("serialno", serialno);
    }

	@Transactional(readOnly = false)
	public int deleteByEmpNoAndFormType(ESignUserLineEntity eSignUserLineEntity) {
		 return this.eSignUserLineDao.batchExecute("delete from ESignUserLineEntity t where t.empNo=?0 and t.formType=?1", eSignUserLineEntity.getEmpNo(),eSignUserLineEntity.getFormType());
	}

	@Transactional(readOnly = false)
	public int deleteByFormTypeAndSignOrd(String formType, String signOrd) {
    	return this.eSignUserLineDao.batchExecute("delete from ESignUserLineEntity t where t.formType=?0 and t.codeId=?1", formType, Integer.parseInt(signOrd));
	}

	@Transactional(readOnly = false)
	public int deleteByEmpNoAndFormType(String empNo, String formType) {
		return this.eSignUserLineDao.batchExecute("delete from ESignUserLineEntity t where t.empNo=?0 and t.formType=?1", empNo, formType);
	}
	
	public List<ESignUserLineEntity> findByEmpNoAndFormType(String empNo,
			String formType) {
		StringBuffer sb = new StringBuffer();
		sb.append(" select * from E_SIGN_USER_LINE t where EMP_NO='" + empNo
				+ "' and FORM_TYPE='" + formType + "' ORDER BY code_id ASC ");
		List<ESignUserLineEntity> list = this.eSignUserLineDao
				.createSQLQuery(sb.toString())
				.addEntity(ESignUserLineEntity.class).list();
		return list;
	}
}
