var dg;
var d;
$(function() {
	dg = $('#dg').datagrid({
		method : "get",
		url : ctx + '/bsposthistory/list',
		fit : true,
		fitColumns : true,
		border : false,
		idField : 'id',
		striped : true,
		pagination : true,
		rownumbers : true,
		pageNumber : 1,
		pageSize : 20,
		pageList : [ 10, 20, 30, 40, 50 ],
		sortName: 'createDate',
		sortOrder: 'desc',
		singleSelect : true,
		columns : [ [ {
			field : 'id',
			title : '主鍵',
			hidden : true
		}, {
			field : 'recno',
			title : '崗位編號',
			sortable : true,
			width : 160
		}, {
			field : 'postName',
			title : '崗位名稱',
			sortable : true,
			width : 150
		}, {
			field : 'applyType',
			title : '需求類別',
			sortable : true,
			width : 80
		}, {
			field : 'applyEmpName',
			title : '申請人姓名',
			sortable : true,
			width : 80
		}, {
			field : 'applyEmpBu',
			title : '申請人部門',
			sortable : true,
			width : 120
		}, {
			field : 'createDate',
			title : '實際安排時間',
			sortable : true,
			width : 150
		}, {
			field : 'area',
			title : '區域',
			sortable : true,
			width : 80
		}, {
			field : 'block',
			title : '棟',
			sortable : true,
			width : 30
		}, {
			field : 'floor',
			title : '層',
			sortable : true,
			width : 30
		}, {
			field : 'position',
			title : '方位',
			sortable : true,
			width : 50
		}, {
			field : 'postType',
			title : '崗位類別',
			sortable : true,
			width : 80
		}, {
			field : 'postShift',
			title : '班制',
			sortable : true,
			width : 50
		}, {
			field : 'postPerNu',
			title : '人數(不含調休)',
			sortable : true,
			width : 80
		}, {
			field : 'postStartDate',
			title : '需求時間1',
			sortable : true,
			width : 100
		}, {
			field : 'postEndDate',
			title : '需求時間2',
			sortable : true,
			width : 100
		}, {
			field : 'securityCom',
			title : '派駐保安公司',
			sortable : true,
			width : 100
		}, {
			field : 'serialno',
			title : '申請單號',
			sortable : true,
			width : 150
		}]],
		enableHeaderClickMenu : true,
		enableHeaderContextMenu : true,
		enableRowContextMenu : false,
		rowTooltip : true,
		toolbar : '#tb'
	});
});

// 创建查询对象并查询
function cx() {
	var obj = $("#searchFrom").serializeObject();
	dg.datagrid('load', obj);
}

// 表單進度查詢頁面查詢條件重置
function listSearchReset() {
	$("#searchFrom").form("reset");
}

// 导出excel
function exportExcel() {
	var form = document.getElementById("searchFrom");
	searchFrom.action = ctx + '/bsposthistory/exportExcel';
	form.submit();
}
