<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>崗位狀況匯總表</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
        <form id="searchFrom" action="">
		<input type="text" name="empNo" class="easyui-validatebox" data-options="width:100,prompt: '工號'" />
		<input type="text" name="empName" class="easyui-validatebox" data-options="width:100,prompt: '姓名'" />
		<!-- <input type="text" name="postType" class="easyui-validatebox" data-options="width:150,prompt: '崗位類別'" /> -->
		<input id="postType" name="postType" class="easyui-combobox" data-options="panelHeight:'auto',valueField:'value', textField:'label',url: '${ctx}/system/dict/getDictByType/guard_postType',width: 100,prompt: '崗位類別'" value="" />
		<input id="securityCom" name="securityCom" class="easyui-combobox"
			   data-options="panelHeight:'auto',valueField:'value', prompt: '保安公司', textField:'label', url: '${ctx}/system/user/userSecurityCom',width: 100"/>
		<input type="text" name="qryDate" class="easyui-my97" datefmt="yyyyMM"
			   data-options="width:100,prompt: '月份'"/>
		<span class="toolbar-item dialog-tool-separator"></span>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
		   onclick="listSearchReset()">重置</a>
		<!-- <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel"
		   onclick="exportExcel()">导出Excel</a> -->
		   <br/>

	</form>

  </div>
<table id="dg"></table>
<div id="dlg"></div>

<script src="${ctx}/static/js/report/workdetail.js?random=<%= Math.random()%>"></script>
</body>
</html>