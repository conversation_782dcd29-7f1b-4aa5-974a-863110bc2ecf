package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.entity.BsServiceCostEntity;
import com.foxconn.ipebg.basics.dao.BsServiceCostDao;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.Dict;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WorkFlowEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.system.service.DictService;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * 警衛服務費用結賬明細主表
 *
 * <AUTHOR>
 * @email <EMAIL> 
 * @date 2019-10-07 13:15:24
 */
@Service
@Transactional(readOnly=true)
public class  BsServiceCostService extends BaseBusinessService<BsServiceCostEntity, String>{
    @Autowired
    private BsServiceCostDao bsServiceCostDao;

    @Autowired
    private UserServiceUtil serviceUtil;
    @Autowired
    private DictService dictService;

    @Override
    public HibernateDao<BsServiceCostEntity, String> getEntityDao() {
        return bsServiceCostDao;
    }

    public BsServiceCostEntity findBySerialno(String serialno) {
        return this.bsServiceCostDao.findUniqueBy("serialno", serialno);
    }

    public BsServiceCostEntity getSummary(String dptBu, String legalId, String workMonth) {
        String sql = "select sys_guid() id, '' bu_id, '' bu_nam, '' legal_id, '' dpt_id, '' dpt_nam, '' cost_id, '' recno, '' post_position, '' post_nam, '' create_by, null ts_cost, null create_date, '' update_by, null update_date, '' del_flag, " +
                "round(sum(total_cost_all),2) total_cost_all, " + (StringUtils.isNotBlank(workMonth) ? workMonth : "''") + " work_month, " +
                "round(sum(ot_cost),2) ot_cost, " +
                "sum(ot_hour) ot_hour, " +
                "round(sum(total_cost),2) total_cost, " +
                "round(sum(training_cost),2) training_cost, " +
                "round(sum(normal_cost),2) normal_cost, " +
                "sum(cast(normal_day as numeric)) normal_day " +
                "from BS_SERVICE_COST " +
                " where 1=1";
        if (StringUtils.isNotBlank(dptBu)) {
            sql += " and bu_nam like '%" + dptBu + "%' ";
        }
        if (StringUtils.isNotBlank(legalId)) {
            sql += " and legal_id = '" + legalId + "' ";
        }
        if (StringUtils.isNotBlank(workMonth)) {
            sql += " and work_month = '"+ workMonth + "'";
        }
        List<BsServiceCostEntity> list = this.bsServiceCostDao.createSQLQuery(sql).addEntity(BsServiceCostEntity.class).list();
        return list.get(0);
    }

    public void generateServiceCostByMonth(Date month) throws SQLException {
        bsServiceCostDao.generateServiceCost(month);
    }
}
