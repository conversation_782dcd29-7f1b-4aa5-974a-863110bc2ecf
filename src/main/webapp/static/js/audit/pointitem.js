var dg;
var d;
$(function() {
	dg = $('#dg').datagrid({
		method : "get",
		url : ctx + '/pointitem/list',
		fit : true,
		fitColumns : true,
		border : false,
		idField : 'id',
		loadFilter : function(data) {
			// 过滤数据
			var value = {
				total : data.total,
				rows : []
			};
			var x = 0;
			for ( var i = 0; i < data.rows.length; i++) {
				// ：0.新申請1.使用中2.手動停用（需重新申請該點位）3.複檢停用
				if (data.rows[i].useStatus == "0") {
					data.rows[i].useStatus = "新申請";
				} else if (data.rows[i].useStatus == "1") {
					data.rows[i].useStatus = "使用中";
				} else if (data.rows[i].useStatus == "3") {
					data.rows[i].useStatus = "複檢停用";
				}
				// :1.直飲水2.自建飲用水系統3.二次供水池/箱4.飲用水轉運水罐5.其他
				if (data.rows[i].pointLocationType == "1") {
					data.rows[i].pointLocationType = "直飲水";
				} else if (data.rows[i].pointLocationType == "2") {
					data.rows[i].pointLocationType = "自建飲用水系統";
				} else if (data.rows[i].pointLocationType == "3") {
					data.rows[i].pointLocationType = "二次供水池/箱";
				} else if (data.rows[i].pointLocationType == "4") {
					data.rows[i].pointLocationType = "飲用水轉運水罐";
				} else if (data.rows[i].pointLocationType == "5") {
					data.rows[i].pointLocationType = "其他";
				}
				value.rows[x++] = data.rows[i];
			}
			return value;

		},
		striped : true,
		pagination : true,
		rownumbers : true,
		pageNumber : 1,
		pageSize : 20,
		pageList : [ 10, 20, 30, 40, 50 ],
		singleSelect : true,
		columns : [ [ {
			field : 'id',
			title : '主鍵',
			hidden : true
		}, {
			field : 'serialno',
			title : '任務編號',
			sortable : true,
			width : 100
		}, {
			field : 'pointno',
			title : '系統編碼',
			sortable : true,
			width : 100
		}, {
			field : 'dptQun',
			title : '事業群',
			sortable : true,
			width : 100
		}, {
			field : 'dptChu',
			title : '事業處',
			sortable : true,
			width : 100
		}, {
			field : 'dptBu',
			title : '部門',
			sortable : true,
			width : 100
		}, {
			field : 'dptKe',
			title : '單位',
			sortable : true,
			width : 100
		}, {
			field : 'manager',
			title : '管理人',
			sortable : true,
			width : 50
		}, {
			field : 'pointLocationType',
			title : '點位類別',
			sortable : true,
			width : 100
		}, {
			field : 'auditType',
			title : '審核類別：0.新啟用1.重新啟用',
			sortable : true,
			width : 50
		}, {
			field : 'building',
			title : '樓棟',
			sortable : true,
			width : 100
		}, {
			field : 'floor',
			title : '樓層',
			sortable : true,
			width : 100
		}, {
			field : 'location',
			title : '方位',
			sortable : true,
			width : 100
		}, {
			field : 'equipmentNumber',
			title : '設備序號',
			sortable : true,
			width : 100
		}, {
			field : 'installLocation',
			title : '安裝位置',
			sortable : true,
			width : 100
		}, {
			field : 'useStatus',
			title : '點位狀態',
			sortable : true,
			width : 100
		} ] ],
		enableHeaderClickMenu : true,
		enableHeaderContextMenu : true,
		enableRowContextMenu : false,
		rowTooltip : true,
		toolbar : '#tb'
	});
	//創建下拉查詢條件
    $.ajax({
        url: ctx+"/system/dict/getDictByType/water_location",
        //dataType:"json",
        type: "GET",
        success: function (data) {
            //绑定第一个下拉框
            $("#qysjzt").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({value: '', label: '請選擇'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });
	
	
});

// 弹窗增加
function add() {
	d = $("#dlg").dialog({
		title : '添加飲用水點位明細表',
		width : 380,
		height : 380,
		href : ctx + '/pointitem/create',
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '确认',
			handler : function() {
				$("#mainform").submit();
			}
		}, {
			text : '取消',
			handler : function() {
				d.panel('close');
			}
		} ]
	});
}

// 删除
function del() {
	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data) {
		if (data) {
			$.ajax({
				type : 'get',
				url : ctx + "/pointitem/delete/" + row.id,
				success : function(data) {
					successTip(data, dg);
				}
			});
		}
	});
}

// 弹窗修改
function upd() {
	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	d = $("#dlg").dialog({
		title : '修改飲用水點位明細表',
		width : 380,
		height : 340,
		href : ctx + '/pointitem/update/' + row.id,
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '修改',
			handler : function() {
				$('#mainform').submit();
			}
		}, {
			text : '取消',
			handler : function() {
				d.panel('close');
			}
		} ]
	});
}
//弹窗修改
function addMaintenance() {
	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	d = $("#dlg").dialog({
		title : '添加點位維護明細',
		width : 380,
		height : 340,
		href : ctx + '/pointitemmaintenance/create/' + row.pointno,
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '新增',
			handler : function() {
				//$('#mainform').action=ctx + '/pointitemmaintenance/create';
				$('#mainform').submit();
			}
		}, {
			text : '取消',
			handler : function() {
				d.panel('close');
			}
		} ]
	});
}
// 创建查询对象并查询
function cx() {
	var obj = $("#searchFrom").serializeObject();
	dg.datagrid('load', obj);
}
// 表單進度查詢頁面查詢條件重置
function listSearchReset() {
	$("#searchFrom").form("reset");
}
// 导出excel
function exportExcel() {
	var form = document.getElementById("searchFrom");
	searchFrom.action = ctx + '/pointitem/exportExcel';
	form.submit();
}
// 使用狀態啟用停用
function setUseStatus(flag) {
	var row = dg.datagrid('getSelected');
	var msg="";
	if (rowIsNull(row))
		return;
	if (flag == '1') {
		msg="確定啟用該點位？";
		//0.新申請1.使用中2.手動停用（需重新申請該點位）3.複檢停用
		if (row.useStatus=="使用中")
			{
			 alert("新申請及複檢停用狀態才可啟用！");
			 return;
			}
	} else if (flag == '2') {
		msg="確定停用該點位？";
		if (row.useStatus=="新申請"||row.useStatus=="複檢停用")
		{
		 alert("只有使用中狀態才可停用！");
		 return;
		}
	}

	parent.$.messager.confirm('提示', msg, function(data) {
		if (data) {
			$.ajax({
				type : 'get',
				url : ctx + "/pointitem/setUseStatus/" + row.id+"?flag="+flag,
				success : function(data) {
					successTip(data, dg);
				}
			});
		}
	});
}