package com.foxconn.ipebg.system.web;

import cn.hutool.json.JSONUtil;
import com.foxconn.ipebg.buessness.workflow.entity.MyTaskCount;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.system.entity.OldMyTask;
import com.foxconn.ipebg.system.entity.TaskList;
import com.foxconn.ipebg.system.service.DictService;
import com.foxconn.ipebg.system.utils.RemoteServiceService;
import com.foxconn.ipebg.system.utils.UserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

/**
 * Company foxconn
 * Copyright (C) 2004-2018 All Rights Reserved.
 */
@Controller
@RequestMapping("system")
public class ExchangeTaskController extends BaseController {
    @Autowired
    private DictService dictService;
    @RequestMapping("exchange/json")
    @ResponseBody
    public List<Object> getOldTasks() {
        List<Object> taskList = new ArrayList<>();
        URL wsdlUrl = null;
        try {
            wsdlUrl = new URL(dictService.get(614).getValue());
            Service s = Service.create(wsdlUrl, new QName("http://ws.chn.foxconn.com/", "RemoteServiceService"));
            RemoteServiceService hs = s.getPort(new QName("http://ws.chn.foxconn.com/", "RemoteServicePort"), RemoteServiceService.class);
            String ret = hs.getAllMyTasks(UserUtil.getCurrentUser().getLoginName());
//            System.out.println(ret);
//            System.out.println(JSONUtil.toJsonStr(ret));
            OldMyTask tasks = JSONUtil.toBean(ret, OldMyTask.class);


            taskList.addAll(tasks.getBackCountList());
            taskList.addAll(tasks.getBackProxyCountList());
            taskList.addAll(tasks.getMyTaskCountList());
            taskList.addAll(tasks.getMyTaskProxyCountList());
        } catch (Exception e) {
//            e.printStackTrace();
            return taskList;
        }
        return taskList;
    }
}
