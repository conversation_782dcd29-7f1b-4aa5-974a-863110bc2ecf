package com.foxconn.ipebg.basics.vo;

import com.foxconn.ipebg.basics.entity.BsTrainingArrangeEntity;
import com.foxconn.ipebg.basics.entity.BsTrainingArrangePerEntity;

import java.util.List;

public class BsTrainingArrangeVO {
    private BsTrainingArrangeEntity trainingArrange;
    private List<BsTrainingArrangePerEntity> trainingPerArr;

    public BsTrainingArrangeEntity getTrainingArrange() {
        return trainingArrange;
    }

    public void setTrainingArrange(BsTrainingArrangeEntity trainingArrange) {
        this.trainingArrange = trainingArrange;
    }

    public List<BsTrainingArrangePerEntity> getTrainingPerArr() {
        return trainingPerArr;
    }

    public void setTrainingPerArr(List<BsTrainingArrangePerEntity> trainingPerArr) {
        this.trainingPerArr = trainingPerArr;
    }
}
