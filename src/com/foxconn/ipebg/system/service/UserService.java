package com.foxconn.ipebg.system.service;

import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseService;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.system.entity.TPubMailrecordEntity;
import com.foxconn.ipebg.system.utils.UsernamePasswordCaptchaToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foxconn.ipebg.common.utils.DateUtils;
import com.foxconn.ipebg.common.utils.security.Digests;
import com.foxconn.ipebg.common.utils.security.Encodes;
import com.foxconn.ipebg.system.dao.UserDao;
import com.foxconn.ipebg.system.entity.User;

import java.util.List;

/**
 * 用户service
 * <AUTHOR>
 * @date 2015年1月13日
 */
@Service
@Transactional(readOnly = true)
public class UserService extends BaseService<User, Integer> {
	@Autowired
	private TPubMailrecordService mailrecordService;
	
	/**加密方法*/
	public static final String HASH_ALGORITHM = "SHA-1";
	public static final int HASH_INTERATIONS = 1024;
	private static final int SALT_SIZE = 8;	//盐长度

	@Autowired
	private UserDao userDao;

	@Override
	public HibernateDao<User, Integer> getEntityDao() {
		return userDao;
	}

	/**
	 * 保存用户
	 * @param user
	 */
	@Transactional(readOnly=false)
	public void save(User user) {
		entryptPassword(user);
		user.setCreateDate(DateUtils.getSysTimestamp());
		userDao.save(user);
	}

	/**
	 * 修改密码
	 * @param user
	 */
	@Transactional(readOnly=false)
	public void updatePwd(User user) {
		entryptPassword(user);
		userDao.save(user);
	}
	
	/**
	 * 删除用户
	 * @param id
	 */
	@Transactional(readOnly=false)
	public void delete(Integer id){
		if(!isSupervisor(id))
			userDao.delete(id);
	}
	
	/**
	 * 按登录名查询用户
	 * @param loginName
	 * @return 用户对象
	 */
	public User getUser(String loginName) {
		return userDao.findUniqueBy("loginName", loginName);
	}
	
	/**
	 * 判断是否超级管理员
	 * @param id
	 * @return boolean
	 */
	private boolean isSupervisor(Integer id) {
		return id == 1;
	}
	
	/**
	 * 设定安全的密码，生成随机的salt并经过1024次 sha-1 hash
	 */
	private void entryptPassword(User user) {
		byte[] salt = Digests.generateSalt(SALT_SIZE);
		user.setSalt(Encodes.encodeHex(salt));

		byte[] hashPassword = Digests.sha1(user.getPlainPassword().getBytes(),salt, HASH_INTERATIONS);
		user.setPassword(Encodes.encodeHex(hashPassword));
	}
	
	/**
	 * 验证原密码是否正确
	 * @param user
	 * @param oldPwd
	 * @return
	 */
	public boolean checkPassword(User user,String oldPassword){
		byte[] salt =Encodes.decodeHex(user.getSalt()) ;
		byte[] hashPassword = Digests.sha1(oldPassword.getBytes(),salt, HASH_INTERATIONS);
		if(user.getPassword().equals(Encodes.encodeHex(hashPassword))){
			return true;
		}else{
			return false;
		}
	}
	
	/**
	 * 修改用户登录
	 * @param user
	 */
	public void updateUserLogin(User user){
		user.setLoginCount((user.getLoginCount()==null?0:user.getLoginCount())+1);
		user.setPreviousVisit(user.getLastVisit());
		user.setLastVisit(DateUtils.getSysTimestamp());
		update(user);
	}

	/**
	  * 方法描述: 批量通過id查詢用戶信息
	  * @Author: S6114648
	  * @CreateDate:   2018/10/11  下午 01:52
	  * @Return
	  **/
	public List<User> getUserInfos(List<Integer> ids){
		return this.userDao.find(ids);
	}

	/**
	 * 獲取免密碼登錄驗證碼
	 * @param token 用戶名
	 * @return 密碼
	 */
	public Boolean getUtokenCipherCode(UsernamePasswordCaptchaToken token){
        TPubMailrecordEntity entity = mailrecordService.findByProperty(token);
        if(entity!=null){
            return true;
        }
		return false;
	}
	/**
	 * 獲取手機驗證碼
	 * @param userName 用戶名
	 * @return 密碼
	 */
	public String getUtokenPhone(String userName){
		return "123456";
	}
}
