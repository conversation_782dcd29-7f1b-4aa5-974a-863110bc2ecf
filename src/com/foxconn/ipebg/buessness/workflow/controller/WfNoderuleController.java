package com.foxconn.ipebg.buessness.workflow.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.common.persistence.Page;


/**
 * 流程中會簽節點流程規則
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-06 11:04:39
 */
@Controller
@RequestMapping("wfnoderule")
public class WfNoderuleController extends BaseController {

    @Autowired
    private WfNoderuleService wfNoderuleService;

    /**
      * 方法描述: 列表信息
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:39
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    @RequiresPermissions("workflow:wfnoderule:list")
    public String list() {
        //查询列表数据
        return "buessness/workflow/wfnoderule/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:39
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    @RequiresPermissions("workflow:wfnoderule:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<WfNoderuleEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        page = wfNoderuleService.search(page, filters);
        return getEasyUIData(page);
    }
    /**
      * 方法描述: 添加跳轉
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:39
      * @Return
      **/

    @RequiresPermissions("workflow:wfnoderule:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("wfNoderule", new WfNoderuleEntity());
        model.addAttribute("action", "create");
        return "buessness/workflow/wfnoderule/listForm";
    }
    /**
      * 方法描述: 保存
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:39
      * @Return
      **/

    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("workflow:wfnoderule:add")
    @ResponseBody
    public String create(@Valid WfNoderuleEntity wfNoderule, Model model) {
        wfNoderuleService.save(wfNoderule);
        return "success";
    }
    /**
      * 方法描述: 修改跳轉
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:39
      * @Return
      **/

    @RequiresPermissions("workflow:wfnoderule:update")
    @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
    public String updateForm(@PathVariable("id") String id, Model model) {
        model.addAttribute("wfNoderule", wfNoderuleService.get(id));
        model.addAttribute("action", "update");
        return "buessness/workflow/wfnoderule/listForm";
    }
    /**
      * 方法描述: 修改
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:39
      * @Return
      **/

    @RequestMapping(value="update", method = RequestMethod.POST)
    @RequiresPermissions("workflow:wfnoderule:update")
    @ResponseBody
    public String update(@Valid WfNoderuleEntity wfNoderule) {
        wfNoderuleService.update(wfNoderule);
        return "success";
    }

    /**
      * 方法描述: 根據主鍵刪除
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:39
      * @Return
      **/

    @RequestMapping("delete/{id}")
    @RequiresPermissions("workflow:wfnoderule:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        wfNoderuleService.delete(id);
        return "success";
    }

}
