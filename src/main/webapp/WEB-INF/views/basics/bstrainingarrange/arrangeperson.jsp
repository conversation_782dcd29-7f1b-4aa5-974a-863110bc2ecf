<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>培訓期日排班人員安排</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
        var _periodList = ${periodList};
        var _trainingPeriodNo="${trainingPeriodNo}";
        var _arrangeDate="${arrangeDate}";
    </script>

    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .trainee{
            float: left;
            text-align: center;
            border: 1px solid #e0ecff;
            width: 140px;
            padding: 10px 5px;
            border-radius: 5px;
            margin: 5px 2px;
        }
        .trainee:hover {
            float: left;
            text-align: center;
            border: 1px solid #999dff;
            background-color: #e0ecff;
            cursor:pointer;
            width: 140px;
            padding: 10px 5px;
        }
        .unenable{
            float: left;
            text-align: center;
            border: 1px solid #e0ecff;
            width: 140px;
            padding: 10px 5px;
            border-radius: 5px;
            margin: 5px 2px;
            background-color: #d6d6d6;
        }

        .trainee-title{
            text-align: center;
            background-color: #e0ecff;
            padding: 10px 0px;
            color: #783bc7;
        }
        .main{
            border: 1px solid #e0ecff;
            margin: 10px 10px;
            border-radius: 5px;
            font-size: 14px;
        }


        .btn {
            float: left;
            text-align: center;
            width: 80px;
            padding: 10px 5px;
            border-radius: 5px;
            margin: 5px 5px;
            border: 1px solid #999dff;
            cursor:pointer;
            font-size: 14px;
            background-color: #e0ecff;
        }

    </style>
</head>
<body>
<div>
    <table class="formTable">

        <tr>
            <td>培訓期名稱：</td>
            <td>
                <input id="trainingPeriodNo" name="trainingPeriodNo" class="easyui-combogrid"
                       data-options="width: 200,required:true" />
            </td>
            <td>排班日期：</td>
            <td>
                <input id="arrangeDate" type="text" name="arrangeDate" class="Wdate easyui-validatebox" datefmt="yyyy-MM-dd"
                       data-options="prompt: '排班日期'" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',onpicked:function(){initData();}})" style="width:150px;"/>

            </td>
            <td>
                <label>培訓時間：</label>
                <span id="trainingTime"></span>
            </td>
        </tr>
    </table>
    <div class="main">

        <div class="trainee-title">培訓人員</div>
        <div id="traineeArr" >

        </div>
        <div class="trainee-title">調休人員</div>
        <div id="no_traineeArr" >

        </div>
        <div style="text-align: -webkit-center;margin-top: 10px;">
            <div style="width: 310px;">
            <shiro:hasPermission name="basics:bstrainingarrange:add">
                <div id="save" class="btn" onclick="save()">保存</div>
            </shiro:hasPermission>
            <shiro:hasPermission name="basics:bstrainingarrange:add">
                <div id="saveAndContinue" class=" btn" onclick="saveAndContinue()">保存並繼續</div>
            </shiro:hasPermission>
                <div id="cancel" class=" btn" onclick="cancel()">取消</div>
                <div style="clear: both"></div>
            </div>
        </div>
    </div>



</div>
<script src='${ctx}/static/js/basics/bstrainingarrangeperson.js?"+Math.random()"'></script>

</body>
</html>