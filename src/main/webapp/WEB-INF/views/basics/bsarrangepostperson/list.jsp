<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>崗位人員表</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
    var _groupList = ${groupList};
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
        <form id="searchFrom" action="">

			<input type="text" id="arrangeGroupId" name="filter_EQS_arrangeGroupId" class="easyui-combogrid"
				   data-options="width:150,prompt: '群組名稱'"/>
			<input id="securityCom" name="filter_EQS_securityCom" class="easyui-combobox" data-options="panelHeight:'auto',valueField:'value', textField:'label',url: '${ctx}/system/user/userSecurityCom',width: 150" />
			<input type="text" name="filter_GED_arrangeDate" class="easyui-my97" datefmt="yyyy-MM-dd"
				   data-options="width:150,prompt: '开始日期'"/>
			- <input type="text" name="filter_LED_arrangeDate" class="easyui-my97" datefmt="yyyy-MM-dd"
					 data-options="width:150,prompt: '结束日期'"/>


		<span class="toolbar-item dialog-tool-separator"></span>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
		   onclick="listSearchReset()">重置</a>

		   <br/>
		   	<shiro:hasPermission name="basics:bsarrangepostperson:add">
	       		<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="add();">添加</a>
	       		<span class="toolbar-item dialog-tool-separator"></span>
	       	</shiro:hasPermission>


	</form>

  </div>
<table id="dg"></table>
<div id="dlg"></div>

<script src="${ctx}/static/js/basics/bsarrangepostperson.js?random=<%= Math.random()%>"></script>
</body>
</html>