package com.foxconn.ipebg.buessness.workflow.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 流程中會簽節點流程規則
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-06 11:04:39
 */
@Entity
@Table(name = "WF_NODERULE")
@DynamicUpdate
@DynamicInsert
public class WfNoderuleEntity extends DataEntity<WfNoderuleEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    //節點id
    private String nodeid;
    //會簽節點會簽核參數名
    private String nodeparamname;
    //會簽節點人總數
    private String totalpeople;
    //會簽通過率
    private String passrate;
    //會簽通過類型1一票否決 2一票通過 3 超過某個比例才算通過
    private String voterule;
    //會簽狀態變更名稱
    private String taskstatusvariable;
    //會簽節點后分支變更
    private String taskcompletevariable;

    private String version;

    /**
     * 设置：節點id
     */
    public void setNodeid(String nodeid) {
        this.nodeid = nodeid;
    }

    /**
     * 获取：節點id
     */
    @Column(name = "NODEID", nullable = false, length = 20)
    public String getNodeid() {
        return nodeid;
    }

    /**
     * 设置：會簽節點會簽核參數名
     */
    public void setNodeparamname(String nodeparamname) {
        this.nodeparamname = nodeparamname;
    }

    /**
     * 获取：會簽節點會簽核參數名
     */
    @Column(name = "NODEPARAMNAME", nullable = false, length = 20)
    public String getNodeparamname() {
        return nodeparamname;
    }

    /**
     * 设置：會簽節點人總數
     */
    public void setTotalpeople(String totalpeople) {
        this.totalpeople = totalpeople;
    }

    /**
     * 获取：會簽節點人總數
     */
    @Column(name = "TOTALPEOPLE", nullable = false, length = 20)
    public String getTotalpeople() {
        return totalpeople;
    }

    /**
     * 设置：會簽通過率
     */
    public void setPassrate(String passrate) {
        this.passrate = passrate;
    }

    /**
     * 获取：會簽通過率
     */
    @Column(name = "PASSRATE", nullable = false, length = 20)
    public String getPassrate() {
        return passrate;
    }

    /**
     * 设置：會簽通過類型1一票否決 2一票通過 3 超過某個比例才算通過
     */
    public void setVoterule(String voterule) {
        this.voterule = voterule;
    }

    /**
     * 获取：會簽通過類型1一票否決 2一票通過 3 超過某個比例才算通過
     */
    @Column(name = "VOTERULE", nullable = false, length = 20)
    public String getVoterule() {
        return voterule;
    }

    /**
     * 设置：會簽狀態變更名稱
     */
    public void setTaskstatusvariable(String taskstatusvariable) {
        this.taskstatusvariable = taskstatusvariable;
    }

    /**
     * 获取：會簽狀態變更名稱
     */
    @Column(name = "TASKSTATUSVARIABLE", nullable = false, length = 20)
    public String getTaskstatusvariable() {
        return taskstatusvariable;
    }

    /**
     * 设置：會簽節點后分支變更
     */
    public void setTaskcompletevariable(String taskcompletevariable) {
        this.taskcompletevariable = taskcompletevariable;
    }

    /**
     * 获取：會簽節點后分支變更
     */
    @Column(name = "TASKCOMPLETEVARIABLE", nullable = false, length = 20)
    public String getTaskcompletevariable() {
        return taskcompletevariable;
    }

    @Column(name = "VERSION", nullable = false, length = 20)
    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
}
