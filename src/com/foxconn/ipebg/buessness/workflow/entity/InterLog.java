package com.foxconn.ipebg.buessness.workflow.entity;

import java.io.Serializable;

/**
 * 接口交互日誌
 * <AUTHOR>
 *
 */
public class InterLog implements Serializable {
	private static final long serialVersionUID = 1L;
	private String id;
	/**
	 * 操作人工號
	 */
	private String empNo;
	/**
	 * 操作時間
	 */
	private String requestTime;
	/**
	 * 操作者ip
	 */
	private String requestIp;
	/**
	 * 請求參數
	 */
	private String requestMsg;
	/**
	 * 接口返回結果
	 */
	private String requestBackMsg;
	/**
	 * 接口調用時長
	 */
	private String requestCostTime;
	private String processid;
	private String status;
	private String times;
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getEmpNo() {
		return empNo;
	}
	public void setEmpNo(String empNo) {
		this.empNo = empNo;
	}
	public String getRequestTime() {
		return requestTime;
	}
	public void setRequestTime(String requestTime) {
		this.requestTime = requestTime;
	}
	public String getRequestIp() {
		return requestIp;
	}
	public void setRequestIp(String requestIp) {
		this.requestIp = requestIp;
	}
	public String getRequestMsg() {
		return requestMsg;
	}
	public void setRequestMsg(String requestMsg) {
		this.requestMsg = requestMsg;
	}
	public String getRequestBackMsg() {
		return requestBackMsg;
	}
	public void setRequestBackMsg(String requestBackMsg) {
		this.requestBackMsg = requestBackMsg;
	}
	public String getRequestCostTime() {
		return requestCostTime;
	}
	public void setRequestCostTime(String requestCostTime) {
		this.requestCostTime = requestCostTime;
	}

	public String getProcessid() {
		return processid;
	}

	public void setProcessid(String processid) {
		this.processid = processid;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getTimes() {
		return times;
	}

	public void setTimes(String times) {
		this.times = times;
	}
}
