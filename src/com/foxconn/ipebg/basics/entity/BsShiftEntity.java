package com.foxconn.ipebg.basics.entity;

        import java.io.Serializable;
        import java.util.Date;
        import javax.persistence.*;

        import com.foxconn.ipebg.common.entity.DataEntity;
        import org.hibernate.annotations.DynamicInsert;
        import org.hibernate.annotations.DynamicUpdate;


/**
 * 班別資料
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-03-07 09:57:22
 */
@Entity
@Table(name = "BS_SHIFT")
@DynamicUpdate
@DynamicInsert
public class BsShiftEntity extends DataEntity<BsShiftEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

                                    //班別代碼
            private String shiftNo;
                                //班別名稱
            private String shiftName;
                                //開始時間
            private String startTime;
                                //結束時間
            private String endTime;
                                                                           
            
                    /**
             * 设置：班別代碼
             */
            public void setShiftNo(String shiftNo) {
                this.shiftNo = shiftNo;
            }

        /**
         * 获取：班別代碼
         */
        
                            @Column(name = "SHIFT_NO", nullable = false, length = 20)
                public String getShiftNo() {
                    return shiftNo;
                }
                    
                    /**
             * 设置：班別名稱
             */
            public void setShiftName(String shiftName) {
                this.shiftName = shiftName;
            }

        /**
         * 获取：班別名稱
         */
        
                            @Column(name = "SHIFT_NAME", nullable = false, length = 20)
                public String getShiftName() {
                    return shiftName;
                }
                    
                    /**
             * 设置：開始時間
             */
            public void setStartTime(String startTime) {
                this.startTime = startTime;
            }

        /**
         * 获取：開始時間
         */
        
                            @Column(name = "START_TIME", nullable = false, length = 20)
                public String getStartTime() {
                    return startTime;
                }
                    
                    /**
             * 设置：結束時間
             */
            public void setEndTime(String endTime) {
                this.endTime = endTime;
            }

        /**
         * 获取：結束時間
         */
        
                            @Column(name = "END_TIME", nullable = false, length = 20)
                public String getEndTime() {
                    return endTime;
                }
                    
        
        
        
        
        
}
