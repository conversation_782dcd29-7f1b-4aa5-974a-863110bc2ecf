package com.foxconn.ipebg.system.utils;

import com.foxconn.ipebg.gen.util.Parse;

import javax.servlet.http.HttpServletRequest;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;

public class CommonUtils {
	
	/**
	 * 判斷一個字符串是否是日期格式
	 */
	  public static boolean isValidDate(String str) {
		  str=str.trim();
		boolean convertSuccess = true;
		// 指定日期格式为四位年/两位月份/两位日期，注意yyyy/MM/dd区分大小写；
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		try {
			// 设置lenient为false.
			// 否则SimpleDateFormat会比较宽松地验证日期，比如2007/02/29会被接受，并转换成2007/03/01
			format.setLenient(false);
			format.parse(str);
		} catch (ParseException e) {
			// e.printStackTrace();
			// 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
			convertSuccess = false;
		}
		return convertSuccess;
	}

	/**
	 * @param request 请求对象
	 * @return 根据请求对象获取服务器地址
	 */
	public static String getServerBaseURL(HttpServletRequest request) {
		try {
			URL url = new URL(request.getRequestURL().toString());
			return url.getProtocol()+"://"+url.getHost()+(url.getPort() == -1 ? "" : (":"+url.getPort()))+request.getContextPath();
		} catch (Exception e) {
			return null;
		}
	}
}
