package com.foxconn.ipebg.system.utils;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.beanutils.BeanUtilsBean;
import org.apache.commons.beanutils.BeanUtilsBean2;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.beanutils.converters.DateConverter;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.*;

/**
 * 文件操作
 */
public class Excel2007Utils {
	public static File createFile(List exportData, LinkedHashMap map, String filePath, String fileName) throws IOException {
		File csvFile = null;
		try {
			String fullPath = filePath + File.separator + fileName + ".xlsx";
			csvFile = new File(fullPath);
			if (!csvFile.getParentFile().exists()) { // 如果父目录不存在，创建父目录
				csvFile.getParentFile().mkdirs();
			}
			if (csvFile.exists()) { // 如果已存在,删除旧文件
				csvFile.delete();
			}
			XSSFWorkbook workbook1 = new XSSFWorkbook();
			SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook(workbook1, 100);
			// Workbook workbook = WorkbookFactory.create(new FileInputStream(new File(filePath)));
			Sheet first = sxssfWorkbook.createSheet();
			// 写入文件头部
			Row row = first.createRow(0);
			int j = 0;
			for (Iterator propertyIterator = map.entrySet().iterator(); propertyIterator.hasNext();) {
				Map.Entry propertyEntry = (Map.Entry) propertyIterator.next();
				row.createCell(j).setCellValue((String) propertyEntry.getValue() != null ? (String) propertyEntry.getValue() : "");
				j++;
			}
			// 写入文件内容
			int exportDataLength = exportData.size();
			for (int rowIndex = 0; rowIndex < exportDataLength; rowIndex++) {
				Row rowData = first.createRow(rowIndex + 1);
				Object rowObject = (Object) exportData.get(rowIndex);
				int columnIndex = 0;
				for (Iterator propertyIterator = map.entrySet().iterator(); propertyIterator.hasNext();) {
					Map.Entry propertyEntry = (Map.Entry) propertyIterator.next();
					//Cell createCell = rowData.createCell(columnIndex);
					rowData.createCell(columnIndex).setCellValue(String.valueOf(BeanUtils.getProperty(rowObject, (String) propertyEntry.getKey())));
					columnIndex++;
				}
			}
//			FileOutputStream out = new FileOutputStream("fileName" + ".xlsx");
			
			FileOutputStream outputStream = new FileOutputStream(fullPath);
			sxssfWorkbook.write(outputStream);
			outputStream.flush();
			outputStream.close();
//			sxssfWorkbook.write(out);
//			out.close();
		} catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return csvFile;
	}
	public static File createFile(List exportData, LinkedHashMap map, String filePath, String fileName,String ...numberCells ) throws IOException {
		File csvFile = null;
		try {
			String fullPath = filePath + File.separator + fileName + ".xlsx";
			csvFile = new File(fullPath);
			if (!csvFile.getParentFile().exists()) { // 如果父目录不存在，创建父目录
				csvFile.getParentFile().mkdirs();
			}
			if (csvFile.exists()) { // 如果已存在,删除旧文件
				csvFile.delete();
			}
			XSSFWorkbook workbook1 = new XSSFWorkbook();
			SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook(workbook1, 100);
			// Workbook workbook = WorkbookFactory.create(new FileInputStream(new File(filePath)));
			Sheet first = sxssfWorkbook.createSheet();
			// 写入文件头部
			Row row = first.createRow(0);
			int j = 0;
			for (Iterator propertyIterator = map.entrySet().iterator(); propertyIterator.hasNext();) {
				Map.Entry propertyEntry = (Map.Entry) propertyIterator.next();
				row.createCell(j).setCellValue((String) propertyEntry.getValue() != null ? (String) propertyEntry.getValue() : "");
				j++;
			}
			// 写入文件内容
			int exportDataLength = exportData.size();
			CellStyle style=sxssfWorkbook.createCellStyle();
			style.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
			BeanUtilsBean.setInstance(new BeanUtilsBean2());
			DateConverter converter = new DateConverter();
			converter.setPatterns(new String[]{"yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss"});
			ConvertUtils.register(converter, Date.class);
			if(numberCells != null){
				List<String> numberCellList = Arrays.asList(numberCells);
				for (int rowIndex = 0; rowIndex < exportDataLength; rowIndex++) {
					Row rowData = first.createRow(rowIndex + 1);
					Object rowObject = (Object) exportData.get(rowIndex);
					int columnIndex = 0;
					for (Iterator propertyIterator = map.entrySet().iterator(); propertyIterator.hasNext();) {
						Map.Entry propertyEntry = (Map.Entry) propertyIterator.next();
						String cellKey = (String) propertyEntry.getKey();
						Cell cell = rowData.createCell(columnIndex);
						if(numberCellList.contains(cellKey)){
							cell.setCellStyle(style);
							cell.setCellValue(Double.valueOf(BeanUtils.getProperty(rowObject, cellKey)));
						}else{
//							cell.setCellValue(String.valueOf(BeanUtils.getProperty(rowObject, (String) propertyEntry.getKey())));
							cell.setCellValue(BeanUtils.getProperty(rowObject, (String) propertyEntry.getKey()));
						}
						columnIndex++;
					}
				}
			}else{
				for (int rowIndex = 0; rowIndex < exportDataLength; rowIndex++) {
					Row rowData = first.createRow(rowIndex + 1);
					Object rowObject = (Object) exportData.get(rowIndex);
					int columnIndex = 0;
					for (Iterator propertyIterator = map.entrySet().iterator(); propertyIterator.hasNext();) {
						Map.Entry propertyEntry = (Map.Entry) propertyIterator.next();
						Cell cell = rowData.createCell(columnIndex);
//						cell.setCellValue(String.valueOf(BeanUtils.getProperty(rowObject, (String) propertyEntry.getKey())));

						cell.setCellValue(BeanUtils.getProperty(rowObject, (String) propertyEntry.getKey()));
						columnIndex++;
					}
				}
			}
			
//			FileOutputStream out = new FileOutputStream("fileName" + ".xlsx");
			
			FileOutputStream outputStream = new FileOutputStream(fullPath);
			sxssfWorkbook.write(outputStream);
			outputStream.flush();
			outputStream.close();
//			sxssfWorkbook.write(out);
//			out.close();
		} catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return csvFile;
	}
	

	/**
	 * 下载文件
	 * 
	 * @param response
	 *            文件路径
	 * @param fileName
	 *            文件名称
	 * @throws IOException
	 */
	public static void exportFile(HttpServletResponse response, String filePatherPath, String fileName) throws IOException {
		try {
			String fullPath = filePatherPath + File.separator + fileName;
			response.reset();
			response.setContentType("application/vnd.ms-excel;charset=utf-8");
			response.setHeader("Content-Disposition","attachment;filename="+fileName);
			ServletOutputStream out = response.getOutputStream();
			Workbook wb = WorkbookFactory.create(new FileInputStream(new File(fullPath)));
	        wb.write(out);
			out.flush();
		} catch (Exception e) {
			e.printStackTrace();
		} 
	}
	/**
	 * 删除该目录filePath下的所有文件
	 * 
	 * @param filePath
	 *            文件目录路径
	 */
	public static void deleteFiles(String filePath) {
		File file = new File(filePath);
		if (file.exists()) {
			File[] files = file.listFiles();
			for (int i = 0; i < files.length; i++) {
				if (files[i].isFile()) {
					files[i].delete();
				}
			}
		}
	}

	/**
	 * 删除单个文件
	 * 
	 * @param filePath
	 *            文件目录路径
	 * @param fileName
	 *            文件名称
	 */
	public static void deleteFile(String filePath, String fileName) {
		File file = new File(filePath);
		if (file.exists()) {
			File[] files = file.listFiles();
			for (int i = 0; i < files.length; i++) {
				if (files[i].isFile()) {
					if (files[i].getName().equals(fileName)) {
						files[i].delete();
						return;
					}
				}
			}
		}
	}


}
