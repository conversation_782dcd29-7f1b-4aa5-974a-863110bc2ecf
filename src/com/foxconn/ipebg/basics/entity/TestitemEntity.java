package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * 水質檢測項目維護
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-12-26 08:57:03
 */
@Entity
@Table(name = "TESTITEM")
@DynamicUpdate
@DynamicInsert
public class TestitemEntity extends DataEntity<TestitemEntity> implements
		Serializable {
	private static final long serialVersionUID = 1L;

	// 檢測項目
	private String testItems;
	// 樣品類別
	private String sampleTyp;
	// 檢驗費用（元）
	private double testCost; 

	/**
	 * 设置：檢測項目
	 */
	public void setTestItems(String testItems) {
		this.testItems = testItems;
	}

	/**
	 * 获取：檢測項目
	 */

	@Column(name = "TEST_ITEMS", nullable = false, length = 20)
	public String getTestItems() {
		return testItems;
	}

	/**
	 * 设置：樣品類別
	 */
	public void setSampleTyp(String sampleTyp) {
		this.sampleTyp = sampleTyp;
	}

	/**
	 * 获取：樣品類別
	 */

	@Column(name = "SAMPLE_TYP", nullable = false, length = 20)
	public String getSampleTyp() {
		return sampleTyp;
	}

	/**
	 * 设置：檢驗費用（元）
	 */
	public void setTestCost(double testCost) {
		this.testCost = testCost;
	}

	/**
	 * 获取：檢驗費用（元）
	 */

	@Column(name = "TEST_COST", nullable = false, length = 20)
	public double getTestCost() {
		return testCost;
	}

}
