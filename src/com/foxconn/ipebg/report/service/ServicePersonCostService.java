package com.foxconn.ipebg.report.service;

import com.foxconn.ipebg.basics.vo.FinancialVO;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import com.foxconn.ipebg.common.utils.ConvertUtils;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.report.dao.ServicePersonCostDao;
import com.foxconn.ipebg.report.entity.ServicePersonCostEntity;
import com.foxconn.ipebg.system.entity.User;
import com.foxconn.ipebg.system.utils.UserUtil;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.hibernate.type.StandardBasicTypes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Service
@Transactional(readOnly=true)
public class ServicePersonCostService extends BaseBusinessService<ServicePersonCostEntity, String> {

    @Autowired
    private ServicePersonCostDao dao;

    @Override
    public HibernateDao<ServicePersonCostEntity, String> getEntityDao() {
        return dao;
    }

    /**
     * 經管報表數據查詢
     * @param params 查詢參數 dptQun legal costId securityCom startDate endDate
     * @return 列表數據
     */
    public List<FinancialVO> financialList(Map<String, String> params) {
        String dptQun = params.get("dptQun");
        String legal = params.get("legal");
        String costId = params.get("costId");
        String securityCom = params.get("securityCom");
        String workMonth = params.get("workMonth");
        StringBuffer sql = new StringBuffer();
        sql.append("select dpt_qun as dptQun, legal_person as legal, cost_id as costId, sum(normal_cost)+sum(trainee_cost) regularCost, sum(overtime_cost) overtimeCost");
        sql.append(", (select round(COALESCE(sum(expenses), 0), 2) from BS_TEMP_SERVICE where cost_code = a.cost_id and date_trunc('month',start_time) = to_timestamp('"+workMonth+"', 'yyyymm')) tempCost1");
        if (StringUtils.isNotBlank(securityCom)) {
            sql.append(", (select round(COALESCE(sum(b.cost), 0), 2) from e_temp_security_cost_aply b, e_temp_security_aply c where b.service_serialno = c.serialno and b.apply_stat = 'E' and c.apply_stat = 'E' and c.cost_id = a.cost_id and b.security_com = '"+securityCom+"' and date_trunc('month',c.start_time) = to_timestamp('"+workMonth+"', 'yyyymm')) tempCost2");
        } else {
            sql.append(", (select round(COALESCE(sum(b.cost), 0), 2) from e_temp_security_cost_aply b, e_temp_security_aply c where b.service_serialno = c.serialno and b.apply_stat = 'E' and c.apply_stat = 'E' and c.cost_id = a.cost_id and date_trunc('month',c.start_time) = to_timestamp('"+workMonth+"', 'yyyymm')) tempCost2");
        }
        sql.append(" from bs_service_person_cost a");
        sql.append(" where date_trunc('month', period_begin_date) = to_timestamp('"+workMonth+"', 'yyyymm')");
        if (StringUtils.isNotBlank(dptQun)) {
            sql.append(" and dpt_qun = '"+dptQun+"'");
        }
        if (StringUtils.isNotBlank(legal)) {
            sql.append(" and legal_person = '"+legal+"'");
        }
        if (StringUtils.isNotBlank(costId)) {
            sql.append(" and cost_id = '"+costId+"' ");
        }
        if (StringUtils.isNotBlank(securityCom)) {
            sql.append(" and company = '"+securityCom+"'");
        }
        User user = UserUtil.getCurrentUser();
        if (user != null) {
            // 按保安公司查询
            String securityComs = user.getSecurityCom().replace(",", "','");
            sql.append(" and company in ('"+securityComs+"') ");
        }
        sql.append(" group by dpt_qun, legal_person, cost_id");
        sql.append(" order by dpt_qun, legal_person, cost_id");
        SQLQuery sqlQuery=this.dao.createSQLQuery(sql.toString());
        sqlQuery.addScalar("dptQun", StandardBasicTypes.STRING);
        sqlQuery.addScalar("legal",StandardBasicTypes.STRING);
        sqlQuery.addScalar("costId",StandardBasicTypes.STRING);
        sqlQuery.addScalar("regularCost",StandardBasicTypes.FLOAT);
        sqlQuery.addScalar("overtimeCost",StandardBasicTypes.FLOAT);
        sqlQuery.addScalar("tempCost1",StandardBasicTypes.FLOAT);
        sqlQuery.addScalar("tempCost2",StandardBasicTypes.FLOAT);
        sqlQuery.setResultTransformer(Transformers.aliasToBean(FinancialVO.class));
        List<FinancialVO> resultList = sqlQuery.list();
        ConvertUtils.convertPropertyToDictLabel(resultList, "legal", "guard_legalperson");
        return resultList;
    }
}
