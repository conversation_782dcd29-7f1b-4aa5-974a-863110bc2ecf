package com.foxconn.ipebg.basics.controller;

import com.foxconn.ipebg.basics.entity.BsCostEntity;
import com.foxconn.ipebg.basics.service.BsCostService;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.web.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 費用基本資料
 *
 * <AUTHOR>
 * @date 2021-7-21 14:54:46
 */
@Controller
@RequestMapping("bscost")
public class BsCostController extends BaseController {
    @Autowired
    private BsCostService bsCostService;

    /**
     * 方法描述: 列表信息
     * @Author: S6114893
     * @CreateDate:   2021-7-21 14:54:46
     * @Return
     **/
    @RequestMapping(method = RequestMethod.GET)
    public String list() {
        //查询列表数据
        return "basics/bscost/list";
    }

    /**
     * 方法描述:  分頁查詢信息
     * @Author: S6114893
     * @CreateDate:   2021-7-21 14:54:46
     * @Return
     **/
    @RequestMapping(value = "list", method = RequestMethod.GET)
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<BsCostEntity> page = getPage(request);
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        String month = request.getParameter("filter_LED_createDate");
        if (StringUtils.isNotBlank(month)) {
            Integer year = Integer.parseInt(month.substring(0, 4));
            Integer mon = Integer.parseInt(month.substring(5));
            Calendar cal = new GregorianCalendar();
            cal.set(year, mon - 1, 1);
            cal.add(Calendar.MONTH, 1);
            SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
            PropertyFilter filter = new PropertyFilter("LED_createDate", dft.format(cal.getTime()));
            filters.add(filter);
        }
        page = bsCostService.search(page, filters);
        return getEasyUIData(page);
    }

    /**
     * 方法描述: 添加跳轉
     * @Author: S6114893
     * @CreateDate:   2021-7-21 14:54:46
     * @Return
     **/
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("bsCost", new BsCostEntity());
        model.addAttribute("action", "create");
        return "basics/bscost/listForm";
    }

    /**
     * 方法描述: 保存
     * @Author: S6114893
     * @CreateDate:   2021-7-21 14:54:46
     * @Return
     **/
    @RequestMapping(value="create", method = RequestMethod.POST)
    @ResponseBody
    public String create(@Valid BsCostEntity bsCost, Model model) {
        bsCost.setNewRecord(true);
        bsCost.setCreateDate(new Date());
        bsCostService.save(bsCost);
        return "success";
    }

    /**
     * 方法描述: 根據主鍵刪除
     * @Author: S6114893
     * @CreateDate:   2021-07-21 14:54:46
     * @Return
     **/
    @RequestMapping("delete/{id}")
    @RequiresPermissions("basics:bscost:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        bsCostService.delete(id);
        return "success";
    }
}
