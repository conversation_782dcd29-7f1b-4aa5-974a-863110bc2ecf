package com.foxconn.ipebg.audit.controller;

import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.basics.entity.TestitemEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfConifgEntity;
import com.foxconn.ipebg.audit.entity.EBsCodeEntity;
import com.foxconn.ipebg.audit.service.EBsCodeService;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;

import com.foxconn.ipebg.buessness.workflow.entity.TaskNode;
import com.foxconn.ipebg.system.entity.Dict;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.system.service.OrganizationService;
import com.foxconn.ipebg.system.service.DictService;
import com.foxconn.ipebg.system.service.TPubFileobjectService;


/**
 * 代碼基本表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-01-23 09:32:47
 */
@Controller
@RequestMapping("ebscode")
public class EBsCodeController extends BaseController {

    @Autowired
    private EBsCodeService eBsCodeService;

    @Autowired
    private TPubFileobjectService tPubFileobjectService;

    /**
     * 下拉框
     * @Author: S6113712
     * @CreateDate:   2018-01-14 08:40:25
     * @Return
     **/
	@ResponseBody
	@RequestMapping(value = "getCodeId/{codeTyp}")
	public List<EBsCodeEntity> getCodeIdByCodeTyp(@PathVariable("codeTyp") String codeTyp,Model model,HttpServletRequest request) {
	  List<EBsCodeEntity> rst=eBsCodeService.getCodeIdByCodeTyp(codeTyp);
      return rst;
      
	}

    @RequestMapping(value = "list", method = RequestMethod.GET)
    public String list(Model model, HttpServletRequest request) {
        return "audit/ebscode/list";
    }

    @RequestMapping(value = "getListData", method = RequestMethod.GET)
    @ResponseBody
    public Map<String, Object> getListData(HttpServletRequest request) {
        Page<EBsCodeEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        page = eBsCodeService.search(page, filters);
        return getEasyUIData(page);
    }

    @RequestMapping(value = "getCodeTypes", method = RequestMethod.GET)
    @ResponseBody
    public List<EBsCodeEntity> getCodeTypes() {
        return eBsCodeService.getCodeTypes();
    }

    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("action", "create");
        return "audit/ebscode/listForm";
    }

    /**
     * 方法描述: 保存
     * @Author: S6114893
     * @CreateDate:   2020-04-02 14:54:46
     * @Return
     **/
    @RequestMapping(value = "create", method = RequestMethod.POST)
    @ResponseBody
    public String create(@Valid EBsCodeEntity ebscode, Model model) {
        ebscode.setNewRecord(true);
        ebscode.setCreateDate(new Date());
        eBsCodeService.save(ebscode);
        return "success";
    }

    /**
     * 方法描述: 修改跳轉
     * @Author: S6114893
     * @CreateDate:   2020-04-02 11:04:39
     * @Return
     **/
    @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
    public String updateForm(@PathVariable("id") String id, Model model) {
        model.addAttribute("eBsCode", eBsCodeService.get(id));
        model.addAttribute("action", "update");
        return "audit/ebscode/listForm";
    }

    /**
     * 方法描述: 修改
     *
     * @Author: S6114893
     * @CreateDate: 2020-04-02 10:55:00
     * @Return
     **/
    @RequestMapping(value = "update", method = RequestMethod.POST)
    @ResponseBody
    public String update(@Valid EBsCodeEntity bscode,@RequestParam(value = "ids") String ids) {
        bscode.setId(ids);
        eBsCodeService.update(bscode);
        return "success";
    }

    @RequestMapping("delete/{id}")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        eBsCodeService.delete(id);
        return "success";
    }
}
