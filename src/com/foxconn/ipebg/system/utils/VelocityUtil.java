package com.foxconn.ipebg.system.utils;

import java.io.StringWriter;

import com.foxconn.ipebg.system.entity.Mail;
import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;
import org.apache.velocity.app.VelocityEngine;


public class VelocityUtil {
    /**
      * 方法描述: 獲取郵件發送標題
      * @Author: S6114648
      * @CreateDate:   2018/10/24  下午 03:47
      * @Return
      **/

    public static String getMailSubject(Mail mail) {
        // 创建引擎  
        VelocityEngine ve = new VelocityEngine();
        // 设置模板加载路径，这里设置的是class下  
        ve.setProperty(Velocity.RESOURCE_LOADER, "class");
        ve.setProperty("class.resource.loader.class", "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader");
        try {
            // 进行初始化操作  
            ve.init();
            // 加载模板，设定模板编码  
            Template t = ve.getTemplate("mail/mailSubject.vm", "UTF-8");
            // 设置初始化数据  
            VelocityContext context = new VelocityContext();
            context.put("mail", mail);

            // 设置输出  
            StringWriter writer = new StringWriter();
            // 将环境数据转化输出  
            t.merge(context, writer);

            return writer.toString();

        } catch (Exception e) {
            throw new RuntimeException("模版转化错误!");
        }
    }
    /**
      * 方法描述: 獲取郵件發送內容
      * @Author: S6114648
      * @CreateDate:   2018/10/24  下午 03:46
      * @Return
      **/

    public static String getMailContent(Mail mail) {
        // 创建引擎  
        VelocityEngine ve = new VelocityEngine();
        // 设置模板加载路径，这里设置的是class下  
        ve.setProperty(Velocity.RESOURCE_LOADER, "class");
        ve.setProperty("class.resource.loader.class", "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader");
        try {
            // 进行初始化操作  
            ve.init();
            // 加载模板，设定模板编码  
            Template t = ve.getTemplate("mail/mailContent.vm", "UTF-8");
            // 设置初始化数据  
            VelocityContext context = new VelocityContext();
            context.put("mail", mail);

            // 设置输出  
            StringWriter writer = new StringWriter();
            // 将环境数据转化输出  
            t.merge(context, writer);

            return writer.toString();

        } catch (Exception e) {
            throw new RuntimeException("模版转化错误!");
        }
    }

    public static void main(String[] args) {
        Mail mail = new Mail();
        mail.setUsername("test");
        mail.setDusername("test222");
        mail.setOrderstatus("2");
        mail.setSystemname("iPBEG電子簽核平台");
        System.out.println("標題");
        System.out.println(getMailSubject(mail));
        System.out.println("內容");
        System.out.println(getMailContent(mail));
    }
}


