package com.foxconn.ipebg.basics.vo;

import com.foxconn.ipebg.basics.entity.BsServiceDayCostEntity;

import java.util.HashMap;
import java.util.Map;

/**
 * 警衛服務費用結賬明細主表
 *
 * <AUTHOR>
 * @date 2022-08-15
 */
public class BsServiceDayCostVO {
    // 費用主表
    private BsServiceDayCostEntity costEntity;
    // 查询日期
    private Map<String, String> days;

    public BsServiceDayCostVO() {
        days = new HashMap<>();
    }

    public BsServiceDayCostEntity getCostEntity() {
        return costEntity;
    }

    public void setCostEntity(BsServiceDayCostEntity costEntity) {
        this.costEntity = costEntity;
    }

    public Map<String, String> getDays() {
        return days;
    }
}
