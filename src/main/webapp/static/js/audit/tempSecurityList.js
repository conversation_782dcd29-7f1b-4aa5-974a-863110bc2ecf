var dg;
var d;
var _legalPersonObj=arr2obj(_legalPersonDicts,"value","label");
$(function() {
    dg = $('#dg').datagrid({
        method : "get",
        url : ctx + '/tempSecurityAply/list',
        fit : true,
        fitColumns : true,
        border : false,
        idField : 'id',
        striped : true,
        pagination : true,
        rownumbers : true,
        sortName : 'createDate',
        sortOrder : 'desc',
        pageNumber : 1,
        pageSize : 20,
        pageList : [ 10, 20, 30, 40, 50 ],
        singleSelect : true,
        columns : [ [ {
            field : 'id',
            title : '主鍵',
            hidden : true
        },{
            field : 'serialno',
            title : '任務編號',
            sortable : true,
            width : 100,
            formatter: operation
        }, {
            field : 'makerno',
            title : '填單人工號',
            sortable : true,
            width : 100
        }, {
            field : 'makername',
            title : '填單人名稱',
            sortable : true,
            width : 100
        }, {
            field : 'applyIp',
            title : '填單人IP',
            sortable : true,
            width : 100
        }, {
            field : 'dpt',
            title : '申請人部門名稱',
            sortable : true,
            width : 100
        }, {
            field : 'costId',
            title : '申請部門費用代碼',
            sortable : true,
            width : 100
        }, {
            field : 'startTime',
            title : '需求開始時間',
            sortable : true,
            width : 100
        }, {
            field : 'endTime',
            title : '需求結束時間',
            sortable : true,
            width : 100
        }, {
            field : 'servicePlace',
            title : '需求服務地點',
            sortable : true,
            width : 100
        }, {
            field : 'legalPerson',
            title : '法人',
            sortable : true,
            width : 100,
            formatter:function (value, row, index) {
                return _legalPersonObj[getCostColumn(row,'legalId')];
            }
        }, {
            field : 'applyStat',
            title : '申請單狀態(E：結案，0：申請，O：，駁回 Q：異常結案)',
            sortable : true,
            width : 100
        }, {
            field : 'signEmpNo',
            title : '審核人工號',
            sortable : true,
            width : 100
        }, {
            field : 'signEmpNam',
            title : '審核人姓名',
            sortable : true,
            width : 100
        }, {
            field : 'signDate',
            title : '審核時間',
            sortable : true,
            width : 100
        }] ],
        enableHeaderClickMenu : true,
        enableHeaderContextMenu : true,
        enableRowContextMenu : false,
        rowTooltip : true,
        toolbar : '#tb'
    });
});

var getCostColumn=function (row,attr) {
    return row.legalPerson?row.legalPerson:0;
}
function arr2obj(arr,key,value) {
    var obj={};
    for (var i=0 ;i< arr.length ;i++) {
        var o=arr[i];
        obj[o[key]]=o[value];
    }
    return obj;
}
function operation(value, row, index) {
    return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('詳情',ctx+'/tempSecurityAply/view/" + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
};
// 创建查询对象并查询
function cx() {
    var obj = $("#searchFrom").serializeObject();
    dg.datagrid('load', obj);
}

// 表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("reset");
}

function checkout() {
    var selectItems = dg.datagrid('getSelections');
    if (selectItems.length > 1) {
        alert("複檢合格報告只能進行單筆上傳");
        return;
    }
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row))
        return;
    if (row.applyStat != "E") {
        alert("僅可對已結案表單發起費用結算申請!");
        return;
    }
    if (row.hasCost != "1") {
        alert("該安保服務不產生費用!");
        return;
    }
    // 查詢費用結算狀態
    $.get(ctx+'/tempSecurityCostAply/listByServiceSerialno', { serviceSerialno : row.serialno },
        function (data) {
            if(data.length > 0) {
                alert("該單已提交過費用結算申請");
            } else {
                window.parent.mainpage.mainTabs.addModule('臨時安保費用結算',ctx+'/tempSecurityCostAply/create/' + row.serialno,'icon-hamburg-customers');
            }
    });
}
