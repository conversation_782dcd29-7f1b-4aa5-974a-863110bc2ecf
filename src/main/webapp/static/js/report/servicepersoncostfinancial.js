var dg;
var d;
var _bsDptList=[];
var _bsDptObj={};
$(function () {
    $("#filter_workMonth").val(getPreWorkMonth());
    initDatas();
    dg = $('#dg').datagrid({
        method: "get",
        url: ctx + '/servicepersoncost/financialData',
        fit: true,
        fitColumns: true,
        border: false,
        striped: true,
        pagination: false,
        rownumbers: true,
        singleSelect: true,
        onLoadSuccess: compute,
        queryParams:{
            workMonth:$("#filter_workMonth").val()
        },
        columns: [[
            {field: 'dptQun', title: '事業群', sortable: true, width: 80,halign:'center',rowspan:2}
            ,
            {field: 'legal', title: '法人', sortable: true, width: 80,halign:'center',rowspan:2}
            ,
            {field: 'costId', title: '費用代碼', sortable: true, width: 80,halign:'center',rowspan:2}
            ,
            {field: '', title: '費用（RMB）', sortable: true, width: 100,halign:'center',colspan:5}
        ],[
            {field: 'regularCost', title: '常規費用', sortable: true, width: 100,halign:'center'},
            {field: 'overtimeCost', title: '加班費用', sortable: true, width: 100,halign:'center'},
            {field: 'tempCost1', title: '臨時勤務費用', sortable: true, width: 100,halign:'center'},
            {field: 'tempCost2', title: '臨時性安保服務費用', sortable: true, width: 100,halign:'center'},
            {field: 'totalCost', title: '合計', sortable: true, width: 100,halign:'center'}
        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        rowTooltip: true,
        toolbar: '#tb'
    });
});

function turnFloat(value) {
    if(value=='' || value==null){
        return 0;
    }else{
        return parseFloat(value);
    }
}

function compute() {//计算函数
    var rows = $('#dg').datagrid('getRows')//获取当前的数据行
    if(rows.length==0){
        return;
    }
    var regularCost = 0;
    var otCost = 0;
    var tsCost1 = 0;
    var tsCost2 = 0;
    var totalCost = 0;
    for (var i = 0; i < rows.length; i++) {
        regularCost += turnFloat(rows[i]['regularCost']);
        otCost += turnFloat(rows[i]['overtimeCost']);
        tsCost1 += turnFloat(rows[i]['tempCost1']);
        tsCost2 += turnFloat(rows[i]['tempCost2']);
    }
    totalCost += regularCost+otCost+tsCost1+tsCost2;
    //新增一行显示统计信息
    $('#dg').datagrid('appendRow', { costId: '<b>合計：</b>', regularCost: regularCost.toFixed(2), overtimeCost: otCost.toFixed(2), tempCost1: tsCost1.toFixed(2), tempCost2: tsCost2.toFixed(2), totalCost: totalCost.toFixed(2) });
    var normalCostPer=new Number(regularCost/totalCost*100);
    var otCostPer=new Number(otCost/totalCost*100);
    var tsCostPer1=new Number(tsCost1/totalCost*100);
    var tsCostPer2=new Number(tsCost2/totalCost*100);
    $('#dg').datagrid('appendRow', { costId: '<b>百分比：</b>',regularCost: normalCostPer.toFixed(2) +'%', overtimeCost: otCostPer.toFixed(2)+'%', tempCost1: tsCostPer1.toFixed(2)+'%', tempCost2: tsCostPer2.toFixed(2)+'%',totalCost:'100%'});
}

function initDatas() {
    $.ajax({
        type: 'get',
        url: ctx + "/tempworkcostdetail/initDatas",
        success: function (data) {
            _bsDptList=data.bsDptList;
            _bsDptObj=arr2obj(_bsDptList,"id","dptQun");

            //绑定事業處下拉框
            $("#filter_buNam").combobox({
                data: _bsDptList,
                valueField: "dptQun",
                textField: "dptQun",
                editable: false,
                panelHeight: 'auto',
                loadFilter: function (data) {
                    var qunArray = new Array();
                    var result = new Array();
                    $.each(data, function(i, item) {
                        if (!qunArray.contains(item.dptQun)) {
                            result.push(item);
                            qunArray.push(item.dptQun);
                        }
                    });
                    return result;
                }
            });
        }
    });
}

function getPreWorkMonth() {
    var date=new Date();
    date.setMonth(date.getMonth()-1);
    var m;
    if(date.getMonth()>=0 && date.getMonth()<9){
        m='0'+(date.getMonth()+1);
    }else {
        m=date.getMonth()+1;
    }
    // S6114893 2019/11/11 修复月份显示不正确的问题
    return date.getFullYear()+ '' +m;
}
//數組轉對象
function arr2obj(arr,key,value) {
    var obj={};
    for (var i=0 ;i< arr.length ;i++) {
        var o=arr[i];
        obj[o[key]]=o[value];
    }
    return obj;
}
/**
 * 获取上一个月
 *
 * @date 格式为yyyy-mm-dd的日期，如：2014-01-25
 */
function getPreMonth(date) {
    var arr = date.split('-');
    var year = arr[0]; //获取当前日期的年份
    var month = arr[1]; //获取当前日期的月份
    var day = arr[2]; //获取当前日期的日
    var days = new Date(year, month, 0);
    days = days.getDate(); //获取当前日期中月的天数
    var year2 = year;
    var month2 = parseInt(month) - 1;
    if (month2 == 0) {
        year2 = parseInt(year2) - 1;
        month2 = 12;
    }
    var day2 = day;
    var days2 = new Date(year2, month2, 0);
    days2 = days2.getDate();
    if (day2 > days2) {
        day2 = days2;
    }
    if (month2 < 10) {
        month2 = '0' + month2;
    }
    var t2 = year2 + '-' + month2 + '-' + day2;
    return t2;
}

//创建查询对象并查询
function cx() {
    var obj = $("#searchFrom").serializeObject();
    dg.datagrid('load', obj);
}

//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("reset");
    var now = new Date();
    var startDate = new Date(now.getFullYear(), now.getMonth(), 1);
    var endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() > 1 ? now.getDate() - 1 : 1);
    $('#startDate').datebox('setValue', startDate.format('yyyy-MM-dd'));
    $('#endDate').datebox('setValue', endDate.format('yyyy-MM-dd'));
}

//导出excel
function exportExcel() {
    var form = document.getElementById("searchFrom");
    searchFrom.action = ctx + '/servicepersoncost/exportFinancial';
    form.submit();
}