package com.foxconn.ipebg.buessness.common.service;

import com.foxconn.ipebg.buessness.common.entity.TQhOrgaformhsEntity;
import com.foxconn.ipebg.buessness.common.dao.TQhOrgaformhsDao;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 組織基本資料表MHS
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-17 08:06:42
 */
@Service
@Transactional(readOnly=true)
public class  TQhOrgaformhsService extends BaseBusinessService<TQhOrgaformhsEntity, String>{
    @Autowired
    private TQhOrgaformhsDao tQhOrgaformhsDao;
    @Override
    public HibernateDao<TQhOrgaformhsEntity, String> getEntityDao() {
        return tQhOrgaformhsDao;
    }

    public TQhOrgaformhsEntity findByFactroyIdAndDeptno(String factoryId,String deptno){
        return tQhOrgaformhsDao.findUnique("from TQhOrgaformhsEntity t where t.deptno=?0 and t.factoryid=?1",deptno,factoryId);
    }
}
