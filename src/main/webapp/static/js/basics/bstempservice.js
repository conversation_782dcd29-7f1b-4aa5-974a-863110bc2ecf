var dg;
var d;
var _legalPersonDicts=[];
var _legalPersonObj={};
$(function () {
    initDatas();

    dg = $('#dg').datagrid({
        method: "get",
        url: ctx + '/bstempservice/list',
        queryParams:{orderBy:"createDate",order:"desc"},
        fit: true,
        fitColumns: true,
        border: false,
        idField: 'id',
        striped: true,
        pagination: true,
        rownumbers: true,
        pageNumber: 1,
        pageSize: 20,
        pageList: [10, 20, 30, 40, 50],
        singleSelect: true,
        columns: [[
            {field: 'id', title: '資料識別ID  Primary', hidden: true},
            {field: 'legalPerson', title: '法人', sortable: true, width: 100,
                formatter:function(value, rowData, rowIndex){
                    return _legalPersonObj[value];
                }
            }
            ,
            {field: 'employerUnit', title: '用人單位', sortable: true, width: 100}
            ,
            {field: 'costCode', title: '費用代碼', sortable: true, width: 100}
            ,
            {field: 'cause', title: '事由', sortable: true, width: 100}
            ,
            {field: 'startTime', title: '開始時間', sortable: true, width: 100}
            ,
            {field: 'endTime', title: '結束時間', sortable: true, width: 100}
            ,
            {field: 'location', title: '地點', sortable: true, width: 100}
            ,
            {field: 'peopleNumber', title: '人數', sortable: true, width: 100}
            ,
            {field: 'expenses', title: '臨時勤務費用(元)', sortable: true, width: 100,
                formatter:function(value, rowData, rowIndex){
                    return value/100.0;
                }
            }
            ,
            {field: 'createBy', title: '創建人', sortable: true, width: 100}
            ,
            {field: 'createDate', title: '創建時間', sortable: true, width: 100}
            ,
            {field: 'updateBy', title: '更新人', sortable: true, width: 100}
            ,
            {field: 'updateDate', title: '更新時間', sortable: true, width: 100}
            ,
            {field: 'delFlag', title: '刪除標示', sortable: true, width: 100,hidden:true}
        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        rowTooltip: true,
        toolbar: '#tb'
    });
});

function initDatas() {
    $.ajax({
        type:'get',
        url:ctx+"/system/dict/getDictByType/guard_legalperson",
        success: function(data){
            _legalPersonDicts=data;
            _legalPersonObj=arr2obj(_legalPersonDicts,'value','label');
            //绑定法人下拉框
            $("#filterLegalPerson").combobox({
                data: _legalPersonDicts,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 'auto',
                loadFilter: function (data) {
                    data.unshift({
                        value: '',
                        label: '請選擇法人'
                    });
                    return data;
                }
            });

            //绑定法人下拉框
            $("#legalPerson").combobox({
                data: _legalPersonDicts,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 'auto'
            });
        }
    });
}
//數組轉對象
function arr2obj(arr,key,value) {
    var obj={};
    for (var i=0 ;i< arr.length ;i++) {
        var o=arr[i];
        obj[o[key]]=o[value];
    }
    return obj;
}

//弹窗增加
function add() {
    window.parent.mainpage.mainTabs.addModule("新增臨時勤務", ctx + '/bstempservice/create', 'icon-hamburg-basket');
}

//删除
function del() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function (data) {
        if (data) {
            $.ajax({
                type: 'get',
                url: ctx + "/bstempservice/delete/" + row.id,
                success: function (data) {
                    successTip(data, dg);
                }
            });
        }
    });
}

//弹窗修改
function upd() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    window.parent.mainpage.mainTabs.addModule("修改臨時勤務", ctx + '/bstempservice/update/'+row.id, 'icon-hamburg-basket');
}

//创建查询对象并查询
function cx() {
    var obj = $("#searchFrom").serializeObject();
    dg.datagrid('load', obj);
}

//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("reset");
}

//导出excel
function exportExcel() {
    var form = document.getElementById("searchFrom");
    searchFrom.action = ctx + '/bstempservice/exportExcel';
    form.submit();
}