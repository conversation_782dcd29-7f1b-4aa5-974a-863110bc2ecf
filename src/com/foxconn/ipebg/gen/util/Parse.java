package com.foxconn.ipebg.gen.util;

import com.foxconn.ipebg.gen.entity.*;
import org.apache.commons.lang3.StringUtils;
import org.jdom.Document;
import org.jdom.Element;
import org.jdom.Namespace;
import org.jdom.input.SAXBuilder;

import java.io.File;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;


public class Parse {
	private static String version= "1";
	private Parse() {
	}

	public static void main(String[] args) {
		try {
			Parse.init();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	private static Map<String, GateWayBean> GATE_WAYSMAP = new HashMap<String, GateWayBean>();

	public static void init() throws Exception {
		/**
		 * 1.流程配置表 1筆記錄 ok
		 * 2.流程參數表 ok
		 * 3.所有節點 會簽 非會簽 ok
		 * 4.會簽節點規則 ok
		 * 5.所有節點參數
		 */
		try {
			String cfgFile = PathUtils.getClassPath() + "wfFile\\工伤事故呈报.txt";
			int n=0;
			Namespace ns = Namespace.getNamespace("", "http://www.omg.org/spec/BPMN/20100524/MODEL");
			Namespace ns1 = Namespace.getNamespace("", "http://activiti.org/bpmn");
			SAXBuilder builder = new SAXBuilder();
			Document doc = builder.build(new File(cfgFile));
			Element root = doc.getRootElement();
			Element node = root.getChild("process",ns);
			WfConfig wc = new WfConfig();
			if (node != null) {
				wc.setWorkFlowId(node.getAttributeValue("id"));
				wc.setWorkFlowName(node.getAttributeValue("name"));
			}
			String sqlConfig="insert into WF_CONIFG " +
					"(id,WORKFLOWID, WORKFLOWNAME, ACTION, MODACTION, SAVEACTION, DETAILACTION, DYNFIELD01, DYNFIELD02, DYNFIELD03,VERSION)" +
					"values('"+UUID.randomUUID().toString().replace("-","")+"','"+wc.getWorkFlowId()+"','"+wc.getWorkFlowName()+"',null,null,null,null,null,null,null,'"+version+"');";
			System.out.println(sqlConfig);
			//List<Map<String, WfNodeInfo>> l = new ArrayList<Map<String, WfNodeInfo>>();
			Map<String, WfNodeInfo> mpMap=new HashMap<String, WfNodeInfo>();
			Map<String, WfNodeRule> mpMap1=new HashMap<String, WfNodeRule>();
			List<Element> userTask = node.getChildren("userTask",ns);
			if (userTask != null && userTask.size() > 0){
				int i=0;
				for (Element r : userTask) {
					WfNodeInfo wnInfo=new WfNodeInfo();
					WfNodeRule rule=new WfNodeRule();
					wnInfo.setWorkFlowId(wc.getWorkFlowId());
					wnInfo.setNodeId(node.getAttributeValue("id")+ ((++i)<10?"0"+i:i));
					wnInfo.setNodeName(r.getAttributeValue("name"));
					if(r.getChild("multiInstanceLoopCharacteristics",ns)==null){
						wnInfo.setSignType("0");
					}else if(r.getChild("multiInstanceLoopCharacteristics",ns)!=null){
						wnInfo.setSignType("1");
						rule.setNodeId(wnInfo.getNodeId());
						rule.setNodeParamName(r.getChild("multiInstanceLoopCharacteristics",ns).getAttributeValue("collection", ns1));
						rule.setTotalPeople("totalPeople=$PERSONNUM$");
						rule.setPassRate("passRate=0");
						rule.setVoteRule("voteRule=1");
						String aString=r.getChild("multiInstanceLoopCharacteristics",ns).getChild("completionCondition", ns).getValue();
						rule.setTaskStatusVariable("taskStatusVariable="+aString.substring(2,aString.indexOf("=")));
						rule.setTaskCompleteVariable("taskCompleteVariable=");
						mpMap1.put(r.getAttributeValue("id"), rule);
						//System.out.println(mpMap1);

								/*
								 * 目前只支持會簽節點為INT類型的
								 */
						WfConfigParam wfp = new WfConfigParam();
						wfp.setWorkFlowId(wc.getWorkFlowId());
						wfp.setParameName(aString.substring(2,aString.indexOf("=")));
						wfp.setParamType("I");
						wfp.setParamValue("0");
						wc.getWfParams().add(wfp);
					}
					wnInfo.setWfNodeRule(rule);
					mpMap.put(r.getAttributeValue("id"), wnInfo);
					//l.add(mpMap);
					wc.getNodeList().add(wnInfo);
					//System.out.println(wnInfo);
				}
			}
			//System.out.println(wc);
			//System.out.println(mpMap.keySet());
			//System.out.println(mpMap1.keySet());
			List<Element> gateWay = node.getChildren("parallelGateway",ns);
			if(gateWay!=null && gateWay.size()>0)
				for(Element e:gateWay){
					GateWayBean bg = new GateWayBean();
					bg.setId(e.getAttributeValue("id"));
					bg.setName(e.getAttributeValue("name"));

					GATE_WAYSMAP.put(bg.getId(), bg);
				}
			gateWay = node.getChildren("exclusiveGateway",ns);
			if(gateWay!=null && gateWay.size()>0)
				for(Element e:gateWay){
					GateWayBean bg = new GateWayBean();
					bg.setId(e.getAttributeValue("id"));
					bg.setName(e.getAttributeValue("name"));

					GATE_WAYSMAP.put(bg.getId(), bg);
				}


			List<Element> sequenceFlow = node.getChildren("sequenceFlow",ns);

			WfNodeInfo wInfo = new WfNodeInfo();
			if(sequenceFlow != null && sequenceFlow.size() > 0){

				for(Element e:sequenceFlow){
					if("start".equalsIgnoreCase(e.getAttributeValue("sourceRef"))){
						wInfo.setNodeId(e.getAttributeValue("targetRef"));
						wInfo.setOrderBy(new BigDecimal(0));
						break;
					}
				}
				String pervIdString = wInfo.getNodeId();
				//Map mpMap2=new HashMap();
				WfNodeInfo _next = wInfo;
				for(int i = 0;i<sequenceFlow.size();i++)
					for(Element e:sequenceFlow){
						//mpMap2.put(e.getAttributeValue("targetRef"), e);
						if(pervIdString.equalsIgnoreCase(e.getAttributeValue("sourceRef"))){
							WfNodeInfo next = new WfNodeInfo();
							next.setNodeId(e.getAttributeValue("targetRef"));
							_next.setNext(next);
							_next = next;
							pervIdString = next.getNodeId();
							break;
						}
					}
				//System.out.println(wInfo.getNodeId());
				//System.out.println(mpMap2);
				WfNodeInfo i = wInfo;
				mpMap.get("tiandanren").setOrderBy(new BigDecimal(0));
				while (i !=null){
					if(!GATE_WAYSMAP.containsKey(i.getNodeId()) && mpMap.containsKey(i.getNodeId())){
						//System.out.println("->"+i.getNodeId()+":"+(++n));
						if(mpMap.get(i.getNodeId())!=null)
							mpMap.get(i.getNodeId()).setOrderBy(new BigDecimal(++n+""));
					}
					i= i.getNext();
				}
				for (WfNodeInfo wfNode : mpMap.values()) {
					String sqlWfNode="insert into WF_NODEINFO" +
							"(id,NODEID, WORKFLOWID, NODENAME, ORDERBY, SIGNTYPE, REQUIRED, COLNAME,VERSION,NODEALAIN) values"+
							"('"+UUID.randomUUID().toString().replace("-","")+"','"+wfNode.getNodeId()+"', '"+wfNode.getWorkFlowId()+"','"+wfNode.getNodeName()+"','"+wfNode.getOrderBy()+"'," +
							"'"+wfNode.getSignType()+"',null,null,'"+version+"','"+wfNode.getNodeName()+"');";
					System.out.println(sqlWfNode);
				}
				System.out.println(wc.getWfParams().size());

				for(Element e:sequenceFlow){
					if(e.getChild("conditionExpression", ns)!=null){
						WfNodeParam nodeParam=new WfNodeParam();
						String sourceRef = e.getAttributeValue("sourceRef");
						if(sourceRef==null) continue;

						if(e.getChild("conditionExpression", ns).getValue().contains("==")){
							nodeParam.setParamType("I");
						}else {
							nodeParam.setParamType("B");
						}
						nodeParam.setWorkFlowId(node.getAttributeValue("id"));
						if(e.getChild("conditionExpression", ns).getValue().contains("sqrjbpd")){
							WfConfigParam wfp = new WfConfigParam();
							wfp.setWorkFlowId(wc.getWorkFlowId());
							wfp.setParameName("sqrjbpd");
							wfp.setParamType("I");
							wfp.setParamValue("$USERGRADE$");

							if(!wc.getWfParams().contains(wfp))

								wc.getWfParams().add(wfp);

							continue;
						}
						String valueString=e.getChild("conditionExpression", ns).getValue();
						if (e.getChild("conditionExpression", ns).getValue().contains("!")) {
							nodeParam.setParameName(valueString.substring(valueString.indexOf("{")+2, valueString.indexOf("}")));
							nodeParam.setParamValue("false");
							nodeParam.setDescrib(StringUtils.isBlank(e.getAttributeValue("name"))?"駁回":e.getAttributeValue("name"));
							nodeParam.setToWorkStatus("4");
							nodeParam.setIsPass("1");
						}else {
							nodeParam.setParameName(valueString.substring(valueString.indexOf("{")+1, valueString.indexOf("}")));
							nodeParam.setParamValue("true");
							nodeParam.setDescrib(StringUtils.isBlank(e.getAttributeValue("name"))?"通過":e.getAttributeValue("name"));
							nodeParam.setToWorkStatus("2");
							nodeParam.setIsPass("0");
						}

						//sourceRef 是否為userTask
						if(mpMap.containsKey(sourceRef)){
							nodeParam.setNodeId(mpMap.get(sourceRef).getNodeId());
						}else{
							for(Element ei:sequenceFlow){
								if(sourceRef.equals(ei.getAttributeValue("targetRef"))){
									String inSourceRef = ei.getAttributeValue("sourceRef");
									//sourceRef 是否為userTask
									if(mpMap.containsKey(inSourceRef)){
										nodeParam.setNodeId(mpMap.get(inSourceRef).getNodeId());
									}else{
										for(Element eii:sequenceFlow){
											if(inSourceRef.equals(eii.getAttributeValue("targetRef", ns))){
												String inInSourceRef = eii.getAttributeValue("sourceRef", ns);
												if(mpMap.containsKey(inInSourceRef)){
													nodeParam.setNodeId(mpMap.get(inInSourceRef).getNodeId());
												}
												break ;
											}
										}
									}

									break;
								}
							}
						}
						if (nodeParam.getDescrib().equalsIgnoreCase("重新提交")) {
							nodeParam.setIsPass("2");
						}
						if (nodeParam.getDescrib().equalsIgnoreCase("取消申請")) {
							nodeParam.setToWorkStatus("9");
							nodeParam.setIsPass("3");
						}

						List<WfNodeInfo> lnInfos = wc.getNodeList();

						for(WfNodeInfo wi:lnInfos){
							if(wi.getNodeId().equals(nodeParam.getNodeId())){
								if("1".equals(wi.getSignType())){
									//System.out.println(wi.getWfNodeRule().getTaskCompleteVariable());

									wi.getWfNodeRule().setTaskCompleteVariable("taskCompleteVariable="+nodeParam.getParameName());
									nodeParam.setParamType("I");
									nodeParam.setParameName("voteResult");
									nodeParam.setParamValue("false".equals(nodeParam.getParamValue())?"0":"1");
								}

								//System.out.println(wi.getNodeId()+"=="+wi.getSignType());
								break;
							}

						}

						if(mpMap.get(nodeParam.getNodeId())!=null ){
							//System.out.println(nodeParam.getNodeId()+":"+mpMap.get(nodeParam.getNodeId()).getOrderBy());
							//System.out.println(nodeParam.getNodeId()+"最後一個節點");


						}

						//System.out.println(nodeParam.getNodeId()+":"+nodeParam.getParameName()+":"+nodeParam.getDescrib());
						String sqlNodeParam="insert into WF_NODEPARAM " +
								"(id,WORKFLOWID, NODEID, PARAMENAME, PARAMVALUE, PARAMTYPE, DESCRIB, TOWORKSTATUS, ISPASS,VERSION)values" +
								"('"+UUID.randomUUID().toString().replace("-","")+"','"+nodeParam.getWorkFlowId()+"','"+nodeParam.getNodeId()+"','"+nodeParam.getParameName()+"'," +
								"'"+nodeParam.getParamValue()+"','"+nodeParam.getParamType()+"','"+nodeParam.getDescrib()+"'," +
								" '"+nodeParam.getToWorkStatus()+"','"+nodeParam.getIsPass()+"','"+version+"');";
						System.out.println(sqlNodeParam);
					}
				}
				for (WfNodeInfo nodeInfo :wc.getNodeList()) {
					if(nodeInfo.getWfNodeRule().getNodeId()!=null){
						WfNodeRule rule=nodeInfo.getWfNodeRule();
						String sqlRule="insert into WF_NODERULE " +
								"(id,NODEID, NODEPARAMNAME, TOTALPEOPLE, PASSRATE, VOTERULE, TASKSTATUSVARIABLE, TASKCOMPLETEVARIABLE,VERSION)" +
								"values('"+UUID.randomUUID().toString().replace("-","")+"','"+rule.getNodeId()+"','"+rule.getNodeParamName()+"','"+rule.getTotalPeople()+"'," +
								"'"+rule.getPassRate()+"','"+rule.getVoteRule()+"','"+rule.getTaskStatusVariable()+"','"+rule.getTaskCompleteVariable()+"','"+version+"');";
						System.out.println(sqlRule);
					}
				}
				for (WfConfigParam cp : wc.getWfParams()) {
					String sqlConfigParam="insert into WF_CONFIGPARAM " +
							"(ID,WORKFLOWID, PARAMENAME, PARAMVALUE, PARAMTYPE,VERSION)values" +
							"('"+ UUID.randomUUID().toString().replace("-","")+"','"+cp.getWorkFlowId()+"', '"+cp.getParameName()+"','"+cp.getParamValue()+"','"+cp.getParamType()+"','"+version+"');";
					System.out.println(sqlConfigParam);
				}
				System.out.println(wc.getWfParams().size());
				//int j=0;
						/*for(Element e:sequenceFlow){
							WfNodeParam nodeParam=new WfNodeParam();
							if(e.getChild("conditionExpression", ns)!=null){
								if(e.getChild("conditionExpression", ns).getValue().contains("==")){
									nodeParam.setParamType("I");
								}else {
									nodeParam.setParamType("B");
								}
							}
							//String pervId = e.getAttributeValue("targetRef");
							if(e.getChild("conditionExpression", ns)!=null&&e.getAttributeValue("sourceRef")!=null){
								List<Object> list=Arrays.asList(mpMap.keySet().toArray());
								if (userTask!=null&&list.contains(e.getAttributeValue("sourceRef"))) {
									nodeParam.setNodeId(e.getAttributeValue("sourceRef"));

								}else {
									List<Object> targets=Arrays.asList(mpMap2.keySet().toArray());
									for (Object target : targets) {
										if(target.equals(e.getAttributeValue("sourceRef"))){
											//System.out.println(((Element)mpMap2.get(target)).getAttributeValue("sourceRef"));

											nodeParam.setNodeId(((Element)mpMap2.get(target)).getAttributeValue("sourceRef"));
											break;
										}
									}
									nodeParam.setNodeId(e.getAttributeValue("sourceRef"));
								}
								nodeParam.setWorkFlowId(node.getAttributeValue("id"));
								String valueString=e.getChild("conditionExpression", ns).getValue();
								nodeParam.setParameName(valueString.substring(valueString.indexOf("{")+1, valueString.indexOf("}")));
								if (e.getChild("conditionExpression", ns).getValue().contains("!")) {
									nodeParam.setParamValue("false");
									nodeParam.setDescrib("駁回");
									nodeParam.setToWorkStatus("4");
									nodeParam.setIsPass("1");
								}else {
									nodeParam.setParamValue("true");
									nodeParam.setDescrib("通過");
									nodeParam.setToWorkStatus("2");
									nodeParam.setIsPass("0");
								}
							}

							if(mpMap.containsKey(nodeParam.getNodeId()))
							System.out.println(nodeParam);
						}*/
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		}
	}

}
