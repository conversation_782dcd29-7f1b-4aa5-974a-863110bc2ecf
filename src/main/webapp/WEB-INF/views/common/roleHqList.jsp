<%--suppress ALL --%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title></title>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div>
<%--    <input id="roleListSort"
           value="">
    </input>--%>
</div>
<table id="roleListdg2"></table>
<script type="text/javascript">
var dg;
var d;
$(function(){
    var dutyid =$('#dutyId').val();
    var factoryid = $('#factoryId').val();
    var param={"dutyid":dutyid,"factoryid":factoryid};
    var isCheckSort = false;
	dg=$('#roleListdg2').datagrid({
		method: "get",
    	url:'${ctx}/system/auditingNode/getAuditHqUsers',
        queryParams:param,
        nowrap: false,
		fit : true,
		fitColumns : true,
		border : false,
		striped:true,
        pagination:true,
		remoteSort:false,
        onClickRow:function(index, row){
            row.num==1;
        },
        checkOnSelect:true,
            columns : [ [
            {field : 'itemid', title : 'Order ID', width : 200, checkbox : true},
			{field:'empno',title:'帐号',sortable:true,width:100,align: 'center'},
			{field:'username',title:'主管名稱',sortable:true,width:100,align: 'center'},
			{field:'ismanager',title:'管理職',sortable:true,width:100,align: 'center'},
			{field:'remarks',title:'備註',sortable:true,width:100,align: 'center'},
			{field:'num',title:'簽核順序',sortable:true,width:100,align:'center'

            },
    	]]
        ,
        onClickRow:function(rowIndex,rowData){
//            console.log("onClickRow11111111");
            if(!isCheckSort) {
                sortByClick('roleListdg2', rowIndex);
                isCheckSort = false;
            }
        }
        ,
        onCheck:function(rowIndex,rowData){
            var isSelected = false;
            var selectItems = dg.datagrid('getSelections');
            for (var i = 0; i <selectItems.length; i++) {
                var index = dg.datagrid('getRowIndex',selectItems[i]);
                // console.log(rowIndex);
                if(rowIndex == index){
                    isSelected = true;
                }
            }
            var dr = dg.datagrid('getRows')[rowIndex];
            if((undefined == dr["num"] || "" == dr["num"])&&isSelected){
                sortByClick('roleListdg2', rowIndex);
                isCheckSort = true;
            }else{
                isCheckSort = false;
            }
        },
        onUncheck:function(rowIndex,rowData){
//            console.log("onUncheck111111111111");
            var isSelected = false;
            var selectItems = dg.datagrid('getSelections');
            for (var i = 0; i <selectItems.length; i++) {
                var index = dg.datagrid('getRowIndex',selectItems[i]);
                // console.log(rowIndex);
                if(rowIndex == index){
                    isSelected = true;
                }
            }
            var dr = dg.datagrid('getRows')[rowIndex];
            if((undefined == dr["num"] || "" != dr["num"])&&!isSelected){
                sortByClick('roleListdg2', rowIndex);
                isCheckSort = true;
            }else{
                isCheckSort = false;
            }
        } ,
        onUncheckAll:function(rowIndex,rowData){
            var alllRows = dg.datagrid('getRows');
            for (var i = 0; i <alllRows.length; i++) {
                alllRows[i]["num"] = "";
                dg.datagrid('refreshRow', i);
            }
        }
        ,
        onSelectAll:function(rowIndex,rowData){
            var alllRows = dg.datagrid('getRows');
            for (var i = 0; i <alllRows.length; i++) {
                alllRows[i]["num"] = i + 1;
                dg.datagrid('refreshRow', i);
            }
        }
	});
}

);
</script>
</body>
</html>