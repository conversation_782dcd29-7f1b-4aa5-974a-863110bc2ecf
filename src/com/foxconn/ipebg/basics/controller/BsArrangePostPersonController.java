package com.foxconn.ipebg.basics.controller;

import java.math.BigDecimal;
import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.foxconn.ipebg.basics.entity.BsArrangeGroupEntity;
import com.foxconn.ipebg.basics.entity.BsArrangeListEntity;
import com.foxconn.ipebg.basics.service.BsArrangeGroupService;
import com.foxconn.ipebg.basics.service.BsArrangeListService;
import com.foxconn.ipebg.basics.service.BsGuardLeaveService;
import com.foxconn.ipebg.common.utils.DateUtils;
import com.foxconn.ipebg.system.service.DictService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.basics.entity.BsArrangePostPersonEntity;
import com.foxconn.ipebg.basics.service.BsArrangePostPersonService;
import com.foxconn.ipebg.system.entity.User;

import com.foxconn.ipebg.system.utils.UserUtil;



/**
 * 崗位人員表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-09-24 14:26:36
 */
@Controller
@RequestMapping("bsarrangepostperson")
public class BsArrangePostPersonController extends BaseController {

    @Autowired
    private BsArrangePostPersonService bsArrangePostPersonService;

    @Autowired
    private DictService dictService;

    @Autowired
    private BsArrangeListService arrangeListService;

    @Autowired
    private BsArrangeGroupService arrangeGroupService;

    @Autowired
    private BsGuardLeaveService guardLeaveService;

    /**
      * 方法描述: 列表信息
      * @Author: ********
      * @CreateDate:   2019-09-24 14:26:36
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:bsarrangepostperson:list")
    public String list(Model model) {
        List<PropertyFilter> groupFilters = new ArrayList<PropertyFilter>();
        User user = UserUtil.getCurrentUser();
        if (user != null) {
            // 按保安公司查询
            PropertyFilter filter = new PropertyFilter("INS_securityCom", user.getSecurityComs());
            groupFilters.add(filter);
        }
        List<BsArrangeGroupEntity> groupList = arrangeGroupService.search(groupFilters);
        model.addAttribute("groupList", JSON.toJSONString(groupList));
        //查询列表数据
        return "basics/bsarrangepostperson/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: ********
      * @CreateDate:   2019-09-24 14:26:36
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:bsarrangepostperson:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<BsArrangePostPersonEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        page = bsArrangePostPersonService.search(page, filters);
        return getEasyUIData(page);
    }
    /**
     * 方法描述: 保存
     * @Author: ********
     * @CreateDate:   2019-09-24 14:26:36
     * @Return
     **/
   
    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("basics:bsarrangepostperson:add")
    @ResponseBody
    public String create(@Valid BsArrangePostPersonEntity bsArrangePostPerson, Model model) {
        bsArrangePostPerson.setNewRecord(true);
    	bsArrangePostPerson.setCreateDate(new Date());
        bsArrangePostPersonService.save(bsArrangePostPerson);
        return "success";
    }
    
    /**
      * 方法描述: 添加跳轉
      * @Author: ********
      * @CreateDate:   2019-09-24 14:26:36
      * @Return
      **/

    @RequiresPermissions("basics:bsarrangepostperson:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("bsArrangePostPerson", new BsArrangePostPersonEntity());
        model.addAttribute("action", "create");
        return "basics/bsarrangepostperson/listForm";
    }
    
     /**
     * 方法描述: 修改跳轉
     * @Author: S6113712
     * @CreateDate:   2018-10-06 11:04:39
     * @Return
     **/

   @RequiresPermissions("basics:bsarrangepostperson:update")
   @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
   public String updateForm(@PathVariable("id") String id, Model model) {
       model.addAttribute("bsArrangePostPerson", bsArrangePostPersonService.get(id));
       model.addAttribute("action", "update");
      return "basics/bsarrangepostperson/listForm";
   }
   
	/**
	 * 方法描述: 修改
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-11 10:55:00
	 * @Return
	 **/

   @RequestMapping(value = "update", method = RequestMethod.POST)
   @RequiresPermissions("basics:bsarrangepostperson:update")
   @ResponseBody
   public String update(@Valid BsArrangePostPersonEntity bsArrangePostPerson,@RequestParam(value = "ids") String ids) {
	   bsArrangePostPerson.setId(ids);
	   bsArrangePostPerson.setUpdateDate(new Date());
	   bsArrangePostPersonService.update(bsArrangePostPerson);
	   
       return "success";
   }
    /**
      * 方法描述: 根據主鍵刪除
      * @Author: ********
      * @CreateDate:   2019-09-24 14:26:36
      * @Return
      **/

    @RequestMapping("delete/{id}")
    @RequiresPermissions("basics:bsarrangepostperson:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        bsArrangePostPersonService.delete(id);
        return "success";
    }
    /**
      * 导出excel
      * @Author: ********
      * @CreateDate:   2019-09-24 14:26:36
      * @Return
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {}


    //警衛排班start

    /**
     * 報表list
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/arrangeList", method = RequestMethod.GET)
    @ResponseBody
    public Map<String, Object> arrangeList(HttpServletRequest request) {
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        Page<BsArrangeListEntity> page = getPage(request);
        User user = UserUtil.getCurrentUser();
        if (user != null) {
            // 按保安公司查询
            PropertyFilter filter = new PropertyFilter("INS_securityCom", user.getSecurityComs());
            filters.add(filter);
        }
        page.setOrderBy("arrangeDate");
        page.setOrder("desc");
        page = arrangeListService.search(page, filters);
        return getEasyUIData(page);
    }



    /**
     * 警衛排班
     *
     * @param model
     * @return
     */
    @RequestMapping(value = "/arrangeper", method = RequestMethod.GET)
    public String arrangeperson(String arrangeDate, String arrangeGroupId, Model model) {
        model.addAttribute("arrangeDate", arrangeDate);
        model.addAttribute("arrangeGroupId", arrangeGroupId);
        List<PropertyFilter> groupFilters = new ArrayList<PropertyFilter>();
        User user = UserUtil.getCurrentUser();
        if (user != null) {
            // 按保安公司查询
            PropertyFilter filter = new PropertyFilter("INS_securityCom", user.getSecurityComs());
            groupFilters.add(filter);
        }
        List<BsArrangeGroupEntity> groupList = arrangeGroupService.search(groupFilters);
        model.addAttribute("groupList", JSON.toJSONString(groupList));
//        model.addAttribute("groupList", JSON.toJSONString(bsArrangePostPersonService.listAllGroup()));
        model.addAttribute("allowContinueWork", dictService.getDictByLabelAndType("allow_continue_work", "arrange_setting").getValue());
        return "basics/bsarrangepostperson/arrangeperson";
    }

    /**
     * 初始化培訓人員數據
     *
     * @param params
     * @return
     */
    @RequestMapping(value = "/arrangeper/initData", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> arrangeperInitData(@RequestParam Map<String, String> params) {
        Map<String, Object> result = new HashMap<>();
        result.put("overSixOnePerArr", bsArrangePostPersonService.overSixOnePer(params));
        result.put("dayOverTimePerArr", bsArrangePostPersonService.dayOverTimePer(params));
        result.put("nightOverTimePerArr", bsArrangePostPersonService.nightOverTimePer(params));
        result.put("groupAllPerArr", bsArrangePostPersonService.listAllGroupPer(params));
        result.put("arrangedPerArr", bsArrangePostPersonService.listArrangePer(params));
        result.put("leavePerArr", guardLeaveService.leaveListByParamForGuardArrange(params));
        result.put("postList", bsArrangePostPersonService.listGroupPost(params));
        return result;
    }

    /**
     * 培訓人員排班保存
     *
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/arrangeper/save", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> arrangeperSave(@RequestBody String jsonStr) {
        Map<String, Object> result = new HashMap<>();
        JSONObject jsonObject = JSON.parseObject(jsonStr);
        JSONObject validParams = jsonObject.getJSONObject("validParams");
        Map<String,String> params=new HashMap<>(3);
        params.put("arrangeDate",validParams.getString("arrangeDate"));
        params.put("arrangeGroupId",validParams.getString("arrangeGroupId"));
        List<Map<String, String>> overSixOnePer = bsArrangePostPersonService.overSixOnePer(params);
        List<Map<String, String>> dayOverTimePer = bsArrangePostPersonService.dayOverTimePer(params);
        List<Map<String, String>> nightOverTimePer = bsArrangePostPersonService.nightOverTimePer(params);
        List<Map<String, String>> listAllGroupPer = bsArrangePostPersonService.listAllGroupPer(params);
        List<Map<String, String>> listArrangePer = bsArrangePostPersonService.listArrangePer(params);
        List<Map<String, Object>> groupPost = bsArrangePostPersonService.listGroupPost(params);
        List<Map<String, String>> leavePer = guardLeaveService.leaveListByParamForGuardArrange(params);

        JSONObject validValues = jsonObject.getJSONObject("validValues");
        JSONArray overSixOnePerArrCopy = validValues.getJSONArray("overSixOnePerArr");
        JSONArray dayOverTimePerArrCopy = validValues.getJSONArray("dayOverTimePerArr");
        JSONArray nightOverTimePerArrCopy = validValues.getJSONArray("nightOverTimePerArr");
        JSONArray groupAllPerArrCopy = validValues.getJSONArray("groupAllPerArr");
        JSONArray leavePerArrCopy = validValues.getJSONArray("leavePerArr");
        JSONArray arrangedPerArrCopy = validValues.getJSONArray("arrangedPerArr");
        JSONArray postListCopy = validValues.getJSONArray("postList");

        boolean isSame=true;
        isSame=isSame(overSixOnePer,overSixOnePerArrCopy,isSame);
        isSame=isSame(dayOverTimePer,dayOverTimePerArrCopy,isSame);
        isSame=isSame(nightOverTimePer,nightOverTimePerArrCopy,isSame);
        isSame=isSame(listAllGroupPer,groupAllPerArrCopy,isSame);
        isSame=isSame(leavePer,leavePerArrCopy,isSame);
        isSame=isSame(listArrangePer,arrangedPerArrCopy,isSame);
        isSame=isSamePost(groupPost,postListCopy,isSame);


        if(isSame){

            Date arrangeDate = DateUtils.parseDate(jsonObject.getString("arrangeDate"));
            String arrangeGroupId = jsonObject.getString("arrangeGroupId");
            String arrangeGroupName = jsonObject.getString("arrangeGroupName");
            JSONArray perArr = jsonObject.getJSONArray("perArr");

            bsArrangePostPersonService.deleteBy(arrangeGroupId,arrangeDate);
            if(perArr!=null && !perArr.isEmpty()){
                List<BsArrangePostPersonEntity> list = perArr.toJavaList(BsArrangePostPersonEntity.class);
                String loginName = UserUtil.getCurrentUser().getLoginName();
                Date now=new Date();
                for (BsArrangePostPersonEntity entity : list) {
                    entity.setUpdateBy(loginName);
                    entity.setUpdateDate(now);
                    entity.setArrangeDate(arrangeDate);
                    entity.setArrangeGroupId(arrangeGroupId);
                    entity.setArrangeGroupName(arrangeGroupName);
                    bsArrangePostPersonService.save(entity);
                }
            }
            result.put("success", true);
            result.put("msg", "修改成功");
        }else{
            result.put("success", false);
            result.put("msg", "數據校驗失敗！請刷新重試！");
        }
        return result;
    }


    private boolean isSame(List<Map<String, String>> alist, JSONArray blist, boolean isSame) {
        if (!isSame) {
            return false;
        }

        if (alist != null && !alist.isEmpty() && blist != null && !blist.isEmpty()) {
            if (alist.size() == blist.size()) {
                for (int i = 0; i < alist.size(); i++) {
                    if (!alist.get(i).get("emp_no").equals(blist.getJSONObject(i).getString("emp_no"))) {
                        System.out.println("i="+i);
                        isSame = false;
                        break;
                    }
                }
            } else {
                isSame = false;
            }
        }else if((alist == null || alist.isEmpty()) && (blist != null && !blist.isEmpty()) ){
            isSame = false;
        }else if((alist != null && !alist.isEmpty()) && (blist == null || blist.isEmpty()) ){
            isSame = false;
        }
        return isSame;
    }
    private boolean isSamePost(List<Map<String, Object>> alist, JSONArray blist, boolean isSame) {
        if (!isSame) {
            return false;
        }

        if (alist != null && !alist.isEmpty() && blist != null && !blist.isEmpty()) {
            if (alist.size() == blist.size()) {
                for (int i = 0; i < alist.size(); i++) {
                    if (!alist.get(i).get("recno").toString().equals(blist.getJSONObject(i).getString("recno"))) {
                        isSame = false;
                        break;
                    }else  if(!alist.get(i).get("post_shift").toString().equals(blist.getJSONObject(i).getString("post_shift"))){
                        isSame = false;
                        break;
                    }else  if(new BigDecimal(alist.get(i).get("post_per_nu").toString()).intValue()!=blist.getJSONObject(i).getInteger("post_per_nu")){
                        isSame = false;
                        break;
                    }
                }
            } else {
                isSame = false;
            }
        }else if((alist == null || alist.isEmpty()) && (blist != null && !blist.isEmpty()) ){
            isSame = false;
        }else if((alist != null && !alist.isEmpty()) && (blist == null || blist.isEmpty()) ){
            isSame = false;
        }
        return isSame;
    }
}
