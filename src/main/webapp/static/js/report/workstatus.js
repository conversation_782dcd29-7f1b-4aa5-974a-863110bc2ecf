var dg;
var d;
$(function(){
    dg=$('#dg').datagrid({
        method: "get",
        url:ctx+'/workstatus/list',
        fit : true,
        fitColumns : true,
        border : false,
        idField : 'id',
        striped:true,
        pagination:true,
        rownumbers:true,
        pageNumber:1,
        pageSize : 20,
        pageList : [ 10, 20, 30, 40, 50 ],
        singleSelect:true,
        columns:[[
							                    { field: 'id', title: '資料識別ID  Primary',hidden:true},
											                    { field: 'colL1', title: '公司',sortable:true,width:200}
					,
																                    { field: 'colL2', title: '崗',sortable:true,width:200}
					,
																                    { field: 'colL3', title: '崗位',sortable:true,width:200,hidden:true}
					,
																                    { field: 'colL4', title: '編制人數',sortable:true,width:200}
					,
																                    { field: 'colL5', title: '應到人數-白班',sortable:true,width:200}
					,
																                    { field: 'colL6', title: '應到人數-夜班',sortable:true,width:200}
					,
																                    { field: 'colL7', title: '應到人數-合計',sortable:true,width:200}
					,
																                    { field: 'colL8', title: '實到人數-白班',sortable:true,width:200}
					,
																                    { field: 'colL9', title: '實到人數-夜班',sortable:true,width:200}
					,
                    																{ field: 'colL10', title: '實到人數-合計',sortable:true,width:200}
					,
																					{ field: 'colL11', title: '調休人數',sortable:true,width:200}
					,
																					{ field: 'colL12', title: '缺勤人數',sortable:true,width:200}
					,
																					{ field: 'colL13', title: '缺勤比率',sortable:true,width:200}
					,
																					{ field: 'colL14', title: '出勤比率',sortable:true,width:200}
												        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        rowTooltip: true,
        toolbar:'#tb'
    });
});

//创建查询对象并查询
function cx(){
    var obj=$("#searchFrom").serializeObject();
    dg.datagrid('load',obj);
}
//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("reset");
}
//导出excel
function exportExcel(){
    var form = document.getElementById("searchFrom");
    searchFrom.action=ctx+'/bsshift/exportExcel';
    form.submit();
}