var dg = null;
var d = null;
$(function() {
    dg = $('#dg').datagrid();
});

$(function() {
    // 初始化去除驗證
    $(":input").removeClass("validatebox-invalid");
});

function saveInfo(flag) {
    if (flag == '2') {
        var isValid = $("#mainform").form('validate');
        if (!isValid) {
            return;
        }
    }
    $.ajax({
        url: ctx + '/tempSecurityCostAply/' + action + '/' + flag,
        type: 'POST',
        beforeSend: ajaxLoading,
        // dataType: 'json',
        data: $('#mainform').serialize(),
        success: function (data) {
            ajaxLoadEnd();
            successTip(data, dg, d);
            window.parent.mainpage.mainTabs.closeCurrentTab();
        }
    });
}

function auditInfo(flag) {
    //console.log('haha');
    if (flag == 'Y') {
        if ($("#applyStatRe").val() == '17') {

            if ($("#securityCom1").combobox('getValue') == null || $("#securityCom1").combobox('getValue') == '') {
                alert("派駐保安公司不能為空！");
                return;
            }
        }
    } else if (flag == 'N') {
        var isValid = $("#mainform").form('validate');
        if (!isValid) {
            return;
        }
    }
    $.ajax({
        url: ctx + '/tempSecurityCostAply/auditSave/' + flag,
        type: 'POST',
        beforeSend: ajaxLoading,
        // dataType: 'json',
        data: $('#mainform').serialize(),
        success: function (data) {
            ajaxLoadEnd();
            successTip(data, dg, d);
            $("#btnPass").linkbutton('disable');
            $("#btnReject").linkbutton('disable');

            window.parent.mainpage.mainTabs.closeCurrentTab();

            //window.close();
        }
    });
}

// 重新提交
function resubmit() {
    window.location.href = ctx+'/tempSecurityCostAply/resubmit/' + $("#serialno").val();
}

// 取消申請
function cancelApply() {
    $.easyui.messager.confirm("操作提醒", "確定要取消該申請嗎？", function (c) {
        if (c) {
            $.ajax({
                url: ctx + '/tempSecurityCostAply/cancelApply/' + $("#serialno").val(),
                type: 'POST',
                beforeSend: ajaxLoading,
                success: function (data) {
                    ajaxLoadEnd();
                    successTip(data, dg, d);

                    if (data === 'success') window.parent.mainpage.mainTabs.closeCurrentTab();
                }
            });
        }
    });
}

// 驗證申請人聯繫電話
function valdApplyTel(obj){
    var phoneL=$.trim($("#dealtel").val()).split("").length;
    var tel = obj.value;
    if(tel!=""){
        var temp = tel;
        var regex  = /^[0-9]{3}\+[0-9]{5}$/;
        var patt1=new RegExp(regex);
        var result = patt1.test(tel);
        if(!result){
            alert("分機格式有誤!");
            obj.value="";
            obj.focus();
            return;
        }
    }
}

// 普通文件上傳
function uploadFile() {
    var attachIds = $.trim($("#attachids").val());
    var attachIdsL = attachIds.split(",").length;
    if (attachIdsL >= 6) {
        $.messager.alert("溫馨提示", "上傳附件個數不能超過五個！", "info");
        return;
    }
    var explorer = window.navigator.userAgent;
    if (!!window.ActiveXObject || "ActiveXObject" in window) {
        ajaxLoading();
        $.ajaxFileUpload({  // Jquery插件上传文件
            url: ctx + "/admin/uploadCompatible",
            secureuri: false,// 是否启用安全提交 默认为false
            fileElementId: "attachidsUpload", // type="file"的id
            dataType: "JSON",  // 返回值类型
            success: function (msg, status) {
                msg = jQuery.parseJSON(msg);
                $("#dowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href=' + ctx + '/admin/download/' + msg.id + ' target="_blank">' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delAtt(\'' + msg.id + '\')"></div></div>');
                $("#attachids").val($("#attachids").val() + msg.id + ",");
                ajaxLoadEnd();
            },
            error: function (data, status, e) {
                ajaxLoadEnd();
            }
        });
    }else{
// 创建
        var form_data = new FormData();
        // 获取文件
// var file = document.getElementById('fileToUpload').files[0];
        var file_data = $("#attachidsUpload").prop("files")[0];
        form_data.append("file", file_data);
        form_data.append("path", "upload");
        // 把所以表单信息
        $.ajax({
            type: "POST",
            url: ctx+"/admin/upload",
            beforeSend: ajaxLoading,
            dataType : "json",
            processData: false,  // 注意：让jQuery不要处理数据
            contentType: false,  // 注意：让jQuery不要设置contentType
            data: form_data
        }).success(function (msg) {
            ajaxLoadEnd();
            $("#dowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href='+ctx+'/admin/download/' + msg.id + ' target="_blank">' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delAtt(\'' + msg.id + '\')"></div></div>');
            $("#attachids").val($("#attachids").val() + msg.id + ",");
            // $("#attachids").val(msg.id);
        }).fail(function (msg) {
            ajaxLoadEnd();
        });
    }
}



function delAtt(fileid) {
    $("#" + fileid).remove();
    var kk = $("#attachids").val();
    kk = kk.replace(fileid + ",", "");
    $("#attachids").val(kk);
    ajaxLoading();
    $.post(ctx + '/admin/delete/' + fileid, {}, function (data) {
        successTip(data);
        ajaxLoadEnd();
    }, 'text').error(function() { ajaxLoadEnd(); });
}

function reUploadFile() {
    var attachIds = $.trim($("#reattachids").val());
    var attachIdsL = attachIds.split(",").length;
    if (attachIdsL >= 6) {
        alert("上傳附件個數不能超過五個！");
        return;
    }
    // 创建
    var form_data = new FormData();
    // 获取文件
// var file = document.getElementById('fileToUpload').files[0];
    var file_data = $("#attachidsUpload").prop("files")[0];
    form_data.append("file", file_data);
    form_data.append("path", "test");
    // 把所以表单信息
    $.ajax({
        type: "POST",
        url: ctx+"/admin/upload",
        beforeSend: ajaxLoading,
// dataType : "json",
        processData: false,  // 注意：让jQuery不要处理数据
        contentType: false,  // 注意：让jQuery不要设置contentType
        data: form_data
    }).success(function (msg) {
        ajaxLoadEnd();
        $("#reDowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href='+ctx+'"/admin /download/' + msg.id + '" target="_blank">' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delReAtt(\'' + msg.id + '\')"></div></div>');
        $("#reattachids").val($("#reattachids").val() + msg.id + ",");
        // $("#attachids").val(msg.id);
    }).fail(function (msg) {
    });
}

function delReAtt(fileid) {
    $("#" + fileid).remove();
    var kk = $("#reattachids").val();
    kk = kk.replace(fileid + ",", "");
    $("#reattachids").val(kk);
    $.post(ctx+'/admin /delete/' + fileid, {}, function (data) {
        successTip(data);
    });
}