# SGAS - Safety Guard Accident System

警衛考勤排班系統 - 已轉換為Maven項目

## 項目結構

```
sgas/
├── pom.xml                     # Maven配置文件
├── src/
│   ├── main/
│   │   ├── java/               # Java源代碼
│   │   │   └── com/foxconn/ipebg/
│   │   ├── resources/          # 配置文件
│   │   │   ├── application.properties
│   │   │   ├── applicationContext.xml
│   │   │   ├── spring-mvc.xml
│   │   │   └── ...
│   │   └── webapp/             # Web資源
│   │       ├── WEB-INF/
│   │       │   └── web.xml
│   │       └── index.jsp
│   └── test/
│       ├── java/               # 測試代碼
│       └── resources/          # 測試資源
├── doc/                        # 文檔
└── target/                     # Maven構建輸出目錄
```

## 技術棧

- **框架**: Spring MVC 4.1.3
- **ORM**: Hibernate 4.3.5
- **安全**: Apache Shiro 1.2.2
- **數據庫**: PostgreSQL
- **連接池**: Druid 0.2.23
- **緩存**: EhCache 2.6.6
- **調度**: Quartz 2.2.1
- **日誌**: Logback 1.2.3
- **JSON**: FastJSON 1.2.41

## Maven轉換說明

### 已完成的轉換
1. ✅ 創建標準Maven目錄結構
2. ✅ 創建pom.xml文件，包含所有依賴
3. ✅ 遷移Java源代碼到src/main/java
4. ✅ 遷移配置文件到src/main/resources
5. ✅ 遷移Web資源到src/main/webapp

### 需要手動完成的任務
1. 🔄 完整複製所有Java源文件（部分文件可能需要手動複製）
2. 🔄 複製所有配置文件到src/main/resources
3. 🔄 複製靜態資源（CSS、JS、圖片等）到src/main/webapp
4. 🔄 複製JSP視圖文件到src/main/webapp/WEB-INF/views
5. 🔄 驗證所有依賴版本是否正確
6. 🔄 測試項目構建和運行

## 構建命令

```bash
# 編譯項目
mvn compile

# 運行測試
mvn test

# 打包WAR文件
mvn package

# 清理構建目錄
mvn clean

# 完整構建
mvn clean package
```

## 部署說明

1. 使用Maven構建WAR文件：`mvn clean package`
2. 將生成的WAR文件部署到Tomcat等應用服務器
3. 確保數據庫連接配置正確
4. 確保所有外部依賴已正確配置

## 注意事項

- 原有的WebRoot/WEB-INF/lib目錄中的JAR包已轉換為Maven依賴
- 數據庫配置在application.properties中
- 日誌配置使用logback.xml
- 項目使用UTF-8編碼

## 開發環境要求

- JDK 1.8+
- Maven 3.6+
- PostgreSQL數據庫
- IDE支持（推薦IntelliJ IDEA或Eclipse）
