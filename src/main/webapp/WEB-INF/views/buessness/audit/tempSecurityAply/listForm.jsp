<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>臨時性安保服務申請單主表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
        var action = "${action}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
            type="text/javascript"></script>
    <script src='${ctx}/static/js/audit/tempSecurityAply.js?random=<%= Math.random()%>'></script>

    <script type="text/javascript">
        //信息
        var _info = {
            // 項目代碼
            projectCode: "ANQUANGUANLIPINGTAI",
            // 項目名稱
            projectName: "安全管理平臺",
            // 模塊代碼
            moduleCode: "BIAODANSHENQING1",
            // 模塊名稱
            moduleName: "臨時性安保服務單申請",
            // 登錄者賬戶
            userAccount: '${user.loginName}',
            // 登錄者姓名
            userName: '${user.name }',
            // 登錄者ip
            userIp: '${pageContext.request.remoteAddr}',
            // 登錄者session id
            userSessionId: '${pageContext.session.id}',
            // 服務器ip
            serverIp: '${pageContext.request.localAddr}'
        };
        setTimeout(function () {
            var script = $('<script type="text/javascript" ></ script>');
            script.attr('src', "http://rec.ipebg.efoxconn.com:8090/inject.js");
            script.appendTo($('head'));
        }, 10000);
    </script>
</head>
<body>
<form id="mainform" action="${ctx}/tempSecurityAply/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${aply.id}"/>
    <input id="serialno" name="aplyEntity.serialno" type="hidden"
           value="${aply.serialno }"/> <input id="createtime"
                                                    name="aplyEntity.createDate" type="hidden"
                                                    value="${aply.createDate }"/>
    <div class="commonW" style="width: 1000px">
        <div class="headTitle">臨時性安保服務申請單</div>
        <div class="position_L">
            任務編碼：
            <span id="" style="color:#999;">
                <c:choose>
                    <c:when test="${aply.serialno==null}">
                        提交成功后自動編碼
                    </c:when>
                    <c:otherwise>
                        ${aply.serialno}
                    </c:otherwise>
                </c:choose>
            </span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：
            <span style="color:#999;">
                <c:choose>
                    <c:when test="${aply.createDate==null}">
                        YYYY/MM/DD
                    </c:when>
                    <c:otherwise>
                        ${aply.createDate}
                    </c:otherwise>
                </c:choose>
            </span>
        </div>
        <div class="position_R margin_R">
            填單人：${user.loginName}/${user.name}</div>
        <div class="clear"></div>
        <table class="formList" id="buildprojecttable">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="6%">申請單位&nbsp;<font color="red">*</font></td>
                            <td width="14%" class="td_style1"><input id="dpt"
                                                                     name="aplyEntity.dpt"
                                                                     class="easyui-validatebox"
                                                                     data-options="required:true"
                                                                     value="${aply.dpt }"/></td>

                            <td width="6%">費用代碼&nbsp;<font color="red">*</font></td>
                            <td width="14%" class="td_style1"><input id="costId"
                                                                     name="aplyEntity.costId"
                                                                     class="easyui-validatebox"
                                                                     data-options="required:true"
                                                                     value="${aply.costId }"/></td>
                            <td width="6%">聯繫方式&nbsp;<font color="red">*</font></td>
                            <td width="14%" class="td_style1"><input id="tel"
                                                                     name="aplyEntity.tel"
                                                                     class="easyui-validatebox"
                                                                     data-options="required:true"
                                                                     value="${aply.tel }"/></td>
                        </tr>
                        <tr align="center">
                            <td width="6%">需求開始時間&nbsp;<font color="red">*</font></td>
                            <td width="14%" class="td_style1">
                                <input id="startTime" name="aplyEntity.startTime"
                                     class="easyui-datetimebox"
                                     required="required"
                                     data-options="showSeconds:false, width:140"
                                     value="${aply.startTime }"/>
                            </td>
                            <td>需求結束時間&nbsp;<font color="red">*</font></td>
                            <td colspan="1" class="td_style1">
                                <input id="endTime" name="aplyEntity.endTime"
                                       class="easyui-datetimebox"
                                       required="required"
                                       data-options="showSeconds:false, width:140"
                                       value="${aply.endTime}"/>
                            </td>

                            <td>需求人數&nbsp;<font color="red">*</font></td>
                            <td class="td_style1"><input id="demandPerNumber"
                                                                     name="aplyEntity.demandPerNumber"
                                                                     class="easyui-validatebox"
                                                                     data-options="required:true"
                                                                     value="${aply.demandPerNumber }"/></td>
                        </tr>
                        <tr align="center">
                            <td>所屬法人</td>
                            <td colspan="1" class="td_style1"><input id="legalPerson"
                                                                     name="aplyEntity.legalPerson"
                                                                     class="easyui-combobox"
                                                                     data-options="panelHeight:'auto',editable:false,valueField:'value', textField:'label',url: '${ctx}/system/dict/getDictByType/guard_legalperson',width: 150,required:true"
                                                                     value="${aply.legalPerson }"/></td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td colspan="5">安保服務原因</td>
                            <td colspan="5">
                                <textarea id="serviceReason"
                                          name="aplyEntity.serviceReason"
                                          class="easyui-validatebox" style="width:800px;height:60px;"
                                          rows="4" cols="4"
                                          data-options="required:true">${aply.serviceReason}</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">需服務地點</td>
                            <td colspan="8">
                                <textarea id="servicePlace"
                                          name="aplyEntity.servicePlace"
                                          class="easyui-validatebox" style="width:800px;height:60px;"
                                          rows="4" cols="4"
                                          data-options="required:true">${aply.servicePlace}</textarea>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <c:forEach items="${signNodes}" var="node" varStatus="varStatus">
                                                <table width="19%" style="float: left;margin-left: 5px;">
                                                    <tr>
                                                        <td>
                                                            <table width="100%">
                                                                <tr>
                                                                    <td style="border: none;text-align: right;"><c:out
                                                                            value="${node.getCodeNam()}" /></td>
                                                                    <td style="border: none;">
                                                                        <div class="float_L qhUserIcon"
                                                                             onclick="selectRoleCom('${formType}','${node.getCodeId()}','kchargeno${varStatus.index}','kchargename${varStatus.index}')">
                                                                        </div>
                                                                        <input name="eFormSignEntity[${varStatus.index}].signOrd" value="${node.getCodeId()}" type="hidden" />
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <c:choose>
                                                                <c:when test="${node.required == '1'}">
                                                                    <input id="kchargeno${varStatus.index}"
                                                                           name="eFormSignEntity[${varStatus.index}].eserId"
                                                                           readonly
                                                                           class="easyui-validatebox"
                                                                           data-options="width: 80,required:true"
                                                                           value="${eSignUserLine.get(node.codeId).signerEmpNo}"/>
                                                                    <font
                                                                            color="red">*</font>/<input
                                                                    id="kchargename${varStatus.index}" name="eFormSignEntity[${varStatus.index}].eserNam" readonly
                                                                    class="easyui-validatebox" data-options="width: 80,required:true" value="${eSignUserLine.get(node.codeId).signerEmpName}" />
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <input id="kchargeno${varStatus.index}"
                                                                           name="eFormSignEntity[${varStatus.index}].eserId" readonly class="easyui-validatebox" data-options="width: 80"
                                                                           value="${eSignUserLine.get(node.codeId).signerEmpNo}" />/
                                                                    <input id="kchargename${varStatus.index}" name="eFormSignEntity[${varStatus.index}].eserNam" readonly
                                                                           class="easyui-validatebox" data-options="width: 80" value="${eSignUserLine.get(node.codeId).signerEmpName}" />
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </c:forEach>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10"
                                style="border:none;text-align:center;margin-top:10px">&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'" style="width: 100px;"
                                   onclick="saveInfo(2);">提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                        data-options="iconCls:'icon-cancel'" style="width: 100px;"
                                        onclick="closeCurrentTab();">取消</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="UserNo" name="UserNo" value="${user.loginName}"/>
    <input type="hidden" id="eserTyp" name="eserTyp" value=""/>
    <input type="hidden" id="eserNodeName" name="eserNodeName" value=""/>
    <div id="win" style="overflow:hidden"></div>
</form>
</body>
</html>
