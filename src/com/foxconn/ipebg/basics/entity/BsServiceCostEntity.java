package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 警衛服務費用結賬明細主表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-10-07 13:15:24
 */
@Entity
@Table(name = "BS_SERVICE_COST")
@DynamicUpdate
@DynamicInsert
public class BsServiceCostEntity extends DataEntity<BsServiceCostEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    //$column.comments
    private Double trainingCost;
    //常規則費用（RMB分）
    private Double totalCost;
    //加班時數（小時）
    private Integer otHour;
    //加班費用（RMB分）
    private Double otCost;
    //臨時勤務費用（RMB分）
    private Double tsCost;
    //總費用（RMB分）
    private Double totalCostAll;
    //工作年月（YYYY-MM）
    private String workMonth;
    //事業群ID
    private String buId;
    //事業群名稱
    private String buNam;
    //法人
    private String legalId;
    //部門ID
    private String dptId;
    //部門名稱
    private String dptNam;
    //費用代碼
    private String costId;
    //崗位編號
    private String recno;
    //崗位詳細位置
    private String postPosition;
    //崗位名稱
    private String postNam;
    //正常上班天數（天）
    private String normalDay;
    //正常上班費用（RMB分）
    private Double normalCost;


    /**
     * 设置：${column.comments}
     */
    public void setTrainingCost(Double trainingCost) {
        this.trainingCost = trainingCost;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "TRAINING_COST", nullable = false, length = 20)
    public Double getTrainingCost() {
        return trainingCost;
    }

    /**
     * 设置：常規則費用（RMB分）
     */
    public void setTotalCost(Double totalCost) {
        this.totalCost = totalCost;
    }

    /**
     * 获取：常規則費用（RMB分）
     */

    @Column(name = "TOTAL_COST", nullable = false, length = 20)
    public Double getTotalCost() {
        return totalCost;
    }

    /**
     * 设置：加班時數（小時）
     */
    public void setOtHour(Integer otHour) {
        this.otHour = otHour;
    }

    /**
     * 获取：加班時數（小時）
     */

    @Column(name = "OT_HOUR", nullable = false, length = 20)
    public Integer getOtHour() {
        return otHour;
    }

    /**
     * 设置：加班費用（RMB分）
     */
    public void setOtCost(Double otCost) {
        this.otCost = otCost;
    }

    /**
     * 获取：加班費用（RMB分）
     */

    @Column(name = "OT_COST", nullable = false, length = 20)
    public Double getOtCost() {
        return otCost;
    }

    /**
     * 设置：臨時勤務費用（RMB分）
     */
    public void setTsCost(Double tsCost) {
        this.tsCost = tsCost;
    }

    /**
     * 获取：臨時勤務費用（RMB分）
     */

    @Column(name = "TS_COST", nullable = false, length = 20)
    public Double getTsCost() {
        return tsCost;
    }

    /**
     * 设置：總費用（RMB分）
     */
    public void setTotalCostAll(Double totalCostAll) {
        this.totalCostAll = totalCostAll;
    }

    /**
     * 获取：總費用（RMB分）
     */

    @Column(name = "TOTAL_COST_ALL", nullable = false, length = 20)
    public Double getTotalCostAll() {
        return totalCostAll;
    }

    /**
     * 设置：工作年月（YYYY-MM）
     */
    public void setWorkMonth(String workMonth) {
        this.workMonth = workMonth;
    }

    /**
     * 获取：工作年月（YYYY-MM）
     */

    @Column(name = "WORK_MONTH", nullable = false, length = 20)
    public String getWorkMonth() {
        return workMonth;
    }


    /**
     * 设置：事業群ID
     */
    public void setBuId(String buId) {
        this.buId = buId;
    }

    /**
     * 获取：事業群ID
     */

    @Column(name = "BU_ID", nullable = false, length = 20)
    public String getBuId() {
        return buId;
    }

    /**
     * 设置：事業群名稱
     */
    public void setBuNam(String buNam) {
        this.buNam = buNam;
    }

    /**
     * 获取：事業群名稱
     */

    @Column(name = "BU_NAM", nullable = false, length = 20)
    public String getBuNam() {
        return buNam;
    }

    /**
     * 设置：法人
     */
    public void setLegalId(String legalId) {
        this.legalId = legalId;
    }

    /**
     * 获取：法人
     */

    @Column(name = "LEGAL_ID", nullable = false, length = 20)
    public String getLegalId() {
        return legalId;
    }

    /**
     * 设置：部門ID
     */
    public void setDptId(String dptId) {
        this.dptId = dptId;
    }

    /**
     * 获取：部門ID
     */

    @Column(name = "DPT_ID", nullable = false, length = 20)
    public String getDptId() {
        return dptId;
    }

    /**
     * 设置：部門名稱
     */
    public void setDptNam(String dptNam) {
        this.dptNam = dptNam;
    }

    /**
     * 获取：部門名稱
     */

    @Column(name = "DPT_NAM", nullable = false, length = 20)
    public String getDptNam() {
        return dptNam;
    }

    /**
     * 设置：費用代碼
     */
    public void setCostId(String costId) {
        this.costId = costId;
    }

    /**
     * 获取：費用代碼
     */

    @Column(name = "COST_ID", nullable = false, length = 20)
    public String getCostId() {
        return costId;
    }

    /**
     * 设置：崗位編號
     */
    public void setRecno(String recno) {
        this.recno = recno;
    }

    /**
     * 获取：崗位編號
     */

    @Column(name = "RECNO", nullable = false, length = 20)
    public String getRecno() {
        return recno;
    }

    /**
     * 设置：崗位詳細位置
     */
    public void setPostPosition(String postPosition) {
        this.postPosition = postPosition;
    }

    /**
     * 获取：崗位詳細位置
     */

    @Column(name = "POST_POSITION", nullable = false, length = 20)
    public String getPostPosition() {
        return postPosition;
    }

    /**
     * 设置：崗位名稱
     */
    public void setPostNam(String postNam) {
        this.postNam = postNam;
    }

    /**
     * 获取：崗位名稱
     */

    @Column(name = "POST_NAM", nullable = false, length = 20)
    public String getPostNam() {
        return postNam;
    }

    /**
     * 设置：正常上班天數（天）
     */
    public void setNormalDay(String normalDay) {
        this.normalDay = normalDay;
    }

    /**
     * 获取：正常上班天數（天）
     */

    @Column(name = "NORMAL_DAY", nullable = false, length = 20)
    public String getNormalDay() {
        return normalDay;
    }

    /**
     * 设置：正常上班費用（RMB分）
     */
    public void setNormalCost(Double normalCost) {
        this.normalCost = normalCost;
    }

    /**
     * 获取：正常上班費用（RMB分）
     */

    @Column(name = "NORMAL_COST", nullable = false, length = 20)
    public Double getNormalCost() {
        return normalCost;
    }

}
