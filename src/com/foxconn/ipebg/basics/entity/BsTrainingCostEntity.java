package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 培訓期費用結帳明細表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-10-16 14:07:35
 */
@Entity
@Table(name = "BS_TRAINING_COST")
@DynamicUpdate
@DynamicInsert
public class BsTrainingCostEntity extends DataEntity<BsTrainingCostEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    //是否結算，0未結算，1已結算
    private String settlementType;
    //結算月份，費用并入那個月進行結算的
    private String workMonth;
    //結算崗位編號
    private String postRecno;
    //結算時間點
    private Date settlementDate;
    //培訓天數
    private Integer days;
    //培訓費用
    private Double trainingCost;
    //所有培訓日期
    private String dates;
    //培訓期編碼
    private String trainingPeriodNo;
    //培訓期名稱
    private String trainingPeriodName;
    //培訓開始時間
    private Date startDate;
    //培訓結束時間
    private Date endDate;
    //工號
    private String empNo;
    //姓名
    private String empName;
    // 保安公司
    private String securityCom;

    /**
     * 设置：是否結算，0未結算，1已結算
     */
    public void setSettlementType(String settlementType) {
        this.settlementType = settlementType;
    }

    /**
     * 获取：是否結算，0未結算，1已結算
     */

    @Column(name = "SETTLEMENT_TYPE", nullable = false, length = 20)
    public String getSettlementType() {
        return settlementType;
    }

    /**
     * 设置：結算月份，費用并入那個月進行結算的
     */
    public void setWorkMonth(String workMonth) {
        this.workMonth = workMonth;
    }

    /**
     * 获取：結算月份，費用并入那個月進行結算的
     */

    @Column(name = "WORK_MONTH", nullable = false, length = 20)
    public String getWorkMonth() {
        return workMonth;
    }

    /**
     * 设置：結算崗位編號
     */
    public void setPostRecno(String postRecno) {
        this.postRecno = postRecno;
    }

    /**
     * 获取：結算崗位編號
     */

    @Column(name = "POST_RECNO", nullable = false, length = 20)
    public String getPostRecno() {
        return postRecno;
    }

    /**
     * 设置：結算時間點
     */
    public void setSettlementDate(Date settlementDate) {
        this.settlementDate = settlementDate;
    }

    /**
     * 获取：結算時間點
     */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "SETTLEMENT_DATE", nullable = false, length = 20)
    public Date getSettlementDate() {
        return settlementDate;
    }

    /**
     * 设置：培訓天數
     */
    public void setDays(Integer days) {
        this.days = days;
    }

    /**
     * 获取：培訓天數
     */

    @Column(name = "DAYS", nullable = false, length = 20)
    public Integer getDays() {
        return days;
    }

    /**
     * 设置：培訓費用
     */
    public void setTrainingCost(Double trainingCost) {
        this.trainingCost = trainingCost;
    }

    /**
     * 获取：培訓費用
     */

    @Column(name = "TRAINING_COST", nullable = false, length = 20)
    public Double getTrainingCost() {
        return trainingCost;
    }

    /**
     * 设置：所有培訓日期
     */
    public void setDates(String dates) {
        this.dates = dates;
    }

    /**
     * 获取：所有培訓日期
     */

    @Column(name = "DATES", nullable = false, length = 20)
    public String getDates() {
        return dates;
    }


    /**
     * 设置：培訓期編碼
     */
    public void setTrainingPeriodNo(String trainingPeriodNo) {
        this.trainingPeriodNo = trainingPeriodNo;
    }

    /**
     * 获取：培訓期編碼
     */

    @Column(name = "TRAINING_PERIOD_NO", nullable = false, length = 20)
    public String getTrainingPeriodNo() {
        return trainingPeriodNo;
    }

    /**
     * 设置：培訓期名稱
     */
    public void setTrainingPeriodName(String trainingPeriodName) {
        this.trainingPeriodName = trainingPeriodName;
    }

    /**
     * 获取：培訓期名稱
     */

    @Column(name = "TRAINING_PERIOD_NAME", nullable = false, length = 20)
    public String getTrainingPeriodName() {
        return trainingPeriodName;
    }

    /**
     * 设置：培訓開始時間
     */
    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    /**
     * 获取：培訓開始時間
     */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "START_DATE", nullable = false, length = 20)
    public Date getStartDate() {
        return startDate;
    }

    /**
     * 设置：培訓結束時間
     */
    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    /**
     * 获取：培訓結束時間
     */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "END_DATE", nullable = false, length = 20)
    public Date getEndDate() {
        return endDate;
    }

    /**
     * 设置：工號
     */
    public void setEmpNo(String empNo) {
        this.empNo = empNo;
    }

    /**
     * 获取：工號
     */

    @Column(name = "EMP_NO", nullable = false, length = 20)
    public String getEmpNo() {
        return empNo;
    }

    /**
     * 设置：姓名
     */
    public void setEmpName(String empName) {
        this.empName = empName;
    }

    /**
     * 获取：姓名
     */
    @Column(name = "EMP_NAME", nullable = false, length = 20)
    public String getEmpName() {
        return empName;
    }

    /**
     * 獲取：保安公司
     */
    @Column(name = "SECURITY_COM", nullable = false, length = 15)
    public String getSecurityCom() {
        return securityCom;
    }

    /**
     * 設置：保安公司
     */
    public void setSecurityCom(String securityCom) {
        this.securityCom = securityCom;
    }
}
