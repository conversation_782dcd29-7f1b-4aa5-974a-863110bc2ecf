<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>輻射安全許可證變更申請單審核</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src="${ctx}/static/js/other/safetyPermit.js?" +Math.random() type="text/javascript"></script>
</head>
<body>
<form id="mainform" action="${ctx}/safetyPermit/${action}" method="post">
    <div class="commonW">
        <div class="headTitle">輻射安全許可證變更申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">${safetyPermitEntity.serialno}</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
                    <input class="inputCss" style="width: 120px" readonly="true"
                           value="<fmt:formatDate value='${safetyPermitEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>">
        </span>

        </div>
        <div class="position_R margin_R">
            填單人：${safetyPermitEntity.makerno}/${safetyPermitEntity.makername}</div>
        <br>
        <div class="clear"></div>
        <table class="formList" id="">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th colspan="10" style="text-align: left" class="td_style1">&nbsp;&nbsp;承辦人詳細信息</th>
                        </tr>
                        <tr align="center">
                            <td width="6%">承辦人工號</td>
                            <td width="6%" class="td_style1" style="text-align: center">
                                <input id="dealno" name="safetyPermitEntity.dealno" class="easyui-validatebox inputCss"
                                       readonly="true" style="width: 80px"
                                       value="${safetyPermitEntity.dealno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="6%">承辦人</td>
                            <td width="6%" class="td_style1" style="text-align: center">
                                <input id="dealname" name="safetyPermitEntity.dealname" class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly="true" value="${safetyPermitEntity.dealname }"/>
                            </td>
                            <td width="6%">單位代碼</td>
                            <td width="6%" class="td_style1" style="text-align: center">
                                <input id="dealdeptno" name="safetyPermitEntity.dealdeptno" class="easyui-validatebox inputCss" style="width: 80px"
                                       readonly="true" value="${safetyPermitEntity.dealdeptno }"/>
                            </td>
                            <td width="6%">提報日期</td>
                            <td width="6%" class="td_style1" style="text-align: center">
                                <input id="dealtime" name="safetyPermitEntity.dealtime" class="easyui-validatebox inputCss" style="width: 80px"
                                       datefmt="yyyy-MM-dd" readonly="true" value="<fmt:formatDate value="${safetyPermitEntity.dealtime}"/>"/>
                            </td>
                            <td width="6%">廠區</td>
                            <td width="6%" class="td_style1" style="text-align: center">
                                <input id="dealfactoryid" name="dealfactoryid" class="easyui-combobox inputCss" disabled
                                       panelHeight="auto" value="${safetyPermitEntity.dealfactoryid }" readonly="true"
                                       data-options="width: 120,required:true"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="3" class="td_style1">
                                <input id="dealdeptname" name="safetyPermitEntity.dealdeptname" class="easyui-validatebox inputCss"
                                       data-options="width: 400"
                                       readonly="true" value="${safetyPermitEntity.dealdeptname }"/>
                            </td>
                            <td>聯繫郵箱</td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="safetyPermitEntity.dealemail" class="easyui-validatebox inputCss"
                                       value="${safetyPermitEntity.dealemail }" style="width:300px;" readonly="true"
                                       data-options="required:true" onblur="valdEmail(this)"/>
                            </td>
                            <td>聯繫分機</td>
                            <td class="td_style1">
                                <input id="dealtel" name="safetyPermitEntity.dealtel" class="easyui-validatebox inputCss" style="width:90px;"
                                       readonly="true"
                                       value="${safetyPermitEntity.dealtel }" data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <th colspan="10" style="text-align: left" class="td_style1">&nbsp;&nbsp;申請單內容</th>
                        </tr>

                        <tr>
                            <td colspan="2"></td>
                            <td colspan="4" style="text-align: center">原核准事項</td>
                            <td colspan="4" style="text-align: center">變更后的事項</td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">單位名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="4" style="text-align: center">
                                <input id="applydeptname" name="safetyPermitEntity.applydeptname" class="easyui-validatebox inputCss"
                                       style="width:80%" readonly="true"
                                       data-options="required:true" value="${safetyPermitEntity.applydeptname }"/>
                            </td>
                            <td colspan="4" style="text-align: center">
                                <input id="newapplydeptname" name="safetyPermitEntity.newapplydeptname" class="easyui-validatebox inputCss"
                                       style="width:80%;" readonly="true"
                                       data-options="required:true" value="${safetyPermitEntity.newapplydeptname }"/>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">地址&nbsp;<font color="red">*</font></td>
                            <td colspan="4" style="text-align: center">
                                <input id="address" name="safetyPermitEntity.address" class="easyui-validatebox inputCss"
                                       style="width:80%;" readonly="true"
                                       data-options="required:true" value="${safetyPermitEntity.address }"/>
                            </td>
                            <td colspan="4" style="text-align: center">
                                <input id="newaddress" name="safetyPermitEntity.newaddress" class="easyui-validatebox inputCss"
                                       style="width:80%;" readonly="true"
                                       data-options="required:true" value="${safetyPermitEntity.newaddress }"/>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">法定代表人&nbsp;<font color="red">*</font></td>
                            <td colspan="4" style="text-align: center">
                                <input id="legalperson" name="safetyPermitEntity.legalperson" class="easyui-validatebox inputCss"
                                       style="width:80%;" readonly="true"
                                       data-options="required:true" value="${safetyPermitEntity.legalperson }"/>
                            </td>
                            <td colspan="4" style="text-align: center">
                                <input id="newlegalperson" name="safetyPermitEntity.newlegalperson" class="easyui-validatebox inputCss"
                                       style="width:80%;" readonly="true"
                                       data-options="required:true" value="${safetyPermitEntity.newlegalperson }"/>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">法定代表人身份證號碼&nbsp;<font color="red">*</font></td>
                            <td colspan="4" style="text-align: center">
                                <input id="legalpersonidno" name="safetyPermitEntity.legalpersonidno" class="easyui-validatebox inputCss"
                                       style="width:80%;" readonly="true"
                                       data-options="required:true" value="${safetyPermitEntity.legalpersonidno }"/>
                            </td>
                            <td colspan="4" style="text-align: center">
                                <input id="newlegalpersonidno" name="safetyPermitEntity.newlegalpersonidno"
                                       class="easyui-validatebox inputCss"
                                       style="width:80%;" readonly="true"
                                       data-options="required:true" value="${safetyPermitEntity.newlegalpersonidno }"/>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">許可證編號&nbsp;<font color="red">*</font></td>
                            <td colspan="3" style="text-align: center">
                                <input id="licenseno" name="safetyPermitEntity.licenseno" class="easyui-validatebox inputCss"
                                       style="width:80%;" readonly="true"
                                       data-options="required:true" value="${safetyPermitEntity.licenseno }"/>
                            </td>
                            <td colspan="2" style="text-align: center">種類和範圍&nbsp;<font color="red">*</font></td>
                            <td colspan="3" style="text-align: center">
                                <input id="typescope" name="safetyPermitEntity.typescope" class="easyui-validatebox inputCss"
                                       style="width:80%;" readonly="true"
                                       data-options="required:true" value="${safetyPermitEntity.typescope }"/>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">發證日期&nbsp;<font color="red">*</font></td>
                            <td colspan="3" style="text-align: center">
                                <input id="licensedate" name="safetyPermitEntity.licensedate" class="easyui-validatebox inputCss"
                                       datefmt="yyyy-MM-dd" data-options="width: 100,required:true" readonly="true"
                                       value="<fmt:formatDate value="${safetyPermitEntity.licensedate}"/>"/>
                            </td>
                            <td colspan="2" style="text-align: center">有效期&nbsp;<font color="red">*</font></td>
                            <td colspan="3" style="text-align: center">
                                <input id="effectivedate" name="safetyPermitEntity.effectivedate" class="easyui-validatebox inputCss"
                                       style="width:80%;" readonly="true"
                                       data-options="required:true" value="${safetyPermitEntity.effectivedate }"/>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">聯繫人&nbsp;<font color="red">*</font></td>
                            <td colspan="3" style="text-align: center">
                                <input id="contactperson" name="safetyPermitEntity.contactperson" class="easyui-validatebox inputCss"
                                       style="width:80%;" readonly="true"
                                       data-options="required:true" value="${safetyPermitEntity.contactperson }"/>
                            </td>
                            <td colspan="2" style="text-align: center">聯繫電話&nbsp;<font color="red">*</font></td>
                            <td colspan="3" style="text-align: center">
                                <input id="contactphone" name="safetyPermitEntity.contactphone" class="easyui-validatebox inputCss"
                                       style="width:80%;" readonly="true"
                                       data-options="required:true" value="${safetyPermitEntity.contactphone }"/>
                            </td>

                        </tr>

                        <tr align="center">
                            <td colspan="2">附件</td>
                            <td colspan="8" class="td_style1">
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L"><a href="${ctx}/admin/download/${item.id}">${item.name}</a></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">說明</td>
                            <td colspan="8" class="td_style1">
                                1.變更后的企業法人營業執照或事業單位法人證書正、副本複印件；
                                2.變更后的法定代表人身份證複印件；
                                3.其它。
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">批註</td>
                            <td colspan="8">
                                <input style="width: 90%" id="attachidsremark" name="attachidsremark" class="easyui-validatebox"/>
                            </td>
                        </tr>
                        <c:if test="${not empty nodeName&&'環保科技處對應窗口' eq nodeName}">
                            <tr>
                                <td colspan="2" style="text-align: center">附件&nbsp;（補充說明）</td>
                                <td colspan="8" class="td_style1">
                    <span class="sl-custom-file">
						<input type="button" value="点击上传文件" class="btn-file"/>
						<input id="attachidsUpload" name="attachidsUpload"
                               type="file" onchange="reUploadFile();" class="ui-input-file">

						</span>
                                    <input type="hidden" id="reattachids" name="safetyPermitEntity.reattachids" value="${safetyPermitEntity.reattachids}"/>
                                    <div id="reDowloadUrl"></div>
                                </td>
                            </tr>
                        </c:if>
                        <c:if test="${safetyPermitEntity.reattachids!=null}">
                            <tr align="center">
                                <td colspan="2" style="text-align: center">附件&nbsp;（補充說明）</td>
                                <td colspan="8" class="td_style1">

                                    <div id="dowloadUrl">
                                        <c:forEach items="${reFile}" varStatus="i" var="item">
                                            <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                                <div class="float_L"><a href="${ctx}/admin/download/${item.id}">${item.name}</a></div>
                                            </div>
                                        </c:forEach>
                                    </div>
                                </td>
                            </tr>
                        </c:if>

                        <tr align="center">
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <fox:action cssClass="easyui-linkbutton" cssStyle="width: 100px;"
                                            serialNo="${safetyPermitEntity.serialno}"></fox:action>

                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','輻射安全許可證變更申請單流程圖');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${safetyPermitEntity.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
<div id="dlg"></div>
</body>
</html>