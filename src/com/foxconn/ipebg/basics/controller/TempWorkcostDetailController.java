package com.foxconn.ipebg.basics.controller;

import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import com.foxconn.ipebg.basics.dao.BsShiftDao;
import com.foxconn.ipebg.basics.service.BsDptService;
import com.foxconn.ipebg.basics.service.BsShiftService;
import com.foxconn.ipebg.basics.vo.TempWorkcostGatherVO;
import com.foxconn.ipebg.system.service.DictService;
import com.foxconn.ipebg.system.utils.CustomExcelUtil;
import com.google.common.collect.Lists;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.basics.entity.TempWorkcostDetailEntity;
import com.foxconn.ipebg.basics.service.TempWorkcostDetailService;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;


/**
 * 旧经管报表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-16 10:36:12
 */
@Controller
@RequestMapping("tempworkcostdetail")
public class TempWorkcostDetailController extends BaseController {

    @Autowired
    private TempWorkcostDetailService tempWorkcostDetailService;
    @Autowired
    private DictService dictService;
    @Autowired
    private BsShiftService bsShiftService;
    @Autowired
    private BsDptService bsDptService;
    /**
      * 方法描述: 列表信息
      * @Author: S6114894
      * @CreateDate:   2019-05-16 10:36:12
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:tempworkcostdetail:list")
    public String list() {
        //查询列表数据
        return "basics/tempworkcostdetail/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6114894
      * @CreateDate:   2019-05-16 10:36:12
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:tempworkcostdetail:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<TempWorkcostDetailEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        page = tempWorkcostDetailService.search(page, filters);
        return getEasyUIData(page);
    }
    /**
     * 方法描述: 保存
     * @Author: S6114894
     * @CreateDate:   2019-05-16 10:36:12
     * @Return
     **/
   
    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("basics:tempworkcostdetail:add")
    @ResponseBody
    public String create(@Valid TempWorkcostDetailEntity tempWorkcostDetail, Model model) {
        tempWorkcostDetail.setNewRecord(true);
    	tempWorkcostDetail.setCreateDate(new Date());
        tempWorkcostDetailService.save(tempWorkcostDetail);
        return "success";
    }
    
    /**
      * 方法描述: 添加跳轉
      * @Author: S6114894
      * @CreateDate:   2019-05-16 10:36:12
      * @Return
      **/

    @RequiresPermissions("basics:tempworkcostdetail:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("tempWorkcostDetail", new TempWorkcostDetailEntity());
        model.addAttribute("action", "create");
        return "basics/tempworkcostdetail/listForm";
    }
    
     /**
     * 方法描述: 修改跳轉
     * @Author: S6113712
     * @CreateDate:   2018-10-06 11:04:39
     * @Return
     **/

   @RequiresPermissions("basics:tempworkcostdetail:update")
   @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
   public String updateForm(@PathVariable("id") String id, Model model) {
       model.addAttribute("tempWorkcostDetail", tempWorkcostDetailService.get(id));
       model.addAttribute("action", "update");
      return "basics/tempworkcostdetail/listForm";
   }
   
	/**
	 * 方法描述: 修改
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-11 10:55:00
	 * @Return
	 **/

   @RequestMapping(value = "update", method = RequestMethod.POST)
   @RequiresPermissions("basics:tempworkcostdetail:update")
   @ResponseBody
   public String update(@Valid TempWorkcostDetailEntity tempWorkcostDetail,@RequestParam(value = "ids") String ids) {
	   tempWorkcostDetail.setId(ids);
	   tempWorkcostDetail.setUpdateDate(new Date());
	   tempWorkcostDetailService.update(tempWorkcostDetail);
	   
       return "success";
   }
    /**
      * 方法描述: 根據主鍵刪除
      * @Author: S6114894
      * @CreateDate:   2019-05-16 10:36:12
      * @Return
      **/

    @RequestMapping("delete/{id}")
    @RequiresPermissions("basics:tempworkcostdetail:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        tempWorkcostDetailService.delete(id);
        return "success";
    }
    /**
      * 导出excel
      * @Author: S6114894
      * @CreateDate:   2019-05-16 10:36:12
      * @Return
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(@RequestParam Map<String,Object> params, HttpServletRequest request, HttpServletResponse response) throws Exception {
        CustomExcelUtil excelUtil = null;
        try {
            if (params.get("buNam").equals("請選擇事業處")) {
                params.put("buNam", "");
            }
            excelUtil = new CustomExcelUtil(response, null);
            excelUtil.createNewSheet("經管報表", 0);
            tempWorkcostDetailService.generateExcelHeader(excelUtil.getCurrentSheet());
            List<TempWorkcostGatherVO> gathers = tempWorkcostDetailService.listGather(params);
            tempWorkcostDetailService.fillSheet(excelUtil.getCurrentSheet(), gathers, dictService.getMapByType("guard_legalperson"));
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (excelUtil != null) {
                try {
                    excelUtil.closeWorkBook();
                } catch (Exception e) {}
            }
        }
    }

    @RequestMapping("initDatas")
    @ResponseBody
    public Map<String,Object> initDatas(){
        Map<String,Object> result=new HashMap<>();

        //法人
        result.put("legalPersonDicts", dictService.getDictByType("guard_legalperson"));
        //崗位類別
        result.put("postTypeDicts",dictService.getDictByType("guard_postType"));
        //部門信息
        result.put("bsDptList",bsDptService.getAll());

        return result;
    }


    /**
     * 方法描述: 經管匯總跳轉
     * @Author: S6113712
     * @CreateDate:   2018-10-06 11:04:39
     * @Return
     **/
    @RequestMapping(value = "gather", method = RequestMethod.GET)
    public String gather() {
        return "basics/tempworkcostdetail/listGather";
    }

    /**
     * 經管匯總查詢
     * @param params
     * @return
     */
    @RequestMapping("listGather")
    @ResponseBody
    public List<TempWorkcostGatherVO> listGather(@RequestParam Map<String,Object> params){
        return tempWorkcostDetailService.listGather(params);
    }

}
