package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * 培訓期基本資料
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-08-29 10:42:47
 */
@Entity
@Table(name = "BS_TRAINING_PERIOD")
@DynamicUpdate
@DynamicInsert
public class BsTrainingPeriodEntity extends DataEntity<BsTrainingPeriodEntity>
		implements Serializable {
	private static final long serialVersionUID = 1L;

	// 培訓期編碼（自動生成）
	private String trainingPeriodNo;
	// 培訓期名稱（自動生成）
	private String trainingPeriodName;
	// 所属保安公司
	private String securityCom;
	// 培訓開始時間
	private Date startDate;
	// 培訓結束時間
	private Date endDate;
	// 開始時間
	private String startTime;
	// 結束時間
	private String endTime;
	// 開始時間下午
	private String startTime1;
	// 結束時間下午
	private String endTime1;

	/**
	 * 设置：培訓期編碼（自動生成）
	 */
	public void setTrainingPeriodNo(String trainingPeriodNo) {
		this.trainingPeriodNo = trainingPeriodNo;
	}

	/**
	 * 获取：培訓期編碼（自動生成）
	 */

	@Column(name = "TRAINING_PERIOD_NO", nullable = false, length = 20)
	public String getTrainingPeriodNo() {
		return trainingPeriodNo;
	}

	/**
	 * 设置：培訓期名稱（自動生成）
	 */
	public void setTrainingPeriodName(String trainingPeriodName) {
		this.trainingPeriodName = trainingPeriodName;
	}

	/**
	 * 获取：培訓期名稱（自動生成）
	 */

	@Column(name = "TRAINING_PERIOD_NAME", nullable = false, length = 20)
	public String getTrainingPeriodName() {
		return trainingPeriodName;
	}

	/**
	 * 设置：培訓開始時間
	 */
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	/**
	 * 获取：培訓開始時間
	 */

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Column(name = "START_DATE", nullable = false, length = 20)
	public Date getStartDate() {
		return startDate;
	}

	/**
	 * 设置：培訓結束時間
	 */
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	/**
	 * 获取：培訓結束時間
	 */

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Column(name = "END_DATE", nullable = false, length = 20)
	public Date getEndDate() {
		return endDate;
	}

	/**
	 * 设置：開始時間
	 */
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	/**
	 * 获取：開始時間
	 */

	@Column(name = "START_TIME", nullable = false, length = 20)
	public String getStartTime() {
		return startTime;
	}

	/**
	 * 设置：結束時間
	 */
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	/**
	 * 获取：結束時間
	 */

	@Column(name = "END_TIME", nullable = false, length = 20)
	public String getEndTime() {
		return endTime;
	}
	@Column(name = "START_TIME1", nullable = false, length = 20)
	public String getStartTime1() {
		return startTime1;
	}

	public void setStartTime1(String startTime1) {
		this.startTime1 = startTime1;
	}
	@Column(name = "END_TIME1", nullable = false, length = 20)
	public String getEndTime1() {
		return endTime1;
	}

	public void setEndTime1(String endTime1) {
		this.endTime1 = endTime1;
	}

	@Column(name = "security_com", nullable = false, length = 15)
	public String getSecurityCom() {
		return securityCom;
	}

	public void setSecurityCom(String securityCom) {
		this.securityCom = securityCom;
	}
}
