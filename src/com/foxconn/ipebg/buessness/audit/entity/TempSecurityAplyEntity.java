package com.foxconn.ipebg.buessness.audit.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

import com.foxconn.ipebg.common.entity.DataEntity;

@Entity
@Table(name = "E_TEMP_SECURITY_APLY")
@DynamicUpdate
@DynamicInsert
public class TempSecurityAplyEntity extends DataEntity<TempSecurityAplyEntity> implements Serializable {
    private static final long serialVersionUID = 1L;
    // 任務編號
    private String serialno;
    // 填單人工號
    private String makerno;
    // 填單人名稱
    private String makername;
    // 簽核完成時間
    private Date complettime;
    // 表單狀態
    private String workstatus;
    // 申請單位
    private String dpt;
    // 費用代碼
    private String costId;
    // 聯繫方式
    private String tel;
    // 需求時間段
    private Date startTime;
    private Date endTime;
    // 需求人數
    private String demandPerNumber;
    // 安保服務原因
    private String serviceReason;
    // 需求服務地點
    private String servicePlace;
    // 服務公司
    private String securityCom;
    // 是否產生費用
    private String hasCost;
    // 申請人IP地址
    private String applyIp;
    // 申請單狀態(E：結案，0：申請，O：，駁回 Q：異常結案)
    private String applyStat;
    // 審核人工號
    private String signEmpNo;
    // 審核人姓名
    private String signEmpNam;
    // 審核時間
    private Date signDate;
    // 法人
    private String legalPerson;

    @Column(name = "LEGAL_PERSON", nullable = false, length = 80)
    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    /**
     * 设置：任務編號
     */
    public void setSerialno(String serialno) {
        this.serialno = serialno;
    }

    /**
     * 获取：任務編號
     */

    @Column(name = "SERIALNO", nullable = false, length = 20)
    public String getSerialno() {
        return serialno;
    }

    /**
     * 设置：填單人工號
     */
    public void setMakerno(String makerno) {
        this.makerno = makerno;
    }

    /**
     * 获取：填單人工號
     */

    @Column(name = "MAKERNO", nullable = false, length = 20)
    public String getMakerno() {
        return makerno;
    }

    /**
     * 设置：填單人名稱
     */
    public void setMakername(String makername) {
        this.makername = makername;
    }

    /**
     * 获取：填單人名稱
     */

    @Column(name = "MAKERNAME", nullable = false, length = 20)
    public String getMakername() {
        return makername;
    }

    /**
     * 设置：簽核完成時間
     */
    public void setComplettime(Date complettime) {
        this.complettime = complettime;
    }

    /**
     * 获取：簽核完成時間
     */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "COMPLETTIME", nullable = false, length = 20)
    public Date getComplettime() {
        return complettime;
    }

    /**
     * 设置：表單狀態
     */
    public void setWorkstatus(String workstatus) {
        this.workstatus = workstatus;
    }

    /**
     * 获取：表單狀態
     */

    @Column(name = "WORKSTATUS", nullable = false, length = 20)
    public String getWorkstatus() {
        return workstatus;
    }

    @Column(name = "DPT", nullable = false, length = 50)
    public String getDpt() {
        return dpt;
    }

    public void setDpt(String dpt) {
        this.dpt = dpt;
    }

    @Column(name = "COST_ID", nullable = false, length = 20)
    public String getCostId() {
        return costId;
    }

    public void setCostId(String costId) {
        this.costId = costId;
    }

    @Column(name = "TEL", nullable = false, length = 15)
    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    @Column(name = "START_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    @Column(name = "END_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    @Column(name = "DEMAND_PER_NUMBER")
    public String getDemandPerNumber() {
        return demandPerNumber;
    }

    public void setDemandPerNumber(String demandPerNumber) {
        this.demandPerNumber = demandPerNumber;
    }

    @Column(name = "SERVICE_REASON", nullable = false, length = 100)
    public String getServiceReason() {
        return serviceReason;
    }

    public void setServiceReason(String serviceReason) {
        this.serviceReason = serviceReason;
    }

    @Column(name = "SERVICE_PLACE", nullable = false, length = 50)
    public String getServicePlace() {
        return servicePlace;
    }

    public void setServicePlace(String servicePlace) {
        this.servicePlace = servicePlace;
    }

    @Column(name = "SECURITY_COM", nullable = false, length = 15)
    public String getSecurityCom() {
        return securityCom;
    }

    public void setSecurityCom(String securityCom) {
        this.securityCom = securityCom;
    }

    @Column(name = "HAS_COST")
    public String getHasCost() {
        return hasCost;
    }

    public void setHasCost(String hasCost) {
        this.hasCost = hasCost;
    }

    /**
     * 设置：申請人IP地址
     */
    public void setApplyIp(String applyIp) {
        this.applyIp = applyIp;
    }

    /**
     * 获取：申請人IP地址
     */
    @Column(name = "APPLY_IP", nullable = false, length = 20)
    public String getApplyIp() {
        return applyIp;
    }

    /**
     * 设置：申請單狀態(E：結案，0：申請，O：，駁回 Q：異常結案)
     */
    public void setApplyStat(String applyStat) {
        this.applyStat = applyStat;
    }

    /**
     * 获取：申請單狀態(E：結案，0：申請，O：，駁回 Q：異常結案)
     */
    @Column(name = "APPLY_STAT", nullable = false, length = 20)
    public String getApplyStat() {
        return applyStat;
    }

    /**
     * 设置：審核人工號
     */
    public void setSignEmpNo(String signEmpNo) {
        this.signEmpNo = signEmpNo;
    }

    /**
     * 获取：審核人工號
     */
    @Column(name = "SIGN_EMP_NO", nullable = false, length = 20)
    public String getSignEmpNo() {
        return signEmpNo;
    }

    /**
     * 设置：審核人姓名
     */
    public void setSignEmpNam(String signEmpNam) {
        this.signEmpNam = signEmpNam;
    }

    /**
     * 获取：審核人姓名
     */
    @Column(name = "SIGN_EMP_NAM", nullable = false, length = 20)
    public String getSignEmpNam() {
        return signEmpNam;
    }

    /**
     * 设置：審核時間
     */
    public void setSignDate(Date signDate) {
        this.signDate = signDate;
    }

    /**
     * 获取：審核時間
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "SIGN_DATE", nullable = false, length = 20)
    public Date getSignDate() {
        return signDate;
    }
}
