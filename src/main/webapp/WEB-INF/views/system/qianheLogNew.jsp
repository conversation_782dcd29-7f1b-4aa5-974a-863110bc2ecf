<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title></title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
	type="text/javascript"></script>

</head>
<body style="font-family: '微软雅黑'">
	<table id="dg"></table>
	<script type="text/javascript">
		var dg;
		$(function() {
			dg = $('#dg')
					.datagrid(
							{
							
								method : "get",
								url : '${ctx}/eformsign/queryChargeLogNew?filter_EQS_applyId=${serialno}',
								fit : false,
								fitColumns : true,
								border : false,
								striped : true,
								nowrap : false,
								idField : 'id',
								scrollbarSize : 0,
								pagination : false,
								rownumbers : true,
								pageNumber : 1,
								pageSize : 20,
								pageList : [ 10, 20, 30, 40, 50 ],
								singleSelect : true,
								columns : [ [
										{
											field : 'id',
											title : 'id',
											hidden : true
										},
										{
											field : 'eserDate',
											title : '簽核時間',
											sortable : true,
											width : 80
										},
										{
											field : 'signNam',
											title : '簽核節點',
											sortable : true,
											width : 120
										},
										{
											field : 'eserNam',
											title : '簽核主管',
											sortable : true,
											width : 50  ,
											formatter : function(value, row,
													index) {
												
												if (value.length>30) {
													return '<img style="max-width:80px;max-height:45px;" src="'+value+'"/>';
												}
												else
												{
												return value;
												}
												return '';
											}  
										}, {
											field : 'eserStat',
											title : '簽核意見',
											sortable : true
										}, {
											field : 'eserMemo',
											title : '批註',
											sortable : true,
											width : 150
										}, {
											field : 'eserIp',
											title : '簽核電腦IP',
											sortable : true,
											width : 80
										} ] ],
								rowStyler : function(index, row) {
									if (row.eserStat == '駁回') {
										return 'background-color:pink;color:blue;font-weight:bold;';
									}
								},
								enableHeaderClickMenu : false,
								enableHeaderContextMenu : false,
								enableRowContextMenu : false,
								onLoadSuccess : function(data) {
									setTimeout("reSetIframeHeight()", 100);
								}
							});
		});

		var reSetIframeHeight = function() {
			try {
				var oIframe = parent.document.getElementById('qianheLogFrame');
				iframeLoaded(oIframe);
			} catch (err) {
				try {
					parent.document.getElementById(window.name).height = 1000;
				} catch (err2) {
				}
			}
		}

		function iframeLoaded(iframe) {
			if (iframe) {
				var iframeWin = iframe.contentWindow
						|| iframe.contentDocument.parentWindow;
				if (iframeWin.document.body) {
					iframe.height = (iframeWin.document.documentElement.scrollHeight || iframeWin.document.body.scrollHeight);
				}
			}
		};
	</script>
</body>
</html>