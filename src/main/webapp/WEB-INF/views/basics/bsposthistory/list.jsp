<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>崗位明細表</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
	<script type="text/javascript">
        //信息
        var _info = {
            // 項目代碼
            projectCode : "ANQUANGUANLIPINGTAI",
            // 項目名稱
            projectName : "安全管理平臺",
            // 模塊代碼
            moduleCode : "XITONGJIBENZILIAOWEIHU3",
            // 模塊名稱
            moduleName : "崗位信息",
            // 登錄者賬戶
            userAccount : '${user.loginName}',
            // 登錄者姓名
            userName : '${user.name }',
            // 登錄者ip
            userIp : '${pageContext.request.remoteAddr}',
            // 登錄者session id
            userSessionId : '${pageContext.session.id}',
            // 服務器ip
            serverIp : '${pageContext.request.localAddr}'
        };
        setTimeout(function(){
            var script = $('<script type="text/javascript" ></ script>');
            script.attr('src',"http://rec.ipebg.efoxconn.com:8090/inject.js");
            script.appendTo($('head'));
        },10000);
	</script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
        <form id="searchFrom" action="">
		<input type="text" name="filter_EQS_recno" class="easyui-validatebox" data-options="width:150,prompt: '崗位編號'"/>
		<input type="text" name="filter_LIKES_postName" class="easyui-validatebox" data-options="width:150,prompt: '崗位名稱'"/>
		<input type="startDate" name="filter_GED_createDate" class="easyui-datebox" data-options="width:150, prompt: '起始生效時間'">
		~&nbsp;<input type="endDate" name="filter_LED_createDate" class="easyui-datebox" data-options="width:150, prompt: '截止生效時間'">
		<input id="applyType" name="filter_EQS_applyType" class="easyui-combobox" data-options="width:150, prompt: '需求類別', panelHeight:'auto',valueField:'value', textField:'label',url: '${ctx}/system/dict/getDictByType/guard_aply_type'"/>
		<input id="securityCom" name="filter_EQS_securityCom" class="easyui-combobox" data-options="prompt: '保安公司', panelHeight:'auto',valueField:'value', textField:'label',url: '${ctx}/system/dict/getDictByType/guard_securityCom',width: 150"/>
		<span class="toolbar-item dialog-tool-separator"></span>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
		   onclick="listSearchReset()">重置</a>
		<a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel"
		   onclick="exportExcel()">导出Excel</a><br/>
	</form>
  </div>
<table id="dg"></table>
<div id="dlg"></div>

<script src="${ctx}/static/js/basics/bsposthistory.js?random=<%= Math.random()%>"></script>
</body>
</html>