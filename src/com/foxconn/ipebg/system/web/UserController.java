package com.foxconn.ipebg.system.web;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;

import cn.hutool.core.bean.BeanUtil;

import com.foxconn.ipebg.basics.entity.BsDptEntity;
import com.foxconn.ipebg.basics.service.BsDptService;
import com.foxconn.ipebg.buessness.common.entity.TQhUserformhsEntity;
import com.foxconn.ipebg.buessness.common.entity.UserDto;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.common.exception.BusinessException;
import com.foxconn.ipebg.common.utils.ResultEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.system.entity.User;
import com.foxconn.ipebg.system.service.UserOrgService;
import com.foxconn.ipebg.system.service.UserRoleService;
import com.foxconn.ipebg.system.service.UserService;
import com.foxconn.ipebg.system.utils.UserUtil;

/**
 * 用户controller
 * <AUTHOR>
 * @date 2015年1月13日
 */
@Controller
@RequestMapping("system/user")
public class UserController extends BaseController {

	@Autowired
	private UserService userService;

	@Autowired
	private UserRoleService userRoleService;

	@Autowired
	private UserOrgService userOrgService;

	@Autowired
	private TQhUserformhsService userformhsService;

	@Autowired
	private UserServiceUtil serviceUtil;
    @Autowired
    private BsDptService bsDptService;

	/**
	 * 默认页面
	 */
	@RequestMapping(method = RequestMethod.GET)
	public String list() {
		return "system/userList";
	}

	/**
	 * 获取用户json
	 */
	@RequiresPermissions("sys:user:view")
	@RequestMapping(value="json",method = RequestMethod.GET)
	@ResponseBody
	public Map<String, Object> getData(HttpServletRequest request) {
        Page<User> page = null;
        try {
			page = getPage(request);
            List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
            page = userService.search(page, filters);
        } catch (Exception e) {
            throw new BusinessException(ResultEnum.SYS_ECEPTION);
        }
        return getEasyUIData(page);
	}

	@RequestMapping(value = "userSecurityCom")
	@ResponseBody
	public List<Map<String, String>> getUserSecurityCom() {
		List<Map<String, String>> result = new ArrayList<Map<String, String>>();
		try {
			User user = UserUtil.getCurrentUser();
			if (user != null) {
				String securityCom = user.getSecurityCom();
				if (securityCom != null) {
					String[] coms = securityCom.split(",");
					for (int i=0; i<coms.length; i++) {
						String com = coms[i];
						if (StringUtils.isNotEmpty(com)) {
							Map<String, String> comMap = new HashMap<String, String>();
							comMap.put("value", com);
							comMap.put("label", com);
							result.add(comMap);
						}
					}
				}
			}
		} catch (Exception e) {
			throw new BusinessException(ResultEnum.SYS_ECEPTION);
		}
		return result;
	}

	/**
	 * 添加用户跳转
	 *
	 * @param model
	 */
	@RequiresPermissions("sys:user:add")
	@RequestMapping(value = "create", method = RequestMethod.GET)
	public String createForm(Model model) {
		model.addAttribute("user", new User());
		model.addAttribute("action", "create");
		return "system/userForm";
	}

	/**
	 * 添加用户
	 *
	 * @param user
	 * @param model
	 */
	@RequiresPermissions("sys:user:add")
	@RequestMapping(value = "create", method = RequestMethod.POST)
	@ResponseBody
	public String create(@Valid User user, Model model) {
		userService.save(user);
		return "success";
	}

	/**
	 * 修改用户跳转
	 *
	 * @param id
	 * @param model
	 * @return
	 */
	@RequiresPermissions("sys:user:update")
	@RequestMapping(value = "update/{id}", method = RequestMethod.GET)
	public String updateForm(@PathVariable("id") Integer id, Model model) {
		model.addAttribute("user", userService.get(id));
		model.addAttribute("action", "update");
		return "system/userForm";
	}

	/**
	 * 修改用户
	 *
	 * @param user
	 * @param model @Valid @ModelAttribute @RequestBody User user
	 * @return
	 */
	@RequiresPermissions("sys:user:update")
	@RequestMapping(value = "update", method = RequestMethod.POST)
	@ResponseBody
	public String update(@Valid @ModelAttribute User user,Model model) {
		userService.update(user);
		return "success";
	}

	/**
	 * 删除用户
	 *
	 * @param id
	 * @return
	 */
	@RequiresPermissions("sys:user:delete")
	@RequestMapping(value = "delete/{id}")
	@ResponseBody
	public String delete(@PathVariable("id") Integer id) {
		userService.delete(id);
		return "success";
	}

	/**
	 * 弹窗页-用户拥有的角色
	 *
	 * @param id
	 * @param model
	 * @return
	 */
	@RequiresPermissions("sys:user:roleView")
	@RequestMapping(value = "{userId}/userRole")
	public String getUserRole(@PathVariable("userId") Integer id, Model model) {
		model.addAttribute("userId", id);
		return "system/userRoleList";
	}
	/**
	 * 弹窗页-用户所在机构
	 *
	 * @param id
	 * @param model
	 * @return
	 */
	@RequiresPermissions("sys:user:orgView")
	@RequestMapping(value = "{userId}/userOrg")
	public String getUserOrg(@PathVariable("userId") Integer id, Model model) {
		model.addAttribute("userId", id);
		return "system/userOrgList";
	}

	/**
	 * 获取用户拥有的角色ID集合
	 *
	 * @param id
	 * @return
	 */
	@RequiresPermissions("sys:user:roleView")
	@RequestMapping(value = "{id}/role")
	@ResponseBody
	public List<Integer> getRoleIdList(@PathVariable("id") Integer id) {
		return userRoleService.getRoleIdList(id);
	}
	/**
	 * 获取用户拥有的机构ID集合
	 * @param id
	 * @return
	 */
	@RequiresPermissions("sys:user:orgView")
	@RequestMapping(value = "{id}/org")
	@ResponseBody
	public List<Integer> getOrgIdList(@PathVariable("id") Integer id) {
		return userOrgService.getOrgIdList(id);
	}

	/**
	 * 修改用户拥有的角色
	 *
	 * @param id
	 * @param newRoleList
	 * @return
	 */
	@RequiresPermissions("sys:user:roleUpd")
	@RequestMapping(value = "{id}/updateRole")
	@ResponseBody
	public String updateUserRole(@PathVariable("id") Integer id,@RequestBody List<Integer> newRoleList) {
		userRoleService.updateUserRole(id, userRoleService.getRoleIdList(id),newRoleList);
		return "success";
	}
	/**
	 * 修改用户所在的部门
	 *
	 * @param id
	 * @param newRoleList
	 * @return
	 */
	@RequiresPermissions("sys:user:orgUpd")
	@RequestMapping(value = "{id}/updateOrg")
	@ResponseBody
	public String updateUserOrg(@PathVariable("id") Integer id,@RequestBody List<Integer> newRoleList) {
		userOrgService.updateUserOrg(id,newRoleList);
		return "success";
	}

	/**
	 * 修改密码跳转
	 */
	@RequestMapping(value = "updatePwd", method = RequestMethod.GET)
	public String updatePwdForm(Model model, HttpSession session) {
		model.addAttribute("user", (User) session.getAttribute("user"));
		return "system/updatePwd";
	}

	/**
	 * 修改密码
	 */
	@RequestMapping(value = "updatePwd", method = RequestMethod.POST)
	@ResponseBody
	public String updatePwd(String oldPassword,@Valid @ModelAttribute @RequestBody User user, HttpSession session) {
		if (userService.checkPassword((User) session.getAttribute("user"),oldPassword)) {
			userService.updatePwd(user);
			session.setAttribute("user", user);
			return "success";
		} else {
			return "fail";
		}

	}

	/**
	 * Ajax请求校验loginName是否唯一。
	 */
	@RequestMapping(value = "checkLoginName")
	@ResponseBody
	public String checkLoginName(String loginName) {
		if (userService.getUser(loginName) == null) {
			return "true";
		} else {
			return "false";
		}
	}

	/**
	 * ajax请求校验原密码是否正确
	 *
	 * @param oldPassword
	 * @return
	 */
	//@RequiresPermissions("sys:user:update")
	@RequestMapping(value = "checkPwd")
	@ResponseBody
	public String checkPwd(String oldPassword, HttpSession session) {
		if (userService.checkPassword((User) session.getAttribute("user"),oldPassword)) {
			return "true";
		} else {
			return "false";
		}
	}

	/**
	 * 所有RequestMapping方法调用前的Model准备方法, 实现Struts2
	 * Preparable二次部分绑定的效果,先根据form的id从数据库查出Task对象,再把Form提交的内容绑定到该对象上。
	 * 因为仅update()方法的form中有id属性，因此仅在update时实际执行.
	 */
	@ModelAttribute
	public void getUser(@RequestParam(value = "id", defaultValue = "-1") Integer id,Model model) {
		if (id != -1) {
			model.addAttribute("user", userService.get(id));
		}
	}
	/**
	  * 方法描述: 根據工號查詢用戶信息
	  * @Author: S6113712
	  * @CreateDate:   2018/10/17  上午 09:51
	  * @Return
	  **/

    @ResponseBody
	@RequestMapping(value = "getUserInfo")
	public Object getUserInfo(@RequestParam("empno") String empno,HttpServletRequest request){
    	
//		if(entity!=null){
//			entity.setDeptname(serviceUtil.getUserFullDeptName(empno));
//		}else{
//			return false;
//		}
    	BsDptEntity entity=null;
		User user = userService.getUser(empno);
		if (user.getDelFlag()!=null){
		 entity = bsDptService.findById(user.getDelFlag());
		}
		UserDto ud = new UserDto();
		BeanUtil.copyProperties(entity,ud);
		BeanUtil.copyProperties(user,ud);

		return ud;
	}

}
