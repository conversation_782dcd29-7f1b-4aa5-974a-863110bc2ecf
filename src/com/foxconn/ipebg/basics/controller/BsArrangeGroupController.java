package com.foxconn.ipebg.basics.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Date;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.basics.entity.BsArrangeGroupEntity;
import com.foxconn.ipebg.basics.entity.BsTrainingPeriodEntity;
import com.foxconn.ipebg.basics.service.BsArrangeGroupPersonService;
import com.foxconn.ipebg.basics.service.BsArrangeGroupPostService;
import com.foxconn.ipebg.basics.service.BsArrangeGroupService;
import com.foxconn.ipebg.system.entity.User;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;


/**
 * 排班群組基本資料
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-09-11 10:28:20
 */
@Controller
@RequestMapping("bsarrangegroup")
public class BsArrangeGroupController extends BaseController {

    @Autowired
    private BsArrangeGroupService bsArrangeGroupService;
	@Autowired
	private UserServiceUtil serviceUtil;
	
    @Autowired
    private BsArrangeGroupPostService bsArrangeGroupPostService;
    
    @Autowired
    private BsArrangeGroupPersonService bsArrangeGroupPersonService;
    
   
    /**
      * 方法描述: 列表信息
      * @Author: S6113712
      * @CreateDate:   2019-09-11 10:28:20
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:bsarrangegroup:list")
    public String list() {
        //查询列表数据
        return "basics/bsarrangegroup/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6113712
      * @CreateDate:   2019-09-11 10:28:20
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:bsarrangegroup:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<BsArrangeGroupEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        User user = UserUtil.getCurrentUser();
        if (user != null) {
            // 按保安公司查询
            PropertyFilter filter = new PropertyFilter("INS_securityCom", user.getSecurityComs());
            filters.add(filter);
        }
//        page.setOrderBy("createDate");
//		page.setOrder("desc");
        page = bsArrangeGroupService.search(page, filters);
        return getEasyUIData(page);
    }
    /**
     * 方法描述: 保存
     * @Author: S6113712
     * @CreateDate:   2019-09-11 10:28:20
     * @Return
     **/
   
    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("basics:bsarrangegroup:add")
    @ResponseBody
    public String create(@Valid BsArrangeGroupEntity bsArrangeGroup, Model model) {
    	if (bsArrangeGroupService.isExistName(bsArrangeGroup)) {
  			return  "排班群組名稱已經存在！";
  		}
        bsArrangeGroup.setNewRecord(true);
        bsArrangeGroup.setArrangeGroupId(serviceUtil.createNo("GROUP"));
        bsArrangeGroup.setArrangeGroupName(bsArrangeGroup.getArrangeGroupName().trim());
    	bsArrangeGroup.setCreateDate(new Date());
        bsArrangeGroupService.save(bsArrangeGroup);
        return "success";
    }
    
    /**
      * 方法描述: 添加跳轉
      * @Author: S6113712
      * @CreateDate:   2019-09-11 10:28:20
      * @Return
      **/

    @RequiresPermissions("basics:bsarrangegroup:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("bsArrangeGroup", new BsArrangeGroupEntity());
        model.addAttribute("action", "create");
        return "basics/bsarrangegroup/listForm";
    }
    
     /**
     * 方法描述: 修改跳轉
     * @Author: S6113712
     * @CreateDate:   2018-10-06 11:04:39
     * @Return
     **/

   @RequiresPermissions("basics:bsarrangegroup:update")
   @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
   public String updateForm(@PathVariable("id") String id, Model model) {
       model.addAttribute("bsArrangeGroup", bsArrangeGroupService.get(id));
       model.addAttribute("action", "update");
      return "basics/bsarrangegroup/listForm";
   }
   
	/**
	 * 方法描述: 修改
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-11 10:55:00
	 * @Return
	 **/

   @RequestMapping(value = "update", method = RequestMethod.POST)
   @RequiresPermissions("basics:bsarrangegroup:update")
   @ResponseBody
   public String update(@Valid BsArrangeGroupEntity bsArrangeGroup,@RequestParam(value = "ids") String ids) {
	   bsArrangeGroup.setId(ids);
	   if (bsArrangeGroupService.isExistName(bsArrangeGroup)) {
			return  "排班群組名稱已經存在！";
	   }
	   
	   bsArrangeGroup.setId(ids);
	   User user = UserUtil.getCurrentUser();
	   bsArrangeGroup.setUpdateBy(user.getLoginName());
	   bsArrangeGroup.setUpdateDate(new Date());
	   bsArrangeGroupService.update(bsArrangeGroup);
	   
       return "success";
   }
    /**
      * 方法描述: 根據主鍵刪除
      * @Author: S6113712
      * @CreateDate:   2019-09-11 10:28:20
      * @Return
      **/

    @RequestMapping("delete/{id}")
    @RequiresPermissions("basics:bsarrangegroup:delete")
    @ResponseBody
	public String delete(@PathVariable("id") String id) {
		String idT = id.split(",")[0];
		String arrangeGroupId = id.split(",")[1];
		System.out.print(idT + arrangeGroupId);
		if (bsArrangeGroupPersonService.isExistPersonArrangeGroupId(arrangeGroupId)) {
			return  "排班群組已經與人員進行匹配不能刪除！";
		} else if (bsArrangeGroupPostService.isExistPostArrangeGroupId(arrangeGroupId)) {
			return   "排班群組已經與崗位進行匹配不能刪除！";
		} else {

			bsArrangeGroupService.delete(idT);
			return "success";
		}
	}
    /**
      * 导出excel
      * @Author: S6113712
      * @CreateDate:   2019-09-11 10:28:20
      * @Return
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {}
    /**
	  * 方法描述: 生成下拉數據 培訓期
	  * @Author: S6113712
	  * @CreateDate:   2018/10/15  上午 08:56
	  * @Return
	  **/

	@ResponseBody
	@RequestMapping(value = "getAllArrangeGroup")
	public List<BsArrangeGroupEntity> getAllArrangeGroup(Model model) {
	    List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
	    User user = UserUtil.getCurrentUser();
	    if (user != null) {
	        filters.add(new PropertyFilter("INS_securityCom", user.getSecurityComs()));
        }
        return bsArrangeGroupService.search(filters);
	}
}
