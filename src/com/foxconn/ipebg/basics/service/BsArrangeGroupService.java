package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.entity.BsArrangeGroupEntity;
import com.foxconn.ipebg.basics.entity.BsArrangeGroupPersonEntity;
import com.foxconn.ipebg.basics.entity.BsTraineeEntity;
import com.foxconn.ipebg.basics.entity.BsTrainingPeriodEntity;
import com.foxconn.ipebg.basics.dao.BsArrangeGroupDao;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.Dict;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WorkFlowEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.system.service.DictService;

import java.util.Date;
import java.util.List;

/**
 * 排班群組基本資料
 *
 * <AUTHOR>
 * @email <EMAIL> 
 * @date 2019-09-11 10:28:20
 */
@Service
@Transactional(readOnly=true)
public class  BsArrangeGroupService extends BaseBusinessService<BsArrangeGroupEntity, String>{
    @Autowired
    private BsArrangeGroupDao bsArrangeGroupDao;

    @Autowired
    private UserServiceUtil serviceUtil;
    @Autowired
    private DictService dictService;

    @Override
    public HibernateDao<BsArrangeGroupEntity, String> getEntityDao() {
        return bsArrangeGroupDao;
    }

    public BsArrangeGroupEntity findByArrangeGroupId(String arrangeGroupId) {
        return this.bsArrangeGroupDao.findUniqueBy("arrangeGroupId", arrangeGroupId);
    }

    /**
     * 方法描述: 生成下拉數據
     **/
	public List<BsArrangeGroupEntity> getAllArrangeGroup() {
     return this.bsArrangeGroupDao.findAll(false);
	}
	
	// 驗證排班名稱
	public boolean isExistName(BsArrangeGroupEntity bsArrangeGroupEntity) {
		boolean result = false;
		List<BsTraineeEntity> list = bsArrangeGroupDao
				.find("from BsArrangeGroupEntity t where t.arrangeGroupName=?0  and id !=?1 and securityCom = ?2",
					bsArrangeGroupEntity.getArrangeGroupName(), bsArrangeGroupEntity.getId() == null ? "888888" : bsArrangeGroupEntity.getId(),
					bsArrangeGroupEntity.getSecurityCom()
				);
		if (list.size() > 0) {
			result = true;
		} else {
			result = false;
		}
		return result;
	}
    
}
