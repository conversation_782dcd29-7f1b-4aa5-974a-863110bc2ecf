var dg;
var d;
$(function () {
    dg = $('#dg').datagrid({
        method: "get",
        url: ctx+'/safetyPermit/list',
        fit: true,
        fitColumns: true,
        border: false,
        idField: 'id',
        striped: true,
        pagination: true,
        rownumbers: true,
        pageNumber: 1,
        pageSize: 20,
        pageList: [10, 20, 30, 40, 50],
        singleSelect: true,
        columns: [[
            {field: 'id', title: '主鍵', hidden: true},
            {field: 'serialno', title: '任務編號', sortable: true, width: 120, formatter: operation},
            {field: 'dealno', title: '承辦人工號', sortable: true, width: 80},
            {field: 'dealname', title: '承辦人名稱', sortable: true, width: 80},
            {field: 'dealdeptno', title: '承辦人單位代碼', sortable: true, width: 80},
            {field: 'dealdeptname', title: '承辦人單位名稱', sortable: true, width: 240},
            {field: 'dealtime', title: '提報日期', sortable: true, width: 100,formatter:formatDate},
            {field: 'workstatus', title: '表單狀態', sortable: true, width: 60},
            {field: 'nodeName', title: '當前簽核節點', sortable: true, width: 80},
            {field: 'auditUser', title: '當前簽核人', sortable: true, width: 100},
            {field: 'complettime', title: '簽核完成時間', sortable: true, width: 100,formatter:formatDate}
        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        toolbar: '#tb'
    });
    $(":input").removeClass("validatebox-invalid");
    //獲取廠區
    $.get(ctx+'/tqhfactoryidconfig/allFactorys/',
        function (result) {
            $("#dealfactoryid").combobox({
                data: result,
                valueField: "factoryid",
                textField: "factoryname",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({factoryid: '', factoryname: '請選擇'});
                    return data;
                }
            });
        });

    //創建下拉查詢條件
    $.ajax({
        url: ctx+"/system/dict/getDictByType/audit_status",
        //dataType:"json",
        type: "GET",
        success: function (data) {
            //绑定第一个下拉框
            $("#qysjzt").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({value: '', label: '請選擇'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });

});

function operation(value, row, index) {
    return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('輻射安全許可證變更申請單詳情',ctx+'/safetyPermit/view/" + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
};

//创建查询对象并查询
function cx() {
    var obj = $("#searchFrom").serializeObject();
    dg.datagrid('load', obj);
}

//导出excel
function exportExcel() {
    var form = document.getElementById("searchFrom");
    searchFrom.action = ctx+'/safetyPermit/exportExcel';
    form.submit();
}

//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("reset");
}

function valdChkbox() {
    var obj = $("#dealemail")[0];
    return valdEmail(obj);

}
function saveInfo(flag) {
    var isValid = $("#mainform").form('validate');
    if (!isValid) {
        return;
    }
    if (!valdChkbox()) {
        return;
    }
    $.ajax({
        url: ctx+'/safetyPermit/create/' + flag,
        type: 'POST',
        beforeSend: ajaxLoading,
        //dataType: 'json',
        data: $('#mainform').serialize(),
        success: function (data) {
            ajaxLoadEnd();
            successTip(data, dg, d);
            window.parent.mainpage.mainTabs.closeCurrentTab();
        }
    });
}

//validatebox驗證擴展
$.extend($.fn.validatebox.defaults.rules, {
    endTime: {
        validator: function (value, param) {
            var startDate = $('#buildcyclebegindate').datebox('getValue');
            var startTmp = new Date(startDate.replace(/-/g, "/"));
            var endTmp = new Date(value.replace(/-/g, "/"));
            return startTmp <= endTmp;
        },
        message: '结束时间要大于开始时间！'
    }
});

//驗證申請人聯繫電話
function valdApplyTel(obj) {
    var phoneL = $.trim($("#dealtel").val()).split("").length;
    var tel = obj.value;
    if (tel != "") {
        var temp = tel;
        var regex = /^[0-9]{3}\+[0-9]{5}$/;
        var patt1 = new RegExp(regex);
        var result = patt1.test(tel);
        if (!result) {
            alert("分機格式有誤!");
            obj.value = "";
            obj.focus();
            return;
        }
    }
}

//驗證郵箱
function valdEmail(obj) {
    var email = obj.value;
    var temp = email;
    if (email == "") return;
    var regex = /\w+([-/+.]\w+)*@(mail.foxconn.com|foxconn.com|fii-foxconn.com)$/;
    var patt1 = new RegExp(regex);
    var result = patt1.test(email);
    if (!result) {
        alert("郵箱的格式不正確!");
        obj.value = "";
        //obj.focus();
        obj.value = temp;
        return;
    }
    ;
}

function delAtt(fileid) {
    $("#" + fileid).remove();
    var kk = $("#attachids").val();
    kk = kk.replace(fileid + ",", "");
    $("#attachids").val(kk);
    $.post(ctx+'/admin/delete/' + fileid, {}, function (data) {
        console.log(data);
        successTip(data);
    });
}

//根據工號查出當前申請人的信息
function queryUserInfo() {
    var empno = $.trim($("#dealno").val().toUpperCase());
    if (empno != null && empno != "") {
        $.ajax({
            url: ctx+'/system/user/getUserInfo/',
            type: 'POST',
            beforeSend: ajaxLoading,
            //dataType: 'json',
            data: {empno: empno},
            success: function (data) {
                ajaxLoadEnd();
                if (!data) {
                    $.messager.alert("溫馨提示", "工號不存在", "error");
                    $('#dealno').val('');
                    $('#dealname').val('');
                    $('#dealdeptno').val('');
                    $('#dealdeptname').val('');
                    $('#dealemail').val('');
                } else {
                    $('#dealname').val(data.empname);
                    $('#dealdeptno').val(data.deptno);
                    $('#dealdeptname').val(data.deptname);
                    $('#dealfactoryid').combobox('setValue', data.factoryid);
                    $('#dealemail').val(data.email);
                }
            }
        });
    }
}


//附件上傳
function uploadFile() {
    var attachIds = $.trim($("#attachids").val());
    var attachIdsL = attachIds.split(",").length;
    if (attachIdsL >= 6) {
        alert("上傳附件個數不能超過五個！");
        return;
    }
    // 创建
    var form_data = new FormData();
    // 获取文件
//        var file = document.getElementById('fileToUpload').files[0];
    var file_data = $("#attachidsUpload").prop("files")[0];
    form_data.append("file", file_data);
    form_data.append("path", "test");
    // 把所以表单信息
    $.ajax({
        type: "POST",
        url: ctx+"/admin/upload",
        beforeSend: ajaxLoading,
//            dataType : "json",
        processData: false,  // 注意：让jQuery不要处理数据
        contentType: false,  // 注意：让jQuery不要设置contentType
        data: form_data
    }).success(function (msg) {
        ajaxLoadEnd();
        $("#dowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href='+ctx+'"/admin/download/' + msg.id + '">' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delAtt(\'' + msg.id + '\')"></div></div>');
        $("#attachids").val($("#attachids").val() + msg.id + ",");
        // $("#attachids").val(msg.id);
    }).fail(function (msg) {
    });
}

function reUploadFile() {
    var attachIds = $.trim($("#reattachids").val());
    var attachIdsL = attachIds.split(",").length;
    if (attachIdsL >= 6) {
        $.messager.alert("溫馨提示", "上傳附件個數不能超過五個！", "info");
        return;
    }
    // 创建
    var form_data = new FormData();
    // 获取文件
//        var file = document.getElementById('fileToUpload').files[0];
    var file_data = $("#attachidsUpload").prop("files")[0];
    form_data.append("file", file_data);
    form_data.append("path", "test");
    // 把所以表单信息
    $.ajax({
        type: "POST",
        url: ctx+"/admin/upload",
        beforeSend: ajaxLoading,
//            dataType : "json",
        processData: false,  // 注意：让jQuery不要处理数据
        contentType: false,  // 注意：让jQuery不要设置contentType
        data: form_data
    }).success(function (msg) {
        ajaxLoadEnd();
        $("#reDowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href='+ctx+'"admin/download/' + msg.id + '">' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delReAtt(\'' + msg.id + '\')"></div></div>');
        $("#reattachids").val($("#reattachids").val() + msg.id + ",");
        // $("#attachids").val(msg.id);
    }).fail(function (msg) {
    });
}

function delReAtt(fileid) {
    $("#" + fileid).remove();
    var kk = $("#reattachids").val();
    kk = kk.replace(fileid + ",", "");
    $("#reattachids").val(kk);
    $.post(ctx+'/admin/delete/' + fileid, {}, function (data) {
        successTip(data);
    });
}