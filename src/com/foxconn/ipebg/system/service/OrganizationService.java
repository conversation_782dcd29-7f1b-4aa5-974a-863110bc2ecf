package com.foxconn.ipebg.system.service;

import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseService;
import com.foxconn.ipebg.system.dao.OrganizationDao;
import com.foxconn.ipebg.system.dao.UserOrgDao;
import com.foxconn.ipebg.system.entity.User;
import com.foxconn.ipebg.system.entity.UserOrg;
import com.foxconn.ipebg.system.utils.UserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foxconn.ipebg.system.entity.Organization;

import java.util.ArrayList;
import java.util.List;

/**
 * 区域service
 * <AUTHOR>
 * @date 2015年5月09日
 */
@Service
@Transactional(readOnly=true)
public class OrganizationService extends BaseService<Organization, Integer> {
	
	@Autowired
	private OrganizationDao organizationDao;
	@Autowired
	private UserOrgDao userOrgDao;
	
	@Override
	public HibernateDao<Organization, Integer> getEntityDao() {
		return organizationDao;
	}

	// 去重
        /*persons.stream().forEach(
			p -> {
		if (!personList.contains(p)) {
			personList.add(p);
		}
	}
        );*/
	public List<String> getUserAllDeptPermision(){
		List<Integer> userOrg = userOrgDao.findOrgIds(UserUtil.getCurrentUser().getId());
//		List<Integer> userOrg = userOrgDao.findOrgIds(1);
		List<Organization> resultList = new ArrayList<Organization>();
		List<String> returnList = new ArrayList<String>();
		for (Integer userO:userOrg){
			List<Organization> oList = organizationDao.findFatherOrganiza(userO);
			for (Organization organization : oList) {
				if(!resultList.contains(organization)){
					resultList.add(organization);
					returnList.add(organization.getOrgCode());
				}
			}
		}

		return returnList;
	}

}
