package com.foxconn.ipebg.basics.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Date;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.basics.entity.BsSignatureEntity;
import com.foxconn.ipebg.basics.service.BsSignatureService;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;


/**
 * 簽名檔
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-09-27 13:16:41
 */
@Controller
@RequestMapping("bssignature")
public class BsSignatureController extends BaseController {

    @Autowired
    private BsSignatureService bsSignatureService;
   
    /**
      * 方法描述: 列表信息
      * @Author: S6114894
      * @CreateDate:   2019-09-27 13:16:41
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:bssignature:list")
    public String list() {
        //查询列表数据
        return "basics/bssignature/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6114894
      * @CreateDate:   2019-09-27 13:16:41
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:bssignature:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<BsSignatureEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        page = bsSignatureService.search(page, filters);
        return getEasyUIData(page);
    }
    /**
     * 方法描述: 保存
     * @Author: S6114894
     * @CreateDate:   2019-09-27 13:16:41
     * @Return
     **/
   
    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("basics:bssignature:add")
    @ResponseBody
    public String create(@Valid BsSignatureEntity bsSignature, Model model) {

        BsSignatureEntity identity = bsSignatureService.findBySignatureNo(bsSignature.getSignatureNo());
        if(identity!=null){
            return  "工號已存在！";
        }

        bsSignature.setNewRecord(true);
    	bsSignature.setCreateDate(new Date());
        bsSignatureService.save(bsSignature);
        return "success";
    }
    
    /**
      * 方法描述: 添加跳轉
      * @Author: S6114894
      * @CreateDate:   2019-09-27 13:16:41
      * @Return
      **/

    @RequiresPermissions("basics:bssignature:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("bsSignature", new BsSignatureEntity());
        model.addAttribute("action", "create");
        return "basics/bssignature/listForm";
    }
    
     /**
     * 方法描述: 修改跳轉
     * @Author: S6113712
     * @CreateDate:   2018-10-06 11:04:39
     * @Return
     **/

   @RequiresPermissions("basics:bssignature:update")
   @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
   public String updateForm(@PathVariable("id") String id, Model model) {
       model.addAttribute("bsSignature", bsSignatureService.get(id));
       model.addAttribute("action", "update");
      return "basics/bssignature/listForm";
   }
   
	/**
	 * 方法描述: 修改
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-11 10:55:00
	 * @Return
	 **/

   @RequestMapping(value = "update", method = RequestMethod.POST)
   @RequiresPermissions("basics:bssignature:update")
   @ResponseBody
   public String update(@Valid BsSignatureEntity bsSignature,@RequestParam(value = "ids") String ids) {
	   bsSignature.setId(ids);
	   bsSignature.setUpdateDate(new Date());
	   bsSignatureService.update(bsSignature);
	   
       return "success";
   }
    /**
      * 方法描述: 根據主鍵刪除
      * @Author: S6114894
      * @CreateDate:   2019-09-27 13:16:41
      * @Return
      **/

    @RequestMapping("delete/{id}")
    @RequiresPermissions("basics:bssignature:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        bsSignatureService.delete(id);
        return "success";
    }
    /**
      * 导出excel
      * @Author: S6114894
      * @CreateDate:   2019-09-27 13:16:41
      * @Return
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {}
}
