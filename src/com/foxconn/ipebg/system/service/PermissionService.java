package com.foxconn.ipebg.system.service;

import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseService;
import com.foxconn.ipebg.common.utils.ListUtils;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.system.dao.PermissionDao;
import com.foxconn.ipebg.system.entity.Permission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 权限service
 * <AUTHOR>
 * @date 2015年1月13日
 */
@Service
@Transactional(readOnly=true)
public class PermissionService extends BaseService<Permission, Integer> {
	List<Map<String,Object>> comboTreeList = null;
	@Autowired
	private PermissionDao permissionDao;
	
	@Override
	public HibernateDao<Permission, Integer> getEntityDao() {
		return permissionDao;
	}
	
	/**
	 * 添加菜单基础操作
	 * @param pid 菜单id
	 */
	@Transactional(readOnly = false)
	public void addBaseOpe(Integer pid,String pClassName){
		List<Permission> pList=new ArrayList<Permission>();
		pList.add(new Permission(pid, "添加", "O", "", "sys:"+pClassName+":add"));
		pList.add(new Permission(pid, "删除", "O", "", "sys:"+pClassName+":delete"));
		pList.add(new Permission(pid, "修改", "O", "", "sys:"+pClassName+":update"));
		pList.add(new Permission(pid, "查看", "O", "", "sys:"+pClassName+":view"));
		
		//添加没有的基本操作
		List<Permission> existPList=getMenuOperation(pid);
		for(Permission permission:pList){
			boolean exist=false;
			for(Permission existPermission:existPList){
				if(permission.getPermCode().equals(existPermission.getPermCode())){
					exist=true;
					break;
				}else{
					exist=false;
				}
			}
			if (exist) {
				continue;
			}
			save(permission);
		}
	}
	
	/**
	 * 获取角色拥有的权限集合
	 * @param userId
	 * @return 结果集合
	 */
	public List<Permission> getPermissions(Integer userId){
		return permissionDao.findPermissions(userId);
	}
	
	/**
	 * 获取角色拥有的菜单
	 * @param userId
	 * @return 菜单集合
	 */
	public List<Map<String,Object>> getMenus(Integer userId,String pid){
		comboTreeList=new ArrayList<Map<String,Object>>();
		if(StringUtils.isEmpty(pid)){
			return this.createComboTreeTree(permissionDao.findMenus(userId),pid);
		}else{
			return this.createChildrenTree(permissionDao.findMenus(userId),pid);
		}
	}
	
	/**
	 * 获取所有菜单
	 * @return 菜单集合
	 */
	public List<Permission> getMenus(){
		return permissionDao.findMenus();
	}
	
	/**
	 * 获取菜单下的操作
	 * @param pid
	 * @return 操作集合
	 */
	public List<Permission> getMenuOperation(Integer pid){
		return permissionDao.findMenuOperation(pid);
	}


	private List<Map<String, Object>> createComboTreeChildren(List<Permission> list, Integer fid) {
		List<Map<String, Object>> childList = new ArrayList<Map<String, Object>>();
		for (int j = 0; j < list.size(); j++) {
			Map<String, Object> map = null;
			Permission treeChild = (Permission) list.get(j);
			if (treeChild.getPid()!=null&&treeChild.getPid().equals(fid)) {
				map = new HashMap<String, Object>();
				//这里必须要将对象角色的id、name转换成ComboTree在页面的显示形式id、text
				//ComboTree,不是数据表格，没有在页面通过columns转换数据的属性
				map.put("id", list.get(j).getId());
				map.put("text", list.get(j).getName());
				map.put("iconCls",list.get(j).getIcon());
				map.put("attributes",list.get(j));
				map.put("children", createComboTreeChildren(list, treeChild.getId()));
			}

			if (map != null) {
				childList.add(map);
			}
		}
		return childList;
	}

	private List<Map<String,Object>> createComboTreeTree(List<Permission> list, String fid) {
		for (int i = 0; i < list.size(); i++) {
			Map<String, Object> map = null;
			Permission role = (Permission) list.get(i);
			if (role.getPid()==null) {
				map = new HashMap<String, Object>();
				//这里必须要将对象角色的id、name转换成ComboTree在页面的显示形式id、text
				//ComboTree,不是数据表格，没有在页面通过columns转换数据的属性
				map.put("id", list.get(i).getId());         //id
				map.put("text",list.get(i).getName());      //角色名
				map.put("iconCls",list.get(i).getIcon());      //角色名
				map.put("attributes",list.get(i));
//				map.put("children", createComboTreeChildren(list, role.getId()));
			}
			if (map != null) {
				comboTreeList.add(map);
			}
		}
//		ListUtils.removeDuplicateWithOrder(comboTreeList);
		return comboTreeList;
	}

	private List<Map<String,Object>> createChildrenTree(List<Permission> list, String fid) {
		for (int i = 0; i < list.size(); i++) {
			Permission role = (Permission) list.get(i);
			if (role.getId().equals(Integer.parseInt(fid))) {
				//这里必须要将对象角色的id、name转换成ComboTree在页面的显示形式id、text
				//ComboTree,不是数据表格，没有在页面通过columns转换数据的属性
				comboTreeList = createComboTreeChildren(list, role.getId());
			}
		}
		return comboTreeList;
	}
}
