<!DOCTYPE HTML>
<html>
<head>
<meta charset="UTF-8">
<title>demo</title>
</head>

<body>
    <h1>演示</h1>
    <nav><a href="../index.html">简洁语法演示</a> | js 原生语法演示</nav>
    <ul>
        <li><a href="basic.html">基本例子</a></li>
        <li><a href="no-escape.html">不转义HTML</a></li>
        <li><a href="compile.html">在javascript中存放模板</a></li>
        <li><a href="include.html">嵌入子模板(include)</a></li>
        <li><a href="helper.html">访问外部公用函数(辅助方法)</a></li>
        <li><a href="debug.html">错误调试</a></li>
        <li><a href="print.html">print方法</a></li>
        <li><a href="tag.html">自定义界定符</a></li>
    </ul>
</body>
</html>
