package com.foxconn.ipebg.common.utils;

/**
 * 枚举定义异常类型以及相对于的错误信息
 * 有助于返回码的唯一性
 * Created by CatalpaFlat
 * on 2017/4/12.
 */
public enum ResultEnum {
    UNKONW_ERROR(-1,"未知错误"),
    SUCCESS(200,"請求成功"),
    FAIL(400,"請求失敗"),
    TEST_ERRORR(30011,"测试异常"),
    SYS_ECEPTION(30010,"系統異常");
    private Integer code;
    private String msg;
    ResultEnum(Integer code,String msg){
        this.code = code;
        this.msg = msg;
    }
    public Integer getCode() {
        return code;
    }
    public String getMsg() {
        return msg;
    }
}
