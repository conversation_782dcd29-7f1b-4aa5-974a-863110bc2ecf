<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>新增飲用水/蓄水池（箱）點位審批表</title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script
		src='${ctx}/static/js/audit/epointaply.js?random=<%= Math.random()%>'></script>
</head>
<body>
	<form id="mainform" action="${ctx}/epointaply/${action}" method="post">
		<input id="ids" name="ids" type="hidden" value="${ePointAply.id }" />
		<input id="serialno" name="ePointAplyEntity.serialno" type="hidden"
			value="${ePointAply.serialno }" /> <input id="createtime"
			name="ePointAplyEntity.createtime" type="hidden"
			value="${ePointAply.createtime }" />
		<div class="commonW">
			<div class="headTitle">新增飲用水/蓄水池（箱）點位審批表</div>
			<div class="position_L">
				任務編碼：<span id="" style="color:#999;"> <c:choose>
						<c:when test="${ePointAply.serialno==null}">
                     提交成功后自動編碼
                 </c:when>
						<c:otherwise>
                     ${ePointAply.serialno}
                 </c:otherwise>
					</c:choose>
				</span>
			</div>
			<div class="position_L1 margin_L">
				填單時間：<span style="color:#999;"> <c:choose>
						<c:when test="${ePointAply.createtime==null}">
                    YYYY/MM/DD
                </c:when>
						<c:otherwise>
                    ${ePointAply.createtime}
                </c:otherwise>
					</c:choose>
				</span>
			</div>
			<div class="position_R margin_R">
				填單人：${user.loginName}/${user.name}</div>
			<div class="clear"></div>
			<table class="formList" id="buildprojecttable">
				<tr>
					<td>
						<table class="formList">
							<tr>
								<td colspan="10" class="td_style1">承辦人詳細信息</td>
							</tr>
							<tr align="center">
								<td width="6%">承辦人工號&nbsp;<font color="red">*</font></td>
								<td width="14%" class="td_style1"><input id="dealno"
									name="ePointAplyEntity.dealno" class="easyui-validatebox"
									readonly data-options="width: 80,required:true"
									value="${ePointAply.dealno }" onblur="queryUserInfo(this);" />
								</td>
								<td width="6%">承辦人</td>
								<td width="14%" class="td_style1"><input id="dealname"
									name="ePointAplyEntity.dealname" class="easyui-validatebox"
									readonly data-options="width:80" readonly
									value="${ePointAply.dealname }" /></td>
								<td width="6%">事業群</td>
								<td width="14%" class="td_style1"><input id="dptQun"
									name="ePointAplyEntity.dptQun" class="easyui-validatebox"
									readonly data-options="width: 90" value="${ePointAply.dptQun }" /></td>
								<td width="6%">事業處</td>
								<td width="14%" class="td_style1"><input id="dptChu"
									name="ePointAplyEntity.dptChu" class="easyui-validatebox"
									readonly data-options="width: 90" value="${ePointAply.dptChu }" /></td>
								<td width="6%">部門</td>
								<td width="14%" class="td_style1"><input id="dptBu"
									name="ePointAplyEntity.dptBu" class="easyui-validatebox "
									readonly data-options="width: 90" value="${ePointAply.dptBu }" /></td>

							</tr>
							<tr align="center">
								<td>單位</td>
								<td colspan="1" class="td_style1"><input id="dptKe"
									name="ePointAplyEntity.dptKe" class="easyui-validatebox"
									readonly data-options="width: 90" value="${ePointAply.dptKe }" /></td>
								<td>聯繫郵箱&nbsp;<font color="red">*</font></td>
								<td colspan="3" class="td_style1"><input id="dealemail"
									name="ePointAplyEntity.dealemail" class="easyui-validatebox"
									value="${ePointAply.dealemail }" style="width:120px;" readonly
									data-options="required:true" onblur="valdEmail(this)" /></td>
								<td>聯繫分機&nbsp;<font color="red">*</font></td>
								<td class="td_style1"><input id="dealtel"
									name="ePointAplyEntity.dealtel" class="easyui-validatebox"
									style="width:90px;" value="${ePointAply.dealtel }" readonly
									data-options="required:true,prompt:'579+66666'"
									onblur="valdApplyTel(this)" /></td>
								<td>管理人</td>
								<td colspan="2" class="td_style1"><input id="manager"
									name="ePointAplyEntity.manager" class="easyui-validatebox"
									readonly data-options="width: 90"
									value="${ePointAply.manager }" /></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table class="formList">
							<tr align="center">
								<td>申請點位類別&nbsp;<font color="red">*</font></td>
								<td colspan="8" class="td_style1"><input type="radio"
									id="auditType" name="ePointAplyEntity.auditType" value="0"
									checked="checked" />新啟用 <input type="radio" id="auditType"
									name="ePointAplyEntity.auditType" value="1" />重新啟用</td>
							</tr>

							<tr>
								<td colspan="10" class="td_style1">新增飲用水點位詳細信息</td>
							</tr>
							<tr align="center">
								<td>申請點位類別&nbsp;<font color="red">*</font></td>
								<td colspan="8" class="td_style1"><input type="radio"
									id="pointLocationType"
									name="ePointAplyEntity.pointLocationType" value="1"
									checked="checked" />直飲水 <input type="radio"
									id="pointLocationType"
									name="ePointAplyEntity.pointLocationType" value="2" />自建飲用水系統
									<input type="radio" id="pointLocationType"
									name="ePointAplyEntity.pointLocationType" value="3" />二次供水池/箱
									<input type="radio" id="pointLocationType"
									name="ePointAplyEntity.pointLocationType" value="4" />飲用水轉運水罐
									<input type="radio" id="pointLocationType"
									name="ePointAplyEntity.pointLocationType" value="5" />其他</td>
							</tr>

							<tr align="center">
								<td colspan="10" width="100%">
									<div style="overflow-x:auto;width: 1200px">
										<table id="buildprojectApplyItemTable" width="100%">
											<thead>
												<tr>

													<th>序號<font color="red">*</font></th>
													<!-- <th>系統編號<font color="red">*</font></th> -->
													<th>棟<font color="red">*</font></th>
													<th>層<font color="red">*</font></th>
													<th>方位<font color="red">*</font></th>
													<th>設備序號<font color="red">*</font></th>
													<th>安裝位置<font color="red">*</font></th>
												</tr>
											</thead>

											<tbody>
											<%-- 	<c:if test="${itemEntity!=null&&itemEntity.size()>0}">
													<c:forEach items="${itemEntity}" var="item"
														varStatus="status">
														<tr align="center" id="1">
															<td>${status.index+1}</td>
															<td><input id="building${status.index}"
																name="pointItemEntity[${status.index}].building"
																class="easyui-validatebox" readonly
																data-options="required:true" style="width:150px;"
																value="${item.building}" /></td>
															<td><input id="floor${status.index}"
																name="pointItemEntity[${status.index}].floor"
																class="easyui-validatebox" readonly
																data-options="required:true" style="width:150px;"
																value="${item.floor}" /></td>
															<td><input id="location${status.index}"
																name="pointItemEntity[${status.index}].location"
																class="easyui-validatebox" readonly
																data-options="required:true" style="width:150px;"
																value="${item.location}" /></td>


															<td><input id="equipmentNumber${status.index}"
																name="pointItemEntity[${status.index}].equipmentNumber"
																class="easyui-validatebox" readonly
																data-options="required:true" style="width:150px;"
																value="${item.equipmentNumber}" /></td>
															<td><input id="installLocation${status.index}"
																name="pointItemEntity[${status.index}].installLocation"
																class="easyui-validatebox" readonly
																data-options="required:true" style="width:150px;"
																value="${item.installLocation}" /></td>



														</tr>
													</c:forEach>
												</c:if> --%>
											<%-- 	<c:if test="${itemEntity==null}">
													<tr align="center" id="1">
														<td>1</td>
														<td><input id="building1"
															name="pointItemEntity[0].building"
															class="easyui-validatebox" readonly
															data-options="required:true" style="width:150px;"
															value="" /></td>
														<td><input id="floor1"
															name="pointItemEntity[0].floor"
															class="easyui-validatebox" readonly
															data-options="required:true" style="width:150px;"
															value="" /></td>
														<td><input id="location1"
															name="pointItemEntity[0].location"
															class="easyui-validatebox" readonly
															data-options="required:true" style="width:150px;"
															value="" /></td>

														<td><input id="equipmentNumber1"
															name="pointItemEntity[0].equipmentNumber"
															class="easyui-validatebox" readonly
															data-options="required:true" style="width:150px;"
															value="" /></td>
														<td><input id="installLocation1"
															name="pointItemEntity[0].installLocation"
															class="easyui-validatebox" readonly
															data-options="required:true" style="width:150px;;"
															value="" /></td>



													</tr>
												</c:if> --%>


											</tbody>
										</table>
									</div>
								</td>
							</tr>


							<tr>
								<th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄

								</th>
							</tr>
							<tr>
								<td colspan="10" style="text-align:left;">
									${chargeNodeInfo}</td>
							</tr>

							<tr>
								<td colspan="10" style="text-align:left;"><iframe
										id="qianheLogFrame" name="qianheLogFrame"
										src="${ctx}/eformsign/goChargeLog?serialNo=${ePointAply.serialno}"
										width="100%"></iframe></td>
							</tr>
							 <tr>
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnClose" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnPrint" class="easyui-linkbutton" data-options="iconCls:'icon-print'"
                                   style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>

                            </td>
                        </tr>
						</table>
					</td>
				</tr>
			</table>
		</div>
	</form>
	</div>
	
</body>
</html>