package com.foxconn.ipebg.report.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 班別資料
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-03-07 09:57:22
 */
@Entity
@Table(name = "TEMP_WORKDETAIL_DETAIL")
@DynamicUpdate
@DynamicInsert
public class WorkDetailEntity extends DataEntity<WorkDetailEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    
    		private String empNo;
    		private String empName;
    		private String postType;
    		private String tempService;
                                    //公司
            private String colL1;
                                //崗
            private String colL2;
                                //崗位
            private String colL3;
                                //編制人數
            private String colL4;
            					//應到人數-白班
            private String colL5;
						//應到人數-夜班
			private String colL6;
			//應到人數-合計
			private String colL7;
			//實到人數-白班
			private String colL8;
			//實到人數-夜班
			private String colL9;
			//實到人數-合計
			private String colL10;
			//調休人數
			private String colL11;
			//缺勤人數
			private String colL12;
			//缺勤比率
			private String colL13;
			//出勤比率
			private String colL14;
			
			private String colL15;
            //崗
			private String colL16;
			            //崗位
			private String colL17;
			            //編制人數
			private String colL18;
						//應到人數-白班
			private String colL19;
				//應到人數-夜班
			private String colL20;
			//應到人數-合計
			private String colL21;
			//實到人數-白班
			private String colL22;
			//實到人數-夜班
			private String colL23;
			//實到人數-合計
			private String colL24;
			//調休人數
			private String colL25;
			//缺勤人數
			private String colL26;
			//缺勤比率
			private String colL27;
			//出勤比率
			private String colL28;
			//出勤比率
			private String colL29;
			//出勤比率
			private String colL30;
			//出勤比率
			private String colL31;
			
			
			@Column(name = "L1", nullable = false, length = 200)
			public String getColL1() {
				return colL1;
			}
			/**
			 * @param colL1 the colL1 to set
			 */
			public void setColL1(String colL1) {
				this.colL1 = colL1;
			}
			
			@Column(name = "L2", nullable = false, length = 200)
			public String getColL2() {
				return colL2;
			}
			/**
			 * @param colL2 the colL2 to set
			 */
			public void setColL2(String colL2) {
				this.colL2 = colL2;
			}
			
			@Column(name = "L3", nullable = false, length = 200)
			public String getColL3() {
				return colL3;
			}
			/**
			 * @param colL3 the colL3 to set
			 */
			public void setColL3(String colL3) {
				this.colL3 = colL3;
			}
			
			@Column(name = "L4", nullable = false, length = 200)
			public String getColL4() {
				return colL4;
			}
			/**
			 * @param colL4 the colL4 to set
			 */
			public void setColL4(String colL4) {
				this.colL4 = colL4;
			}
			
			@Column(name = "L5", nullable = false, length = 200)
			public String getColL5() {
				return colL5;
			}
			/**
			 * @param colL5 the colL5 to set
			 */
			public void setColL5(String colL5) {
				this.colL5 = colL5;
			}
			
			@Column(name = "L6", nullable = false, length = 200)
			public String getColL6() {
				return colL6;
			}
			/**
			 * @param colL6 the colL6 to set
			 */
			public void setColL6(String colL6) {
				this.colL6 = colL6;
			}
			
			@Column(name = "L7", nullable = false, length = 200)
			public String getColL7() {
				return colL7;
			}
			/**
			 * @param colL7 the colL7 to set
			 */
			public void setColL7(String colL7) {
				this.colL7 = colL7;
			}

			@Column(name = "L8", nullable = false, length = 200)
			public String getColL8() {
				return colL8;
			}
			/**
			 * @param colL8 the colL8 to set
			 */
			public void setColL8(String colL8) {
				this.colL8 = colL8;
			}
			
			@Column(name = "L9", nullable = false, length = 200)
			public String getColL9() {
				return colL9;
			}
			/**
			 * @param colL9 the colL9 to set
			 */
			public void setColL9(String colL9) {
				this.colL9 = colL9;
			}
			
			@Column(name = "L10", nullable = false, length = 200)
			public String getColL10() {
				return colL10;
			}
			/**
			 * @param colL10 the colL10 to set
			 */
			public void setColL10(String colL10) {
				this.colL10 = colL10;
			}
			
			@Column(name = "L11", nullable = false, length = 200)
			public String getColL11() {
				return colL11;
			}
			/**
			 * @param colL11 the colL11 to set
			 */
			public void setColL11(String colL11) {
				this.colL11 = colL11;
			}
			
			@Column(name = "L12", nullable = false, length = 200)
			public String getColL12() {
				return colL12;
			}
			/**
			 * @param colL12 the colL12 to set
			 */
			public void setColL12(String colL12) {
				this.colL12 = colL12;
			}
			
			@Column(name = "L13", nullable = false, length = 200)
			public String getColL13() {
				return colL13;
			}
			/**
			 * @param colL13 the colL13 to set
			 */
			public void setColL13(String colL13) {
				this.colL13 = colL13;
			}
			
			@Column(name = "L14", nullable = false, length = 200)
			public String getColL14() {
				return colL14;
			}
			/**
			 * @param colL14 the colL14 to set
			 */
			public void setColL14(String colL14) {
				this.colL14 = colL14;
			}
			@Column(name = "EMP_NO", nullable = false, length = 200)
			public String getEmpNo() {
				return empNo;
			}
			/**
			 * @param empNo the empNo to set
			 */
			public void setEmpNo(String empNo) {
				this.empNo = empNo;
			}
			@Column(name = "EMP_NAM", nullable = false, length = 200)
			public String getEmpName() {
				return empName;
			}
			/**
			 * @param empName the empName to set
			 */
			public void setEmpName(String empName) {
				this.empName = empName;
			}
			@Column(name = "POST_TYPE", nullable = false, length = 200)
			public String getPostType() {
				return postType;
			}
			/**
			 * @param postType the postType to set
			 */
			public void setPostType(String postType) {
				this.postType = postType;
			}
			@Column(name = "TEMP_SERVICE", nullable = false, length = 200)
			public String getTempService() {
				return tempService;
			}
			/**
			 * @param tempService the tempService to set
			 */
			public void setTempService(String tempService) {
				this.tempService = tempService;
			}
			@Column(name = "L15", nullable = false, length = 200)
			public String getColL15() {
				return colL15;
			}
			/**
			 * @param colL15 the colL15 to set
			 */
			public void setColL15(String colL15) {
				this.colL15 = colL15;
			}
			@Column(name = "L16", nullable = false, length = 200)
			public String getColL16() {
				return colL16;
			}
			/**
			 * @param colL16 the colL16 to set
			 */
			public void setColL16(String colL16) {
				this.colL16 = colL16;
			}
			@Column(name = "L17", nullable = false, length = 200)
			public String getColL17() {
				return colL17;
			}
			/**
			 * @param colL17 the colL17 to set
			 */
			public void setColL17(String colL17) {
				this.colL17 = colL17;
			}
			@Column(name = "L18", nullable = false, length = 200)
			public String getColL18() {
				return colL18;
			}
			/**
			 * @param colL18 the colL18 to set
			 */
			public void setColL18(String colL18) {
				this.colL18 = colL18;
			}
			@Column(name = "L19", nullable = false, length = 200)
			public String getColL19() {
				return colL19;
			}
			/**
			 * @param colL19 the colL19 to set
			 */
			public void setColL19(String colL19) {
				this.colL19 = colL19;
			}
			@Column(name = "L20", nullable = false, length = 200)
			public String getColL20() {
				return colL20;
			}
			/**
			 * @param colL20 the colL20 to set
			 */
			public void setColL20(String colL20) {
				this.colL20 = colL20;
			}
			@Column(name = "L21", nullable = false, length = 200)
			public String getColL21() {
				return colL21;
			}
			/**
			 * @param colL21 the colL21 to set
			 */
			public void setColL21(String colL21) {
				this.colL21 = colL21;
			}
			@Column(name = "L22", nullable = false, length = 200)
			public String getColL22() {
				return colL22;
			}
			/**
			 * @param colL22 the colL22 to set
			 */
			public void setColL22(String colL22) {
				this.colL22 = colL22;
			}
			@Column(name = "L23", nullable = false, length = 200)
			public String getColL23() {
				return colL23;
			}
			/**
			 * @param colL23 the colL23 to set
			 */
			public void setColL23(String colL23) {
				this.colL23 = colL23;
			}
			@Column(name = "L24", nullable = false, length = 200)
			public String getColL24() {
				return colL24;
			}
			/**
			 * @param colL24 the colL24 to set
			 */
			public void setColL24(String colL24) {
				this.colL24 = colL24;
			}
			@Column(name = "L25", nullable = false, length = 200)
			public String getColL25() {
				return colL25;
			}
			/**
			 * @param colL25 the colL25 to set
			 */
			public void setColL25(String colL25) {
				this.colL25 = colL25;
			}
			@Column(name = "L26", nullable = false, length = 200)
			public String getColL26() {
				return colL26;
			}
			/**
			 * @param colL26 the colL26 to set
			 */
			public void setColL26(String colL26) {
				this.colL26 = colL26;
			}
			@Column(name = "L27", nullable = false, length = 200)
			public String getColL27() {
				return colL27;
			}
			/**
			 * @param colL27 the colL27 to set
			 */
			public void setColL27(String colL27) {
				this.colL27 = colL27;
			}
			@Column(name = "L28", nullable = false, length = 200)
			public String getColL28() {
				return colL28;
			}
			/**
			 * @param colL28 the colL28 to set
			 */
			public void setColL28(String colL28) {
				this.colL28 = colL28;
			}
			@Column(name = "L29", nullable = false, length = 200)
			public String getColL29() {
				return colL29;
			}
			/**
			 * @param colL29 the colL29 to set
			 */
			public void setColL29(String colL29) {
				this.colL29 = colL29;
			}
			@Column(name = "L30", nullable = false, length = 200)
			public String getColL30() {
				return colL30;
			}
			/**
			 * @param colL30 the colL30 to set
			 */
			public void setColL30(String colL30) {
				this.colL30 = colL30;
			}
			@Column(name = "L31", nullable = false, length = 200)
			public String getColL31() {
				return colL31;
			}
			/**
			 * @param colL31 the colL31 to set
			 */
			public void setColL31(String colL31) {
				this.colL31 = colL31;
			}
			
			
                                                                           
            
			
}
