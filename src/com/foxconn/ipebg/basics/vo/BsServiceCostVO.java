package com.foxconn.ipebg.basics.vo;

import com.foxconn.ipebg.basics.entity.BsServiceCostEntity;
import com.foxconn.ipebg.basics.entity.BsServiceCostTbEntity;
import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;


/**
 * 警衛服務費用結賬明細主表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-10-07 13:15:24
 */
public class BsServiceCostVO {
    //費用主表
    private BsServiceCostEntity costEntity;
    //白班
    private BsServiceCostTbEntity dayTbEntity;
    //夜班
    private BsServiceCostTbEntity nightTbEntity;

    public BsServiceCostEntity getCostEntity() {
        return costEntity;
    }

    public void setCostEntity(BsServiceCostEntity costEntity) {
        this.costEntity = costEntity;
    }

    public BsServiceCostTbEntity getDayTbEntity() {
        if (dayTbEntity != null) {
            return dayTbEntity;
        } else {
            return new BsServiceCostTbEntity();
        }
    }

    public void setDayTbEntity(BsServiceCostTbEntity dayTbEntity) {
        this.dayTbEntity = dayTbEntity;
    }

    public BsServiceCostTbEntity getNightTbEntity() {
        if (nightTbEntity != null) {
            return nightTbEntity;
        } else {
            return new BsServiceCostTbEntity();
        }
    }

    public void setNightTbEntity(BsServiceCostTbEntity nightTbEntity) {
        this.nightTbEntity = nightTbEntity;
    }
}
