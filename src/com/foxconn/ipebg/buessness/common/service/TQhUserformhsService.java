package com.foxconn.ipebg.buessness.common.service;

import com.foxconn.ipebg.buessness.common.entity.TQhUserformhsEntity;
import com.foxconn.ipebg.buessness.common.dao.TQhUserformhsDao;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 人員基本資料MHS
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-08 09:28:37
 */
@Service
@Transactional(readOnly=true)
public class  TQhUserformhsService extends BaseBusinessService<TQhUserformhsEntity, String>{
    @Autowired
    private TQhUserformhsDao tQhUserformhsDao;
    @Override
    public HibernateDao<TQhUserformhsEntity, String> getEntityDao() {
        return tQhUserformhsDao;
    }

    public TQhUserformhsEntity findByEmpno(String empNo){
        return tQhUserformhsDao.findUniqueBy("empno",empNo);
    }
}
