package com.foxconn.ipebg.buessness.workflow.entity;

import org.json.JSONObject;

import java.io.Serializable;
public class MyTaskCount implements Serializable {
	private static final long serialVersionUID = 1L;
	private String wfName;
	private int taskCount;
	private String workflowId;
	private String flag;
	@Override
	public String toString() {
		return new JSONObject(this).toString();
	}
	public String getWfName() {
		return wfName;
	}
	public void setWfName(String wfName) {
		this.wfName = wfName;
	}
	public int getTaskCount() {
		return taskCount;
	}
	public void setTaskCount(int taskCount) {
		this.taskCount = taskCount;
	}
	public String getWorkflowId() {
		return workflowId;
	}
	public void setWorkflowId(String workflowId) {
		this.workflowId = workflowId;
	}

	public String getFlag() {
		return flag;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}
}
