package com.foxconn.ipebg.system.web;

import com.foxconn.ipebg.basics.service.ESignUserinfoService;

import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.system.entity.AuditConnonUser;
import com.foxconn.ipebg.system.entity.AuditHqUser;
import com.foxconn.ipebg.system.entity.TQhChargepathEntity;
import com.foxconn.ipebg.system.entity.User;
import com.foxconn.ipebg.system.service.TQhChargepathService;
import com.foxconn.ipebg.system.service.TQhUserdutyService;
import com.foxconn.ipebg.system.service.UserOrgService;
import com.foxconn.ipebg.system.service.UserService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.xml.soap.SAAJResult;
import java.util.List;

/**
 * Company foxconn Copyright (C) 2004-2018 All Rights Reserved.
 */
@Controller
@RequestMapping("system/auditingNode")
public class AuditingNodeController extends BaseController {
	@Autowired
	private UserOrgService userOrgService;
	@Autowired
	private UserService userService;
	@Autowired
	private TQhChargepathService chargepathService;
	@Autowired
	private TQhUserdutyService userdutyService;

	@Autowired
	private ESignUserinfoService eSignUserinfoService;

	/**
	 * 方法描述: 獲取審核節點信息
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018/10/11 下午 01:39
	 * @Return
	 **/
	@RequestMapping(value = "userList", method = RequestMethod.GET)
	@ResponseBody
	public List<User> getAuditUserList(String deptNo) {
		List<Integer> ids = userOrgService.getUserListByDeptNO(deptNo);
		List<User> userList = userService.getUserInfos(ids);
		return userList;
	}

	/**
	 * 方法描述: 獲取審核節點信息
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019/01/01 下午 01:39
	 * @Return
	 **/
	@RequestMapping(value = "getAuditCommonUsers", method = RequestMethod.GET)
	@ResponseBody
	// public List<AuditConnonUser> getAuditHqUsers(@RequestParam("chargeno")
	// String chargeno,
	// @RequestParam("chargename") String chargename,
	// TQhChargepathEntity entity) {
	// List<AuditConnonUser> userList =
	// chargepathService.findAuditUser(chargeno,chargename,entity);
	// return userList;
	// }
	public List<AuditConnonUser> getAuditHqUsers(
			@RequestParam("eserTyp") String eserTyp,
			@RequestParam("eserNodeName") String eserNodeName) {
		List<AuditConnonUser> userList = eSignUserinfoService.findAuditUser(
				eserTyp, eserNodeName);
		return userList;
	}

	/**
	 * 方法描述: 獲取審核節點信息
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018/10/11 下午 01:39
	 * @Return
	 **/
	@RequestMapping(value = "getAuditHqUsers", method = RequestMethod.GET)
	@ResponseBody
	public List<AuditHqUser> getAuditCommonUsers(
			@RequestParam("dutyid") String dutyid,
			@RequestParam("factoryid") String factoryid) {
		List<AuditHqUser> userList = userdutyService.findAuditUser(dutyid,
				factoryid);
		return userList;
	}

	/**
	 * 方法描述: 添加跳轉
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018-10-11 18:34:27
	 * @Return
	 **/

	@RequestMapping(value = "roleList", method = RequestMethod.GET)
	public String createForm(Model model) {
		return "common/roleCommonList";
	}

	/**
	 * 方法描述: 添加跳轉
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018-10-11 18:34:27
	 * @Return
	 **/

	@RequestMapping(value = "roleList3", method = RequestMethod.GET)
	public String createForm3(Model model) {
		return "common/roleCommonHqList";
	}

	/**
	 * 方法描述: 添加跳轉
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018-10-11 18:34:27
	 * @Return
	 **/

	@RequestMapping(value = "roleList2", method = RequestMethod.GET)
	public String createForm2(Model model) {
		return "common/roleHqList";
	}

	/**
	 * 方法描述: 添加跳轉
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018-10-11 18:34:27
	 * @Return
	 **/

	@RequestMapping(value = "roleList4", method = RequestMethod.GET)
	public String createForm4(Model model) {
		return "common/roleHqListCommon";
	}

}
