<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>培訓期基本資料</title>
<script type="text/javascript"> 
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
        <form id="searchFrom" action="">
		<input type="text" id="filter_EQS_trainingPeriodNo" name="filter_EQS_trainingPeriodNo" class="easyui-validatebox"
			   data-options="width:150,prompt: '培訓期名稱'"/>
		<input id="filter_EQS_securityCom" name="filter_EQS_securityCom" class="easyui-combobox"
		 	data-options="url: '${ctx}/system/user/userSecurityCom',valueField:'value', textField:'label',width: 150,prompt: '保安公司'"/>
	<!-- 	<input type="text" name="filter_EQS_trainingPeriodName" class="easyui-validatebox"
			   data-options="width:150,prompt: '培訓期名稱 '"/> -->
		<input type="text" name="filter_GED_startDate" class="easyui-my97" datefmt="yyyy-MM-dd"
			   data-options="width:150,prompt: '培訓開始時間 '"/>
		- <input type="text" name="filter_LED_endDate" class="easyui-my97" datefmt="yyyy-MM-dd"
				 data-options="width:150,prompt: '培訓結束時間 '"/>
	
	<!-- 	<input id="qysjzt" style="width:100px" class="easyui-validatebox" name="filter_EQS_workstatus"/> -->
		<span class="toolbar-item dialog-tool-separator"></span>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
		   onclick="listSearchReset()">重置</a>
	<!-- 	<a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel"
		   onclick="exportExcel()">导出Excel</a> -->
		   <br/>
		   	<shiro:hasPermission name="basics:bstrainingperiod:add">
	       		<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="add();">添加</a>
	       		<span class="toolbar-item dialog-tool-separator"></span>
	       	</shiro:hasPermission>
           <%--  <shiro:hasPermission name="basics:bstrainingperiod:delete">
	            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true" data-options="disabled:false" onclick="del()">删除</a>
	        	<span class="toolbar-item dialog-tool-separator"></span>
	        </shiro:hasPermission> --%>
            <shiro:hasPermission name="basics:bstrainingperiod:update">
	            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true" onclick="upd()">修改</a>
	            <span class="toolbar-item dialog-tool-separator"></span>
	        </shiro:hasPermission>

	</form>

  </div>
<table id="dg"></table>
<div id="dlg"></div>

<script src="${ctx}/static/js/basics/bstrainingperiod.js?random=<%= Math.random()%>"></script>
</body>
</html>