package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.dao.BsTrainingDayCostDao;
import com.foxconn.ipebg.basics.entity.BsTrainingDayCostEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 培訓期費用結帳明細表
 *
 * <AUTHOR>
 * @date 2022-08-24
 */
@Service
@Transactional(readOnly=true)
public class BsTrainingDayCostService extends BaseBusinessService<BsTrainingDayCostEntity, String>{
    @Autowired
    private BsTrainingDayCostDao bsTrainingDayCostDao;

    @Override
    public HibernateDao<BsTrainingDayCostEntity, String> getEntityDao() {
        return bsTrainingDayCostDao;
    }
}
