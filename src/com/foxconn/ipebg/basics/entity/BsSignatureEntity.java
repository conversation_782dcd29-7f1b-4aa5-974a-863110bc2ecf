package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 簽名檔
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-09-27 13:16:41
 */
@Entity
@Table(name = "BS_SIGNATURE")
@DynamicUpdate
@DynamicInsert
public class BsSignatureEntity extends DataEntity<BsSignatureEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    //簽名編號
    private String signatureNo;
    //簽名名稱
    private String signatureName;
    //備註
    private String signatureDesc;
    //base64圖片
    private String image;


    /**
     * 设置：簽名編號
     */
    public void setSignatureNo(String signatureNo) {
        this.signatureNo = signatureNo;
    }

    /**
     * 获取：簽名編號
     */

    @Column(name = "SIGNATURE_NO", nullable = false, length = 20)
    public String getSignatureNo() {
        return signatureNo;
    }

    /**
     * 设置：簽名名稱
     */
    public void setSignatureName(String signatureName) {
        this.signatureName = signatureName;
    }

    /**
     * 获取：簽名名稱
     */

    @Column(name = "SIGNATURE_NAME", nullable = false, length = 20)
    public String getSignatureName() {
        return signatureName;
    }

    /**
     * 设置：備註
     */
    public void setSignatureDesc(String signatureDesc) {
        this.signatureDesc = signatureDesc;
    }

    /**
     * 获取：備註
     */

    @Column(name = "SIGNATURE_DESC", nullable = false, length = 20)
    public String getSignatureDesc() {
        return signatureDesc;
    }

    /**
     * 设置：base64圖片
     */
    public void setImage(String image) {
        this.image = image;
    }

    /**
     * 获取：base64圖片
     */

    @Column(name = "IMAGE", nullable = false, length = 20)
    public String getImage() {
        return image;
    }


}
