package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.dao.BsPostPatchDao;
import com.foxconn.ipebg.basics.entity.BsPostPatchEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.docx4j.wml.P;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 崗位修改记录明細表
 * 
 * <AUTHOR>
 * @date 2022-07-20
 */
@Service
@Transactional(readOnly = true)
public class BsPostPatchService extends BaseBusinessService<BsPostPatchEntity, String> {
	@Autowired
	private BsPostPatchDao bsPostPatchDao;

	@Override
	public HibernateDao<BsPostPatchEntity, String> getEntityDao() {
		return bsPostPatchDao;
	}

	/**
	 * 根據日期查詢崗位人數、班制
	 * @param recno 崗位編號
	 * @param arrangeDate 排班日期
	 * @return 補丁
	 */
	public BsPostPatchEntity patchEntityByDate(String recno, Date arrangeDate) {
		List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
		filters.add(new PropertyFilter("EQS_recno", recno));
		filters.add(new PropertyFilter("LED_startDate", arrangeDate));
		filters.add(new PropertyFilter("GED_endDate", arrangeDate));
		List<BsPostPatchEntity> result = search(filters);
		if (result.size() > 0) {
			return result.get(0);
		} else {
			return null;
		}
	}
}
