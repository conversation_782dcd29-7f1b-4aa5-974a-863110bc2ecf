<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>培訓期培訓人員明細基本資料</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>

<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
	<form id="mainform" action="${ctx}/bstrainingperson/${action}"
		method="post">
		<table class="formTable">
			<tr>
			<td><input id="ids" name="ids" type="hidden"
					value="${bsTrainingPerson.id }" /></td>
				
			</tr>
			<tr>
				<td>工號：</td>
				<td><input id="empNo" name="empNo" class="easyui-validatebox"
					data-options="width: 150" value="${bsTrainingPerson.empNo }" /></td>
			</tr>
			<tr>
				<td>姓名：</td>
				<td><input id="empName" name="empName"
					class="easyui-validatebox" data-options="width: 150"
					value="${bsTrainingPerson.empName }" /></td>
			</tr>
			<tr>
				<td>身份證號：</td>
				<td><input id="psnId" name="psnId" class="easyui-validatebox"
					data-options="width: 150" value="${bsTrainingPerson.psnId }" /></td>
			</tr>
			<tr>
				<td>保安公司：</td>
				<td><input id="company" name="company"
					class="easyui-validatebox" data-options="width: 150"
					value="${bsTrainingPerson.company }" /></td>
			</tr>
			<tr>
				<td>培訓期編碼：</td>
				<td><input id="trainingPeriodNo" name="trainingPeriodNo"
					class="easyui-validatebox" data-options="width: 150"
					value="${bsTrainingPerson.trainingPeriodNo }" /></td>
			</tr>
			<tr>
				<td>培訓期名稱：</td>
				<td><input id="trainingPeriodName" name="trainingPeriodName"
					class="easyui-validatebox" data-options="width: 150"
					value="${bsTrainingPerson.trainingPeriodName }" /></td>
			</tr>
			<tr>
				<td>培訓開始時間：</td>
				<td><input id="startDate" name="startDate" class="easyui-my97"
					datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"
					disabled="disabled"
					value="<fmt:formatDate value="${bsTrainingPerson.startDate}"/>" />
				</td>
			</tr>
			<tr>
				<td>培訓結束時間：</td>
				<td><input id="endDate" name="endDate" class="easyui-my97"
					datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"
					disabled="disabled"
					value="<fmt:formatDate value="${bsTrainingPerson.endDate}"/>" /></td>
			</tr>
			<tr>
				<td>開始時間：</td>
				<td><input id="startTime" name="startTime"
					class="easyui-validatebox" data-options="width: 150"
					value="${bsTrainingPerson.startTime }" /></td>
			</tr>
			<tr>
				<td>結束時間：</td>
				<td><input id="endTime" name="endTime"
					class="easyui-validatebox" data-options="width: 150"
					value="${bsTrainingPerson.endTime }" /></td>
			</tr>
			<tr>
				<td>創建人：</td>
				<td><input id="createBy" name="createBy"
					class="easyui-validatebox" data-options="width: 150"
					value="${bsTrainingPerson.createBy }" /></td>
			</tr>
			<tr>
				<td>創建時間：</td>
				<td><input id="createDate" name="createDate"
					class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
					data-options="width: 160" disabled="disabled"
					value="<fmt:formatDate value="${bsTrainingPerson.createDate}"/>" />
				</td>
			</tr>
			<tr>
				<td>更新人：</td>
				<td><input id="updateBy" name="updateBy"
					class="easyui-validatebox" data-options="width: 150"
					value="${bsTrainingPerson.updateBy }" /></td>
			</tr>
			<tr>
				<td>更新時間：</td>
				<td><input id="updateDate" name="updateDate"
					class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
					data-options="width: 160" disabled="disabled"
					value="<fmt:formatDate value="${bsTrainingPerson.updateDate}"/>" />
				</td>
			</tr>
		
		</table>
	</form>
	</div>
	<script
		src='${ctx}/static/js/basics/bstrainingperson.js?"+Math.random()"'></script>
	<script type="text/javascript">
   //提交表单
$('#mainform').form({
    onSubmit: function(){
        var isValid = $(this).form('validate');
        return isValid;	// 返回false终止表单提交
    },
    success:function(data){ 
        successTip(data,dg,d);
    }
});
</script>
</body>
</html>