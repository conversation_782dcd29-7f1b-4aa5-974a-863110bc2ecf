package com.foxconn.ipebg.basics.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.LinkedHashMap;
import java.util.Date;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.utils.ConvertUtils;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.basics.entity.BsArrangeEntity;
import com.foxconn.ipebg.basics.service.BsArrangeService;
import com.foxconn.ipebg.basics.service.TempArrangeService;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;

/**
 * 排班基本資料表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-03-07 09:57:20
 */
@Controller
@RequestMapping("bsarrange")
public class BsArrangeController extends BaseController {

	@Autowired
	private BsArrangeService bsArrangeService;

	/**
	 * 方法描述: 列表信息
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-03-07 09:57:20
	 * @Return
	 **/

	@RequestMapping(method = RequestMethod.GET)
	// @RequiresPermissions("basics:bsarrange:list")
	public String list(HttpServletRequest request) {
		request.getSession().removeAttribute(
				TempArrangeService.TEMPARRANGEENTITIES);
		request.getSession().removeAttribute(
				TempArrangeService.TEMPARRANGEERRORDTOS);
		// 查询列表数据
		return "basics/bsarrange/list";
	}

	/**
	 * 方法描述: 分頁查詢信息
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-03-07 09:57:20
	 * @Return
	 **/

	@RequestMapping(value = "list", method = RequestMethod.GET)
	// @RequiresPermissions("basics:bsarrange:list")
	@ResponseBody
	public Map<String, Object> infoList(HttpServletRequest request) {
		Page<BsArrangeEntity> page = getPage(request);
		List<PropertyFilter> filters = PropertyFilter
				.buildFromHttpRequest(request);
		page = bsArrangeService.search(page, filters);
		return getEasyUIData(page);
	}

	/**
	 * 方法描述: 保存
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-03-07 09:57:20
	 * @Return
	 **/

	@RequestMapping(value = "create", method = RequestMethod.POST)
	@RequiresPermissions("basics:bsarrange:add")
	@ResponseBody
	public String create(@Valid BsArrangeEntity bsArrange, Model model) {
		if (bsArrangeService.isTogetherABC(bsArrange)) {
			return "該人員今天已經排班！";
		} else if (bsArrangeService.isContinuous(bsArrange)) {
			return "該人員不能白晚班連排！";
		} else if (bsArrangeService.isContinuousSix(bsArrange)) {
			return "該人員已經連續上了六天班！";
		} else if (bsArrangeService.verifyP10(bsArrange) && bsArrange.getShiftNo().equals("P")) {
			return "培訓崗排班天數最多為10天！";
		} else {
			bsArrange.setNewRecord(true);
			bsArrange.setCreateDate(new Date());
			bsArrangeService.save(bsArrange);
			return "success";
		}
	}

	/**
	 * 方法描述: 添加跳轉
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-03-07 09:57:20
	 * @Return
	 **/

	@RequiresPermissions("basics:bsarrange:add")
	@RequestMapping(value = "create", method = RequestMethod.GET)
	public String createForm(Model model) {
		model.addAttribute("bsArrange", new BsArrangeEntity());
		model.addAttribute("action", "create");
		return "basics/bsarrange/listForm";
	}

	/**
	 * 方法描述: 修改跳轉
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-06 11:04:39
	 * @Return
	 **/

	@RequiresPermissions("basics:bsarrange:update")
	@RequestMapping(value = "update/{id}", method = RequestMethod.GET)
	public String updateForm(@PathVariable("id") String id, Model model) {
		model.addAttribute("bsArrange", bsArrangeService.get(id));
		model.addAttribute("action", "update");
		return "basics/bsarrange/listForm";
	}

	/**
	 * 方法描述: 修改
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-11 10:55:00
	 * @Return
	 **/

	@RequestMapping(value = "update", method = RequestMethod.POST)
	@RequiresPermissions("basics:bsarrange:update")
	@ResponseBody
	public String update(@Valid BsArrangeEntity bsArrange,
			@RequestParam(value = "ids") String ids) {
		bsArrange.setId(ids);
		if (bsArrangeService.isTogetherABC(bsArrange)) {
			return "該人員今天已經排班！";
		} else if (bsArrangeService.isContinuous(bsArrange)) {
			return "該人員不能白晚班連排！";
		} else if (bsArrangeService.isContinuousSix(bsArrange)) {
			return "該人員已經連續上了六天班！";
		}else if (bsArrangeService.verifyP10(bsArrange) && bsArrange.getShiftNo().equals("P")) {
			return "培訓崗排班天數最多為10天！";
		} else {
			bsArrange.setUpdateDate(new Date());
			bsArrange.setUpdateBy(UserUtil.getCurrentUser().getLoginName());
			bsArrangeService.update(bsArrange);

			return "success";

		}
	}

	/**
	 * 方法描述: 根據主鍵刪除
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-03-07 09:57:20
	 * @Return
	 **/

	@RequestMapping("delete/{id}")
	@RequiresPermissions("basics:bsarrange:delete")
	@ResponseBody
	public String delete(@PathVariable("id") String id) {
		bsArrangeService.delete(id);
		return "success";
	}

	/**
	 * 导出excel
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-03-07 09:57:20
	 * @Return
	 **/
	@RequestMapping("exportExcel")
	public void exportExcel(HttpServletRequest request,
			HttpServletResponse response) throws Exception {

		List<PropertyFilter> filters = PropertyFilter
				.buildFromHttpRequest(request);
		//Page<BsArrangeEntity> page = getPage(request);
		//page.orderBy("createDate");
		//page.order("desc");
		//page = bsArrangeService.search(page, filters);
		// List<BsArrangeEntity> list = page.getResult();

		// Page<BsArrangeEntity> page = getPage(request);
		// List<PropertyFilter> filters = PropertyFilter
		// .buildFromHttpRequest(request);
		// page = bsArrangeService.search(page, filters);
		
		List<BsArrangeEntity> list = bsArrangeService.search(filters);

		//List<BsArrangeEntity> listFormQuery = page.getResult();
		List<BsArrangeEntity> listFormQuery = list;
		ConvertUtils.confertDateToStanded(listFormQuery);// 日期格式化
		if (listFormQuery.size() > 0) {

			// 導出excel2007
			LinkedHashMap<String, String> fieldMap = new LinkedHashMap<String, String>();
			fieldMap.put("postName", "崗位名稱");
			fieldMap.put("postRecno", "崗位編號");
			fieldMap.put("location", "詳細位置（小崗位名稱）");
			fieldMap.put("postShift", "班制");
			fieldMap.put("shiftDate", "排班日期");
			fieldMap.put("shiftNo", "班別代碼");
			fieldMap.put("shiftName", "班別");
			fieldMap.put("startTime", "開始時間");
			fieldMap.put("endTime", "結束時間");
			fieldMap.put("empNo", "工號");
			fieldMap.put("empName", "姓名");

			fieldMap.put("createBy", "創建人");
			fieldMap.put("createDate", "創建時間");

			// WfConifgEntity entity = flowService.findByWorkFlowId(workFlowId);
			String name = "排班明細";// entity.getWorkflowname();
			ExcelUtil.listToExcel(listFormQuery, fieldMap, name, response);

		}
	}
	
	/**
	 * 方法描述: 獲取警衛人員信息
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-04-18 10:34:27
	 * @Return
	 **/
    @RequestMapping(value = "getEmpInfo", method = RequestMethod.GET)
	@ResponseBody
	public BsArrangeEntity getEmpInfo(@Valid BsArrangeEntity bsArrange) {
    	BsArrangeEntity empInfo = bsArrangeService.getEmpInfo(bsArrange);
		return empInfo;
	}
}
