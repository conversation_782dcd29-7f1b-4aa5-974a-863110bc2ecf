var dg;
var d;
$(function(){
    dg=$('#dg').datagrid({
		method : "get",
		url : ctx + '/pointitem/list',
		fit : true,
		fitColumns : true,
		border : false,
		idField : 'id',
		loadFilter : function(data) {
			// 过滤数据
			var value = {
				total : data.total,
				rows : []
			};
			var x = 0;
			for ( var i = 0; i < data.rows.length; i++) {
				// ：0.新申請1.使用中2.手動停用（需重新申請該點位）3.複檢停用
				if (data.rows[i].useStatus == "0") {
					data.rows[i].useStatus = "新申請";
				} else if (data.rows[i].useStatus == "1") {
					data.rows[i].useStatus = "使用中";
				} else if (data.rows[i].useStatus == "3") {
					data.rows[i].useStatus = "複檢停用";
				}
				// :1.直飲水2.自建飲用水系統3.二次供水池/箱4.飲用水轉運水罐5.其他
				if (data.rows[i].pointLocationType == "1") {
					data.rows[i].pointLocationType = "直飲水";
				} else if (data.rows[i].pointLocationType == "2") {
					data.rows[i].pointLocationType = "自建飲用水系統";
				} else if (data.rows[i].pointLocationType == "3") {
					data.rows[i].pointLocationType = "二次供水池/箱";
				} else if (data.rows[i].pointLocationType == "4") {
					data.rows[i].pointLocationType = "飲用水轉運水罐";
				} else if (data.rows[i].pointLocationType == "5") {
					data.rows[i].pointLocationType = "其他";
				}
				value.rows[x++] = data.rows[i];
			}
			return value;

		},
		striped : true,
		pagination : true,
		rownumbers : true,
		pageNumber : 1,
		pageSize : 20,
		pageList : [ 10, 20, 30, 40, 50 ],
		singleSelect : true,
		columns : [ [ {
			field : 'id',
			title : '主鍵',
			hidden : true
		}, {
			field : 'pointno',
			title : '系統編碼',
			sortable : true,
			width : 100
		}, {
			field : 'dptQun',
			title : '事業群',
			sortable : true,
			width : 100
		}, {
			field : 'dptChu',
			title : '事業處',
			sortable : true,
			width : 100
		}, {
			field : 'dptBu',
			title : '部門',
			sortable : true,
			width : 100
		}, {
			field : 'dptKe',
			title : '單位',
			sortable : true,
			width : 100
		}, {
			field : 'manager',
			title : '管理人',
			sortable : true,
			width : 100
		}, {
			field : 'pointLocationType',
			title : '點位類別',
			sortable : true,
			width : 100
		}, {
			field : 'auditType',
			title : '審核類別：0.新啟用1.重新啟用',
			sortable : true,
			width : 100
		}, {
			field : 'building',
			title : '樓棟',
			sortable : true,
			width : 100
		}, {
			field : 'floor',
			title : '樓層',
			sortable : true,
			width : 100
		}, {
			field : 'location',
			title : '方位',
			sortable : true,
			width : 100
		}, {
			field : 'equipmentNumber',
			title : '設備序號',
			sortable : true,
			width : 100
		}, {
			field : 'installLocation',
			title : '安裝位置',
			sortable : true,
			width : 100
		}, {
			field : 'useStatus',
			title : '點位狀態',
			sortable : true,
			width : 100
		}, {
			field : 'attachids',
			title : '附件Id',
			sortable : true,
			width : 100
		}, {
			field : 'reattachids',
			title : '補充說明附件Id',
			sortable : true,
			width : 100
		} ] ],
		enableHeaderClickMenu : true,
		enableHeaderContextMenu : true,
		enableRowContextMenu : false,
		rowTooltip : true,
		toolbar : '#tb'
	});
});

//弹窗增加
function add() {
    d=$("#dlg").dialog({
        title: '添加點位維保記錄明細表',
        width: 380,
        height: 380,
        href:ctx+'/pointitemmaintenance/create',
        maximizable:true,
        modal:true,
        buttons:[{
            text:'确认',
            handler:function(){
                $("#mainform").submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}

//删除
function del(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data){
        if (data){
	$.ajax({
                type:'get',
                url:ctx+"/pointitemmaintenance/delete/"+row.id,
                success: function(data){
                    successTip(data,dg);
                }
            });
        }
    });
}

//弹窗修改
function upd(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    d=$("#dlg").dialog({
        title: '修改點位維保記錄明細表',
        width: 380,
        height: 340,
        href:ctx+'/pointitemmaintenance/update/'+row.id,
        maximizable:true,
        modal:true,
        buttons:[{
            text:'修改',
            handler:function(){
                $('#mainform').submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}
//创建查询对象并查询
function cx(){
    var obj=$("#searchFrom").serializeObject();
    dg.datagrid('load',obj);
}
//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("reset");
}
//导出excel
function exportExcel(){
    var form = document.getElementById("searchFrom");
    searchFrom.action=ctx+'/pointitemmaintenance/exportExcel';
    form.submit();
}