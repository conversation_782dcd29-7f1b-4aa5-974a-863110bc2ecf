package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.entity.BsTraineeEntity;
import com.foxconn.ipebg.basics.entity.BsTrainingPeriodEntity;
import com.foxconn.ipebg.basics.dao.BsTrainingPeriodDao;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.Dict;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WorkFlowEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.system.service.DictService;
import com.foxconn.ipebg.system.entity.User;
import com.foxconn.ipebg.system.utils.UserUtil;

import java.util.Date;
import java.util.List;

/**
 * 培訓期基本資料
 *
 * <AUTHOR>
 * @email <EMAIL> 
 * @date 2019-08-29 10:42:47
 */
@Service
@Transactional(readOnly=true)
public class  BsTrainingPeriodService extends BaseBusinessService<BsTrainingPeriodEntity, String>{
    @Autowired
    private BsTrainingPeriodDao bsTrainingPeriodDao;

    @Autowired
    private UserServiceUtil serviceUtil;
    @Autowired
    private DictService dictService;

    @Override
    public HibernateDao<BsTrainingPeriodEntity, String> getEntityDao() {
        return bsTrainingPeriodDao;
    }

    public BsTrainingPeriodEntity findByTrainingPeriodNo(String trainingPeriodNo) {
        return this.bsTrainingPeriodDao.findUniqueBy("trainingPeriodNo", trainingPeriodNo);
    }
    public BsTrainingPeriodEntity findById(String id) {
        return this.bsTrainingPeriodDao.findUniqueBy("id", id);
    }
    
    /**
     * 方法描述: 生成下拉數據 
     * N 為未結束的培訓期，Y為已經結束的培訓期  ALL 所有培訓期
     **/
	public List<BsTrainingPeriodEntity> getAllTrainingPeriodList(String isAll) {
     //return this.bsTrainingPeriodDao.findAll(false);
		String sql=" select * from BS_TRAINING_PERIOD t  ";
		if (isAll.equals("N"))
		{
			sql=sql+" where  t.END_DATE>=CURRENT_DATE   ";
		}else if ((isAll.equals("Y"))) 
		{
			sql=sql+" where  t.END_DATE<CURRENT_DATE   ";
		}else  if ((isAll.equals("ALL")))
		{
			
		}
        User user = UserUtil.getCurrentUser();
		if (user != null) {
		    String coms = user.getSecurityCom();
		    if (coms != null) {
		        coms = coms.replaceAll(",","','");
		        if (!sql.contains("where")) {
		            sql = sql + " where ";
                } else {
		            sql = sql + " and ";
                }
		        sql = sql + "t.security_com in ('" + coms + "')";
            }
        }
		sql=sql+" order by create_date desc";
		 return this.bsTrainingPeriodDao.createSQLQuery(sql).addEntity(BsTrainingPeriodEntity.class).list();
	}
}
