<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>排班基本資料表</title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
</script>

<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
	<form id="mainform" action="${ctx}/bsarrange/${action}" method="post">
		<table class="formTable">
			<tr>
				<input id="ids" name="ids" type="hidden" value="${bsArrange.id }" />

			</tr>
			<tr>
				<td>崗位編號：</td>
				<td><input id="postRecno" name="postRecno"
					class="easyui-validatebox" data-options="width: 150"
					value="${bsArrange.postRecno }" onblur="queryBsPost(this);"  /></td>
			</tr>
			<tr>
				<td>崗位名稱：</td>
				<td><input id="postName" name="postName" readonly
					class="easyui-validatebox" data-options="width: 150"
					value="${bsArrange.postName }" /></td>
			</tr>

			<tr>
				<td>詳細位置（小崗位名稱）：</td>
				<td><input id="location" name="location" readonly
					class="easyui-validatebox" data-options="width: 150"
					value="${bsArrange.location }" /></td>
			</tr>
			<tr>
				<td>班制：</td>
				<td><input id="postShift" name="postShift" readonly
					class="easyui-validatebox" data-options="width: 150"
					value="${bsArrange.postShift }" /></td>
			</tr>
			<tr>
				<td>排班日期：</td>
				<td><input id="shiftDate" name="shiftDate" class="easyui-my97"
					datefmt="yyyy-MM-dd" data-options="width: 160"
					value="<fmt:formatDate value="${bsArrange.shiftDate}"/>" /></td>
			</tr>
			
			<tr>
				<td>班別代碼：</td>
				<td><input id="shiftNo" name="shiftNo" class="easyui-combobox"
					data-options="panelHeight:'auto',valueField:'shiftNo', textField:'shiftNo',editable:false,onBeforeLoad:function(){loadShift();},onSelect:function(){queryShift();}"
					value="${bsArrange.shiftNo }" /></td>
			</tr>
			<tr>
				<td>班別：</td>
				<td><input id="shiftName" name="shiftName" readonly
					class="easyui-validatebox" data-options="width: 150"
					value="${bsArrange.shiftName}" /></td>
			</tr>
			<tr>
				<td>開始時間：</td>
				<td><input id="startTime" name="startTime" readonly
					class="easyui-validatebox" data-options="width: 150"
					value="${bsArrange.startTime }" /></td>
			</tr>
			<tr>
				<td>結束時間：</td>
				<td><input id="endTime" name="endTime" readonly
					class="easyui-validatebox" data-options="width: 150"
					value="${bsArrange.endTime }" /></td>
			</tr>
			<tr>
				<td>工號：</td>
				<td><input id="empNo" name="empNo" class="easyui-validatebox"
					data-options="width: 150" value="${bsArrange.empNo }" onblur="queryEmpInfo(this);" /></td>
			</tr>
			<tr>
				<td>姓名：</td>
				<td><input id="empName" name="empName" readonly
					class="easyui-validatebox" data-options="width: 150"
					value="${bsArrange.empName }" /></td>
			</tr>
			<tr>
				<td>創建人：</td>
				<td><input id="createBy" name="createBy" readonly
					class="easyui-validatebox" data-options="width: 150"
					value="${bsArrange.createBy }" /></td>
			</tr>
			<tr>
				<td>創建時間：</td>
				<td><input id="createDate" name="createDate"
					class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
					data-options="width: 160" disabled="disabled"
					value="<fmt:formatDate value="${bsArrange.createDate}"/>" /></td>
			</tr>
			<tr>
				<td>更新者：</td>
				<td><input id="updateBy" name="updateBy" readonly
					class="easyui-validatebox" data-options="width: 150"
					value="${bsArrange.updateBy }" /></td>
			</tr>
			<tr>
				<td>更新時間：</td>
				<td><input id="updateDate" name="updateDate"  
					class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
					data-options="width: 160" disabled="disabled"
					value="<fmt:formatDate value="${bsArrange.updateDate}"/>" /></td>
			</tr>
			<%-- <tr>
				<td>刪除標識：</td>
				<td><input id="delFlag" name="delFlag"
					class="easyui-validatebox" data-options="width: 150"
					value="${bsArrange.delFlag }" /></td>
			</tr> --%>

		</table>
	</form>
	</div>
	<script src='${ctx}/static/js/basics/bsarrange.js?"+Math.random()"'></script>
	<script type="text/javascript">
		//提交表单
		$('#mainform').form({
			onSubmit : function() {
				var isValid = $(this).form('validate');
				return isValid; // 返回false终止表单提交
			},
			success : function(data) {
				successTip(data, dg, d);
			}/*,
			 error : function(error) {
    
			//if (error== "isTogetherABC")
			//$.messager.alert("溫馨提示", " 同一人，不能白晚班連排！", "info");
				//alert("同一人，不能白晚班連排！");
			} */
		});
	</script>
</body>
</html>