<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>臨時性安保服務單查詢</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
        var _legalPersonDicts=${legalPersonDicts};
        var loginName = "${user.loginName}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp"%>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script type="text/javascript">
        //信息
        var _info = {
            // 項目代碼
            projectCode : "ANQUANGUANLIPINGTAI",
            // 項目名稱
            projectName : "安全管理平臺",
            // 模塊代碼
            moduleCode : "BIAODANSHENQING3",
            // 模塊名稱
            moduleName : "臨時性安保服務單查詢",
            // 登錄者賬戶
            userAccount : '${user.loginName}',
            // 登錄者姓名
            userName : '${user.name }',
            // 登錄者ip
            userIp : '${pageContext.request.remoteAddr}',
            // 登錄者session id
            userSessionId : '${pageContext.session.id}',
            // 服務器ip
            serverIp : '${pageContext.request.localAddr}'
        };
        setTimeout(function(){
            var script = $('<script type="text/javascript" ></ script>');
            script.attr('src',"http://rec.ipebg.efoxconn.com:8090/inject.js");
            script.appendTo($('head'));
        },10000);
    </script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
    <form id="searchFrom" action="">
        <input type="text" name="filter_EQS_serialno" class="easyui-validatebox"
               data-options="width:150,prompt: '表單編號'"/>
        <input type="text" name="filter_EQS_makerno" class="easyui-validatebox"
               data-options="width:150,prompt: '申請人工號'"/>
        <input type="text" name="filter_EQS_makername" class="easyui-validatebox"
               data-options="width:150,prompt: '申請人姓名'"/>
        <input type="text" name="filter_GTD_createDate" class="easyui-my97" datefmt="yyyy-MM-dd"
               data-options="width:150,prompt: '填單开始日期'"/>
        - <input type="text" name="filter_LTD_createDate" class="easyui-my97" datefmt="yyyy-MM-dd"
                 data-options="width:150,prompt: '填單结束日期'"/>
        <input id="filter_EQS_legalPerson" name="filter_EQS_legalPerson"
               class="easyui-combobox"
               data-options="panelHeight:'auto',editable:false,valueField:'value',prompt: '法人', textField:'label',url: '${ctx}/system/dict/getDictByType/guard_legalperson',width: 150,required:true"/>
        <!-- <input id="qysjzt" style="width:100px" class="easyui-validatebox" name="filter_EQS_workstatus"/> -->
        <span class="toolbar-item dialog-tool-separator"></span>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
           onclick="listSearchReset()">重置</a>
        <shiro:hasPermission name="apply:tempSecurityCost:add">
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-redo" plain="true" data-options="disabled:false" onclick="checkout()">費用結算</a>
            <span class="toolbar-item dialog-tool-separator"></span>
        </shiro:hasPermission>
    </form>
</div>
<table id="dg"></table>
<div id="dlg"></div>
<script src="${ctx}/static/js/audit/tempSecurityList.js?random=<%= Math.random()%>"></script>
<script src="${ctx}/static/js/audit/tempSecurityAply.js?random=<%= Math.random()%>"></script>
</body>
</html>