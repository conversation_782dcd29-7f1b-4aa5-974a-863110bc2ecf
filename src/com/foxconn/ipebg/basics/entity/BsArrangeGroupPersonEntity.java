package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.ColumnTransformer;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 排班群組匹配合格人員
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-09-11 10:28:22
 */
@Entity
@Table(name = "V_BS_ARRANGE_GROUP_PERSON")
@DynamicUpdate
@DynamicInsert
public class BsArrangeGroupPersonEntity extends DataEntity<BsArrangeGroupPersonEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

                                    //排班群組編碼
            private String arrangeGroupId;
                                //排班群組
            private String arrangeGroupName;
                                //身份證號
            private String psnId;
                                //工號
            private String empNo;
                                //姓名
            private String empName;
                                //硬卡號
            private String cardId;
                                //公司
            private String company;
                                //考試合格日期
            private Date passDate;
                                                                           
            
                    /**
             * 设置：排班群組編碼
             */
            public void setArrangeGroupId(String arrangeGroupId) {
                this.arrangeGroupId = arrangeGroupId;
            }

        /**
         * 获取：排班群組編碼
         */
        
                            @Column(name = "ARRANGE_GROUP_ID", nullable = false, length = 20)
                public String getArrangeGroupId() {
                    return arrangeGroupId;
                }
                    
                    /**
             * 设置：排班群組
             */
            public void setArrangeGroupName(String arrangeGroupName) {
                this.arrangeGroupName = arrangeGroupName;
            }

        /**
         * 获取：排班群組
         */
        
                            @Column(name = "ARRANGE_GROUP_NAME", nullable = false, length = 20)
                public String getArrangeGroupName() {
                    return arrangeGroupName;
                }
                    
                    /**
             * 设置：身份證號
             */
            public void setPsnId(String psnId) {
                this.psnId = psnId;
            }

        /**
         * 获取：身份證號
         */
        @Column(name = "PSN_ID", nullable = false, length = 20)
        @ColumnTransformer(forColumn = "PSN_ID", read = "CONVERT_FROM(decrypt(decode(PSN_ID, 'base64'), 'sgas', 'aes'), 'UTF-8')", write = "encode(encrypt(cast(? as bytea), 'sgas', 'aes'), 'base64')")
        public String getPsnId() {
                    return psnId;
                }
                    
                    /**
             * 设置：工號
             */
            public void setEmpNo(String empNo) {
                this.empNo = empNo;
            }

        /**
         * 获取：工號
         */
        
                            @Column(name = "EMP_NO", nullable = false, length = 20)
                public String getEmpNo() {
                    return empNo;
                }
                    
                    /**
             * 设置：姓名
             */
            public void setEmpName(String empName) {
                this.empName = empName;
            }

        /**
         * 获取：姓名
         */
        
                            @Column(name = "EMP_NAME", nullable = false, length = 20)
                public String getEmpName() {
                    return empName;
                }
                    
                    /**
             * 设置：硬卡號
             */
            public void setCardId(String cardId) {
                this.cardId = cardId;
            }

        /**
         * 获取：硬卡號
         */
        
                            @Column(name = "CARD_ID", nullable = false, length = 20)
                public String getCardId() {
                    return cardId;
                }
                    
                    /**
             * 设置：公司
             */
            public void setCompany(String company) {
                this.company = company;
            }

        /**
         * 获取：公司
         */
        
                            @Column(name = "COMPANY", nullable = false, length = 20)
                public String getCompany() {
                    return company;
                }
                    
                    /**
             * 设置：考試合格日期
             */
            public void setPassDate(Date passDate) {
                this.passDate = passDate;
            }

        /**
         * 获取：考試合格日期
         */
        
                            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
                @Column(name = "PASS_DATE", nullable = false, length = 20)
                public Date getPassDate() {
                return passDate;
            }
                    
        
        
        
        
        
}
