package com.foxconn.ipebg.system.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 上傳文件附件信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-10 15:38:57
 */
@Entity
@Table(name = "t_pub_fileobject")
@DynamicUpdate
@DynamicInsert
public class TPubFileobjectEntity extends DataEntity<TPubFileobjectEntity>  implements Serializable {
    private static final long serialVersionUID = 1L;


            //文件名稱
        private String name;
            //文件保存路徑
        private String url;
            //文件大小 byte
        private Integer sizez;
            //文件類型
        private String type;
            //記錄生成時間
        private String createdate;
            //上傳者
        private String uploader;
            //上傳者ip
        private String ip;
            //說明
        private String describ;

            /**
         * 设置：文件名稱
         */
        public void setName(String name) {
            this.name = name;
        }
    /**
     * 获取：文件名稱
     */
                    @Column(name = "name", nullable = false, length = 20)
            public String getName() {
                return name;
            }
        
            /**
         * 设置：文件保存路徑
         */
        public void setUrl(String url) {
            this.url = url;
        }
    /**
     * 获取：文件保存路徑
     */
                    @Column(name = "url", nullable = false, length = 20)
            public String getUrl() {
                return url;
            }
        
            /**
         * 设置：文件大小 byte
         */
        public void setSizez(Integer sizez) {
            this.sizez = sizez;
        }
    /**
     * 获取：文件大小 byte
     */
                    @Column(name = "sizez", nullable = false, length = 20)
            public Integer getSizez() {
                return sizez;
            }
        
            /**
         * 设置：文件類型
         */
        public void setType(String type) {
            this.type = type;
        }
    /**
     * 获取：文件類型
     */
                    @Column(name = "type", nullable = false, length = 20)
            public String getType() {
                return type;
            }
        
            /**
         * 设置：記錄生成時間
         */
        public void setCreatedate(String createdate) {
            this.createdate = createdate;
        }
    /**
     * 获取：記錄生成時間
     */
                    @Column(name = "createdate", nullable = false, length = 20)
            public String getCreatedate() {
                return createdate;
            }
        
            /**
         * 设置：上傳者
         */
        public void setUploader(String uploader) {
            this.uploader = uploader;
        }
    /**
     * 获取：上傳者
     */
                    @Column(name = "uploader", nullable = false, length = 20)
            public String getUploader() {
                return uploader;
            }
        
            /**
         * 设置：上傳者ip
         */
        public void setIp(String ip) {
            this.ip = ip;
        }
    /**
     * 获取：上傳者ip
     */
                    @Column(name = "ip", nullable = false, length = 20)
            public String getIp() {
                return ip;
            }
        
            /**
         * 设置：說明
         */
        public void setDescrib(String describ) {
            this.describ = describ;
        }
    /**
     * 获取：說明
     */
                    @Column(name = "describ", nullable = false, length = 20)
            public String getDescrib() {
                return describ;
            }
        
    }
