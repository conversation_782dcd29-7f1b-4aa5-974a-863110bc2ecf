var dg;
var d;
$(function () {
    dg = $('#dg').datagrid({
        method: "get",
        url: ctx+'/tqhwfhbfilecopyprocess/list',
        fit: true,
        fitColumns: true,
        border: false,
        idField: 'id',
        striped: true,
        pagination: true,
        rownumbers: true,
        pageNumber: 1,
        pageSize: 20,
        pageList: [10, 20, 30, 40, 50],
        singleSelect: true,
        columns: [[
            {field: 'id', title: '主鍵', hidden: true},
            {field: 'serialno', title: '任務編號', sortable: true, width: 100, formatter: operation},
            {field: 'dealno', title: '承辦人工號', sortable: true, width: 100},
            {field: 'dealname', title: '承辦人名稱', sortable: true, width: 100},
            {field: 'dealdeptno', title: '承辦人單位代碼', sortable: true, width: 100},
            {field: 'dealdeptname', title: '承辦人單位名稱', sortable: true, width: 100},
            {field: 'filename', title: '文件名稱', sortable: true, width: 100},
            {field: 'workstatus', title: '表單狀態', sortable: true, width: 100},
            {field: 'nodeName', title: '當前簽核節點', sortable: true, width: 100},
            {field: 'auditUser', title: '當前簽核人', sortable: true, width: 100},
            {field: 'complettime', title: '簽核完成時間', sortable: true, width: 100}
        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        toolbar: '#tb'
    });
    $(":input").removeClass("validatebox-invalid");
    //獲取廠區
    $.get(ctx+'/tqhfactoryidconfig/allFactorys/',
        function (result) {
            $("#dealfactoryid").combobox({
                data: result,
                valueField: "factoryid",
                textField: "factoryname",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({factoryid: '', factoryname: '請選擇'});
                    return data;
                }
            });
        });
    //創建下拉查詢條件
    $.ajax({
        url: ctx+"/system/dict/getDictByType/audit_status",
        //dataType:"json",
        type: "GET",
        success: function (data) {
            //绑定第一个下拉框
            $("#qysjzt").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({value: '', label: '請選擇'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });
});

function operation(value, row, index) {
    return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('詳情',ctx+'/tqhwfhbfilecopyprocess/view/" + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
};
//弹窗增加
function add() {
    d = $("#dlg").dialog({
        title: '添加環保文件复印申請表',
        width: 380,
        height: 380,
        href: ctx+'/tqhwfhbfilecopyprocess/create',
        maximizable: true,
        modal: true,
        buttons: [{
            text: '确认',
            handler: function () {
                $("#mainform").submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//删除
function del() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function (data) {
        if (data) {
            $.ajax({
                type: 'get',
                url: ctx+"/tqhwfhbfilecopyprocess/delete/" + row.id,
                success: function (data) {
                    successTip(data, dg);
                }
            });
        }
    });
}

//弹窗修改
function upd() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    d = $("#dlg").dialog({
        title: '修改環保文件复印申請表',
        width: 380,
        height: 340,
        href: ctx+'/tqhwfhbfilecopyprocess/update/' + row.id,
        maximizable: true,
        modal: true,
        buttons: [{
            text: '修改',
            handler: function () {
                $('#mainform').submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//创建查询对象并查询
function cx() {
    var obj = $("#searchFrom").serializeObject();
    dg.datagrid('load', obj);
}
//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("reset");
}
//驗證申請人聯繫電話
function valdApplyTel(obj) {
    var phoneL = $.trim($("#dealtel").val()).split("").length;
    var tel = obj.value;
    if (tel != "") {
        var temp = tel;
        var regex = /^[0-9]{3}\+[0-9]{5}$/;
        var patt1 = new RegExp(regex);
        var result = patt1.test(tel);
        if (!result) {
            alert("分機格式有誤!");
            obj.value = "";
            obj.focus();
            return;
        }
    }
}
//驗證郵箱
function valdEmail(obj) {
    var email = obj.value;
    var temp = email;
    if (email == "") return;
    var regex = /\w+([-/+.]\w+)*@(mail.foxconn.com|foxconn.com|fii-foxconn.com)$/;
    var patt1 = new RegExp(regex);
    var result = patt1.test(email);
    if (!result) {
        alert("郵箱的格式不正確!");
        obj.value = "";
        //obj.focus();
        obj.value = temp;
        return;
    }
    ;
}
//根據工號查出當前申請人的信息
function queryUserInfo() {
    var empno = $.trim($("#dealno").val().toUpperCase());
    if (empno != null && empno != "") {
        $.ajax({
            url: ctx+'/system/user/getUserInfo/',
            type: 'POST',
            beforeSend: ajaxLoading,
            //dataType: 'json',
            data: {empno: empno},
            success: function (data) {
                ajaxLoadEnd();
                if (!data) {
                    $.messager.alert("溫馨提示", "工號不存在", "error");
                    $('#dealno').val('');
                    $('#dealname').val('');
                    $('#dealdeptno').val('');
                    $('#dealdeptname').val('');
                    $('#dealemail').val('');
                } else {
                    $('#dealname').val(data.empname);
                    $('#dealdeptno').val(data.deptno);
                    $('#dealdeptname').val(data.deptname);
                    $('#dealfactoryid').combobox('setValue', data.factoryid);
                    $('#dealemail').val(data.email);
                }
            }
        });
    }
}
function saveInfo(flag) {
    if(flag=='0'){
        var empno = $.trim($("#dealno").val());
        if (empno=='') {
            alert("承辦人工號不能為空！");
            return;
        }
    }else {
        var isValid = $("#mainform").form('validate');
        if (!isValid) {
            return;
        }
    }
    $.ajax({
        url: ctx+'/tqhwfhbfilecopyprocess/create/' + flag,
        type: 'POST',
        beforeSend: ajaxLoading,
        //dataType: 'json',
        data: $('#mainform').serialize(),
        success: function (data) {
            ajaxLoadEnd();
            successTip(data, dg, d);
            window.parent.mainpage.mainTabs.closeCurrentTab();
        }
    });
}
//导出excel
function exportExcel(){
    var form = document.getElementById("searchFrom");
    searchFrom.action=ctx+'/tqhwfhbfilecopyprocess/exportExcel';
    form.submit();
}

