package com.foxconn.ipebg.buessness.common.dao;

import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.buessness.workflow.entity.Gtasks;
import com.foxconn.ipebg.buessness.workflow.entity.MyTaskCount;
import com.foxconn.ipebg.common.utils.StringUtils;
import org.hibernate.HibernateException;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.transform.AliasToBeanResultTransformer;
import org.hibernate.transform.Transformers;
import org.springframework.cglib.core.Transformer;
import org.springframework.stereotype.Repository;
import com.foxconn.ipebg.common.persistence.HibernateDao;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 簽核業務中間表，保存所有簽核業務主信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-08 09:28:35
 */
@Repository
public class TQhAllRelationDao extends HibernateDao<TQhAllRelationEntity, String> {

    /**
     * 方法描述:
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/8  下午 04:29
     * @Return
     **/

//    public List<MyTaskCount> findIndexData(String hql, List<String> value) {
//        Session session = null;
//        try {
//            if (value.size() > 0) {
//                session = getSession();
//                Query query = session.createQuery(hql);
//                query.setParameterList("processIds", value);
//                List<Object[]> list = query.list();
//                List<MyTaskCount> taskList = new ArrayList<MyTaskCount>();
//                if (list.size() > 0) {
//                    for (Object[] objects : list) {
//                        MyTaskCount task = new MyTaskCount();
//                        task.setWorkflowId(objects[0].toString());
//                        task.setWfName(objects[1].toString());
//                        task.setTaskCount(Integer.parseInt(objects[2].toString()));
//                        task.setFlag(objects[3].toString());
//                        taskList.add(task);
//                    }
//                }
//                return taskList;
//            } else {
//                return null;
//            }
//        } catch (HibernateException e) {
//            e.printStackTrace();
//            return null;
//        }
//
//    }
//
//    public List<Map<String, Object>> findToMapBySql(String sql, Object serialno) {
//        Session sqlSession = getSession();
//        Query query = sqlSession.createQuery(sql);
////        query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
//        query.setParameter("serialno",serialno);
//        return query.list();
//    }
//
//    public String updateFormStatic(String sql,String workstatus,String serialno){
//        Session sqlSession = getSession();
//        Query query = sqlSession.createQuery(sql);
//        query.setParameter("serialno",serialno);
//        query.setParameter("workstatus",workstatus);
//        return query.executeUpdate()+"";
//    }
//
//    public String updateFormReattachids(String sql,String serialno,String reattachids){
//        Session sqlSession = getSession();
//        Query query = sqlSession.createQuery(sql);
//        query.setParameter("serialno",serialno);
//        query.setParameter("reattachids",reattachids);
//        return query.executeUpdate()+"";
//    }
//
//    public String updateFormProcessId(String sql,String serialno,String processid){
//        Session sqlSession = getSession();
//        Query query = sqlSession.createQuery(sql);
//        query.setParameter("serialno",serialno);
//        query.setParameter("processid",processid);
//        return query.executeUpdate()+"";
//    }
//
//    public Gtasks findGtasks(String hql, String value){
//        Session session = null;
//        try {
//            if (StringUtils.isNotBlank(value)) {
//                session = getSession();
//                Query query = session.createQuery(hql);
//                query.setString("serialno", value);
//                List<Object[]> list = query.list();
//                Gtasks task = new Gtasks();
//                if (list.size() > 0) {
//                    for (Object[] objects : list) {
//                        task.setMakerno(objects[0].toString());
//                        task.setMakername(objects[1].toString());
//                        SimpleDateFormat f = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                        task.setCreatetime(f.parse(objects[2].toString()));
//                        task.setWorkstatus(objects[3].toString());
//                    }
//                }
//                return task;
//            } else {
//                return null;
//            }
//        } catch (HibernateException e) {
//            e.printStackTrace();
//            return null;
//        } catch (ParseException e) {
//            e.printStackTrace();
//        }
//        return null;
//    }
//
//    public String findUserEmpno(String hql,String serialno){
//        String userEmpno="";
//        Session session = getSession();
//        Query query = session.createQuery(hql);
//        query.setString("serialno",serialno);
//        List<String> list = query.list();
//        if (list.size() > 0) {
//                userEmpno=list.get(0).toString();
//        }
//        return userEmpno;
//    }
//
//    public List<TQhAllRelationEntity> getMyDownTask(String hql){
//        SQLQuery query = getSession().createSQLQuery(hql);
//        query.addScalar("id");
//        query.addScalar("workflowid");
//        query.addScalar("createBy");
//        query.addScalar("createDate");
//        query.addScalar("updateBy");
//        query.addScalar("updateDate");
//        query.addScalar("serialno");
//        query.addScalar("processid");
//        query.addScalar("version");
//        query.addScalar("dtoName");
////        query.addScalar("workstatus");
//        query.addScalar("wfName");
//        query.setResultTransformer(Transformers.aliasToBean(TQhAllRelationEntity.class));
//        return query.list();
//    }
//
//    public int getMyDownTaskCount(String hql){
//        SQLQuery query = createSQLQuery(hql);
//        return ((BigDecimal) query.list().get(0)).intValue();
//    }
}
