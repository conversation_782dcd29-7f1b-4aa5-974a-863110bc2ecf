var dg;
var d;
$(function(){
    dg=$('#dg').datagrid({
        method: "get",
        url:ctx+'/wfconfigparam/list',
        fit : true,
        fitColumns : true,
        border : false,
        idField : 'id',
        striped:true,
        pagination:true,
        rownumbers:true,
        pageNumber:1,
        pageSize : 20,
        pageList : [ 10, 20, 30, 40, 50 ],
        singleSelect:true,
        columns:[[
							                    { field: 'workflowid', title: '流程編碼',sortable:true,width:100}
					,
																                    { field: 'paramename', title: '參數名稱',sortable:true,width:200}
					,
																                    { field: 'paramvalue', title: '條件字段',sortable:true,width:100}
					,
																                    { field: 'paramtype', title: '參數類型',sortable:true,width:100}
					,
																                    { field: 'id', title: '主鍵',hidden:true},
											                                        { field: 'createBy', title: '創建人',sortable:true,width:100}
					,
																                    { field: 'createDate', title: '創建時間',sortable:true,width:100}
												        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        toolbar:'#tb'
    });
});

//弹窗增加
function add() {
    d=$("#dlg").dialog({
        title: '添加流程參數配置表',
        width: 380,
        height: 380,
        href:ctx+'/wfconfigparam/create',
        maximizable:true,
        modal:true,
        buttons:[{
            text:'确认',
            handler:function(){
                $("#mainform").submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}

//删除
function del(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data){
        if (data){
	$.ajax({
                type:'get',
                url:ctx+"/wfconfigparam/delete/"+row.id,
                success: function(data){
                    successTip(data,dg);
                }
            });
        }
    });
}

//弹窗修改
function upd(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    d=$("#dlg").dialog({
        title: '修改流程參數配置表',
        width: 380,
        height: 340,
        href:ctx+'/wfconfigparam/update/'+row.id,
        maximizable:true,
        modal:true,
        buttons:[{
            text:'修改',
            handler:function(){
                $('#mainform').submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}
//创建查询对象并查询
function cx(){
    var obj=$("#searchFrom").serializeObject();
    dg.datagrid('load',obj);
}
