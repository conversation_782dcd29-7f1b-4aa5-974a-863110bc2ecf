package com.foxconn.ipebg.basics.entity;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "BS_COST")
@DynamicUpdate
@DynamicInsert
public class BsCostEntity extends DataEntity<BsCostEntity> {
    private static final long serialVersionUID = 1L;
    // 類型 trainee 為培訓期，guard為正式崗位
    private String type;
    // 月費用
    private Float cost;
    // 一個月的天數，按當月天數計算則設置為 0
    private Float dayCount;
    // 備註
    private String remark;
    // 保安公司
    private String securityCom;

    @Column(name = "TYPE", nullable = false, length = 20)
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Column(name = "COST", nullable = false)
    public Float getCost() {
        return cost;
    }

    public void setCost(Float cost) {
        this.cost = cost;
    }

    @Column(name = "DAY_COUNT", nullable = false)
    public Float getDayCount() {
        return dayCount;
    }

    public void setDayCount(Float dayCount) {
        this.dayCount = dayCount;
    }

    @Column(name = "REMARK", nullable = false, length = 255)
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Column(name = "security_com", nullable = false, length = 15)
    public String getSecurityCom() {
        return securityCom;
    }

    public void setSecurityCom(String securityCom) {
        this.securityCom = securityCom;
    }
}
