package com.foxconn.ipebg.basics.entity;

        import java.io.Serializable;
        import java.util.Date;
        import javax.persistence.*;

        import com.foxconn.ipebg.common.entity.DataEntity;
        import org.hibernate.annotations.DynamicInsert;
        import org.hibernate.annotations.DynamicUpdate;


/**
 * 排班群組基本資料
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-09-11 10:28:20
 */
@Entity
@Table(name = "BS_ARRANGE_GROUP")
@DynamicUpdate
@DynamicInsert
public class BsArrangeGroupEntity extends DataEntity<BsArrangeGroupEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    //排班群組編碼
    private String arrangeGroupId;
    //排班群組
    private String arrangeGroupName;
    // 保安公司
    private String securityCom;

    /**
     * 设置：排班群組編碼
     */
    public void setArrangeGroupId(String arrangeGroupId) {
        this.arrangeGroupId = arrangeGroupId;
    }

    /**
     * 获取：排班群組編碼
     */

    @Column(name = "ARRANGE_GROUP_ID", nullable = false, length = 20)
    public String getArrangeGroupId() {
        return arrangeGroupId;
    }

    /**
     * 设置：排班群組
     */
    public void setArrangeGroupName(String arrangeGroupName) {
        this.arrangeGroupName = arrangeGroupName;
    }

    /**
     * 获取：排班群組
     */

    @Column(name = "ARRANGE_GROUP_NAME", nullable = false, length = 20)
    public String getArrangeGroupName() {
        return arrangeGroupName;
    }

    @Column(name = "security_com", nullable = false, length = 15)
    public String getSecurityCom() {
        return securityCom;
    }

    public void setSecurityCom(String securityCom) {
        this.securityCom = securityCom;
    }
}
