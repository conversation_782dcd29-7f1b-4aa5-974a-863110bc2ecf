var dg;
var d;
$(function(){
    dg=$('#dg').datagrid({
        method: "get",
        url:ctx+'/tqhwfnoequipmentdesterilize/list',
        fit: true,
        fitColumns: true,
        border: false,
        idField: 'id',
        striped: true,
        pagination: true,
        rownumbers: true,
        pageNumber: 1,
        pageSize: 20,
        pageList: [10, 20, 30, 40, 50],
        singleSelect: true,
        columns: [[
            {field: 'id', title: '主鍵', hidden: true},
            {field: 'serialno', title: '任務編號', sortable: true, width: 150, formatter: operation},
            {field: 'dealno', title: '承辦人工號', sortable: true, width: 50},
            {field: 'dealname', title: '承辦人名稱', sortable: true, width: 50},
            {field: 'dealdeptno', title: '承辦人單位代碼', sortable: true, width: 100},
            {field: 'dealdeptname', title: '承辦人單位名稱', sortable: true, width: 200},
            {field: 'dealtime', title: '提報日期', sortable: true, width: 60,formatter:formatDate},
            // {field: 'createtime', title: '填單時間', sortable: true, width: 60,formatter:formatDatebox},
            //{field: 'createtime', title: '填單時間', sortable: true, width: 100},

            {field: 'workstatus', title: '表單狀態', sortable: true, width: 100},
            {field: 'nodeName', title: '當前簽核節點', sortable: true, width: 100},
            {field: 'auditUser', title: '當前簽核人', sortable: true, width: 100},
            {field: 'complettime', title: '簽核完成時間', sortable: true, width: 100}
        ]],

        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        // sortName: 'dealtime',
        // sortOrder: 'desc',
        //sort:'itemid',
        toolbar:'#tb'
    });





$(":input").removeClass("validatebox-invalid");
//獲取廠區
$.get(ctx+'/tqhfactoryidconfig/allFactorys/',
    function(result) {
        $("#dealfactoryid").combobox({
            data: result,
            valueField: "factoryid",
            textField: "factoryname",
            editable: false,
            panelHeight: 400,
            loadFilter:function(data){
                data.unshift({factoryid:'',factoryname:'請選擇'});
                return data;
            }
        });
    });
//批量上傳模板下載
    $("#btnBatchImportTpl").click(batchImportTpl);
//添加一筆
$("#add").click(function(){
    var len=$("#buildprojectApplyItemTable tr").length;
    $("#buildprojectApplyItemTable").append("<tr align='center' id="+len+">"
        +"<td>"+len+"</td>"
        +"<td><input id='equipmentname"+len+"' name='tQhWfnoequipmentEntity["+(len-1)+"].equipmentname' class='easyui-validatebox' required='true' style='width:150px;;'></td>"
        +"<td><input id='manufacturer"+len+"' name='tQhWfnoequipmentEntity["+(len-1)+"].manufacturer' class='easyui-validatebox' required='true' style='width:150px;;'></td>"

        +"<td><input id='model"+len+"' name='tQhWfnoequipmentEntity["+(len-1)+"].model' class='easyui-validatebox' required='true' style='width:150px;;'></td>"
        +"<td><input id='dateofproduction"+len+"' name='tQhWfnoequipmentEntity["+(len-1)+"].dateofproduction' class='easyui-my97'  datefmt='yyyy-MM-dd' data-options='width: 100,required:true' ></td>"
        +"<td><input id='use"+len+"' name='tQhWfnoequipmentEntity["+(len-1)+"].use' class='easyui-validatebox' required='true' style='width:150px;;'></td>"
        +"<td><input id='maximumtubevoltage"+len+"' name='tQhWfnoequipmentEntity["+(len-1)+"].maximumtubevoltage' class='easyui-validatebox' required='true' style='width:150px;;'></td>"
        +"<td><input id='maximumcurrent"+len+"' name='tQhWfnoequipmentEntity["+(len-1)+"].maximumcurrent' class='easyui-validatebox' required='true' style='width:150px;;'></td>"
        +"<td><input id='rayspecies"+len+"' name='tQhWfnoequipmentEntity["+(len-1)+"].rayspecies' class='easyui-validatebox' required='true' style='width:150px;;'></td>"
        +"<td><input id='stopdate"+len+"' name='tQhWfnoequipmentEntity["+(len-1)+"].stopdate' class='easyui-my97'  datefmt='yyyy-MM-dd' data-options='width: 100,required:true' ></td>"
        +"<td><input id='reusedate"+len+"' name='tQhWfnoequipmentEntity["+(len-1)+"].reusedate' class='easyui-my97'  datefmt='yyyy-MM-dd' data-options='width: 100,required:true' ></td>"
        +"<td><input id='reusereason"+len+"' name='tQhWfnoequipmentEntity["+(len-1)+"].reusereason' class='easyui-validatebox' required='true' style='width:150px;;'></td>"

        +"<td><input id='personofsafe"+len+"' name='tQhWfnoequipmentEntity["+(len-1)+"].personofsafe' class='easyui-validatebox' required='true' style='width:150px;;'></td>"
        +"<td><input id='telephoneofsafe"+len+"' name='tQhWfnoequipmentEntity["+(len-1)+"].telephoneofsafe' class='easyui-validatebox' required='true' style='width:150px;;' onblur='checkMobile(this)'></td>"
        +"<td><input type='image' src='"+ctx+"/static/images/deleteRow.png' onclick='deltr("+len+");return false;'/></td>"+"</tr>");
    $.parser.parse($("#buildprojectApplyItemTable"));
    $("#equipmentname"+len).val('');
    $("#manufacturer"+len).val('');
    $("#model"+len).val('');
    $("#dateofproduction"+len).val('');
    $("#use"+len).val('');
    $("#maximumtubevoltage"+len).val('');
    $("#maximumcurrent"+len).val('');
    $("#rayspecies"+len).val('');
    $("#stopdate"+len).val('');
    $("#reusedate"+len).val('');
    $("#reusereason"+len).val('');
    $("#personofsafe"+len).val('');
    $("#telephoneofsafe"+len).val('');

    $("#batchImport").linkbutton('disable');
});
//創建下拉查詢條件
    $.ajax({
        url: ctx+"/system/dict/getDictByType/audit_status",
        //dataType:"json",
        type: "GET",
        success: function (data) {
            //绑定第一个下拉框
            $("#qysjzt").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({value: '', label: '請選擇'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });
});
//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("clear");
}
//刪除一筆
function deltr(index) {
    debugger;
    var b=$("#buildprojectApplyItemTable tr").length;
    $("#"+index).remove();
    for(var i=index+1,j=index;i<=b;i++,j++){
        $("#"+i).replaceWith("<tr align='center' id="+(i-1)+">"
            +"<td>"+(i-1)+"</td>"
            // +"<td><input id='costname"+(i-1)+"' name='tQhWfbuildprojectitemEntity["+(i-2)+"].costname' required='true' class='easyui-validatebox' style='width:150px;;'></td>"
            // +"<td><input id='costno"+(i-1)+"' name='tQhWfbuildprojectitemEntity["+(i-2)+"].costno' required='true' class='easyui-validatebox' style='width:150px;;'></td>"

            +"<td><input id='equipmentname"+(i-1)+"' name='tQhWfnoequipmentEntity["+(i-2)+"].equipmentname' class='easyui-validatebox' required='true' style='width:150px;'></td>"
            +"<td><input id='manufacturer"+(i-1)+"' name='tQhWfnoequipmentEntity["+(i-2)+"].manufacturer' class='easyui-validatebox' required='true' style='width:150px;'></td>"
            +"<td><input id='model"+(i-1)+"' name='tQhWfnoequipmentEntity["+(i-2)+"].model' class='easyui-validatebox' required='true' style='width:150px;'></td>"
            +"<td><input id='dateofproduction"+(i-1)+"' name='tQhWfnoequipmentEntity["+(i-2)+"].dateofproduction' class='easyui-my97'  datefmt='yyyy-MM-dd' data-options='width: 100,required:true' ></td>"
            +"<td><input id='use"+(i-1)+"' name='tQhWfnoequipmentEntity["+(i-2)+"].use' class='easyui-validatebox' required='true' style='width:150px;'></td>"
            +"<td><input id='maximumtubevoltage"+(i-1)+"' name='tQhWfnoequipmentEntity["+(i-2)+"].maximumtubevoltage' class='easyui-validatebox' required='true' style='width:150px;'></td>"
            +"<td><input id='maximumcurrent"+(i-1)+"' name='tQhWfnoequipmentEntity["+(i-2)+"].maximumcurrent' class='easyui-validatebox' required='true' style='width:150px;'></td>"
            +"<td><input id='rayspecies"+(i-1)+"' name='tQhWfnoequipmentEntity["+(i-2)+"].rayspecies' class='easyui-validatebox' required='true' style='width:150px;'></td>"
            +"<td><input id='stopdate"+(i-1)+"' name='tQhWfnoequipmentEntity["+(i-2)+"].stopdate' class='easyui-my97'  datefmt='yyyy-MM-dd' data-options='width: 100,required:true' ></td>"
            +"<td><input id='reusedate"+(i-1)+"' name='tQhWfnoequipmentEntity["+(i-2)+"].reusedate' class='easyui-my97'  datefmt='yyyy-MM-dd' data-options='width: 100,required:true' ></td>"
            +"<td><input id='reusereason"+(i-1)+"' name='tQhWfnoequipmentEntity["+(i-2)+"].reusereason' class='easyui-validatebox' required='true' style='width:150px;'></td>"

            +"<td><input id='personofsafe"+(i-1)+"' name='tQhWfnoequipmentEntity["+(i-2)+"].personofsafe' class='easyui-validatebox' required='true' style='width:150px;'></td>"
            +"<td><input id='telephoneofsafe"+(i-1)+"' name='tQhWfnoequipmentEntity["+(i-2)+"].telephoneofsafe' class='easyui-validatebox' required='true' style='width:150px;'></td>"

            +"<td><input type='image' src='/static/images/deleteRow.png' onclick='deltr("+(i-1)+");return false;'/></td>"+"</tr>");
    }
    if (b == 2) {
        $("#batchImport").linkbutton('enable');
    }
}

function operation(value, row, index) {
    return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('詳情',ctx+'/tqhwfnoequipmentdesterilize/view/" + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
};

//弹窗增加
function add() {
    d=$("#dlg").dialog({
        title: '添加報停射線裝置恢復使用申請表',
        width: 380,
        height: 380,
        href:ctx+'/tqhwfnoequipmentdesterilize/create',
        maximizable:true,
        modal:true,
        buttons:[{
            text:'确认',
            handler:function(){
                $("#mainform").submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}

//删除
function del(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data){
        if (data){
	$.ajax({
                type:'get',
                url:ctx+"/tqhwfnoequipmentdesterilize/delete/"+row.dealno,
                success: function(data){
                    successTip(data,dg);
                }
            });
        }
    });
}

//弹窗修改
function upd(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    d=$("#dlg").dialog({
        title: '修改報停射線裝置恢復使用申請表',
        width: 380,
        height: 340,
        href:ctx+'/tqhwfnoequipmentdesterilize/update/'+row.dealno,
        maximizable:true,
        modal:true,
        buttons:[{
            text:'修改',
            handler:function(){
                $('#mainform').submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}
//创建查询对象并查询
function cx(){
    var obj=$("#searchFrom").serializeObject();
    dg.datagrid('load',obj);
}

function saveInfo(flag) {
    if (flag == '2') {
        var isValid = $("#mainform").form('validate');
        if (!isValid) {
            return;
        }
        if(($("#zchargeno").val()==null||$("#zchargeno").val()=='')&&($("#zcchargeno").val()==null||$("#zcchargeno").val()=='')){
            alert("至少簽核至處級主管及以上主管");
            return;
        }
    }
    $.ajax({
        url: ctx+'/tqhwfnoequipmentdesterilize/create/' + flag,
        type: 'POST',
        beforeSend: ajaxLoading,
        //dataType: 'json',
        data: $('#mainform').serialize(),
        success: function (data) {
            ajaxLoadEnd();
            successTip(data, dg, d);
            window.parent.mainpage.mainTabs.closeCurrentTab();
        }
    });
}
//validatebox驗證擴展
$.extend($.fn.validatebox.defaults.rules, {
    endTime: {
        validator: function (value, param) {
            var startDate = $('#buildcyclebegindate').datebox('getValue');
            var startTmp = new Date(startDate.replace(/-/g, "/"));
            var endTmp = new Date(value.replace(/-/g, "/"));
            return startTmp <= endTmp;
        },
        message: '结束时间要大于开始时间！'
    }
});

//驗證申請人聯繫電話
function valdApplyTel(obj){
    var phoneL=$.trim($("#dealtel").val()).split("").length;
    var tel = obj.value;
    if(tel!=""){
        var temp = tel;
        var regex  = /^[0-9]{3}\+[0-9]{5}$/;
        var patt1=new RegExp(regex);
        var result = patt1.test(tel);
        if(!result){
            alert("分機格式有誤!");
            obj.value="";
            obj.focus();
            return;
        }
    }
}
//驗證手機號碼
function checkMobile(obj) {
    var
        re = /^1\d{10}$/

    if (obj.value!="")
    {
        if (re.test(obj.value)) {
            //alert("正确");
        } else {
            alert("聯繫方式格式有誤！");
            obj.value="";
            obj.focus();
            return;
        }

    }
}

//驗證郵箱
function valdEmail(obj){
    var email = obj.value;
    var temp = email;
    if(email=="")return;
    var regex = /\w+([-/+.]\w+)*@(mail.foxconn.com|foxconn.com|fii-foxconn.com)$/;
    var patt1=new RegExp(regex);
    var result = patt1.test(email);
    if(!result){
        alert("郵箱的格式不正確!");
        obj.value="";
        //obj.focus();
        obj.value =temp;
        return;
    };
}
//驗證數字
function valdMoney(obj){
    var money = obj.value;
    var temp = money;
    if(money=="")return;
    var regex = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
    var patt1=new RegExp(regex);
    var result = patt1.test(money);
    if(!result){
        alert("格式不正確!");
        obj.value="";
        //obj.focus();
        obj.value =temp;
        return;
    };
}
//根據工號查出當前申請人的信息
function queryUserInfo() {
    var empno = $.trim($("#dealno").val().toUpperCase());
    if (empno != null && empno != "") {
        $.ajax({
            url: ctx+'/system/user/getUserInfo/',
            type: 'POST',
            beforeSend: ajaxLoading,
            //dataType: 'json',
            data: {empno: empno},
            success: function (data) {
                ajaxLoadEnd();
                if (!data) {
                    $.messager.alert("溫馨提示", "工號不存在", "error");
                    $('#dealno').val('');
                    $('#dealname').val('');
                    $('#dealdeptno').val('');
                    $('#dealdeptname').val('');
                    $('#dealemail').val('');
                } else {
                    $('#dealname').val(data.empname);
                    $('#dealdeptno').val(data.deptno);
                    $('#dealdeptname').val(data.deptname);
                    $('#dealfactoryid').combobox('setValue', data.factoryid);
                    $('#dealemail').val(data.email);
                }
            }
        });
    }
}
//附件上傳
function uploadFile() {
    var attachIds = $.trim($("#attachids").val());
    var attachIdsL = attachIds.split(",").length;
    if (attachIdsL >= 6) {
        alert("上傳附件個數不能超過五個！");
        return;
    }
    // 创建
    var form_data = new FormData();
    // 获取文件
//        var file = document.getElementById('fileToUpload').files[0];
    var file_data = $("#attachidsUpload").prop("files")[0];
    form_data.append("file", file_data);
    form_data.append("path", "test");
    // 把所以表单信息
    $.ajax({
        type: "POST",
        url: ctx+"/admin /upload",
        beforeSend: ajaxLoading,
//            dataType : "json",
        processData: false,  // 注意：让jQuery不要处理数据
        contentType: false,  // 注意：让jQuery不要设置contentType
        data: form_data
    }).success(function (msg) {
        ajaxLoadEnd();
        $("#dowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href='+ctx+'"/admin /download/' + msg.id + '">' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delAtt(\'' + msg.id + '\')"></div></div>');
        $("#attachids").val($("#attachids").val() + msg.id + ",");
        // $("#attachids").val(msg.id);
    }).fail(function (msg) {
    });
}

function delAtt(fileid) {
    $("#" + fileid).remove();
    var kk = $("#attachids").val();
    kk = kk.replace(fileid + ",", "");
    $("#attachids").val(kk);
    $.post(ctx+'/admin /delete/' + fileid, {}, function (data) {
        console.log(data);
        successTip(data);
    });
}
function reUploadFile() {
    var attachIds = $.trim($("#reattachids").val());
    var attachIdsL = attachIds.split(",").length;
    if (attachIdsL >= 6) {
        $.messager.alert("溫馨提示", "上傳附件個數不能超過五個！", "info");
        return;
    }
    // 把所以表单信息
    $.ajaxFileUpload({  //Jquery插件上传文件
        url: ctx + "/admin/uploadCompatible",
        secureuri: false,//是否启用安全提交  默认为false
        fileElementId: "attachidsUpload", //type="file"的id
        dataType: "json",  //返回值类型
        success: function (msg) {
            // alert(msg);
            // msg = eval(msg);vardata = eval("(" + data1 + ")");
            msg = jQuery.parseJSON(msg);
            $("#dowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href=' + ctx + '/admin/download/' + msg.id + '>' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delReAtt(\'' + msg.id + '\')"></div></div>');
            $("#attachids").val($("#attachids").val() + msg.id + ",");
        },

    });
}

function delReAtt(fileid) {
    $("#" + fileid).remove();
    var kk = $("#reattachids").val();
    kk = kk.replace(fileid + ",", "");
    $("#reattachids").val(kk);
    $.post(ctx+'/admin /delete/' + fileid, {}, function (data) {
        successTip(data);
    });
}
//function reUploadFile() {
//    var attachIds = $.trim($("#reattachids").val());
//    var attachIdsL = attachIds.split(",").length;
//    if (attachIdsL >= 6) {
//        alert("上傳附件個數不能超過五個！");
//        return;
//    }
//    // 创建
//    var form_data = new FormData();
//    // 获取文件
////        var file = document.getElementById('fileToUpload').files[0];
//    var file_data = $("#attachidsUpload").prop("files")[0];
//    form_data.append("file", file_data);
//    form_data.append("path", "test");
//    // 把所以表单信息
//    $.ajax({
//        type: "POST",
//        url: ctx+"/admin /upload",
//        beforeSend: ajaxLoading,
////            dataType : "json",
//        processData: false,  // 注意：让jQuery不要处理数据
//        contentType: false,  // 注意：让jQuery不要设置contentType
//        data: form_data
//    }).success(function (msg) {
//        ajaxLoadEnd();
//        $("#reDowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href='+ctx+'"/admin /download/' + msg.id + '">' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delReAtt(\'' + msg.id + '\')"></div></div>');
//        $("#reattachids").val($("#reattachids").val() + msg.id + ",");
//        // $("#attachids").val(msg.id);
//    }).fail(function (msg) {
//    });
//}
//
//function delReAtt(fileid) {
//    $("#" + fileid).remove();
//    var kk = $("#reattachids").val();
//    kk = kk.replace(fileid + ",", "");
//    $("#reattachids").val(kk);
//    $.post(ctx+'/admin /delete/' + fileid, {}, function (data) {
//        successTip(data);
//    });
//}
//導入
function btnUploadExcel() {
    //deleteAllRow("buildprojectApplyItemTable");
    var table = document.getElementById("buildprojectApplyItemTable");
    var len = table.rows.length;
    if (len>1)
    {
        for (var i = 0; i < len-1 ; i++) {
                table.deleteRow(len-i-1);
        }
    }


    var file_data = $("#batchFile").prop("files")[0];
    if (file_data.name == "") {
        $.messager.alert('系統提示', '請選擇文件，再進行導入！', 'info');
        return;
    }
    var suffix = /\.[^\.]+$/.exec(file_data.name);
    var isExcel2003 = "";
    if (suffix == ".xls") {
        isExcel2003 = "true";
    } else if (suffix == ".xlsx") {
        isExcel2003 = "false";
    } else {
        $.messager.alert('系統提示', '導入文件格式不正確！', 'info');
        return;
    }
    // 创建
    var form_data = new FormData();
    // 获取文件
    form_data.append("file", file_data);
    form_data.append("isExcel2003", isExcel2003);

    // 把所以表单信息
    $.ajax({
        type: "POST",
        url: ctx+"/tqhwfnoequipmentdesterilize/upload",
//            dataType : "json",
        processData: false,  // 注意：让jQuery不要处理数据
        contentType: false,  // 注意：让jQuery不要设置contentType
        data: form_data
    }).success(function (msg) {
        // console.log(msg)
        if (msg == 'success') {
            $("#labelListAddResult").html("導入成功");


            document.getElementById("add").setAttribute("disabled", true);
                //加載導入成功的數據
            $.ajax({
                type: "get",
                url:ctx+'/tqhwfnoequipmentdesterilize/importView',
                cache: false,
                dataType : "json",
                success:function(data){
                    var item;
                    $.each(data,function(i,result){

                        item =
                            "<tr><td>"+(i+1)+"</td>"
                             +"<td>"+result['equipmentname']+"</td>"
                             +"<td>"+result['manufacturer']+"</td>"
                             +"<td>"+result['model']+"</td>"
                             +"<td>"+crtTimeFtt(result['dateofproduction'])+"</td>"
                             +"<td>"+result['use']+"</td>"
                             +"<td>"+result['maximumtubevoltage']+"</td>"
                             +"<td>"+result['maximumcurrent']+"</td>"
                             +"<td>"+result['rayspecies']+"</td>"
                             +"<td>"+crtTimeFtt(result['stopdate'])+"</td>"
                             +"<td>"+crtTimeFtt(result['reusedate'])+"</td>"
                             +"<td>"+result['reusereason']+"</td>"
                             +"<td>"+result['personofsafe']+"</td>"
                             +"<td>"+result['telephoneofsafe']+"</td>"
                             +"<td>  </td>"
                            +"</tr>";
                        $('#buildprojectApplyItemTable').append(item);
                    });
                }
            });




            $("#downloadError").hide();
        }else{
            if(msg =='2') {
                $("#labelListAddResult").html("導入失敗, ");
                $("#downloadError").show();
            }else{
                $("#labelListAddResult").html(msg);
            }
        }
    }).fail(function (msg) {
        $("#labelListAddResult").html("導入失敗");
    });
}

//查看錯誤信息
function downloadError(){
    // document.location.href = '/tqhwfnoequipmentdesterilize/downLoad/errorExcel';
}

//批量導入模板下載
function batchImportTpl(){
    document.location.href = ctx+'/tqhwfnoequipmentdesterilize/downLoad/batchImportTpl';
}

function openBatchImportWin(){
    var optionWin = $("#optionWin").window();

    optionWin.window({
        center : true
    });
    //顯示於畫面中
    optionWin.slideDown();
    //清空optionWin頁面中所有元件的值
    /*     $("#pointForm .easyui-combobox").each(function(){
     $(this).combobox("clear");
     }); */
    $("#batchImportForm").form("clear");
    $("#labelListAddResult").html("");
    $("#downloadError").hide();
    //optionWin.find("input").val("");
    optionWin.window("open");
}
//导出excel
function exportExcel(){
    var form = document.getElementById("searchFrom");
    searchFrom.action=ctx+'/tqhwfnoequipmentdesterilize/exportExcel';
    form.submit();
}
//刪除table處表頭之外的所有行
function deleteAllRow(tableName){

    var table = document.getElementById(tableName);
    var len = table.rows.length;
    for(var i = 0;i < len-1;i++){
        table.deleteRow(i+1);
    }
}
//格式化日期
function crtTimeFtt(val) {
    if (val != null) {
        var date = new Date(val);
        return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
    }
}
