package com.foxconn.ipebg.basics.controller;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Date;

import com.foxconn.ipebg.basics.entity.*;
import com.foxconn.ipebg.basics.service.*;
import com.foxconn.ipebg.common.entity.DataEntity;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.basics.entity.PerInfoEntity;

//0422
import com.foxconn.ipebg.basics.service.PerInfoService;
import com.foxconn.ipebg.report.service.AbsentReportService;
import com.foxconn.ipebg.system.utils.CommonUtils;
import com.foxconn.ipebg.system.utils.Excel2007Utils;
import com.foxconn.ipebg.system.utils.FileDownloadUtil;
import com.foxconn.ipebg.system.utils.ImportExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import com.foxconn.ipebg.system.entity.User;


/**
 * 合格人員
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-16 10:12:01
 */
@Controller
@RequestMapping("perinfo")
public class PerInfoController extends BaseController {

    @Autowired
    private PerInfoService perInfoService;
   
    //0419新增
    @Autowired
	private AbsentReportService absentReportService;
	
	@Autowired
	//private BsArrangeService bsArrangeService;
	
	private BsSecPerService bsSecPerService;
	
	@Autowired
	private BsTraineeService bsTraineeService;

	private static final String TPLFILEPATH = "/static/resources/templet";

	private static final String BATCHIMPORTTPL = "perinfo.xlsx";

	private static final String filePath = "/static/resources/download";
    
	//0419新增
    /**
      * 方法描述: 列表信息
      * @Author: S6113712
      * @CreateDate:   2019-04-16 10:12:01
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:perinfo:list")
    public String list() {
        //查询列表数据
        return "basics/perinfo/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6113712
      * @CreateDate:   2019-04-16 10:12:01
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:perinfo:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<PerInfoEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        page = perInfoService.search(page, filters);
        return getEasyUIData(page);
    }
    /**
     * 方法描述: 保存
     * @Author: S6113712
     * @CreateDate:   2019-04-16 10:12:01
     * @Return
     **/
   
    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("basics:perinfo:add")
    @ResponseBody
    public String create(@Valid PerInfoEntity perInfo, Model model) {
    	try {
			perInfo.setNewRecord(true);
			perInfo.setCreateDate(new Date());
			perInfoService.save(perInfo);
		} catch (Exception e) {
			return "該人員已存在";
		}
        return "success";
    }
    
    /**
      * 方法描述: 添加跳轉
      * @Author: S6113712
      * @CreateDate:   2019-04-16 10:12:01
      * @Return
      **/

    @RequiresPermissions("basics:perinfo:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("perInfo", new PerInfoEntity());
        model.addAttribute("action", "create");
        return "basics/perinfo/listForm";
    }
    
     /**
     * 方法描述: 修改跳轉
     * @Author: S6113712
     * @CreateDate:   2018-10-06 11:04:39
     * @Return
     **/

   @RequiresPermissions("basics:perinfo:update")
   @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
   public String updateForm(@PathVariable("id") String id, Model model) {
       model.addAttribute("perInfo", perInfoService.get(id));
       model.addAttribute("action", "update");
      return "basics/perinfo/listForm";
   }
   
	/**
	 * 方法描述: 修改
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-11 10:55:00
	 * @Return
	 **/

   @RequestMapping(value = "update", method = RequestMethod.POST)
   @RequiresPermissions("basics:perinfo:update")
   @ResponseBody
   public String update(@Valid PerInfoEntity perInfo,@RequestParam(value = "ids") String ids) {
	   perInfo.setId(ids);
	   perInfo.setUpdateDate(new Date());
	   perInfoService.update(perInfo);
	   perInfo.setUpdateBy(UserUtil.getCurrentUser().getLoginName());
	   perInfoService.update(perInfo);
	   
       return "success";
   }
    /**
      * 方法描述: 根據主鍵刪除
      * @Author: S6113712
      * @CreateDate:   2019-04-16 10:12:01
      * @Return
      **/

    @RequestMapping("delete/{id}")
    @RequiresPermissions("basics:perinfo:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
    	PerInfoEntity per = perInfoService.get(id);
    	per.setDelFlag(DataEntity.DEL_FLAG_DELETE);
    	perInfoService.save(per);
//        perInfoService.delete(id);
        return "success";
    }
    /**
      * 导出excel
      * @Author: S6113712
      * @CreateDate:   2019-04-16 10:12:01
      * @Return
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {}
	
    /**
	 * 方法描述: 獲取警衛人員信息
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-03-08 10:34:27
	 * @Return
	 **/
    @RequestMapping(value = "getPerInfo/{empNo}", method = RequestMethod.GET)
	@ResponseBody
	public PerInfoEntity getPerInfo(@PathVariable("empNo") String empNo) {
    	PerInfoEntity perInfo = perInfoService.findByEmpNo(empNo);
		return perInfo;
	}


//0419新增


/**
 * 方法描述: 下載批量上傳模板
 * 
 * @Author: S6113712
 * @CreateDate: 2018-10-22 18:57:46
 * @Return
 **/

@RequestMapping(value = "downLoad/batchImportTpl", method = RequestMethod.GET)
// @RequiresPermissions("other:tqhraymachinescrap:add")
//@ResponseBody
public void batchImportTpl(HttpServletRequest request,
		HttpServletResponse response) {
	fileDownload(request, response, BATCHIMPORTTPL);
}

private void fileDownload(HttpServletRequest request,
		HttpServletResponse response, String fileName) {
	String path = request.getSession().getServletContext()
			.getRealPath(TPLFILEPATH);
	try {
		FileDownloadUtil.fileDownload(request, response, path, fileName);// 下載點位信息模板
	} catch (IOException e) {
		e.printStackTrace();
	}
}

/**
 * 方法描述: 下載批量上傳異常信息
 * 
 * @Author: S6113712
 * @CreateDate: 2019-02-13 18:57:46
 * @Return
 **/

@RequestMapping(value = "downLoad/errorExcel", method = RequestMethod.GET)
// @RequiresPermissions("other:tqhraymachinescrap:add")
@ResponseBody
public void errorExcelDownload(HttpServletRequest request,
		HttpServletResponse response) throws IOException {

	List<PerInfoErrorDto> errorDtos = (List<PerInfoErrorDto>) request
			.getSession().getAttribute(
					PerInfoService.PERINFOERRORDTOS);
	if (errorDtos != null) {
		// 導出excel2007
		LinkedHashMap<String, String> map = this
				.getErrorFieldMap();

		String name = this.getCurUser().getLoginName() + "In";
		String tplPath = request.getSession().getServletContext()
				.getRealPath(filePath);
		File file = Excel2007Utils.createFile(errorDtos, map, tplPath,
				name, null);// 生成xlsx文件
		// long endOfBuildExcel = System.currentTimeMillis();
		// System.out.println("生成excel："+(endOfBuildExcel -
		// endOfLoadData)+"毫秒");
		String fileName = file.getName();
		Excel2007Utils.exportFile(response, tplPath, fileName);// 下载生成的xlsx文件
		request.getSession().removeAttribute(
				PerInfoService.PERINFOERRORDTOS);
	}

														 
}

/**
 * 批量導入
 * 
 * @param file
 *            文檔
 * @param isExcel2003
 *            是否2003
 * @return
 */
@RequestMapping(value = "upload", method = RequestMethod.POST)
@ResponseBody
public String upload(MultipartFile file, boolean isExcel2003, Model model,
		HttpServletRequest request) {

	if (file.equals("") || file.getSize() <= 0) {
		return Constant.FAIL;
	} else {
		String result = null;
		try {

//			result = artificialAuditService.batchImport(
//					file.getInputStream(), isExcel2003);
			result = this.batchImport(
					file.getInputStream(), isExcel2003);

		} catch (IOException e) {
			e.printStackTrace();
		}

		if (Constant.RESULT.CODE_YES.getValue().equals(result)) {
			return Constant.SUCCESS;
		} else {
			return result;
		}
	}

}

// *******************************導入相關邏輯**********
	/**
	 * 批量導入
	 * 
	 * @param in
	 *            excel文件輸入流
	 * @param isExcel2003
	 *            是否excel2003
	 * @return 是否成功
	 */

	public String batchImport(InputStream in, boolean isExcel2003) {
		try {

			List<PerInfoErrorDto> perinfoErrorDtos=null;
			ImportExcelUtil poi = new ImportExcelUtil();
			int success = 0, failed = 0;
			// 讀取上傳的excel file
			List<List<String>> list = poi.read(in, isExcel2003);
			// 判断需要的字段在Excel中是否都存在
			boolean isExist = true;
			Map<String, String> fieldMap = getFieldMap();
			Map<String, Integer> fieldIndexMap = new HashMap();
			List<String> excelFieldList = list.get(0);
			// 第一列是序號不需要
			for (int i = 1; i < excelFieldList.size(); i++) {
				if (!fieldMap.containsKey(excelFieldList.get(i))) {
					isExist = false;
					break;
				}
				fieldIndexMap.put(fieldMap.get(excelFieldList.get(i)), i);
			}
			// 如果有列名不存在，则抛出异常，提示错误
			if (!isExist) {
				return "有列名不存在，請對照模板";
			}
			//導入數量限制
//			if (list.size()-1 > 10) {
//				return "資料導入不能大於100筆;";
//			}
			//開始導入
			 perinfoErrorDtos = listToPerInfoErrorDto(
					list, 1, fieldIndexMap);
			
			if (perinfoErrorDtos.size()>0)
			{
				UserUtil.getSession().setAttribute(PerInfoService.PERINFOERRORDTOS,perinfoErrorDtos);
			  return "導入成功"+(list.size()-1-perinfoErrorDtos.size())+"筆，失敗"+perinfoErrorDtos.size()+"筆!";	
			}


		} catch (Exception e) {
			this.logger.info(e.getMessage(), e);
			return Constant.RESULT.CODE_NO.getValue();
		}
		return Constant.RESULT.CODE_YES.getValue();
	}

	public HashMap<String, String> getFieldMap() {
		HashMap<String, String> fieldMap = new HashMap<String, String>();

		
		fieldMap.put("工號", "empNo");			
		fieldMap.put("姓名", "empName");
		fieldMap.put("公司", "company");	
		fieldMap.put("身份證號", "psnId");
		fieldMap.put("合格日期", "passDate");
		fieldMap.put("入職日期", "entryDate");
		//fieldMap.put("第二次稽核結果", "abnormalResult2");
		//fieldMap.put("第二次稽核原因", "abnormalCauses2");

		return fieldMap;
	}

	public LinkedHashMap<String, String> getErrorFieldMap() {
		LinkedHashMap<String, String> fieldMap = new LinkedHashMap<String, String>();

		fieldMap.put("empNo", "工號");				
//		fieldMap.put("empName", "姓名");
//		fieldMap.put("company", "公司");
//		fieldMap.put("psnId", "身份證號");
		fieldMap.put("passDate", "合格日期");
		fieldMap.put("entryDate", "入職日期");
		fieldMap.put("errorLog", "導入失敗原因");

		return fieldMap;
	}

	/**
	 * @param list
	 *            列表
	 * @param startIndex
	 *            開始的行
	 * @param fieldIndexMap
	 *            list中的index和类的英文属性的对应关系Map
	 * @return
	 * @throws IllegalAccessException
	 * @throws InstantiationException
	 */
	@Transactional
	public List<PerInfoErrorDto> listToPerInfoErrorDto(
			List<List<String>> list, int startIndex,
			Map<String, Integer> fieldIndexMap) {
		// 定义要返回的list
		List<PerInfoErrorDto> resultList = new ArrayList<PerInfoErrorDto>();
		// 将sheet转换为list
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		Date passDate = new Date();
		Date entryDate = new Date();
		for (int i = startIndex; i < list.size(); i++) {
			PerInfoErrorDto entity = new PerInfoErrorDto();
			// 新建要转换的对象
			if (CommonUtils.isValidDate(list.get(i).get(2))) {
				try {
					passDate=format.parse(list.get(i).get(2));
					entryDate = format.parse(list.get(i).get(3));
				} catch (ParseException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				//0921 改為培訓人員表獲取數據
				BsTraineeEntity bsSecPerEntity = bsTraineeService.isExistEmpInfo1(list.get(i).get(1));
                 //驗證人員是否排班
				if (bsSecPerEntity != null) {
					PerInfoEntity perInfoEntity = new PerInfoEntity();
					perInfoEntity.setEmpNo(list.get(i).get(1));
					perInfoEntity.setPassDate(passDate);
					perInfoEntity.setEntryDate(entryDate);
					perInfoEntity.setEmpName(bsSecPerEntity.getEmpName());
					perInfoEntity.setCompany(bsSecPerEntity.getCompany());
					perInfoEntity.setPsnId(bsSecPerEntity.getPsnId());
					try {
						perInfoService.save(perInfoEntity);
					} catch (Exception e) {
						entity.setEmpNo(list.get(i).get(1));
						entity.setPassDate(list.get(i).get(2));
						entity.setEntryDate(list.get(i).get(3));
						entity.setErrorLog("工號重複，請確認！");
						resultList.add(entity);
					}
				} else {//人員未排班
					 entity.setEmpNo(list.get(i).get(1));
					 entity.setPassDate(list.get(i).get(2));
					 entity.setEntryDate(list.get(i).get(3));
					 entity.setErrorLog("工號不存在或工號重複，請確認！");
					 resultList.add(entity);
				}
			} else {
				 entity.setEmpNo(list.get(i).get(1));
				 entity.setPassDate(list.get(i).get(2));
				 entity.setEntryDate(list.get(i).get(3));
				 entity.setErrorLog("日期格式錯誤！");
				 resultList.add(entity);	
			}
		}
		return resultList;
	}
	// *******************************導入相關邏輯**********
	

	/**
	 * 方法描述: 獲取排班群組人员信息（匹配排班群組）
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-09-15 18:34:27
	 * @Return
	 **/
	@RequestMapping(value = "getPersonList", method = RequestMethod.GET)
	@ResponseBody
	public List<PerInfoEntity> getPersonList(
			@RequestParam("empNo") String empNo,
			@RequestParam("empName") String empName) {


		PerInfoEntity perInfo = new PerInfoEntity();
		perInfo.setEmpName(empName);
		perInfo.setEmpNo(empNo);

		String coms = "";
		User user = UserUtil.getCurrentUser();
		if (user != null && user.getSecurityCom() != null) {
			coms = user.getSecurityCom().replaceAll(",", "','");
		}

		List<PerInfoEntity> personList = perInfoService
				.findPersonList(perInfo, coms);
		return personList;
	}
}
