package com.foxconn.ipebg.basics.controller;

import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import com.foxconn.ipebg.common.utils.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.basics.entity.BsTraineeEntity;
import com.foxconn.ipebg.basics.entity.BsTrainingPeriodEntity;
import com.foxconn.ipebg.basics.entity.BsTrainingPersonEntity;
import com.foxconn.ipebg.basics.service.BsTraineeService;
import com.foxconn.ipebg.basics.service.BsTrainingPeriodService;
import com.foxconn.ipebg.basics.service.BsTrainingPersonService;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;
import com.foxconn.ipebg.system.entity.User;

/**
 * 培訓期培訓人員明細基本資料
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-08-29 10:42:48
 */
@Controller
@RequestMapping("bstrainingperson")
public class BsTrainingPersonController extends BaseController {

    @Autowired
    private BsTrainingPersonService bsTrainingPersonService;
    

    @Autowired
    private BsTraineeService bsTraineeService;
    

    @Autowired
    private BsTrainingPeriodService bsTrainingPeriodService;
   
    /**
      * 方法描述: 列表信息
      * @Author: S6113712
      * @CreateDate:   2019-08-29 10:42:48
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:bstrainingperson:list")
    public String list() {
        //查询列表数据
        return "basics/bstrainingperson/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6113712
      * @CreateDate:   2019-08-29 10:42:48
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:bstrainingperson:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
    	
    	String trainingPeriodNos=request.getParameter("trainingPeriodNosHide")==null?"":request.getParameter("trainingPeriodNosHide");
		List<PropertyFilter> filters = PropertyFilter
				.buildFromHttpRequest(request);
		if (!trainingPeriodNos.equals(""))
		{
	    	List<String> trainingPeriodNoList = Arrays.asList(trainingPeriodNos.split(","));
			PropertyFilter filter = new PropertyFilter("INS_trainingPeriodNo", trainingPeriodNoList );
			filters.add(filter);
		}
        User user = UserUtil.getCurrentUser();
        if (user != null) {
            // 按保安公司查询
            PropertyFilter filter = new PropertyFilter("INS_company", user.getSecurityComs());
            filters.add(filter);
        }
        Page<BsTrainingPersonEntity> page = getPage(request);
        //List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
//        page.setOrderBy("createDate");
//  		page.setOrder("desc");
        page = bsTrainingPersonService.search(page, filters);
        return getEasyUIData(page);
    }
    /**
     * 方法描述: 保存
     * @Author: S6113712
     * @CreateDate:   2019-08-29 10:42:48
     * @Return
     **/
   
    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("basics:bstrainingperson:add")
    @ResponseBody
    public String create( HttpServletRequest request,Model model) {
    	
    	String trainingPeriodNo= request.getParameter("trainingPeriodNo");
    	String empNos= request.getParameter("empNos");
    	List<BsTraineeEntity> BsTraineeEntities=bsTraineeService.findPersonListByEmpNos(empNos);
    	BsTrainingPeriodEntity bsTrainingPeriodEntity=bsTrainingPeriodService.findByTrainingPeriodNo(trainingPeriodNo);
    	for ( BsTraineeEntity bsTraineeEntity :BsTraineeEntities)
    	{
    		BsTrainingPersonEntity bsTrainingPerson=new BsTrainingPersonEntity();
    		bsTrainingPerson.setTrainingPeriodNo(trainingPeriodNo);
//    		bsTrainingPerson.setTrainingPeriodName(bsTrainingPeriodEntity.getTrainingPeriodName());
//    		bsTrainingPerson.setStartDate(bsTrainingPeriodEntity.getStartDate());
//    		bsTrainingPerson.setEndDate(bsTrainingPeriodEntity.getEndDate());
//    		bsTrainingPerson.setStartTime(bsTrainingPeriodEntity.getStartTime());
//    		bsTrainingPerson.setEndTime(bsTrainingPeriodEntity.getEndTime());
    		 
    		bsTrainingPerson.setEmpNo(bsTraineeEntity.getEmpNo());
//    		bsTrainingPerson.setEmpName(bsTraineeEntity.getEmpName());
//    		bsTrainingPerson.setPsnId(bsTraineeEntity.getPsnId());
//    		bsTrainingPerson.setCompany(bsTraineeEntity.getCompany());
	        bsTrainingPerson.setNewRecord(true);
	    	bsTrainingPerson.setCreateDate(new Date());
	        bsTrainingPersonService.save(bsTrainingPerson);
    	}
        return "success";
    }
    
    /**
      * 方法描述: 添加跳轉
      * @Author: S6113712
      * @CreateDate:   2019-08-29 10:42:48
      * @Return
      **/

    @RequiresPermissions("basics:bstrainingperson:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("bsTrainingPerson", new BsTrainingPersonEntity());
        model.addAttribute("action", "create");
        return "basics/bstrainingperson/listForm";
    }
    /**
     * 方法描述: 添加跳轉
     * @Author: S6113712
     * @CreateDate:   2019-08-29 10:42:48
     * @Return
     **/

   @RequiresPermissions("basics:bstrainingperson:add")
   @RequestMapping(value = "createNew", method = RequestMethod.GET)
   public String createFormNew(Model model) {
       //model.addAttribute("bsTrainingPerson", new BsTrainingPersonEntity());
      //model.addAttribute("action", "create");
       return "basics/bstrainingperson/addTrainPersonList";
   }
     /**
     * 方法描述: 修改跳轉
     * @Author: S6113712
     * @CreateDate:   2018-10-06 11:04:39
     * @Return
     **/

   @RequiresPermissions("basics:bstrainingperson:update")
   @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
   public String updateForm(@PathVariable("id") String id, Model model) {
       model.addAttribute("bsTrainingPerson", bsTrainingPersonService.get(id));
       model.addAttribute("action", "update");
      return "basics/bstrainingperson/listForm";
   }
   
	/**
	 * 方法描述: 修改
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-11 10:55:00
	 * @Return
	 **/

   @RequestMapping(value = "update", method = RequestMethod.POST)
   @RequiresPermissions("basics:bstrainingperson:update")
   @ResponseBody
   public String update(@Valid BsTrainingPersonEntity bsTrainingPerson,@RequestParam(value = "ids") String ids) {
	   bsTrainingPerson.setId(ids);
	   bsTrainingPerson.setUpdateDate(new Date());
	   bsTrainingPersonService.update(bsTrainingPerson);
	   
       return "success";
   }
    /**
      * 方法描述: 根據主鍵刪除
      * @Author: S6113712
      * @CreateDate:   2019-08-29 10:42:48
      * @Return
      **/

    @RequestMapping("delete/{ids}")
    @RequiresPermissions("basics:bstrainingperson:delete")
    @ResponseBody
    public String delete(@PathVariable("ids") String ids) {
    	String[] idArray =ids.split(",");
    	for (int i=0;i<idArray.length;i++)
    	{
    		 bsTrainingPersonService.delete(idArray[i]);
    	}    
        return "success";
    }
    /**
      * 导出excel
      * @Author: S6113712
      * @CreateDate:   2019-08-29 10:42:48
      * @Return
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {}
    
    /**
	 * 方法描述: 培訓人員明細表
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-01-15 18:34:27
	 * @Return
	 **/
	@RequestMapping(value = "getTraineeReportDetailList", method = RequestMethod.GET)
	@ResponseBody
	public Map<String, Object> getTraineeReportDetailList(HttpServletRequest request
		/*	@RequestParam("empNo") String empNo,
			@RequestParam("empName") String empName*/) {

		// BsTraineeEntity bsTrainee
		// HttpServletRequest request
        String empNo= request.getParameter("empNo")==null?"":request.getParameter("empNo");
        String empName=request.getParameter("empName")==null?"":request.getParameter("empName");
        String trainingPeriodNo=request.getParameter("trainingPeriodNos")==null?"":request.getParameter("trainingPeriodNos");
        String company=request.getParameter("result")==null?"":request.getParameter("result");
		 
		BsTrainingPersonEntity bsTrainingPerson = new BsTrainingPersonEntity();
		bsTrainingPerson.setEmpName(empName);
		bsTrainingPerson.setEmpNo(empNo);
		bsTrainingPerson.setTrainingPeriodNo(trainingPeriodNo);
		bsTrainingPerson.setCompany(company);

        Page<BsTrainingPersonEntity> page = getPage(request);
		page = bsTrainingPersonService
				.getTraineeReportDetailList(page, bsTrainingPerson);
		return getEasyUIData(page);
	}
    /**
	 * 方法描述: 培訓人員明細表進入
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-09-17 18:34:27
	 * @Return
	 **/  
	  @RequestMapping(value = "traineeReportDetailList", method = RequestMethod.GET)
    //@RequiresPermissions("basics:bstrainingperson:list")
    public String traineeReportDetailList() {
        //查询報表數據
        return "basics/bstrainingperson/traineeReportList";
    }

}
