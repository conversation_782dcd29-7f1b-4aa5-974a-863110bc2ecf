package com.foxconn.ipebg.system.dao;

import com.foxconn.ipebg.system.entity.TPubAreabaseinfoEntity;
import org.springframework.stereotype.Repository;
import com.foxconn.ipebg.common.persistence.HibernateDao;

import java.util.List;

/**
 * 各廠區樓棟及區域對應關係表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-11-27 09:33:14
 */
@Repository
public class TPubAreabaseinfoDao extends HibernateDao<TPubAreabaseinfoEntity,String> {
    /**
      * 方法描述: 查詢區域樓棟信息
      * @Author: S6114648
      * @CreateDate:   2018/11/27  上午 11:06
      * @Return
      **/

	public List<TPubAreabaseinfoEntity> getAreaInfoBySuperId(String superId){
	    return this.find("from TPubAreabaseinfoEntity t where t.status='Y' and t.superid=?0 order by t.orderby",superId);
    }
}
