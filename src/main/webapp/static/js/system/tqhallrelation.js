var dg;
var d;
$(function () {
    dg = $('#dg').datagrid({
        method: "get",
        url: ctx + '/tqhallrelation/list',
        fit: true,
        fitColumns: true,
        border: false,
        idField: 'id',
        striped: true,
        pagination: true,
        rownumbers: true,
        pageNumber: 1,
        pageSize: 20,
        pageList: [10, 20, 30, 40, 50],
        singleSelect: true,
        columns: [[
            {field: 'id', title: '主鍵', hidden: true},
            {field: 'serialno', title: '工單流水號', sortable: true, width: 100, formatter: operation},
            {field: 'createBy', title: '創建人', sortable: true, width: 100}
            , {field: 'workflowid', title: '流程編碼', sortable: true, width: 100, hidden: true},
            {field: 'createDate', title: '創建時間', sortable: true, width: 100}
            /*,
            {field: 'dtoName', title: '表對應實體名稱', sortable: true, width: 100}*/
            ,
            {field: 'workstatus',
                title: '表單狀態',
                sortable: true,
                width: 100
            }
            ,
            {field: 'wfName', title: '流程名稱', sortable: true, width: 100},
            {
                field: 'operation', title: '操作', width: 100,
                formatter: function (value, rec, index) {
                    var del = '<a href="#" class="mybutton" data-options="plain:true,iconCls:\'icon-remove\'" onclick="delTransferredStaff(\'' + rec.id + '\')">删除</a>';
                    return del;
                }
            }
        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        toolbar: '#tb',
        onLoadSuccess: function (data) {
            $('.mybutton').linkbutton({text: '删除', plain: true, iconCls: 'icon-remove'});
        }
    });
});

function operation(value, row, index) {
    var actionName = "";
    $.ajax({
        url: ctx + "/wfcontroller/getActionInfo/" + row.workflowid,
        type: "GET",
        async: false,
        success: function (data) {
            actionName = data.saveaction;
        }
    });
    return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('" + row.wfName + "',ctx+'/" + actionName + "/" + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
};

function delTransferredStaff(id) {
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function (data) {
        if (data) {
            $.ajax({
                type: 'get',
                url: ctx + "/tqhallrelation/delete/" + id,
                success: function (data) {
                    successTip(data, dg);
                    $('#dg').datagrid('reload');
                }
            });
        }
    });
}

//弹窗增加
function add() {
    d = $("#dlg").dialog({
        title: '添加簽核業務中間表，保存所有簽核業務主信息',
        width: 380,
        height: 380,
        href: ctx + '/tqhallrelation/create',
        maximizable: true,
        modal: true,
        buttons: [{
            text: '确认',
            handler: function () {
                $("#mainform").submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//删除
function del() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function (data) {
        if (data) {
            $.ajax({
                type: 'get',
                url: ctx + "/tqhallrelation/delete/" + row.id,
                success: function (data) {
                    successTip(data, dg);
                }
            });
        }
    });
}

//弹窗修改
function upd() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    d = $("#dlg").dialog({
        title: '修改簽核業務中間表，保存所有簽核業務主信息',
        width: 380,
        height: 340,
        href: ctx + '/tqhallrelation/update/' + row.id,
        maximizable: true,
        modal: true,
        buttons: [{
            text: '修改',
            handler: function () {
                $('#mainform').submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//创建查询对象并查询
function cx() {
    var obj = $("#searchFrom").serializeObject();
    dg.datagrid('load', obj);
}
