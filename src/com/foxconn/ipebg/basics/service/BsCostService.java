package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.dao.BsCostDao;
import com.foxconn.ipebg.basics.entity.BsCostEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(readOnly=true)
public class BsCostService extends BaseBusinessService<BsCostEntity, String> {
    @Autowired
    private BsCostDao bsCostDao;

    @Override
    public HibernateDao<BsCostEntity, String> getEntityDao() {
        return bsCostDao;
    }


}
