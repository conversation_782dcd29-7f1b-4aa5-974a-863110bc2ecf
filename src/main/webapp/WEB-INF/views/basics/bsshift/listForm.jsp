<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>班別資料</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>

<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/bsshift/${action}" method="post">
		<table class="formTable">
		    			<tr>
				<td>資料識別ID  Primary：</td>
				<td>
				    					<input type="hidden" name="id" value="${bsShift.id}"/>
															<input id="id" name="id" class="easyui-validatebox" data-options="width: 150" value="${bsShift.id }" />
									</td>
			</tr>
						<tr>
				<td>班別代碼：</td>
				<td>
				    										<input id="shiftNo" name="shiftNo" class="easyui-validatebox" data-options="width: 150" value="${bsShift.shiftNo }" />
									</td>
			</tr>
						<tr>
				<td>班別名稱：</td>
				<td>
				    										<input id="shiftName" name="shiftName" class="easyui-validatebox" data-options="width: 150" value="${bsShift.shiftName }" />
									</td>
			</tr>
						<tr>
				<td>開始時間：</td>
				<td>
				    										<input id="startTime" name="startTime" class="easyui-validatebox" data-options="width: 150" value="${bsShift.startTime }" />
									</td>
			</tr>
						<tr>
				<td>結束時間：</td>
				<td>
				    										<input id="endTime" name="endTime" class="easyui-validatebox" data-options="width: 150" value="${bsShift.endTime }" />
									</td>
			</tr>
						<tr>
				<td>創建人：</td>
				<td>
				    										<input id="createBy" name="createBy" class="easyui-validatebox" data-options="width: 150" value="${bsShift.createBy }" />
									</td>
			</tr>
						<tr>
				<td>創建時間：</td>
				<td>
				    										<input id="createDate" name="createDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${bsShift.createDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>更新人：</td>
				<td>
				    										<input id="updateBy" name="updateBy" class="easyui-validatebox" data-options="width: 150" value="${bsShift.updateBy }" />
									</td>
			</tr>
						<tr>
				<td>更新時間：</td>
				<td>
				    										<input id="updateDate" name="updateDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${bsShift.updateDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>刪除標示：</td>
				<td>
				    										<input id="delFlag" name="delFlag" class="easyui-validatebox" data-options="width: 150" value="${bsShift.delFlag }" />
									</td>
			</tr>
					</table>
	</form>
  </div>
<script src='${ctx}/static/js/basics/bsshift.js?"+Math.random()"'></script>
<script type="text/javascript">
   //提交表单
$('#mainform').form({
    onSubmit: function(){
        var isValid = $(this).form('validate');
        return isValid;	// 返回false终止表单提交
    },
    success:function(data){ 
        successTip(data,dg,d);
    }
});
</script>
</body>
</html>