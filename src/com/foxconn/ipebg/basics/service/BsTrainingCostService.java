package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.entity.BsTrainingCostEntity;
import com.foxconn.ipebg.basics.dao.BsTrainingCostDao;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.Dict;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WorkFlowEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.system.service.DictService;

import java.util.Date;
import java.util.List;

/**
 * 培訓期費用結帳明細表
 *
 * <AUTHOR>
 * @email <EMAIL> 
 * @date 2019-10-07 08:29:34
 */
@Service
@Transactional(readOnly=true)
public class  BsTrainingCostService extends BaseBusinessService<BsTrainingCostEntity, String>{
    @Autowired
    private BsTrainingCostDao bsTrainingCostDao;

    @Autowired
    private UserServiceUtil serviceUtil;
    @Autowired
    private DictService dictService;

    @Override
    public HibernateDao<BsTrainingCostEntity, String> getEntityDao() {
        return bsTrainingCostDao;
    }

    public BsTrainingCostEntity findBySerialno(String serialno) {
        return this.bsTrainingCostDao.findUniqueBy("serialno", serialno);
    }
    
}
