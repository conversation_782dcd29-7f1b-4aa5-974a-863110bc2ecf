<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>崗位明細表</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/bspost/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${BsPostEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${BsPostEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">崗位明細表</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${BsPostEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${BsPostEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${BsPostEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${BsPostEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${BsPostEntity.makerno}/${BsPostEntity.makername</div>
        <div class="clear"></div>
        <table  class="formList">
           <tr>
                <td>
                    <table class="formList">
                    </table>
                </td>
           </tr>
           <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td>附件<font color="red">*</font></td>
                            <td colspan="9" class="td_style1"><span
                                    class="sl-custom-file"> <input type="button"
                                                                   value="点击上传文件" class="btn-file"/>
								<input id="attachidsUpload" name="attachidsUpload" type="file"
                                       onchange="uploadFile();" class="ui-input-file"/>
								</span> <input type="hidden" id="attachids"
                                               name="attachids" value="${BsPostEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/newEsign/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="delAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${processId}','崗位明細表');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td colspan="10" style="text-align:left;">
                    <iframe id="qianheLogFrame" name="qianheLogFrame"
                            src="${ctx}/wfcontroller/goChargeLog?serialNo=${BsPostEntity.serialno}"
                            width="100%"></iframe>
                </td>
            </tr>
            <tr>
                <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                    <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                       data-options="iconCls:'icon-add'"
                       style="width: 100px;" onclick="saveInfo(2);">重新提交</a>&nbsp;&nbsp;&nbsp;&nbsp;
                    <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                       style="width: 100px;" onclick="canelTask('${BsPostEntity.serialno }');">取消申請</a>
                </td>
            </tr>
        </table>
	</div>
	<input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
	</form>
  </div>
<script src='${ctx}/static/js/basics/bspost.js?random=<%= Math.random()%>'></script>
</body>
</html>