package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.entity.BsSignatureEntity;
import com.foxconn.ipebg.basics.dao.BsSignatureDao;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.Dict;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WorkFlowEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.system.service.DictService;

import java.util.Date;
import java.util.List;

/**
 * 簽名檔
 *
 * <AUTHOR>
 * @email <EMAIL> 
 * @date 2019-09-27 13:16:41
 */
@Service
@Transactional(readOnly=true)
public class  BsSignatureService extends BaseBusinessService<BsSignatureEntity, String>{
    @Autowired
    private BsSignatureDao bsSignatureDao;

    @Autowired
    private UserServiceUtil serviceUtil;
    @Autowired
    private DictService dictService;

    @Override
    public HibernateDao<BsSignatureEntity, String> getEntityDao() {
        return bsSignatureDao;
    }

    public BsSignatureEntity findBySignatureNo(String signatureNo) {
        return this.bsSignatureDao.findUniqueBy("signatureNo",signatureNo);
    }
    
}
