<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>費用基本資料</title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
</script>

<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
	<form id="mainform" action="${ctx}/bscost/${action}" method="post">
		<table class="formTable">
			<tr>
				<%-- 	<td>資料識別ID  Primary：</td>
				<td>
				    					<input type="hidden" name="id" value="${bsDpt.id}"/>
															<input id="id" name="id" class="easyui-validatebox" data-options="width: 150" value="${bsDpt.id }" />
									</td> --%>
				<input id="ids" name="ids" type="hidden" value="${bsCost.id }" />
				
			</tr>
			<tr>
				<td>類型：</td>
				<td><input id="type" name="type" class="easyui-combobox"
					data-options="width: 150, required:true, panelHeight:'auto',valueField:'value',textField:'label',
					url: '${ctx}/system/dict/getDictByType/cost_type'" value="${bsCost.type }" /></td>
			</tr>
			<tr>
				<td>月費用：</td>
				<td><input id="cost" name="cost" class="easyui-validatebox"
					data-options="width: 150, required:true" value="${bsCost.cost }" /></td>
			</tr>
			<tr>
				<td>月天數：</td>
				<td><input id="dayCount" name="dayCount" class="easyui-validatebox"
					data-options="width: 150, required:true" value="${bsCost.dayCount }" /></td>
			</tr>
			<tr>
				<td>備註：</td>
				<td><input id="remark" name="remark" class="easyui-validatebox"
					data-options="width: 150" value="${bsCost.remark }" /></td>
			</tr>
			<tr>
				<td>保安公司</td>
				<td>
					<input id="securityCom" name="securityCom" class="easyui-combobox"
						   data-options="panelHeight:'auto',valueField:'value', required:true, textField:'label',
							   url: '${ctx}/system/dict/getDictByType/guard_securityCom',width: 150"
						   style="width:150px;" value="${bsCost.securityCom}" />
				</td>
			</tr>
		</table>
	</form>
	</div>
	<script src='${ctx}/static/js/basics/bscost.js?"+Math.random()"'></script>
	<script type="text/javascript">
		//提交表单
		$('#mainform').form({
			onSubmit : function() {
				var isValid = $(this).form('validate');
				return isValid; // 返回false终止表单提交
			},
			success : function(data) {
				successTip(data, dg, d);
			}
		});
	</script>
</body>
</html>