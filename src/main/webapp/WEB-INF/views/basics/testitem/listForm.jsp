<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>水質檢測項目維護</title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
</script>

<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
	<form id="mainform" action="${ctx}/testitem/${action}" method="post">
		<table class="formTable">
			<tr>
				<!-- <td>資料識別ID：</td> -->
				<input type="hidden" name="ids" id="ids" value="${testitem.id}" />
			</tr>
			<tr>
				<td>檢測項目：</td>
				<td><input id="testItems" name="testItems"
					class="easyui-validatebox" data-options="width: 150"
					value="${testitem.testItems }" /></td>
			</tr>
			<tr>
				<td>樣品類別：</td>
				<td><input id="sampleTyp" name="sampleTyp"
					class="easyui-validatebox" data-options="width: 150"
					value="${testitem.sampleTyp }" /></td>
			</tr>
			<tr>
				<td>檢驗費用（元）：</td>
				<td><input id="testCost" name="testCost"
					class="easyui-validatebox" data-options="width: 150"
					value="${testitem.testCost }" /></td>
			</tr>
			<tr>
				<td>創建人：</td>
				<td><input id="createBy" name="createBy"
					class="easyui-validatebox" data-options="width: 150"
					value="${testitem.createBy }" /></td>
			</tr>
			<tr>
				<td>創建時間：</td>
				<td><input id="createDate" name="createDate"
					class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
					data-options="width: 160" disabled="disabled"
					value="<fmt:formatDate value="${testitem.createDate}"/>" /></td>
			</tr>
			<tr>
				<td>更新者：</td>
				<td><input id="updateBy" name="updateBy"
					class="easyui-validatebox" data-options="width: 150"
					value="${testitem.updateBy }" /></td>
			</tr>
			<tr>
				<td>更新時間：</td>
				<td><input id="updateDate" name="updateDate"
					class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
					data-options="width: 160" disabled="disabled"
					value="<fmt:formatDate value="${testitem.updateDate}"/>" /></td>
			</tr>
			<%-- <tr>
				<td>刪除標識：</td>
				<td><input id="delFlag" name="delFlag"
					class="easyui-validatebox" data-options="width: 150"
					value="${testitem.delFlag }" /></td>
			</tr> --%>
		</table>
	</form>
	</div>
	<script src='${ctx}/static/js/basics/testitem.js?"+Math.random()"'></script>
	<script type="text/javascript">
		//提交表单
		$('#mainform').form({
			onSubmit : function() {
				var isValid = $(this).form('validate');
				return isValid; // 返回false终止表单提交
			},
			success : function(data) {
				successTip(data, dg, d);
			}
		});
	</script>
</body>
</html>