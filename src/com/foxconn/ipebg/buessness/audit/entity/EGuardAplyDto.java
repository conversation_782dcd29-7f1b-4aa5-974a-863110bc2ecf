package com.foxconn.ipebg.buessness.audit.entity;

import com.foxconn.ipebg.audit.entity.EFormSignEntity;
import com.foxconn.ipebg.buessness.audit.entity.EGuardAplyEntity;
import com.foxconn.ipebg.buessness.audit.entity.EGuardItemAplyEntity;
import java.io.Serializable;

/**
 * 
 * 警衛服務dto
 * <AUTHOR>
 * @email
 * @date 2018-1-10 10:47:15
 */
public class EGuardAplyDto {
	private static final long serialVersionUID = 1L;
	// 警卫服务申請主表實體
	private EGuardAplyEntity eGuardAplyEntity;
	// 警卫服务點位實體
	private EGuardItemAplyEntity[] eGuardItemAplyEntity;
	// 签核节点
	private EFormSignEntity[] eFormSignEntity;

	public EGuardAplyEntity geteGuardAplyEntity() {
		return eGuardAplyEntity;
	}
	public void seteGuardAplyEntity(EGuardAplyEntity eGuardAplyEntity) {
		this.eGuardAplyEntity = eGuardAplyEntity;
	}
	public EGuardItemAplyEntity[] getEGuardItemAplyEntity() {
		return eGuardItemAplyEntity;
	}
	public void setEGuardItemAplyEntity(EGuardItemAplyEntity[] eGuardItemAplyEntity) {
		this.eGuardItemAplyEntity = eGuardItemAplyEntity;
	}
	public EFormSignEntity[] geteFormSignEntity() { return eFormSignEntity; }
	public void seteFormSignEntity(EFormSignEntity[] eFormSignEntity) { this.eFormSignEntity = eFormSignEntity; }
}
