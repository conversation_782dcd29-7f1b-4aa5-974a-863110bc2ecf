var dg;
var d;
$(function () {
    dg = $('#dg').datagrid({
        method: "get",
        url: ctx+'/tqhwfbuildprojectrocess/list',
        fit: true,
        fitColumns: true,
        border: false,
        idField: 'id',
        striped: true,
        pagination: true,
        rownumbers: true,
        pageNumber: 1,
        pageSize: 20,
        pageList: [10, 20, 30, 40, 50],
        singleSelect: true,
        columns: [[
            {field: 'id', title: '主鍵', hidden: true},
            {field: 'serialno', title: '任務編號', sortable: true, width: 150, formatter: operation},
            {field: 'dealno', title: '承辦人工號', sortable: true, width: 100},
            {field: 'dealname', title: '承辦人名稱', sortable: true, width: 100},
            {field: 'dealdeptno', title: '承辦人單位代碼', sortable: true, width: 100},
            {field: 'dealdeptname', title: '承辦人單位名稱', sortable: true, width: 100},
            {field: 'dealtime', title: '提報日期', sortable: true, width: 100},
            {field: 'projecname', title: '項目名稱', sortable: true, width: 100},
            {field: 'projeccorporateid', title: '項目法人', sortable: true, width: 100},
            {field: 'workstatus', title: '表單狀態', sortable: true, width: 100},
            {field: 'nodeName', title: '當前簽核節點', sortable: true, width: 100},
            {field: 'auditUser', title: '當前簽核人', sortable: true, width: 100},
            {field: 'complettime', title: '簽核完成時間', sortable: true, width: 100}
        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        rowTooltip: true,
        toolbar: '#tb'
    });
    $(":input").removeClass("validatebox-invalid");
    //獲取廠區
    $.get(ctx+'/tqhfactoryidconfig/allFactorys/',
        function (result) {
            $("#dealfactoryid").combobox({
                data: result,
                valueField: "factoryid",
                textField: "factoryname",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({factoryid: '', factoryname: '請選擇'});
                    return data;
                }
            });
        });
    //獲取排污類型
    $.ajax({
        type: "GET",
        url: ctx+"/system/dict/getDictByType/discharge_type",
        success: function (data) {
            var html = '';
            if ($("#dischargetype").val() == null || $("#dischargetype").val() == '') {
                $.each(data, function (i, v) {
                    html += "<input type='checkbox' value=" + v.value + " name='discharge'/>" + v.label + "&nbsp;&nbsp;&nbsp;&nbsp;";
                })
            } else {
                var str = $("#dischargetype").val().split(",");
                $.each(data, function (n, item) {
                    var flag = false;
                    for (var i = 0; i < str.length; i++) {
                        if (str[i] == item.value) {
                            html += "<input type='checkbox' onclick='return false' checked='checked' value=" + item.value + " name='discharge'/>" + item.label + "&nbsp;&nbsp;&nbsp;&nbsp;";
                            flag = true;
                        }
                    }
                    if (!flag)
                        html += "<input type='checkbox' onclick='return false' value=" + item.value + " name='discharge'/>" + item.label + "&nbsp;&nbsp;&nbsp;&nbsp;";
                });
            }
            $(".discharge").html(html);
        }
    });
    //添加一筆
    $("#add").click(function () {
        var len = parseInt($("#buildprojectApplyItemTableIndex").val());
        var b = $("#buildprojectApplyItemTable tr").length;
        $("#buildprojectApplyItemTable").append("<tr align='center' id=costItem" + len + ">"
            + "<td>" + b + "</td>"
            + "<td><input id='costname" + len + "' name='tQhWfbuildprojectitemEntity[" + (len - 1) + "].costname' class='easyui-validatebox' required='true' style='width:400px;'><font color='red'>*</font></td>"
            + "<td><input id='costno" + len + "' name='tQhWfbuildprojectitemEntity[" + (len - 1) + "].costno' class='easyui-validatebox' required='true' style='width:400px;'><font color='red'>*</font></td>"
            + "<td><input type='image' src='"+ctx+"/static/images/deleteRow.png' onclick='deltr(" + len + ");return false;'/></td>" + "</tr>");
        $.parser.parse($("#costItem" + len));
        $("#buildprojectApplyItemTableIndex").val(len + 1);
        $("#costname" + len).val('');
        $("#costno" + len).val('');
    });
    //創建下拉查詢條件
    $.ajax({
        url: ctx+"/system/dict/getDictByType/audit_status",
        //dataType:"json",
        type: "GET",
        success: function (data) {
            //绑定第一个下拉框
            $("#qysjzt").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({value: '', label: '請選擇'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });
    //獲取項目性質
    $.ajax({
        url: ctx+"/system/dict/getDictByType/project_nature",
        type: "GET",
        success: function (data) {
            $("#projecnature").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({value: '', label: '請選擇'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });

    //獲取項目法人
    $.ajax({
        url: ctx+"/system/dict/getDictByType/project_entity",
        type: "GET",
        success: function (data) {
            $("#projeccorporateid").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                panelWidth:"auto",
                loadFilter: function (data) {
                    data.unshift({value: '', label: '請選擇'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });
});

//刪除一筆
function deltr(index) {
    debugger;
    if (confirm("確定刪除此筆記錄？")) {
        if ($("#buildprojectApplyItemTable tr").length == 2) {
            alert("至少保留一筆！")
            return;
        }
        $("#costItem" + index).remove();
        var b = $("#buildprojectApplyItemTable tr").length;
        for (var i = 1; i <= b; i++) {
            $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(0).html(i);
            $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(1).find("input[name^='tQhWfbuildprojectitemEntity']").attr("name", "tQhWfbuildprojectitemEntity[" + (i - 1) + "].costname");
            $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(2).find("input[name^='tQhWfbuildprojectitemEntity']").attr("name", "tQhWfbuildprojectitemEntity[" + (i - 1) + "].costno");
        }
    }
}

function operation(value, row, index) {
    if(row.workstatus==0){
        return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('新增建設項目環保手續辦理委託申請單',ctx+'/tqhwfbuildprojectrocess/create/" + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
    }else if(row.workstatus==4){
        return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('新增建設項目環保手續辦理委託申請單',ctx+'/tqhwfbuildprojectrocess/reject/" + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
    }
    return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('新增建設項目環保手續辦理委託申請單詳情',ctx+'/tqhwfbuildprojectrocess/view/" + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
};

//弹窗增加
function add() {
    d = $("#dlg").dialog({
        title: '添加新增建設項目環保手續辦理委託申請單',
        width: 380,
        height: 380,
        href: ctx+'tqhwfbuildprojectrocess/create',
        maximizable: true,
        modal: true,
        buttons: [{
            text: '确认',
            handler: function () {
                $("#mainform").submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//删除
function del() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function (data) {
        if (data) {
            $.ajax({
                type: 'get',
                url: ctx+"tqhwfbuildprojectrocess/delete/" + row.id,
                success: function (data) {
                    successTip(data, dg);
                }
            });
        }
    });
}

//弹窗修改
function upd() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    d = $("#dlg").dialog({
        title: '修改新增建設項目環保手續辦理委託申請單',
        width: 380,
        height: 340,
        href: ctx+'tqhwfbuildprojectrocess/update/' + row.id,
        maximizable: true,
        modal: true,
        buttons: [{
            text: '修改',
            handler: function () {
                $('#mainform').submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//创建查询对象并查询
function cx() {
    var obj = $("#searchFrom").serializeObject();
    dg.datagrid('load', obj);
}

function saveInfo(flag) {
    if (flag == '2') {
        var isValid = $("#mainform").form('validate');
        if (!isValid) {
            return;
        }
        if (!valdChkbox()) {
            return;
        }
        if (($("#zchargeno").val() == null || $("#zchargeno").val() == '') && ($("#zcchargeno").val() == null || $("#zcchargeno").val() == '')) {
            $.messager.alert("溫馨提示", "至少簽核至處級主管及以上主管", "info");
            return;
        }
    }
    $.ajax({
        url: ctx+'/tqhwfbuildprojectrocess/create/' + flag,
        type: 'POST',
        beforeSend: ajaxLoading,
        //dataType: 'json',
        data: $('#mainform').serialize(),
        success: function (data) {
            ajaxLoadEnd();
            successTip(data, dg, d);
            window.parent.mainpage.mainTabs.closeCurrentTab();
        }
    });
}

//validatebox驗證擴展
$.extend($.fn.validatebox.defaults.rules, {
    endTime: {
        validator: function (value, param) {
            var startDate = $('#buildcyclebegindate').datebox('getValue');
            var startTmp = new Date(startDate.replace(/-/g, "/"));
            var endTmp = new Date(value.replace(/-/g, "/"));
            return startTmp <= endTmp;
        },
        message: '结束时间要大于开始时间！'
    }
});

//驗證申請人聯繫電話
function valdApplyTel(obj) {
    var phoneL = $.trim($("#dealtel").val()).split("").length;
    var tel = obj.value;
    if (tel != "") {
        var temp = tel;
        var regex = /^[0-9]{3}\+[0-9]{5}$/;
        var patt1 = new RegExp(regex);
        var result = patt1.test(tel);
        if (!result) {
            // alert("分機格式有誤!");
            $.messager.alert("溫馨提示", "分機格式有誤!", "info");
            obj.value = "";
            obj.focus();
            return;
        }
    }
}

//驗證郵箱
function valdEmail(obj) {
    var email = obj.value;
    var temp = email;
    if (email == "") return;
    var regex = /\w+([-/+.]\w+)*@(mail.foxconn.com|foxconn.com|fii-foxconn.com)$/;
    var patt1 = new RegExp(regex);
    var result = patt1.test(email);
    if (!result) {
        // alert("郵箱的格式不正確!");
        $.messager.alert("溫馨提示", "郵箱的格式不正確!", "info");
        obj.value = "";
        //obj.focus();
        obj.value = temp;
        return;
    }
    ;
}

//驗證數字
function valdMoney(obj) {
    var money = obj.value;
    var temp = money;
    if (money == "") return;
    var regex = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
    var patt1 = new RegExp(regex);
    var result = patt1.test(money);
    if (!result) {
        $.messager.alert("溫馨提示", "格式不正確!", "info");
        obj.value = "";
        //obj.focus();
        obj.value = temp;
        return;
    }
    ;
}

//根據工號查出當前申請人的信息
function queryUserInfo() {
    var empno = $.trim($("#dealno").val().toUpperCase());
    if (empno != null && empno != "") {
        $.ajax({
            url: ctx+'/system/user/getUserInfo/',
            type: 'POST',
            beforeSend: ajaxLoading,
            //dataType: 'json',
            data: {empno: empno},
            success: function (data) {
                ajaxLoadEnd();
                if (!data) {
                    $.messager.alert("溫馨提示", "工號不存在", "error");
                    $('#dealno').val('');
                    $('#dealname').val('');
                    $('#dealdeptno').val('');
                    $('#dealdeptname').val('');
                    $('#dealemail').val('');
                } else {
                    $('#dealname').val(data.empname);
                    $('#dealdeptno').val(data.deptno);
                    $('#dealdeptname').val(data.deptname);
                    $('#dealfactoryid').combobox('setValue', data.factoryid);
                    $('#dealemail').val(data.email);
                }
            }
        });
    }
}

function valdChkbox() {
    var chk_value = [];
    $('input[name="discharge"]:checked').each(function () {
        chk_value.push($(this).val());
    })
    if (chk_value.length == 0) {
        $.messager.alert("溫馨提示", "至少選擇一項排污類型", "info");
        return false;
    } else {
        var chk_values = '';
        $.each(chk_value, function (index, value) {
            chk_values += value + ',';
        })
        var chk_vals = chk_values.substring(0, chk_values.length - 1);
        $('#dischargetype').val(chk_vals);
        return true;
    }
}

//附件上傳
function uploadFile() {
    var attachIds = $.trim($("#attachids").val());
    var attachIdsL = attachIds.split(",").length;
    if (attachIdsL >= 6) {
        $.messager.alert("溫馨提示", "上傳附件個數不能超過五個！", "info");
        return;
    }
    // 创建
    var form_data = new FormData();
    // 获取文件
//        var file = document.getElementById('fileToUpload').files[0];
    var file_data = $("#attachidsUpload").prop("files")[0];
    form_data.append("file", file_data);
    form_data.append("path", "test");
    // 把所以表单信息
    $.ajax({
        type: "POST",
        url: ctx+"/admin/upload",
        beforeSend: ajaxLoading,
//            dataType : "json",
        processData: false,  // 注意：让jQuery不要处理数据
        contentType: false,  // 注意：让jQuery不要设置contentType
        data: form_data
    }).success(function (msg) {
        ajaxLoadEnd();
        $("#dowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href='+ctx+'"/admin/download/' + msg.id + '">' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delAtt(\'' + msg.id + '\')"></div></div>');
        $("#attachids").val($("#attachids").val() + msg.id + ",");
        // $("#attachids").val(msg.id);
    }).fail(function (msg) {
    });
}

function delAtt(fileid) {
    $("#" + fileid).remove();
    var kk = $("#attachids").val();
    kk = kk.replace(fileid + ",", "");
    $("#attachids").val(kk);
    $.post(ctx+'/admin/delete/' + fileid, {}, function (data) {
        console.log(data);
        successTip(data);
    });
}


function reUploadFile() {
    var attachIds = $.trim($("#reattachids").val());
    var attachIdsL = attachIds.split(",").length;
    if (attachIdsL >= 6) {
        $.messager.alert("溫馨提示", "上傳附件個數不能超過五個！", "info");
        return;
    }
    // 创建
    var form_data = new FormData();
    // 获取文件
//        var file = document.getElementById('fileToUpload').files[0];
    var file_data = $("#attachidsUpload").prop("files")[0];
    form_data.append("file", file_data);
    form_data.append("path", "test");
    // 把所以表单信息
    $.ajax({
        type: "POST",
        url: ctx+"/admin/upload",
//            dataType : "json",
        beforeSend: ajaxLoading,
        processData: false,  // 注意：让jQuery不要处理数据
        contentType: false,  // 注意：让jQuery不要设置contentType
        data: form_data
    }).success(function (msg) {
        ajaxLoadEnd();
        $("#reDowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href='+ctx+'"/admin/download/' + msg.id + '">' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delReAtt(\'' + msg.id + '\')"></div></div>');
        $("#reattachids").val($("#reattachids").val() + msg.id + ",");
        // $("#attachids").val(msg.id);
    }).fail(function (msg) {
    });
}

function delReAtt(fileid) {
    $("#" + fileid).remove();
    var kk = $("#reattachids").val();
    kk = kk.replace(fileid + ",", "");
    $("#reattachids").val(kk);
    $.post(ctx+'/admin/delete/' + fileid, {}, function (data) {
        successTip(data);
    });
}
//导出excel
function exportExcel(){
    var form = document.getElementById("searchFrom");
    searchFrom.action=ctx+'/tqhwfbuildprojectrocess/exportExcel';
    form.submit();
}
/*function uploadFile111(){
    $.ajaxFileUpload({
        method:"POST",
        url:"/admin/upload",            //需要链接到服务器地址
        secureuri:false,
        beforeSend: ajaxLoading,
        fileElementId:'attachids',                        //文件选择框的id属性
        success: function(data,s, status){
            ajaxLoadEnd();
            $("#dowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href="/admin/download/' + msg.id + '">' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delAtt(\'' + msg.id + '\')"></div></div>');
            $("#attachids").val($("#attachids").val() + msg.id + ",");
            //上传成功之后的操作
        },error: function (data, status, e){
            //上传失败之后的操作
        }
    });
}*/
