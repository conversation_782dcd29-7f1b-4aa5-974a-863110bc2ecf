<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>我的待辦</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
    <form id="searchFrom" action="">
        <input type="text" name="serialno" class="easyui-validatebox"
               data-options="width:150,prompt: '任務編碼'"/>
        <%--<input type="text" name="filter_GTD_complettime" class="easyui-my97" datefmt="yyyy-MM-dd"--%>
               <%--data-options="width:150,prompt: '簽核完成开始日期'"/>--%>
        <%--- <input type="text" name="filter_LTD_complettime" class="easyui-my97" datefmt="yyyy-MM-dd"--%>
                 <%--data-options="width:150,prompt: '簽核完成结束日期'"/>--%>
        <input id="qysjzt" style="width:300px" class="easyui-validatebox" name="formName"/>
        <span class="toolbar-item dialog-tool-separator"></span>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
           onclick="listSearchReset()">重置</a>
        <%--<a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel"--%>
           <%--onclick="exportExcel()">导出Excel</a>--%>

    </form>
</div>
<table id="dg"></table>
<script type="text/javascript">
    var dg;
    $(function () {
        dg = $('#dg').datagrid({
            method: "get",
            //url: '${ctx}/eformsign/listMyTask',
            url: '${ctx}/eformsign/listTaskNo',
            fit: true,
            fitColumns: true,
            border: false,
            striped: true,
            idField: 'id',
            pagination: true,
            rownumbers: true,
            loadFilter : function(data) {
			// 过滤数据
			var value = {
				total : data.total,
				rows : []
			};
			var x = 0;
			for ( var i = 0; i < data.rows.length; i++) {
				// 
				
				if (data.rows[i].wfName == "DWJC") 
				{
					data.rows[i].wfName="太原園區水質檢驗服務申請單"	;
				}
				else if (data.rows[i].wfName == "DWSQ")
				{
						data.rows[i].wfName="新增飲用水/蓄水池（箱）點位審批表"	;
				}
				data.rows[i].workstatus="簽核中";
				/* else
				{
						data.rows[i].applyStat="簽核中"	;
				} */
				
				value.rows[x++] = data.rows[i];
			}
			return value;

		},
            pageNumber: 1,
            pageSize: 20,
            pageList: [10, 20, 30, 40, 50],
            singleSelect: true,
            columns: [[
                {field: 'serialno', title: '表單流水號', width: 50, formatter: operation},
                {field: 'auditAction', title: '流程審核', width: 50, hidden:true},
                {field: 'makerno', title: '填單人工號', width: 20},
                {field: 'makername', title: '填單人', width: 20},
                {field: 'wfName', title: '表單類型', width: 80},
                {field: 'taskName', title: '簽核節點', width: 50},
                {field: 'createtime', title: '填單時間', width: 50},//,formatter:formatDate
                {field: 'workstatus', title: '表單狀態', width: 10},
            ]],
            enableHeaderClickMenu: false,
            enableHeaderContextMenu: false,
            enableRowContextMenu: false,
            toolbar: '#tb'
        });
        //初始化審核狀態
        $.ajax({
            url: ctx + "/system/dict/getDictByType/form_name",
            //dataType:"json",
            type: "GET",
            success: function (data) {
                //绑定第一个下拉框
                $("#qysjzt").combobox({
                    data: data,
                    valueField: "value",
                    textField: "label",
                    editable: false,
                    panelHeight: 400,
                    loadFilter: function (data) {
                        data.unshift({
                            value: '',
                            label: '請選擇'
                        });
                        return data;
                    }
                });
            },
            error: function (error) {
                alert("初始化下拉控件失败");
            }
        });
    });

    function operation(value, row, index) {
        return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('"+row.wfName+"',ctx+'/"+row.auditAction+"/"+row.serialno+"','icon-hamburg-basket')\">"+value+"</a>";
    };
    //创建查询对象并查询
    function cx() {
        var obj = $("#searchFrom").serializeObject();
        dg.datagrid('load', obj);
    }
    //表單進度查詢頁面查詢條件重置
    function listSearchReset() {
        $("#searchFrom").form("reset");
    }
</script>
</body>
</html>