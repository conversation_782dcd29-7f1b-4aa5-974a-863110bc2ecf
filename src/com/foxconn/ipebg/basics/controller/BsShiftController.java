package com.foxconn.ipebg.basics.controller;

import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.basics.entity.BsShiftEntity;
import com.foxconn.ipebg.basics.service.BsShiftService;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;


/**
 * 班別資料
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-03-07 09:57:22
 */
@Controller
@RequestMapping("bsshift")  
public class BsShiftController extends BaseController {

    @Autowired
    private BsShiftService bsShiftService;
   
    /**
      * 方法描述: 列表信息
      * @Author: S6113712
      * @CreateDate:   2019-03-07 09:57:22
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:bsshift:list")
    public String list() {
        //查询列表数据
        return "basics/bsshift/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6113712
      * @CreateDate:   2019-03-07 09:57:22
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:bsshift:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<BsShiftEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        page = bsShiftService.search(page, filters);
        return getEasyUIData(page);
    }

    @RequestMapping(value = "getGuardShiftList")
    @ResponseBody
    public List<BsShiftEntity> getGuardShiftList() {
        List<String> shiftNoArray = Arrays.asList("A", "B");
        List<PropertyFilter> filters = new ArrayList<>();
        filters.add(new PropertyFilter("INS_shiftNo", shiftNoArray));
        return bsShiftService.search(filters);
    }

    /**
     * 方法描述: 保存
     * @Author: S6113712
     * @CreateDate:   2019-03-07 09:57:22
     * @Return
     **/
   
    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("basics:bsshift:add")
    @ResponseBody
    public String create(@Valid BsShiftEntity bsShift, Model model) {
        bsShift.setNewRecord(true);
    	bsShift.setCreateDate(new Date());
        bsShiftService.save(bsShift);
        return "success";
    }
    
    /**
      * 方法描述: 添加跳轉
      * @Author: S6113712
      * @CreateDate:   2019-03-07 09:57:22
      * @Return
      **/

    @RequiresPermissions("basics:bsshift:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("bsShift", new BsShiftEntity());
        model.addAttribute("action", "create");
        return "basics/bsshift/listForm";
    }
    
     /**
     * 方法描述: 修改跳轉
     * @Author: S6113712
     * @CreateDate:   2018-10-06 11:04:39
     * @Return
     **/

   @RequiresPermissions("basics:bsshift:update")
   @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
   public String updateForm(@PathVariable("id") String id, Model model) {
       model.addAttribute("bsShift", bsShiftService.get(id));
       model.addAttribute("action", "update");
      return "basics/bsshift/listForm";
   }
   
	/**
	 * 方法描述: 修改
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-11 10:55:00
	 * @Return
	 **/

   @RequestMapping(value = "update", method = RequestMethod.POST)
   @RequiresPermissions("basics:bsshift:update")
   @ResponseBody
   public String update(@Valid BsShiftEntity bsShift,@RequestParam(value = "ids") String ids) {
	   bsShift.setId(ids);
	   bsShift.setUpdateDate(new Date());
	   bsShiftService.update(bsShift);
	   
       return "success";
   }
    /**
      * 方法描述: 根據主鍵刪除
      * @Author: S6113712
      * @CreateDate:   2019-03-07 09:57:22
      * @Return
      **/

    @RequestMapping("delete/{id}")
    @RequiresPermissions("basics:bsshift:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        bsShiftService.delete(id);
        return "success";
    }
    /**
      * 导出excel
      * @Author: S6113712
      * @CreateDate:   2019-03-07 09:57:22
      * @Return
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {}
    
	/**
	 * 方法描述: 獲取班別信息下拉列表
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2019-01-15 18:34:27
	 * @Return
	 **/
    @RequestMapping(value = "getShiftList", method = RequestMethod.GET)///{shiftNo}
    //@RequestMapping("getShiftList")
	@ResponseBody
	public List<BsShiftEntity> getShiftList() {
		List<BsShiftEntity> shiftList = bsShiftService.findShiftEntityes();
		return shiftList;
	}
	/**
	 * 方法描述: 獲取班別信息
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-03-08 18:34:27
	 * @Return
	 **/
    @RequestMapping(value = "getShift/{shiftNo}", method = RequestMethod.GET)///{shiftNo}
    //@RequestMapping("getShiftList")
	@ResponseBody
	public BsShiftEntity getShift(@PathVariable("shiftNo") String shiftNo) {
		BsShiftEntity shift = bsShiftService.findByShiftNo(shiftNo);
		return shift;
	}
    
    
}
