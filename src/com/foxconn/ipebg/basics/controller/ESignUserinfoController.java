package com.foxconn.ipebg.basics.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.basics.entity.ESignUserinfoEntity;
import com.foxconn.ipebg.basics.service.ESignUserinfoService;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;


/**
 * 簽核者信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-12-25 14:52:53
 */
@Controller
@RequestMapping("esignuserinfo")
public class ESignUserinfoController extends BaseController {

    @Autowired
    private ESignUserinfoService eSignUserinfoService;
   
    /**
      * 方法描述: 列表信息
      * @Author: S6113712
      * @CreateDate:   2018-12-25 14:52:53
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:esignuserinfo:list")
    public String list() {
        //查询列表数据
        return "basics/esignuserinfo/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6113712
      * @CreateDate:   2018-12-25 14:52:53
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:esignuserinfo:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<ESignUserinfoEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        page = eSignUserinfoService.search(page, filters);
        return getEasyUIData(page);
    }
    /**
     * 方法描述: 保存
     * @Author: S6113712
     * @CreateDate:   2018-12-25 14:52:53
     * @Return
     **/
   
    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("basics:esignuserinfo:add")
    @ResponseBody
    public String create(@Valid ESignUserinfoEntity eSignUserinfo, Model model) {
        eSignUserinfo.setNewRecord(true);
    	eSignUserinfo.setCreateDate(new Date());
    	try {
            eSignUserinfoService.save(eSignUserinfo);
        } catch (Exception e) {
    	    e.printStackTrace();
    	    return "審核人員重複添加";
        }
        return "success";
    }
    
    /**
      * 方法描述: 添加跳轉
      * @Author: S6113712
      * @CreateDate:   2018-12-25 14:52:53
      * @Return
      **/

    @RequiresPermissions("basics:esignuserinfo:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("eSignUserinfo", new ESignUserinfoEntity());
        model.addAttribute("action", "create");
        return "basics/esignuserinfo/listForm";
    }
    
     /**
     * 方法描述: 修改跳轉
     * @Author: S6113712
     * @CreateDate:   2018-10-06 11:04:39
     * @Return
     **/

   @RequiresPermissions("basics:esignuserinfo:update")
   @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
   public String updateForm(@PathVariable("id") String id, Model model) {
       model.addAttribute("eSignUserinfo", eSignUserinfoService.get(id));
       model.addAttribute("action", "update");
      return "basics/esignuserinfo/listForm";
   }
   
	/**
	 * 方法描述: 修改
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-11 10:55:00
	 * @Return
	 **/

   @RequestMapping(value = "update", method = RequestMethod.POST)
   @RequiresPermissions("basics:esignuserinfo:update")
   @ResponseBody
   public String update(@Valid ESignUserinfoEntity eSignUserinfo,@RequestParam(value = "ids") String ids) {
	   eSignUserinfo.setId(ids);
	   eSignUserinfo.setUpdateDate(new Date());
	   eSignUserinfoService.update(eSignUserinfo);
	   
       return "success";
   }
    /**
      * 方法描述: 根據主鍵刪除
      * @Author: S6113712
      * @CreateDate:   2018-12-25 14:52:53
      * @Return
      **/

    @RequestMapping("delete/{id}")
    @RequiresPermissions("basics:esignuserinfo:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        eSignUserinfoService.delete(id);
        return "success";
    }
    /**
      * 导出excel
      * @Author: S6113712
      * @CreateDate:   2018-12-25 14:52:53
      * @Return
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {}
}
