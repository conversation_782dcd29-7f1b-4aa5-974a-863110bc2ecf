package com.foxconn.ipebg.system.service;

import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseService;
import com.foxconn.ipebg.system.dao.DictDao;
import com.foxconn.ipebg.system.entity.Dict;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.management.ManagementPermission;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 字典service
 * <AUTHOR>
 * @date 2015年1月13日
 */
@Service
@Transactional(readOnly=true)
public class DictService extends BaseService<Dict, Integer> {
	
	@Autowired
	private DictDao dictDao;

	@Override
	public HibernateDao<Dict, Integer> getEntityDao() {
		return dictDao;
	}
    /**
      * 方法描述: 字典生成下拉數據 通用
      * @Author: S6114648
      * @CreateDate:   2018/10/15  上午 09:17
      * @Return
      **/

	public List<Dict> getDictByType(String dictType) {
      return dictDao.findAllByOrder("type",dictType,"sort",true);
	}
	public Dict getDictByTypeAndVlaue(String dictType,String value) {
      return dictDao.findUnique("from Dict t where t.type=?0 and t.value=?1",dictType,value);
	}
	public Map<String, String> getMapByType(String dictType) {
		Map<String, String> resultMap = new HashMap<String, String>();
		List<Dict> dictList = getDictByType(dictType);
		for(Dict dict : dictList) {
			resultMap.put(dict.getValue(), dict.getLabel());
		}
		return resultMap;
	}
	public Dict getDictByLabelAndType(String label, String type) {
		return dictDao.findUnique("from Dict t where t.label=?0 and t.type=?1", label, type);
	}
}
