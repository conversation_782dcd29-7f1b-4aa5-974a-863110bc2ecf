package com.foxconn.ipebg.system.web;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;

import cn.hutool.extra.mail.MailUtil;
import com.foxconn.ipebg.common.utils.ConvertUtils;
import com.foxconn.ipebg.system.entity.Mail;
import com.foxconn.ipebg.system.service.DictService;
import com.foxconn.ipebg.system.utils.SendMailUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.system.entity.TPubMailrecordEntity;
import com.foxconn.ipebg.system.service.TPubMailrecordService;
import com.foxconn.ipebg.common.persistence.Page;


/**
 * 郵件發送記錄表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-11-09 13:29:33
 */
@Controller
@RequestMapping("tpubmailrecord")
public class TPubMailrecordController extends BaseController {

    @Autowired
    private TPubMailrecordService tPubMailrecordService;
    @Autowired
    private DictService dictService;

    /**
      * 方法描述: 列表信息
      * @Author: S6114648
      * @CreateDate:   2018-11-09 13:29:33
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("system:tpubmailrecord:list")
    public String list() {
        //查询列表数据
        return "system/tpubmailrecord/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6114648
      * @CreateDate:   2018-11-09 13:29:33
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("system:tpubmailrecord:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<TPubMailrecordEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        page = tPubMailrecordService.search(page, filters);
//        ConvertUtils.convertPropertyToDictLabel(page,"orderstatus","audit_status");
        return getEasyUIData(page);
    }
    /**
      * 方法描述: 添加跳轉
      * @Author: S6114648
      * @CreateDate:   2018-11-09 13:29:33
      * @Return
      **/

    //@RequiresPermissions("system:tpubmailrecord:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("tPubMailrecord", new TPubMailrecordEntity());
        model.addAttribute("action", "create");
        return "system/tpubmailrecord/listForm";
    }
    /**
      * 方法描述: 保存
      * @Author: S6114648
      * @CreateDate:   2018-11-09 13:29:33
      * @Return
      **/

    @RequestMapping(value="create", method = RequestMethod.POST)
    //@RequiresPermissions("system:tpubmailrecord:add")
    @ResponseBody
    public String create(@Valid TPubMailrecordEntity tPubMailrecord, Model model) {
        tPubMailrecord.setNewRecord(true);
        tPubMailrecordService.save(tPubMailrecord);
        return "success";
    }
    /**
      * 方法描述: 修改跳轉
      * @Author: S6114648
      * @CreateDate:   2018-11-09 13:29:33
      * @Return
      **/

    //@RequiresPermissions("system:tpubmailrecord:update")
    @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
    public String updateForm(@PathVariable("id") String id, Model model) {
        model.addAttribute("tPubMailrecord", tPubMailrecordService.get(id));
        model.addAttribute("action", "update");
        return "system/tpubmailrecord/listForm";
    }
    /**
      * 方法描述: 修改
      * @Author: S6114648
      * @CreateDate:   2018-11-09 13:29:33
      * @Return
      **/

    @RequestMapping(value="update", method = RequestMethod.POST)
    //@RequiresPermissions("system:tpubmailrecord:update")
    @ResponseBody
    public String update(@Valid TPubMailrecordEntity tPubMailrecord) {
        tPubMailrecordService.update(tPubMailrecord);
        return "success";
    }

    /**
      * 方法描述: 根據主鍵刪除
      * @Author: S6114648
      * @CreateDate:   2018-11-09 13:29:33
      * @Return
      **/

    @RequestMapping("delete/{id}")
    //@RequiresPermissions("system:tpubmailrecord:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        tPubMailrecordService.delete(id);
        return "success";
    }
    @ResponseBody
    @RequestMapping("sendMail/{ids}")
    public String reSendMail(@PathVariable("ids") String ids){
        TPubMailrecordEntity entity = tPubMailrecordService.findById(ids);
        Mail mail = new Mail();
        mail.setUsermail(entity.getUsermail());
        mail.setUrlip(entity.getUrlip());
        mail.setUrl(entity.getUrl());
        mail.setUrl(entity.getUrl());
        mail.setUrlip(entity.getUrlip());
        mail.setFreeloginurl(entity.getFreeloginurl());
        mail.setFreeloginurlip(entity.getFreeloginurlip());
        mail.setOrderstatus(entity.getOrderstatus());
        mail.setSerialno(entity.getSerialno());
        mail.setOrdertype(entity.getOrdertype());
        mail.setDusername(entity.getDusername());
        mail.setSystemname(dictService.getDictByTypeAndVlaue("sys_property", "sys_name").getLabel());
        mail.setUsername(entity.getUsername());
        mail.setChargerman(entity.getChargerman());
        String sendResult = new SendMailUtil().sendMail(mail);
        if ("0".equals(sendResult)) {
            //發送成功，更新標誌
            entity.setSendStatus("1");
            tPubMailrecordService.save(entity);
            return "success";
        }else{
            return "發送失敗,稍後重試";
        }
    }

}
