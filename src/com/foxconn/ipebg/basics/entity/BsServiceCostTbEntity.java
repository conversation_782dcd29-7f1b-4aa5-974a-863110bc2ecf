package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.foxconn.ipebg.common.entity.DataEntity;
import com.foxconn.ipebg.common.utils.StringUtils;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 警衛服務費用結賬明細主表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-10-07 13:15:26
 */
@Entity
@Table(name = "BS_SERVICE_COST_TB")
@DynamicUpdate
@DynamicInsert
public class BsServiceCostTbEntity extends DataEntity<BsServiceCostTbEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    //$column.comments
    private String l21;
    //$column.comments
    private String l22;
    //$column.comments
    private String l23;
    //$column.comments
    private String l24;
    //$column.comments
    private String l25;
    //$column.comments
    private String l26;
    //$column.comments
    private String l27;
    //$column.comments
    private String l28;
    //$column.comments
    private String l29;
    //$column.comments
    private String l30;
    //$column.comments
    private String l31;
    //警衛服務費用結賬明細主表ID
    private String scId;
    //接崗人力
    private Integer pernu;
    //$column.comments
    private String shiftNo;
    //$column.comments
    private String shiftName;
    //$column.comments
    private String l1;
    //$column.comments
    private String l2;
    //$column.comments
    private String l3;
    //$column.comments
    private String l4;
    //$column.comments
    private String l5;
    //$column.comments
    private String l6;
    //$column.comments
    private String l7;
    //$column.comments
    private String l8;
    //$column.comments
    private String l9;
    //$column.comments
    private String l10;
    //$column.comments
    private String l11;
    //$column.comments
    private String l12;
    //$column.comments
    private String l13;
    //$column.comments
    private String l14;
    //$column.comments
    private String l15;
    //$column.comments
    private String l16;
    //$column.comments
    private String l17;
    //$column.comments
    private String l18;
    //$column.comments
    private String l19;
    //$column.comments
    private String l20;

    /**
     * 设置：${column.comments}
     */
    public void setL21(String l21) {
        this.l21 = l21;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L21", nullable = false, length = 20)
    public String getL21() {
        return l21;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL22(String l22) {
        this.l22 = l22;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L22", nullable = false, length = 20)
    public String getL22() {
        return l22;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL23(String l23) {
        this.l23 = l23;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L23", nullable = false, length = 20)
    public String getL23() {
        return l23;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL24(String l24) {
        this.l24 = l24;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L24", nullable = false, length = 20)
    public String getL24() {
        return l24;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL25(String l25) {
        this.l25 = l25;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L25", nullable = false, length = 20)
    public String getL25() {
        return l25;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL26(String l26) {
        this.l26 = l26;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L26", nullable = false, length = 20)
    public String getL26() {
        return l26;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL27(String l27) {
        this.l27 = l27;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L27", nullable = false, length = 20)
    public String getL27() {
        return l27;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL28(String l28) {
        this.l28 = l28;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L28", nullable = false, length = 20)
    public String getL28() {
        return l28;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL29(String l29) {
        this.l29 = l29;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L29", nullable = false, length = 20)
    public String getL29() {
        return l29;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL30(String l30) {
        this.l30 = l30;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L30", nullable = false, length = 20)
    public String getL30() {
        return l30;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL31(String l31) {
        this.l31 = l31;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L31", nullable = false, length = 20)
    public String getL31() {
        return l31;
    }


    /**
     * 设置：警衛服務費用結賬明細主表ID
     */
    public void setScId(String scId) {
        this.scId = scId;
    }

    /**
     * 获取：警衛服務費用結賬明細主表ID
     */

    @Column(name = "SC_ID", nullable = false, length = 20)
    public String getScId() {
        return scId;
    }

    /**
     * 设置：接崗人力
     */
    public void setPernu(Integer pernu) {
        this.pernu = pernu;
    }

    /**
     * 获取：接崗人力
     */

    @Column(name = "PERNU", nullable = false, length = 20)
    public Integer getPernu() {
        if (pernu != null) {
            return pernu;
        } else {
            return 0;
        }
    }


    /**
     * 设置：${column.comments}
     */
    public void setShiftNo(String shiftNo) {
        this.shiftNo = shiftNo;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "SHIFT_NO", nullable = false, length = 20)
    public String getShiftNo() {
        return shiftNo;
    }

    /**
     * 设置：${column.comments}
     */
    public void setShiftName(String shiftName) {
        this.shiftName = shiftName;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "SHIFT_NAME", nullable = false, length = 20)
    public String getShiftName() {
        return shiftName;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL1(String l1) {
        this.l1 = l1;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L1", nullable = false, length = 20)
    public String getL1() {
        return l1;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL2(String l2) {
        this.l2 = l2;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L2", nullable = false, length = 20)
    public String getL2() {
        return l2;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL3(String l3) {
        this.l3 = l3;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L3", nullable = false, length = 20)
    public String getL3() {
        return l3;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL4(String l4) {
        this.l4 = l4;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L4", nullable = false, length = 20)
    public String getL4() {
        return l4;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL5(String l5) {
        this.l5 = l5;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L5", nullable = false, length = 20)
    public String getL5() {
        return l5;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL6(String l6) {
        this.l6 = l6;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L6", nullable = false, length = 20)
    public String getL6() {
        return l6;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL7(String l7) {
        this.l7 = l7;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L7", nullable = false, length = 20)
    public String getL7() {
        return l7;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL8(String l8) {
        this.l8 = l8;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L8", nullable = false, length = 20)
    public String getL8() {
        return l8;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL9(String l9) {
        this.l9 = l9;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L9", nullable = false, length = 20)
    public String getL9() {
        return l9;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL10(String l10) {
        this.l10 = l10;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L10", nullable = false, length = 20)
    public String getL10() {
        return l10;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL11(String l11) {
        this.l11 = l11;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L11", nullable = false, length = 20)
    public String getL11() {
        return l11;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL12(String l12) {
        this.l12 = l12;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L12", nullable = false, length = 20)
    public String getL12() {
        return l12;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL13(String l13) {
        this.l13 = l13;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L13", nullable = false, length = 20)
    public String getL13() {
        return l13;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL14(String l14) {
        this.l14 = l14;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L14", nullable = false, length = 20)
    public String getL14() {
        return l14;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL15(String l15) {
        this.l15 = l15;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L15", nullable = false, length = 20)
    public String getL15() {
        return l15;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL16(String l16) {
        this.l16 = l16;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L16", nullable = false, length = 20)
    public String getL16() {
        return l16;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL17(String l17) {
        this.l17 = l17;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L17", nullable = false, length = 20)
    public String getL17() {
        return l17;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL18(String l18) {
        this.l18 = l18;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L18", nullable = false, length = 20)
    public String getL18() {
        return l18;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL19(String l19) {
        this.l19 = l19;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L19", nullable = false, length = 20)
    public String getL19() {
        return l19;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL20(String l20) {
        this.l20 = l20;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L20", nullable = false, length = 20)
    public String getL20() {
        return l20;
    }

}
