var dg;
var d;
$(function () {
    dg = $('#dg').datagrid({
        method: "get",
        url: ctx+'/tqhwfbuildprojectchange/list',
        fit: true,
        fitColumns: true,
        border: false,
        idField: 'id',
        striped: true,
        pagination: true,
        rownumbers: true,
        pageNumber: 1,
        pageSize: 20,
        pageList: [10, 20, 30, 40, 50],
        singleSelect: true,
        columns: [[
            {field: 'id', title: '主鍵', hidden: true},
            {field: 'serialno', title: '任務編號', sortable: true, width: 150, formatter: operation},
            {field: 'dealno', title: '承辦人工號', sortable: true, width: 100},
            {field: 'dealname', title: '承辦人名稱', sortable: true, width: 100},
            {field: 'dealdeptno', title: '承辦人單位代碼', sortable: true, width: 100},
            {field: 'dealdeptname', title: '承辦人單位名稱', sortable: true, width: 100},
            {field: 'dealtime', title: '提報日期', sortable: true, width: 100},
            {field: 'projectname', title: '項目名稱', sortable: true, width: 100},
            // {field: 'projectcorporateid', title: '項目法人', sortable: true, width: 100},
            {field: 'projectcorporateid', title: '法人', sortable: true, width: 100},
            {field: 'workstatus', title: '表單狀態', sortable: true, width: 100},
            {field: 'nodeName', title: '當前簽核節點', sortable: true, width: 100},
            {field: 'auditUser', title: '當前簽核人', sortable: true, width: 100},
            {field: 'complettime', title: '簽核完成時間', sortable: true, width: 100}
        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        toolbar: '#tb'
    });

    $(":input").removeClass("validatebox-invalid");

    //是否變更"是否園鈕"賦值
    $.ajax({
        type: "GET",
        url: ctx+"/system/dict/getDictByType/ishave_permit",
        success: function (data) {
            // var myProject=new Array("projectname");
            var myProject=new Array("projectname","buildcorporate","projectnature","buildsize","buildsite","buildtotalcost","investmode","projectbuildcontent","producteart","dischargelink");
            $.each(myProject,function(index,value){
            var html = '';
            var ifChange = value+"IfChange";
            var radioName = value +"IfChangeName";
            var trName= "tr_"+value+"IfChange";
            var proOld= value+"Old";
            var proNew= value+"New";

                // $("#" + key1 + "")
            if ($("#"+ifChange+"").val() == null || $("#"+ifChange+"").val() == '') {
                $.each(data, function (i, v) {
                    var ch = '';
                    if (v.value == '0') {
                        ch = ' checked=\'true\'';
                    }
                    html += "<input type='radio' value=" + v.value + ch + " name='"+radioName+"'  onchange=\"changeLeve('"+proOld+"','"+proNew+"','" + v.value + "');\"  />" + v.label + "&nbsp;</input>";
                })
            } else {
                var str = $("#"+ifChange+"").val().split(",");
                var disOrEnabled = $("#disOrEnabledIF").val();
                $.each(data, function (n, item) {
                    var flag = false;

                    for (var i = 0; i < str.length; i++) {
                        if (str[i] == item.value) {
                            html += "<input type='radio'  checked='checked' " + disOrEnabled + " value=" + item.value + " name='"+radioName+"'  onchange=\"changeLeve('"+proOld+"','"+proNew+"','" + item.value + "');\" />" + item.label + "&nbsp;</input>";
                            flag = true;
                        }
                    }
                    if (!flag) {

                        html += "<input type='radio' " + disOrEnabled + " value=" + item.value + "  name='"+radioName+"' onchange=\"changeLeve('"+proOld+"','"+proNew+"','" + item.value + "');\" />" + item.label + "&nbsp;</input>";
                    }
                });
            }

            //項目變更內容 在查詢/審核 頁面的內容顯示與隱藏開始==========
            if ($("#"+value+"IfChange").val() == "1") {

                $("#"+proOld+"").attr("disabled", false);
                $("#"+proNew+"").attr("disabled", false);

            } else {
                $("#"+trName+"").hide();
                $("#"+proOld+"").attr("disabled", true);
                $("#"+proNew+"").attr("disabled", true);

            } ;

            $("."+value+"IfChangeDiv").html(html);
        });
        }
    });


    //獲取廠區
    $.get(ctx+'/tqhfactoryidconfig/allFactorys/',
        function (result) {
            $("#dealfactoryid").combobox({
                data: result,
                valueField: "factoryid",
                textField: "factoryname",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({factoryid: '', factoryname: '請選擇'});
                    return data;
                }
            });
        });

    //獲取項目法人
    $.ajax({
        url: ctx+"/system/dict/getDictByType/project_entity",
        type: "GET",
        success: function (data) {
            $("#projectcorporateid ").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({value: '', label: '請選擇'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });

    //項目階段賦值
    $.ajax({
        type: "GET",
        url: ctx+"/system/dict/getDictByType/project_stage",
        success: function (data) {
            var html = '';
            if ($("#projectstage").val() == null || $("#projectstage").val() == '') {
                $.each(data, function (i, v) {
                    html += "<input type='radio' value=" + v.value + " name='projectstageName'/>" + v.label + "&nbsp;&nbsp;";
                })
            } else {
                var str = $("#projectstage").val().split(",");
                var disOrEnabled = $("#disOrEnabled").val();
                $.each(data, function (n, item) {
                    var flag = false;
                    for (var i = 0; i < str.length; i++) {
                        if (str[i] == item.value) {
                            html += "<input type='radio' checked='checked' " + disOrEnabled + "  value=" + item.value + " name='projectstageName'/>" + item.label + "&nbsp;&nbsp;";
                            flag = true;
                        }
                    }
                    if (!flag)
                        html += "<input type='radio'  " + disOrEnabled + " value=" + item.value + " name='projectstageName'/>" + item.label + "&nbsp;&nbsp;";
                });
            }
            $(".projectstageDiv").html(html);


        }
    });

    //添加一筆
    $("#add").click(function () {
        var len = $("#buildprojectApplyItemTable tr").length;
        $("#buildprojectApplyItemTable").append("<tr align='center' id='costItem" + len + "'>"
            + "<td>" + len + "</td>"
            + "<td><input id='costname" + len + "' name='tQhWfbuildprojectchangeitemEntity[" + (len - 1) + "].costname' class='easyui-validatebox' required='true' style='width:400px;'><font color='red'>*</font></td>"
            + "<td><input id='costno" + len + "' name='tQhWfbuildprojectchangeitemEntity[" + (len - 1) + "].costno' class='easyui-validatebox' required='true' style='width:400px;'><font color='red'>*</font></td>"
            + "<td><input type='image' src='"+ctx+"/static/images/deleteRow.png' onclick='deltr(" + len + ");return false;'/></td>" + "</tr>");
        $.parser.parse($("#buildprojectApplyItemTable"));
        $("#costname" + len).val('');
        $("#costno" + len).val('');
    });
    //創建下拉查詢條件
    $.ajax({
        url: ctx+"/system/dict/getDictByType/audit_status",
        //dataType:"json",
        type: "GET",
        success: function (data) {
            //绑定第一个下拉框
            $("#qysjzt").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({value: '', label: '請選擇'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });


});

function operation(value, row, index) {
    return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('詳情',ctx+'/tqhwfbuildprojectchange/view/" + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
};

//弹窗增加
function add() {
    d = $("#dlg").dialog({
        title: '添加建設項目環保變更報備申請單主表',
        width: 380,
        height: 380,
        href: ctx+'tqhwfbuildprojectchange/create',
        maximizable: true,
        modal: true,
        buttons: [{
            text: '确认',
            handler: function () {
                $("#mainform").submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//驗證是否變更為空
function valdRadioIF() {
    var myProject=new Array("projectname","buildcorporate","projectnature","buildsize","buildsite","buildtotalcost","investmode","projectbuildcontent","producteart","dischargelink");
    // var myProject=new Array("projectname");
    var rt = false;
    $.each(myProject,function(index,value) {
        var chk_value = [];
        var radioName = value +"IfChangeName";
        var ifChange = value+"IfChange";

        $('input[name="'+radioName+'"]:checked').each(function () {
            chk_value.push($(this).val());
        })
        if (chk_value.length == 0) {
            alert("請選擇項目是否變更");
            return false;
        } else {
            var chk_values = '';
            $.each(chk_value, function (i, v) {
                chk_values += v + ',';
            })
            var chk_vals = chk_values.substring(0, chk_values.length - 1);
            $('#'+ifChange+'').val(chk_vals);
            // $("#"+ifChange+"").val(chk_vals);
            rt="true";

        }
    });
    return rt;
}

//其它為空驗證
function valdOther() {

    //製造處級主管和製造總處級主管不能同時為空驗證
    if (($("#zcchargeno").val()== null || $("#zcchargeno").val()== '') && ($("#zchargeno").val()== null || $("#zchargeno").val()== '')){
        alert("[製造處級主管]和[製造總處級主管]不能同時為空！");
        return false;
    }else {
        return true;
    }
}

//删除
function del() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function (data) {
        if (data) {
            $.ajax({
                type: 'get',
                url: ctx+"tqhwfbuildprojectchange/delete/" + row.id,
                success: function (data) {
                    successTip(data, dg);
                }
            });
        }
    });
}

//弹窗修改
function upd() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    d = $("#dlg").dialog({
        title: '修改建設項目環保變更報備申請單主表',
        width: 380,
        height: 340,
        href: ctx+'/tqhwfbuildprojectchange/update/' + row.id,
        maximizable: true,
        modal: true,
        buttons: [{
            text: '修改',
            handler: function () {
                $('#mainform').submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//表單提交2,重新提交2/保存0
function saveInfo(flag) {
    var isValid = $("#mainform").form('validate');
    if (!isValid) {
        return;
    }
    if (!valdChkbox()) {
        return;
    }
    if (!valdRadioIF()) {
        return;
    }
    if (!valdOther()) {
        return;
    }

    $.ajax({
        url: ctx+'/tqhwfbuildprojectchange/create/' + flag,
        type: 'POST',
        beforeSend: ajaxLoading,
        //dataType: 'json',
        data: $('#mainform').serialize(),
        success: function (data) {
            ajaxLoadEnd();
            successTip(data, dg, d);
            window.parent.mainpage.mainTabs.closeCurrentTab();
        }
    });
}

//创建查询对象并查询
function cx() {
    var obj = $("#searchFrom").serializeObject();
    dg.datagrid('load', obj);
}

//根據工號查出當前申請人的信息
function queryUserInfo() {
    var empno = $.trim($("#dealno").val().toUpperCase());
    if (empno != null && empno != "") {
        $.ajax({
            url: ctx+'/system/user/getUserInfo/',
            type: 'POST',
            beforeSend: ajaxLoading,
            //dataType: 'json',
            data: {empno: empno},
            success: function (data) {
                ajaxLoadEnd();
                if (!data) {
                    $.messager.alert("溫馨提示", "工號不存在", "error");
                    $('#dealno').val('');
                    $('#dealname').val('');
                    $('#dealdeptno').val('');
                    $('#dealdeptname').val('');
                    $('#dealemail').val('');
                } else {
                    $('#dealname').val(data.empname);
                    $('#dealdeptno').val(data.deptno);
                    $('#dealdeptname').val(data.deptname);
                    $('#dealfactoryid').combobox('setValue', data.factoryid);
                    $('#dealemail').val(data.email);
                }
            }
        });
    }
}

//1.驗證項目階段為空 2.查看頁賦值
function valdChkbox() {
    var chk_value = [];
    $('input[name="projectstageName"]:checked').each(function () {
        chk_value.push($(this).val());
    })
    if (chk_value.length == 0) {
        alert("項目階段未選擇");
        return false;
    } else {
        var chk_values = '';
        $.each(chk_value, function (index, value) {
            chk_values += value + ',';
        })
        var chk_vals = chk_values.substring(0, chk_values.length - 1);
        $('#projectstage').val(chk_vals);
        return true;
    }
}


//圓鈕控制兩個欄位的顯示隱藏
function changeLeve(key1, key2, value) {
    // var changeLevel = function (key1, key2, value) {
    if (value == 1) { //使用
        $("#" + key1 + "").attr("disabled", false);
        $("#" + key2 + "").attr("disabled", false);
    } else { //禁用
        $("#" + key1 + "").attr("disabled", true);
        $("#" + key2 + "").attr("disabled", true);
    }
}


//刪除一筆
function deltr(index) {
     debugger;
    if (confirm("確定刪除此筆記錄？")) {
        if (index == 1) {
            alert("至少保留一筆！")
            return;
        }
        var b = $("#buildprojectApplyItemTable tr").length;
        $("#costItem" + index).remove();
        for (var i = index + 1, j = index; i <= b; i++, j++) {
            $("#costItem" + i).replaceWith("<tr align='center' id='costItem" + (i - 1) + "'>"
                + "<td>" + (i - 1) + "</td>"
                + "<td><input id='costname" + (i - 1) + "' name='tQhWfbuildprojectitemEntity[" + (i - 2) + "].costname' required='true' class='easyui-validatebox' style='width:400px;'><font color='red'>*</font></td>"
                + "<td><input id='costno" + (i - 1) + "' name='tQhWfbuildprojectitemEntity[" + (i - 2) + "].costno' required='true' class='easyui-validatebox' style='width:400px;'><font color='red'>*</font></td>"
                + "<td><input type='image' src='/static/images/deleteRow.png' onclick='deltr(" + (i - 1) + ");return false;'/></td>" + "</tr>");
        }
    }

}


//驗證申請人聯繫電話
function valdApplyTel(obj) {
    var phoneL = $.trim($("#dealtel").val()).split("").length;
    var tel = obj.value;
    if (tel != "") {
        var temp = tel;
        var regex = /^[0-9]{3}\+[0-9]{5}$/;
        var patt1 = new RegExp(regex);
        var result = patt1.test(tel);
        if (!result) {
            alert("分機格式有誤!");
            obj.value = "";
            obj.focus();
            return;
        }
    }
}

//驗證郵箱
function valdEmail(obj) {
    var email = obj.value;
    var temp = email;
    if(email=="")return;
    var regex = /\w+([-/+.]\w+)*@(mail.foxconn.com|foxconn.com|fii-foxconn.com)$/;
    var patt1=new RegExp(regex);
    var result = patt1.test(email);
    if(!result){
        alert("郵箱的格式不正確!");
        obj.value="";
        //obj.focus();
        obj.value =temp;
        return;
    };
    return true;
}

//驗證數字
function valdMoney(obj) {
    var money = obj.value;
    var temp = money;
    if (money == "") return;
    var regex = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
    var patt1 = new RegExp(regex);
    var result = patt1.test(money);
    if (!result) {
        alert("格式不正確!");
        obj.value = "";
        //obj.focus();
        obj.value = temp;
        return;
    }
    ;
}

//附件上傳
function uploadFile() {
    var attachIds = $.trim($("#attachids").val());
    var attachIdsL = attachIds.split(",").length;
    if (attachIdsL >= 6) {
        alert("上傳附件個數不能超過五個！");
        return;
    }
    // 创建
    var form_data = new FormData();
    // 获取文件
//  var file = document.getElementById('fileToUpload').files[0];
    var file_data = $("#attachidsUpload").prop("files")[0];
    form_data.append("file", file_data);
    form_data.append("path", "test");
    // 把所以表单信息
    $.ajax({
        type: "POST",
        url: ctx+"/admin/upload",
        beforeSend: ajaxLoading,
//            dataType : "json",
        processData: false,  // 注意：让jQuery不要处理数据
        contentType: false,  // 注意：让jQuery不要设置contentType
        data: form_data
    }).success(function (msg) {
        ajaxLoadEnd();
        $("#dowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href='+ctx+'"/admin/download/' + msg.id + '">' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delAtt(\'' + msg.id + '\')"></div></div>');
        $("#attachids").val($("#attachids").val() + msg.id + ",");
        // $("#attachids").val(msg.id);
    }).fail(function (msg) {
    });
}

function reUploadFile() {
    var attachIds = $.trim($("#reattachids").val());
    var attachIdsL = attachIds.split(",").length;
    if (attachIdsL >= 6) {
        alert("上傳附件個數不能超過五個！");
        return;
    }
    // 创建
    var form_data = new FormData();
    // 获取文件
//        var file = document.getElementById('fileToUpload').files[0];
    var file_data = $("#attachidsUpload").prop("files")[0];
    form_data.append("file", file_data);
    form_data.append("path", "test");
    // 把所以表单信息
    $.ajax({
        type: "POST",
        url: ctx+"/admin/upload",
        beforeSend: ajaxLoading,
//            dataType : "json",
        processData: false,  // 注意：让jQuery不要处理数据
        contentType: false,  // 注意：让jQuery不要设置contentType
        data: form_data
    }).success(function (msg) {
        ajaxLoadEnd();
        $("#reDowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href='+ctx+'"/admin/download/' + msg.id + '">' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delReAtt(\'' + msg.id + '\')"></div></div>');
        $("#reattachids").val($("#reattachids").val() + msg.id + ",");
        // $("#attachids").val(msg.id);
    }).fail(function (msg) {
    });
}

function delAtt(fileid) {
    $("#" + fileid).remove();
    var kk = $("#attachids").val();
    kk = kk.replace(fileid + ",", "");
    $("#attachids").val(kk);
    $.post(ctx+'/admin/delete/' + fileid, {}, function (data) {
        console.log(data);
        successTip(data);
    });
}
//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("clear");
}

//导出excel
function exportExcel(){
    var form = document.getElementById("searchFrom");
    searchFrom.action=ctx+'/tqhwfbuildprojectchange/exportExcel';
    form.submit();
}

