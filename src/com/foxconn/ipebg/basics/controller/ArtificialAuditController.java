package com.foxconn.ipebg.basics.controller;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Date;

import cn.hutool.core.date.DateUtil;
import com.foxconn.ipebg.common.utils.Result;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.basics.entity.ArtificialAuditEntity;
import com.foxconn.ipebg.basics.entity.BsArrangeEntity;
import com.foxconn.ipebg.basics.entity.TempArrangeEntity;
import com.foxconn.ipebg.basics.entity.ArtificialAuditErrorDto;

import com.foxconn.ipebg.basics.service.ArtificialAuditService;
import com.foxconn.ipebg.basics.service.ArtificialAuditService;
import com.foxconn.ipebg.basics.service.BsArrangeService;

import com.foxconn.ipebg.report.service.AbsentReportService;
import com.foxconn.ipebg.system.utils.CommonUtils;
import com.foxconn.ipebg.system.utils.Excel2007Utils;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import com.foxconn.ipebg.system.utils.FileDownloadUtil;
import com.foxconn.ipebg.system.utils.IPUtil;
import com.foxconn.ipebg.system.utils.ImportExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;

/**
 * 人工稽核錄入基本資料
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-18 09:09:19
 */
@Controller
@RequestMapping("artificialaudit")
public class ArtificialAuditController extends BaseController {

	@Autowired
	private ArtificialAuditService artificialAuditService;
	@Autowired
	private AbsentReportService absentReportService;

	@Autowired
	private BsArrangeService bsArrangeService;

	private static final String TPLFILEPATH = "/static/resources/templet";

	private static final String BATCHIMPORTTPL = "artificialaudit.xlsx";

	private static final String filePath = "/static/resources/download";

	/**
	 * 方法描述: 列表信息
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-04-18 09:09:19
	 * @Return
	 **/

	@RequestMapping(method = RequestMethod.GET)
	// @RequiresPermissions("basics:artificialaudit:list")
	public String list() {
		// 查询列表数据
		return "basics/artificialaudit/list";
	}

	/**
	 * 方法描述: 分頁查詢信息
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-04-18 09:09:19
	 * @Return
	 **/

	@RequestMapping(value = "list", method = RequestMethod.GET)
	// @RequiresPermissions("basics:artificialaudit:list")
	@ResponseBody
	public Map<String, Object> infoList(HttpServletRequest request) {
		Page<ArtificialAuditEntity> page = getPage(request);
		List<PropertyFilter> filters = PropertyFilter
				.buildFromHttpRequest(request);
		page = artificialAuditService.search(page, filters);
		return getEasyUIData(page);
	}

	/**
	 * 方法描述: 保存
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-04-18 09:09:19
	 * @Return
	 **/

	@RequestMapping(value = "create", method = RequestMethod.POST)
	@RequiresPermissions("basics:artificialaudit:add")
	@ResponseBody
	public String create(@Valid ArtificialAuditEntity artificialAudit,
			Model model, HttpServletRequest request) {
		String clientIP = IPUtil.getIpAddress(request);
		if (UserUtil.getCurrentUser().getIp().equals(clientIP)) {
			artificialAudit.setNewRecord(true);
			artificialAudit.setCreateDate(new Date());
			artificialAuditService.save(artificialAudit);
//			String abnormalState = "";
//			if (artificialAudit.getAbnormalState().equals("Y")) {
//				abnormalState = "1";
//			} else if (artificialAudit.getAbnormalState().equals("N")) {
//				abnormalState = "0";
//			}
//			absentReportService.updateAbnormalStateByEmpNo(
//					artificialAudit.getEmpNo(), artificialAudit.getShiftDate(),
//					abnormalState);
			return "success";
		} else {
			return "ip地址未綁定，無權限操作！";
		}

	}

	/**
	 * 方法描述: 添加跳轉
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-04-18 09:09:19
	 * @Return
	 **/

	@RequiresPermissions("basics:artificialaudit:add")
	@RequestMapping(value = "create", method = RequestMethod.GET)
	public String createForm(Model model, HttpServletRequest request) {
		String clientIP = IPUtil.getIpAddress(request);
		String tempIp = UserUtil.getCurrentUser().getIp()==null?"":UserUtil.getCurrentUser().getIp();
		if (tempIp.equals(clientIP)) {
			model.addAttribute("artificialAudit", new ArtificialAuditEntity());
			model.addAttribute("action", "create");
			return "basics/artificialaudit/listForm";
		} else {
			return "basics/artificialaudit/listFormError";
		}
	}

	/**
	 * 方法描述: 修改跳轉
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-06 11:04:39
	 * @Return
	 **/

	@RequiresPermissions("basics:artificialaudit:update")
	@RequestMapping(value = "update/{id}", method = RequestMethod.GET)
	public String updateForm(@PathVariable("id") String id, Model model) {
		model.addAttribute("artificialAudit", artificialAuditService.get(id));
		model.addAttribute("action", "update");
		return "basics/artificialaudit/listForm";
	}

	/**
	 * 方法描述: 修改
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-11 10:55:00
	 * @Return
	 **/

	@RequestMapping(value = "update", method = RequestMethod.POST)
	@RequiresPermissions("basics:artificialaudit:update")
	@ResponseBody
	public String update(@Valid ArtificialAuditEntity artificialAudit,
			@RequestParam(value = "ids") String ids) {
		artificialAudit.setId(ids);
		artificialAudit.setUpdateDate(new Date());
		artificialAuditService.update(artificialAudit);

		return "success";
	}

	/**
	 * 方法描述: 根據主鍵刪除
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-04-18 09:09:19
	 * @Return
	 **/

	@RequestMapping("delete/{id}")
	@RequiresPermissions("basics:artificialaudit:delete")
	@ResponseBody
	public String delete(@PathVariable("id") String id) {
		artificialAuditService.delete(id);
		return "success";
	}

	/**
	 * 导出excel
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-04-18 09:09:19
	 * @Return
	 **/
	@RequestMapping("exportExcel")
	public void exportExcel(HttpServletRequest request,
			HttpServletResponse response) throws Exception {
	}

	/**
	 * 方法描述: 下載批量上傳模板
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-22 18:57:46
	 * @Return
	 **/

	@RequestMapping(value = "downLoad/batchImportTpl", method = RequestMethod.GET)
	// @RequiresPermissions("other:tqhraymachinescrap:add")
	@ResponseBody
	public void batchImportTpl(HttpServletRequest request,
			HttpServletResponse response) {
		fileDownload(request, response, BATCHIMPORTTPL);
	}

	private void fileDownload(HttpServletRequest request,
			HttpServletResponse response, String fileName) {
		String path = request.getSession().getServletContext()
				.getRealPath(TPLFILEPATH);
		try {
			FileDownloadUtil.fileDownload(request, response, path, fileName);// 下載點位信息模板
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	/**
	 * 方法描述: 下載批量上傳異常信息
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-02-13 18:57:46
	 * @Return
	 **/

	@RequestMapping(value = "downLoad/errorExcel", method = RequestMethod.GET)
	// @RequiresPermissions("other:tqhraymachinescrap:add")
	@ResponseBody
	public void errorExcelDownload(HttpServletRequest request,
			HttpServletResponse response) throws IOException {

		List<ArtificialAuditErrorDto> errorDtos = (List<ArtificialAuditErrorDto>) request
				.getSession().getAttribute(
						ArtificialAuditService.ARTIFICIALAUDITERRORDTOS);
		if (errorDtos != null) {
			// 導出excel2007
			LinkedHashMap<String, String> map = this.getErrorFieldMap();

			String name = this.getCurUser().getLoginName() + "In";
			String tplPath = request.getSession().getServletContext()
					.getRealPath(filePath);
			File file = Excel2007Utils.createFile(errorDtos, map, tplPath,
					name, null);// 生成xlsx文件
			// long endOfBuildExcel = System.currentTimeMillis();
			// System.out.println("生成excel："+(endOfBuildExcel -
			// endOfLoadData)+"毫秒");
			String fileName = file.getName();
			Excel2007Utils.exportFile(response, tplPath, fileName);// 下载生成的xlsx文件
			UserUtil.getSession().removeAttribute(
					ArtificialAuditService.ARTIFICIALAUDITERRORDTOS);
		}

	}

	/**
	 * 批量導入
	 * 
	 * @param file
	 *            文檔
	 * @param isExcel2003
	 *            是否2003
	 * @return
	 */
	@RequestMapping(value = "upload", method = RequestMethod.POST)
	@ResponseBody
	public String upload(MultipartFile file, boolean isExcel2003, Model model,
			HttpServletRequest request) {

		String clientIP = IPUtil.getIpAddress(request);

		if (file.equals("") || file.getSize() <= 0) {
			return Constant.FAIL;
		} else if (!UserUtil.getCurrentUser().getIp().equals(clientIP)) {
			return "ip地址未綁定，無權限操作！";
		} else {
			String result = null;
			try {

				// result = artificialAuditService.batchImport(
				// file.getInputStream(), isExcel2003);
				result = this.batchImport(file.getInputStream(), isExcel2003);

			} catch (IOException e) {
				e.printStackTrace();
			}

			if (Constant.RESULT.CODE_YES.getValue().equals(result)) {
				return Constant.SUCCESS;
			} else {
				return result;
			}
		}

	}

	// *******************************導入相關邏輯**********
	/**
	 * 批量導入
	 * 
	 * @param in
	 *            excel文件輸入流
	 * @param isExcel2003
	 *            是否excel2003
	 * @return 是否成功
	 */

	public String batchImport(InputStream in, boolean isExcel2003) {
		try {

			List<ArtificialAuditErrorDto> artificialauditErrorDtos = null;
			ImportExcelUtil poi = new ImportExcelUtil();
			int success = 0, failed = 0;
			// 讀取上傳的excel file
			List<List<String>> list = poi.read(in, isExcel2003);
			// 判断需要的字段在Excel中是否都存在
			boolean isExist = true;
			Map<String, String> fieldMap = getFieldMap();
			Map<String, Integer> fieldIndexMap = new HashMap();
			List<String> excelFieldList = list.get(0);
			// 第一列是序號不需要
			for (int i = 1; i < excelFieldList.size(); i++) {
				if (!fieldMap.containsKey(excelFieldList.get(i))) {
					isExist = false;
					break;
				}
				fieldIndexMap.put(fieldMap.get(excelFieldList.get(i)), i);
			}
			// 如果有列名不存在，则抛出异常，提示错误
			if (!isExist) {
				return "有列名不存在，請對照模板";
			}
			// 導入數量限制
			// if (list.size()-1 > 10) {
			// return "資料導入不能大於100筆;";
			// }
			// 開始導入
			artificialauditErrorDtos = listToArtificialAuditErrorDto(list, 1,
					fieldIndexMap);

			if (artificialauditErrorDtos.size() > 0) {
				UserUtil.getSession().removeAttribute(
						ArtificialAuditService.ARTIFICIALAUDITERRORDTOS);
				UserUtil.getSession().setAttribute(
						ArtificialAuditService.ARTIFICIALAUDITERRORDTOS,
						artificialauditErrorDtos);
				return "導入成功"
						+ (list.size() - 1 - artificialauditErrorDtos.size())
						+ "筆，失敗" + artificialauditErrorDtos.size() + "筆!";
			}

		} catch (Exception e) {
			this.logger.info(e.getMessage(), e);
			return Constant.RESULT.CODE_NO.getValue();
		}
		return Constant.RESULT.CODE_YES.getValue();
	}

	public HashMap<String, String> getFieldMap() {
		HashMap<String, String> fieldMap = new HashMap<String, String>();

		fieldMap.put("排班日期", "shiftDate");
		fieldMap.put("班別", "shiftNo");
		fieldMap.put("工號", "empNo");
		fieldMap.put("狀態", "abnormalState");
		fieldMap.put("第一次稽核結果", "abnormalResult1");
		fieldMap.put("第一次稽核原因", "abnormalCauses1");
		fieldMap.put("第二次稽核結果", "abnormalResult2");
		fieldMap.put("第二次稽核原因", "abnormalCauses2");

		return fieldMap;
	}

	public LinkedHashMap<String, String> getErrorFieldMap() {
		LinkedHashMap<String, String> fieldMap = new LinkedHashMap<String, String>();

		fieldMap.put("shiftDate", "排班日期");
		fieldMap.put("shiftNo", "班別");
		fieldMap.put("empNo", "工號");
		fieldMap.put("abnormalState", "狀態");
		fieldMap.put("abnormalResult1", "第一次稽核結果");
		fieldMap.put("abnormalCauses1", "第一次稽核原因");
		fieldMap.put("abnormalResult2", "第二次稽核結果");
		fieldMap.put("abnormalCauses2", "第二次稽核原因");

		fieldMap.put("errorLog", "導入失敗原因");

		return fieldMap;
	}

	/**
	 * @param list
	 *            列表
	 * @param startIndex
	 *            開始的行
	 * @param fieldIndexMap
	 *            list中的index和类的英文属性的对应关系Map
	 * @return
	 * @throws IllegalAccessException
	 * @throws InstantiationException
	 */
	@Transactional
	public List<ArtificialAuditErrorDto> listToArtificialAuditErrorDto(
			List<List<String>> list, int startIndex,
			Map<String, Integer> fieldIndexMap) {
		// 定义要返回的list
		List<ArtificialAuditErrorDto> resultList = new ArrayList<ArtificialAuditErrorDto>();
		// 将sheet转换为list

		ArtificialAuditErrorDto entity = null;
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		Date shiftDate = new Date();
		for (int i = startIndex; i < list.size(); i++) {
			// 新建要转换的对象

			entity = new ArtificialAuditErrorDto();
			if (CommonUtils.isValidDate(list.get(i).get(1))) {
				//
				if (list.get(i).get(6).length()<51 && list.get(i).get(8).length()<51) {

					try {
						shiftDate = format.parse(list.get(i).get(1));
					} catch (ParseException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}

					BsArrangeEntity bsArrangeEntity = bsArrangeService
							.isExistEmpInfo(list.get(i).get(3), list.get(i).get(2), shiftDate);
					// 驗證人員是否排班
					if (bsArrangeEntity != null) {

						// artificialAuditEntity.setNewRecord(false);
						// artificialAuditEntity.setId(IdGen.uuid());
						ArtificialAuditEntity artificialAuditEntity = new ArtificialAuditEntity();

						artificialAuditEntity.setShiftDate(shiftDate);
						artificialAuditEntity.setEmpNo(list.get(i).get(3));
						artificialAuditEntity.setAbnormalState(list.get(i).get(
								4));
						artificialAuditEntity.setAbnormalResult1(list.get(i)
								.get(5));
						artificialAuditEntity.setAbnormalCauses1(list.get(i)
								.get(6));
						artificialAuditEntity.setAbnormalResult2(list.get(i)
								.get(7));
						artificialAuditEntity.setAbnormalCauses2(list.get(i)
								.get(8));

						artificialAuditEntity.setEmpName(bsArrangeEntity
								.getEmpName());
						artificialAuditEntity.setPostRecno(bsArrangeEntity
								.getPostRecno());
						artificialAuditEntity.setPostName(bsArrangeEntity
								.getPostName());
						artificialAuditEntity.setShiftNo(bsArrangeEntity
								.getShiftNo());
						artificialAuditEntity.setShiftName(bsArrangeEntity
								.getShiftName());
						artificialAuditEntity.setCreateBy(UserUtil
								.getCurrentUser().getLoginName());
						artificialAuditEntity.setCreateDate(new Date());
						artificialAuditEntity.setCompany(bsArrangeEntity.getCompany());
//						String abnormalState = "";
//						if (artificialAuditEntity.getAbnormalState()
//								.equals("Y")) {
//							abnormalState = "1";
//						} else if (artificialAuditEntity.getAbnormalState()
//								.equals("N")) {
//							abnormalState = "0";
//						}
//
//						absentReportService.updateAbnormalStateByEmpNo(list
//								.get(i).get(3), shiftDate, abnormalState);
						artificialAuditService.save(artificialAuditEntity);
					} else// 人員未排班
					{
						entity.setShiftDate(shiftDate);
						entity.setShiftNo(list.get(i).get(2));
						entity.setEmpNo(list.get(i).get(3));
						entity.setAbnormalState(list.get(i).get(4));
						entity.setAbnormalResult1(list.get(i).get(5));
						entity.setAbnormalCauses1(list.get(i).get(6));
						entity.setAbnormalResult2(list.get(i).get(7));
						entity.setAbnormalCauses2(list.get(i).get(8));
						entity.setErrorLog("人員未排班！");
						resultList.add(entity);
					}
				}
				else
				{
					entity.setShiftDate(shiftDate);
					entity.setShiftNo(list.get(i).get(2));
					entity.setEmpNo(list.get(i).get(3));
					entity.setAbnormalState(list.get(i).get(4));
					entity.setAbnormalResult1(list.get(i).get(5));
					entity.setAbnormalCauses1(list.get(i).get(6));
					entity.setAbnormalResult2(list.get(i).get(7));
					entity.setAbnormalCauses2(list.get(i).get(8));
					entity.setErrorLog("異常原因必須在50字以內！");
					resultList.add(entity);
				}
			} else {
				entity.setShiftNo(list.get(i).get(2));
				entity.setEmpNo(list.get(i).get(3));
				entity.setAbnormalState(list.get(i).get(4));
				entity.setAbnormalResult1(list.get(i).get(5));
				entity.setAbnormalCauses1(list.get(i).get(6));
				entity.setAbnormalResult2(list.get(i).get(7));
				entity.setAbnormalCauses2(list.get(i).get(8));
				entity.setErrorLog("日期格式錯誤！");
				resultList.add(entity);
			}

		}
		return resultList;
	}
	// *******************************導入相關邏輯**********

	/**
	 * 重新比对警卫人员考勤
	 * */
	@RequestMapping(value = "checkAbnormalPerson", method = RequestMethod.POST)
	@ResponseBody
	public Result checkAbnormalPerson(@RequestParam(value = "date") String date) {
		Result result = new Result();
		try {
			Date checkDate = DateUtil.parse(date, "yyyy-MM-dd");
			artificialAuditService.checkAbnormalPerson(checkDate);
			result.setCode(Result.SUCCESS);
			result.setMsg("比對完成");
		} catch (SQLException e) {
			e.printStackTrace();
			result.setCode(Result.FAILURE);
			result.setMsg("比對失敗："+e.getMessage());
		}
		return result;
	}
}
