package com.foxconn.ipebg.basics.controller;

import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.json.simple.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.utils.ConvertUtils;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.basics.entity.BsDptEntity;

import com.foxconn.ipebg.basics.service.BsDptService;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;

import com.foxconn.ipebg.buessness.workflow.entity.TaskNode;
import com.foxconn.ipebg.system.entity.Dict;
import com.foxconn.ipebg.system.entity.TQhChargepathEntity;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;

import com.foxconn.ipebg.system.service.OrganizationService;
import com.foxconn.ipebg.system.service.DictService;
import com.foxconn.ipebg.system.service.TPubFileobjectService;


/**
 * 部門基本資料
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-12-21 14:54:46
 */
@Controller
@RequestMapping("bsdpt")
public class BsDptController extends BaseController {

    @Autowired
    private BsDptService bsDptService;


    /**
      * 方法描述: 列表信息
      * @Author: S6113712
      * @CreateDate:   2018-12-21 14:54:46
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:bsdpt:list")
    public String list() {
        //查询列表数据
        return "basics/bsdpt/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6113712
      * @CreateDate:   2018-12-21 14:54:46
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:bsdpt:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
  
		
        Page<BsDptEntity> page = getPage(request);
        //name = name == null ? "" : URLDecoder.decode(name, "UTF-8");
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);  
        page = bsDptService.search(page, filters);
        return getEasyUIData(page);
    }
    /**
     * 方法描述: 保存
     * @Author: S6113712
     * @CreateDate:   2018-12-21 14:54:46
     * @Return
     **/

   
    @RequestMapping(value="create", method = RequestMethod.POST)
    //@RequiresPermissions("basics:bsdpt:add")
    @ResponseBody
    public String create(@Valid BsDptEntity bsDpt, Model model) {
        
    	bsDpt.setNewRecord(true);
    	bsDpt.setCreateDate(new Date());
        bsDptService.save(bsDpt);
        return "success";
    }
    
    /**
      * 方法描述: 添加跳轉
      * @Author: S6113712
      * @CreateDate:   2018-12-21 14:54:46
      * @Return
      **/

    //@RequiresPermissions("basics:bsdpt:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("bsDpt", new BsDptEntity());
        model.addAttribute("action", "create");
        return "basics/bsdpt/listForm";
    }
    /**
     * 方法描述: 修改跳轉
     * @Author: S6113712
     * @CreateDate:   2018-10-06 11:04:39
     * @Return
     **/

   //@RequiresPermissions("basics:bsdpt:update")
   @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
   public String updateForm(@PathVariable("id") String id, Model model) {
       model.addAttribute("bsDpt", bsDptService.get(id));
       model.addAttribute("action", "update");
       return "basics/bsdpt/listForm";
   }
   
	/**
	 * 方法描述: 修改
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-11 10:55:00
	 * @Return
	 **/

   @RequestMapping(value = "update", method = RequestMethod.POST)
   //@RequiresPermissions("basics:bsdpt:update")
   @ResponseBody
   public String update(@Valid BsDptEntity bsDpt,@RequestParam(value = "ids") String ids) {
	   bsDpt.setId(ids);
	   bsDpt.setUpdateDate(new Date());
	   bsDptService.update(bsDpt);
       return "success";
   }
    /**
      * 方法描述: 根據主鍵刪除
      * @Author: S6113712
      * @CreateDate:   2018-12-21 14:54:46
      * @Return
      **/

    @RequestMapping("delete/{id}")
    //@RequiresPermissions("basics:bsdpt:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        bsDptService.delete(id);
        return "success";
    }
    /**
      * 导出excel
      * @Author: S6113712
      * @CreateDate:   2018-12-21 14:54:46
      * @Return
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {}
    /**
     * 下拉框
     * @Author: S6113712
     * @CreateDate:   2018-02-14 08:40:25
     * @Return
     **/
	@ResponseBody
	@RequestMapping(value = "getDptInfoId")  
	public String getDptInfoId(Model model,HttpServletRequest request) {
	  List rst=bsDptService.getDptInfoId();
	  String returnInfo="";
      try{
        
         String[] strs = {"value","label"};
         List<Map<String, Object>> mapList = listToMapList(strs, rst); // 將查詢結果轉換為list
         returnInfo = JSONArray.toJSONString(mapList);
      }catch(Exception e){
          returnInfo= "error";    
      }
	  
	  //return JSONArray.toJSONString(rst);
	  
      return returnInfo;
      
	}
    /**
     * List<Object>轉換為List<Map<String, Object>>
     * @param strs strs
     * @param result result
     * @return result
     */
    public static List<Map<String, Object>> listToMapList(String[] strs, List<?> result) {
        List<Map<String, Object>> mapList = new ArrayList<Map<String, Object>>();
        for (int i = 0; i < result.size(); i++) {
            Object[] objs = (Object[]) result.get(i);
            Map<String, Object> map = new HashMap<String, Object>();
            for (int j = 0; j < strs.length; j++) {
                String[] strss = strs[j].split(";");
                if (strss.length == 1) {
                    map.put(strss[0], objs[j].toString());
                } else if (strss.length == 2) {
                    if (strss[1].toUpperCase().equals("D")) {
                        map.put(strss[0], getDateStr((Date) objs[j], null));
                    }
                }
            }
            mapList.add(map);
        }
        return mapList;
    }
    /**
     * @param date date
     * @param dateFormat dateFormat
     * @return dateFormat
     */
    public static String getDateStr(Date date, String dateFormat) {
        String df = dateFormat;
        String strNow;
        if (df == null || df.equals("")) {
            df = "yyyy/MM/dd HH:mm:ss";
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(df);
            strNow = sdf.format(date);
        } catch (Exception e) {
            strNow = "";
        }
        return strNow;
    }

    /**
     * 方法描述: 生成下拉數據
     * @Author: S6114893
     * @CreateDate:   2019/9/24
     * @Return
     **/

    @ResponseBody
    @RequestMapping(value = "getAllBusinessGroup")
    public List<BsDptEntity> getAllBusinessGroup(Model model) {
        return bsDptService.getAllBusinessGroup();
    }
}
