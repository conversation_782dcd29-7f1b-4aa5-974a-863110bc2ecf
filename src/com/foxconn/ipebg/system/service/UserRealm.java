package com.foxconn.ipebg.system.service;

import com.foxconn.ipebg.buessness.common.entity.TQhUserformhsEntity;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.security.Digests;
import com.foxconn.ipebg.common.utils.security.Encodes;
import com.foxconn.ipebg.system.entity.*;
import com.foxconn.ipebg.system.utils.CaptchaException;
import com.foxconn.ipebg.system.utils.IPUtil;
import com.foxconn.ipebg.system.utils.IpException;
import com.foxconn.ipebg.system.utils.UsernamePasswordCaptchaToken;
import com.foxconn.so.LibSsoUtil;
import com.google.common.base.Objects;
import com.org.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.authc.credential.HashedCredentialsMatcher;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.session.InvalidSessionException;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.util.ByteSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.naming.Context;
import javax.naming.InitialContext;
import javax.naming.NamingException;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.HashSet;
import java.util.List;
import java.util.Properties;
import java.util.Set;

import static com.foxconn.ipebg.system.service.UserService.HASH_INTERATIONS;

/**
 * 用户登录授权service(shrioRealm)
 * <AUTHOR>
 * @date 2015年1月14日
 */
@Service
@DependsOn({"userDao","permissionDao","rolePermissionDao","userRoleDao"})
public class UserRealm extends AuthorizingRealm {
private Logger logger = LoggerFactory.getLogger(UserRealm.class);
	@Autowired
	private UserService userService;
	@Autowired
	private PermissionService permissionService;
	@Autowired
	private DictService dictService;
	@Autowired
	private TQhUserformhsService userformhsService;
	@Autowired
	private UserRoleService roleService;
	/**
	 * 认证回调函数,登录时调用.
	 */
	@Override
	protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken authcToken) throws AuthenticationException {
		try {
			UsernamePasswordCaptchaToken token = (UsernamePasswordCaptchaToken) authcToken;
			User user = userService.getUser(token.getUsername());

			//如果用戶不存在，默認生成用戶密碼為SHZBG的用戶，并賦值默認權限
			if(user==null){
				return null;
//                TQhUserformhsEntity userformhsEntity = userformhsService.findByEmpno(token.getUsername());
//                if(userformhsEntity!=null) {
//                    user = new User();
//                    user.setPlainPassword("SHZBG");
//                    user.setLoginName(token.getUsername());
//                    user.setBirthday(Timestamp.valueOf(userformhsEntity.getBirthday().toString()));
//                    user.setGender(Short.valueOf(userformhsEntity.getSex()));
//                    user.setName(userformhsEntity.getEmpname());
//                    userService.save(user);
//                    //賦值默認角色 來客角色
//                    Set<UserRole> roleList = new HashSet<UserRole>();
//                    UserRole userRole = roleService.getUserRole(user.getId(),5);
//                    roleList.add(userRole);
//                    user.setUserRoles(roleList);
//                    roleService.save(userRole);
//                    logger.info(String.format("new user %s is insert!",user.getLoginName()));
//                }
            }

			byte[] salt = Encodes.decodeHex(user.getSalt());
			String pwd = null;
			String loginType = token.getLoginType();
			if(Constant.LoginType.CIPHER_CODE.getValue().equals(loginType)){
                //免密碼登錄
                Boolean utoken = userService.getUtokenCipherCode(token);
                if(!utoken){
                    return null;
                }else{
                    byte[] hashPassword = Digests.sha1(Constant.DEF_PASSWORD.getBytes(),salt, HASH_INTERATIONS);
                    pwd = Encodes.encodeHex(hashPassword);
                }
            }else if(Constant.LoginType.PHONE_CAPTCHA.getValue().equals(loginType)){
                //手機驗證碼登錄
                String utoken = userService.getUtokenPhone(token.getUsername());

                if(!utoken.equals(token.getUtoken())){
                    return null;
                }else{
                    byte[] hashPassword = Digests.sha1(Constant.DEF_PASSWORD.getBytes(),salt, HASH_INTERATIONS);
                    pwd = Encodes.encodeHex(hashPassword);
                }
            }else if(Constant.LoginType.SESSION_LOGIN.getValue().equals(loginType)){
					byte[] hashPassword = Digests.sha1(Constant.DEF_PASSWORD.getBytes(),salt, HASH_INTERATIONS);
					pwd = Encodes.encodeHex(hashPassword);
			}else{
                boolean result=authenticate(token.getUsername(),new String(token.getPassword()), token.getIp());
                if(!result){
                    pwd = user.getPassword();
                }else{
                    byte[] hashPassword = Digests.sha1(new String(token.getPassword()).getBytes(),salt, HASH_INTERATIONS);
                    pwd = Encodes.encodeHex(hashPassword);
                }
                //正常登錄

            }
			if (user != null&&doCaptchaValidate(token)&&doIPValidate(user, token)) {
    //		if (user != null) {
                ShiroUser shiroUser=new ShiroUser(user.getId(), user.getLoginName(), user.getName());
                //设置用户session
                Session session =SecurityUtils.getSubject().getSession();
                session.setAttribute("user", user);
                //人員人事資料數據
                TQhUserformhsEntity userMoreInfo = userformhsService.findByEmpno(user.getLoginName());
                session.setAttribute("userMoreInfo",userMoreInfo);
                return new SimpleAuthenticationInfo(shiroUser,pwd, ByteSource.Util.bytes(salt), getName());
            } else {
                return null;
            }
		} catch (NumberFormatException e) {
			e.printStackTrace();
		} catch (InvalidSessionException e) {
			e.printStackTrace();
		}
		return null;
//		UsernamePasswordCaptchaToken token = (UsernamePasswordCaptchaToken) authcToken;
//		User user = userService.getUser(token.getUsername());
//
////		if (user != null&&doCaptchaValidate(token)) {
//		if (user != null) {
//			byte[] salt = Encodes.decodeHex(user.getSalt());
//			ShiroUser shiroUser=new ShiroUser(user.getId(), user.getLoginName(), user.getName());
//			//设置用户session
//			Session session =SecurityUtils.getSubject().getSession();
//			session.setAttribute("user", user);
//			return new SimpleAuthenticationInfo(shiroUser,user.getPassword(), ByteSource.Util.bytes(salt), getName());
//		} else {
//			return null;
//		}
	}

	/**
	 * 使用一帳通登錄
	 * @param empNo 賬號
	 * @param passWord 密碼
	 * @return
	 */
	public boolean authenticate(String empNo, String passWord, String ip){
//		return true;
		if(StringUtils.isEmpty(empNo)||StringUtils.isEmpty(passWord)){
			System.err.println("賬號或者密碼為空，不允許登錄.");
			return false;
		}
		try {
			JSONObject response = LibSsoUtil.authenticate(empNo, passWord, ip, "sgas", "iPEBG");
			logger.info("LibSsoUtil.authenticate 返回："+response);
			return response.getString("code").equals("0000");
		} catch (Throwable e) {
			e.printStackTrace();
		}
		return false;
	}

	/**
	 * 授权查询回调函数, 进行鉴权但缓存中无用户的授权信息时调用.
	 */
	@Override
	protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
		ShiroUser shiroUser = (ShiroUser) principals.getPrimaryPrincipal();
		User user = userService.getUser(shiroUser.loginName);
		
		//把principals放session中 key=userId value=principals
		SecurityUtils.getSubject().getSession().setAttribute(String.valueOf(user.getId()),SecurityUtils.getSubject().getPrincipals());
		
		SimpleAuthorizationInfo info = new SimpleAuthorizationInfo();
		//赋予角色
		for(UserRole userRole:user.getUserRoles()){
			info.addRole(userRole.getRole().getName());
		}
		//赋予权限
		for(Permission permission:permissionService.getPermissions(user.getId())){
			if(StringUtils.isNotBlank(permission.getPermCode()))
			info.addStringPermission(permission.getPermCode());
		}
		
		//设置登录次数、时间
		userService.updateUserLogin(user);
		return info;
	}


	/**
	 * 验证码校验
	 * @param token
	 * @return boolean
	 */
	protected boolean doCaptchaValidate(UsernamePasswordCaptchaToken token)
	{
		String captcha = (String) SecurityUtils.getSubject().getSession().getAttribute(com.google.code.kaptcha.Constants.KAPTCHA_SESSION_KEY);
		if (captcha != null &&!captcha.equalsIgnoreCase(token.getCaptcha())){
			throw new CaptchaException("验证码错误！");
		}
		return true;
	}

	/**
	 * 驗證綁定的IP
	 */
	boolean doIPValidate(User user, UsernamePasswordCaptchaToken token) {
		List<Dict> configs = dictService.getDictByType("ip_control");
		if (configs.isEmpty()) {
			return true;
		}
		Dict ipControl = configs.get(0);
		String allowdIP = null;
		if (ipControl.getValue().equals("global")) {
			allowdIP = ipControl.getRemark();
		} else if (ipControl.getValue().equals("user")) {
			allowdIP = user.getIp();
		}
		if (!IPUtil.isIPMatchRules(token.getIp(), allowdIP)) {
			throw new IpException("IP 未授權");
		}
		return true;
	}

	/**
	 * 设定Password校验的Hash算法与迭代次数.
	 */
	@SuppressWarnings("static-access")
	@PostConstruct
	public void initCredentialsMatcher() {
		HashedCredentialsMatcher matcher = new HashedCredentialsMatcher(UserService.HASH_ALGORITHM);
		matcher.setHashIterations(HASH_INTERATIONS);
		setCredentialsMatcher(matcher);
	}

	/**
	 * 自定义Authentication对象，使得Subject除了携带用户的登录名外还可以携带更多信息.
	 */
	public static class ShiroUser implements Serializable {
		private static final long serialVersionUID = -1373760761780840081L;
		public Integer id;
		public String loginName;
		public String name;

		public ShiroUser(Integer id, String loginName, String name) {
			this.id = id;
			this.loginName = loginName;
			this.name = name;
		}

		public Integer getId(){
			return id;
		}

		public String getName() {
			return name;
		}

		/**
		 * 本函数输出将作为默认的<shiro:principal/>输出.
		 */
		@Override
		public String toString() {
			return loginName;
		}

		/**
		 * 重载hashCode,只计算loginName;
		 */
		@Override
		public int hashCode() {
			return Objects.hashCode(loginName);
		}

		/**
		 * 重载equals,只计算loginName;
		 */
		@Override
		public boolean equals(Object obj) {
			if (this == obj) {
				return true;
			}
			if (obj == null) {
				return false;
			}
			if (getClass() != obj.getClass()) {
				return false;
			}
			ShiroUser other = (ShiroUser) obj;
			if (loginName == null) {
				if (other.loginName != null) {
					return false;
				}
			} else if (!loginName.equals(other.loginName)) {
				return false;
			}
			return true;
		}
	}
	
	@Override
    public void clearCachedAuthorizationInfo(PrincipalCollection principals) {
        super.clearCachedAuthorizationInfo(principals);
    }

    @Override
    public void clearCachedAuthenticationInfo(PrincipalCollection principals) {
        super.clearCachedAuthenticationInfo(principals);
    }

    @Override
    public void clearCache(PrincipalCollection principals) {
        super.clearCache(principals);
    }

    public void clearAllCachedAuthorizationInfo() {
        getAuthorizationCache().clear();
    }

    public void clearAllCachedAuthenticationInfo() {
        getAuthenticationCache().clear();
    }

    public void clearAllCache() {
        clearAllCachedAuthenticationInfo();
        clearAllCachedAuthorizationInfo();
    }
 
}
