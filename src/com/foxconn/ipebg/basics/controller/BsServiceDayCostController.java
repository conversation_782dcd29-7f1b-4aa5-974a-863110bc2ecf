package com.foxconn.ipebg.basics.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.foxconn.ipebg.basics.entity.BsServiceDayCostEntity;
import com.foxconn.ipebg.basics.service.*;
import com.foxconn.ipebg.basics.vo.BsServiceDayCostVO;
import com.foxconn.ipebg.basics.vo.FinancialVO;
import com.foxconn.ipebg.basics.vo.TempWorkcostGatherVO;
import com.foxconn.ipebg.buessness.audit.service.TempSecurityCostAplyService;
import com.foxconn.ipebg.common.exception.ExcelException;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.utils.Result;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.system.service.DictService;
import com.foxconn.ipebg.system.utils.CustomExcelUtil;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.Colour;
import jxl.format.VerticalAlignment;
import jxl.format.*;
import jxl.write.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;


/**
 * 警衛服務費用結賬明細主表v2
 * 
 * <AUTHOR>
 * @date 2022-08-12
 */
@Controller
@RequestMapping("bsservicedaycost")
public class BsServiceDayCostController extends BaseController {

    @Autowired
    private BsServiceDayCostService bsServiceDayCostService;
    @Autowired
    private DictService dictService;
    @Autowired
    private BsDptService bsDptService;
    @Autowired
    private BsArrangePostPersonService bsArrangePostPersonService;
    @Autowired
    private TempSecurityCostAplyService tempSecurityCostAplyService;

    /**
      * 方法描述: 列表信息
      * @Author: S6114894
      * @CreateDate:   2019-10-07 13:15:24
      * @Return
      **/
    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:bsservicecost:list")
    public String list(Model model) {
        //法人
        model.addAttribute("legalPersonDicts", JSON.toJSONString(dictService.getDictByType("guard_legalperson")));
        //崗位類別
//        result.put("postTypeDicts",dictService.getDictByType("guard_postType"));
        //部門信息
        model.addAttribute("bsDptList",JSON.toJSONString(bsDptService.getAll()));
        //查询列表数据
        return "basics/bsservicedaycost/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: ********
      * @CreateDate: 2022-08-12
      * @Return
      **/
    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:bsservicecost:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        String qun = request.getParameter("filter_EQS_dptQun");
        String legal = request.getParameter("filter_EQS_legal");
        String securityCom = request.getParameter("filter_EQS_securityCom");
        String startDate = request.getParameter("filter_GED_workDate");
        String endDate = request.getParameter("filter_LED_workDate");
        Page<BsServiceDayCostEntity> page = getPage(request);
        page = bsServiceDayCostService.getGroupedEntityList(page, qun, legal, securityCom, startDate, endDate);

        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        List<String> recnoListInCurrentPage = new ArrayList<>();
        for (BsServiceDayCostEntity position: page.getResult()) {
            recnoListInCurrentPage.add(position.getRecno());
        }
        if (recnoListInCurrentPage.size() == 0) {
            recnoListInCurrentPage.add("");
        }
        filters.add(new PropertyFilter("INS_recno", recnoListInCurrentPage));
        List<BsServiceDayCostEntity> detailsList = bsServiceDayCostService.search(filters);
        List<BsServiceDayCostVO> rows = new ArrayList<>();
        for (BsServiceDayCostEntity position : page.getResult()) {
            BsServiceDayCostVO vo = new BsServiceDayCostVO();
            vo.setCostEntity(position); // 主表
            Integer perNuA = 0;
            Integer perNuB = 0;
            for (BsServiceDayCostEntity detail : detailsList) {
                if (position.getRecno().equals(detail.getRecno()) &&
                        position.getDptQun().equals(detail.getDptQun()) &&
                        position.getLegal().equals(detail.getLegal()) &&
                        position.getDptBu().equals(detail.getDptBu()) &&
                        position.getCostId().equals(detail.getCostId()) &&
                        position.getPostName().equals(detail.getPostName())) {
                    perNuA += detail.getPernuA();
                    perNuB += detail.getPernuB();
                    String dateString = DateUtil.format(detail.getWorkDate(), "yyyyMMdd");
                    vo.getDays().put(dateString, detail.getPernuA()+","+detail.getPernuB());
                }
            }
            vo.getDays().put("perNuAB", perNuA+","+perNuB);
            rows.add(vo);
        }
        Page<BsServiceDayCostVO> newPage = new Page<>();
        newPage.setResult(rows);
        newPage.setPageNo(page.getPageNo());
        newPage.setPageSize(page.getPageSize());
        newPage.setTotalCount(page.getTotalCount());
        return getEasyUIData(newPage);
    }

   /**
    * 方法描述：查看匯總信息
    * */
   @RequestMapping(value = "summary", method = RequestMethod.GET)
   public String summary() {
       return "basics/bsservicedaycost/summary";
   }

   @ResponseBody
   @RequestMapping(value = "getSummary", method = RequestMethod.GET)
   public BsServiceDayCostEntity getSummary(@RequestParam("qun") String qun,
                                            @RequestParam("legal") String legal,
                                            @RequestParam("securityCom") String securityCom,
                                            @RequestParam("beginDate") String beginDate,
                                            @RequestParam("endDate") String endDate) {
       BsServiceDayCostEntity summary = bsServiceDayCostService.getSummary(qun, legal, securityCom, beginDate, endDate);
       return summary;
   }

    /**
      * 导出excel
      * @Author: ********
      * @CreateDate: 2022-08-23
      * @Return
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) {
        CustomExcelUtil excelUtil = null;
        try {
            excelUtil = new CustomExcelUtil(response, null);
            String sheetName = "園區警衛服務費用結報明細表";
            // 獲取參數
            String qun = request.getParameter("filter_EQS_dptQun");
            String legal = request.getParameter("filter_EQS_legal");
            String securityCom = request.getParameter("filter_EQS_securityCom");
            String startDate = request.getParameter("filter_GED_workDate");
            String endDate = request.getParameter("filter_LED_workDate");
            // 匯總
            Page<BsServiceDayCostEntity> page = getPage(request);
            page.setPageNo(1);
            page.setPageSize(Page.MAXSIZE);
            page = bsServiceDayCostService.getGroupedEntityList(page, qun, legal, securityCom, startDate, endDate);
            // 明細
            List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
            List<BsServiceDayCostEntity> detailList = bsServiceDayCostService.search(filters);
            // 獲取匯總
            BsServiceDayCostEntity summary = bsServiceDayCostService.getSummary(qun, legal, securityCom, startDate, endDate);
            // vo
            List<BsServiceDayCostVO> rows = new ArrayList<>();
            for (BsServiceDayCostEntity position : page.getResult()) {
                BsServiceDayCostVO vo = new BsServiceDayCostVO();
                vo.setCostEntity(position);
                Integer perNuA = 0;
                Integer perNuB = 0;
                for (BsServiceDayCostEntity detail : detailList) {
                    if (position.getRecno().equals(detail.getRecno()) &&
                            position.getDptQun().equals(detail.getDptQun()) &&
                            position.getLegal().equals(detail.getLegal()) &&
                            position.getDptBu().equals(detail.getDptBu()) &&
                            position.getCostId().equals(detail.getCostId()) &&
                            position.getPostName().equals(detail.getPostName())) {
                        perNuA += detail.getPernuA();
                        perNuB += detail.getPernuB();
                        String dateString = DateUtil.format(detail.getWorkDate(), "yyyyMMdd");
                        vo.getDays().put(dateString, detail.getPernuA()+","+detail.getPernuB());
                    }
                }
                vo.getDays().put("perNuAB", perNuA+","+perNuB);
                rows.add(vo);
            }
            // 計算需要幾個Sheet
            int sheetSize = 65533 / 2;
            double sheetNum = Math.ceil(rows.size() / new Integer(sheetSize).doubleValue());
            if (sheetNum == 1) {
                excelUtil.createNewSheet(sheetName, 0);
                setupExcelHeader(excelUtil.getCurrentSheet(), startDate, endDate);
                fillSheet(excelUtil.getCurrentSheet(), rows, 0, rows.size() - 1, startDate, endDate);
                int summaryIndex = 2 + rows.size() * 2;
                if (rows.size() >= sheetSize) {
                    excelUtil.createNewSheet(sheetName, 1);
                    setupExcelHeader(excelUtil.getCurrentSheet(), startDate, endDate);
                    summaryIndex = 2;
                }
                fillSummary(excelUtil.getCurrentSheet(), summary, summaryIndex);
            } else {
                int summaryIndex = 0;
                for (int i = 0; i < sheetNum; i++) {
                    excelUtil.createNewSheet(sheetName, i);
                    setupExcelHeader(excelUtil.getCurrentSheet(), startDate, endDate);
                    int firstIndex = i * sheetSize;
                    int lastIndex = (i + 1) * sheetSize - 1 > rows.size() - 1 ? rows
                            .size() - 1 : (i + 1) * sheetSize - 1;
                    fillSheet(excelUtil.getCurrentSheet(), rows, firstIndex, lastIndex, startDate, endDate);
                    if (i == sheetNum - 1) {
                        if (lastIndex - firstIndex >= sheetSize) {
                            excelUtil.createNewSheet(sheetName, i+1);
                            setupExcelHeader(excelUtil.getCurrentSheet(), startDate, endDate);
                            summaryIndex = 2;
                        } else {
                            summaryIndex = 2 + (lastIndex - firstIndex + 1) * 2;
                        }
                    }
                }
                fillSummary(excelUtil.getCurrentSheet(), summary, summaryIndex);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (excelUtil != null) {
                try {
                    excelUtil.closeWorkBook();
                } catch (ExcelException e) {}
            }
        }
    }

    private void setupExcelHeader(WritableSheet sheet, String beginDateString, String endDateString) throws WriteException {
        Date beginDate = DateUtil.parse(beginDateString, "yyyy-MM-dd");
        Date endDate = DateUtil.parse(endDateString, "yyyy-MM-dd");
        Integer dayCount = (int)(endDate.getTime() - beginDate.getTime())/1000/60/60/24 + 1;
        // 表头样式
        WritableFont headerFont = new WritableFont(WritableFont.ARIAL, 11, WritableFont.BOLD, false, UnderlineStyle.NO_UNDERLINE, Colour.BLACK);
        WritableCellFormat headerFormat = new WritableCellFormat(headerFont);
        headerFormat.setAlignment(Alignment.CENTRE);
        headerFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
        headerFormat.setBorder(Border.ALL, BorderLineStyle.THIN, Colour.BLACK);
        headerFormat.setWrap(true);
        Label index1 = new Label(0, 0, "序號", headerFormat);
        sheet.addCell(index1);
        sheet.mergeCells(0, 0, 0, 1);
        sheet.setColumnView(0, 5);
        Label qun1 = new Label(1, 0, "事業群", headerFormat);
        sheet.addCell(qun1);
        sheet.mergeCells(1, 0, 1, 1);
        sheet.setColumnView(1, 15);
        Label faren1 = new Label(2, 0, "法人", headerFormat);
        sheet.addCell(faren1);
        sheet.mergeCells(2, 0, 2, 1);
        sheet.setColumnView(2, 13);
        Label bumen1 = new Label(3, 0, "部門", headerFormat);
        sheet.addCell(bumen1);
        sheet.mergeCells(3, 0, 3, 1);
        sheet.setColumnView(3, 20);
        Label feiyongdaima1 = new Label(4, 0, "費用代碼", headerFormat);
        sheet.addCell(feiyongdaima1);
        sheet.mergeCells(4, 0, 4, 1);
        sheet.setColumnView(4, 12);
        Label gangwei1 = new Label(5, 0, "崗位", headerFormat);
        sheet.addCell(gangwei1);
        sheet.mergeCells(5, 0, 5, 1);
        sheet.setColumnView(5, 18);
        Label banbie1 = new Label(6, 0, "班別", headerFormat);
        sheet.addCell(banbie1);
        sheet.mergeCells(6, 0, 6, 1);
        sheet.setColumnView(6, 5);
        Label shangbanrenyuanxinxi = new Label(7, 0, "上班人員信息", headerFormat);
        sheet.addCell(shangbanrenyuanxinxi);
        sheet.mergeCells(7, 0, 7+dayCount, 0);
        for (int i=0; i<dayCount; i++) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(beginDate);
            calendar.add(Calendar.DATE, i);

            Label day = new Label(7+i, 1, DateUtil.format(calendar.getTime(), "yyyy/MM/dd"));
            sheet.addCell(day);
            sheet.setColumnView(7+i, 4);
        }
        Label renli1 = new Label(7+dayCount, 1, "接崗人力", headerFormat);
        sheet.addCell(renli1);
        sheet.setColumnView(7+dayCount, 9);
        Label zongrenli1 = new Label(8+dayCount, 0, "接崗總人力", headerFormat);
        sheet.addCell(zongrenli1);
        sheet.mergeCells(8+dayCount, 0, 8+dayCount, 1);
        Label banfei1 = new Label(9+dayCount, 0, "正常上班費用(RMB/元)", headerFormat);
        sheet.addCell(banfei1);
        sheet.mergeCells(9+dayCount, 0, 9+dayCount, 1);
        sheet.setColumnView(9+dayCount, 14);
        Label peifei1 = new Label(10+dayCount, 0, "培訓費用(RMB/元)", headerFormat);
        sheet.addCell(peifei1);
        sheet.mergeCells(10+dayCount, 0, 10+dayCount, 1);
        sheet.setColumnView(10+dayCount, 14);
        Label changgui1 = new Label(11+dayCount, 0, "常規費用(RMB/元)", headerFormat);
        sheet.addCell(changgui1);
        sheet.mergeCells(11+dayCount, 0, 11+dayCount, 1);
        sheet.setColumnView(11+dayCount, 14);
        Label jiashi1 = new Label(12+dayCount, 0, "加班時數(小時)", headerFormat);
        sheet.addCell(jiashi1);
        sheet.mergeCells(12+dayCount, 0, 12+dayCount, 1);
        Label jiafei1 = new Label(13+dayCount, 0, "加班費用(RMB/元)", headerFormat);
        sheet.addCell(jiafei1);
        sheet.mergeCells(13+dayCount, 0, 13+dayCount, 1);
        Label zongfei1 = new Label(14+dayCount, 0, "總費用(RMB/元)", headerFormat);
        sheet.addCell(zongfei1);
        sheet.mergeCells(14+dayCount, 0, 14+dayCount, 1);
        sheet.setColumnView(14+dayCount, 14);
    }

    private void fillSheet(WritableSheet sheet, List<BsServiceDayCostVO> list, int firstIndex, int lastIndex, String beginDateString, String endDateString) throws WriteException {
        // 内容格式
        Date beginDate = DateUtil.parse(beginDateString, "yyyy-MM-dd");
        Date endDate = DateUtil.parse(endDateString, "yyyy-MM-dd");
        Integer dayCount = (int)(endDate.getTime() - beginDate.getTime())/1000/60/60/24 + 1;
        WritableFont contentFont = new WritableFont(WritableFont.ARIAL, 11, WritableFont.NO_BOLD, false, UnderlineStyle.NO_UNDERLINE, Colour.BLACK);
        WritableCellFormat contentFormat = new WritableCellFormat(contentFont);
        contentFormat.setAlignment(Alignment.CENTRE);
        contentFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
        contentFormat.setBorder(Border.ALL, BorderLineStyle.THIN, Colour.BLACK);
        for (int i = firstIndex; i <= lastIndex; i++) {
            BsServiceDayCostVO vo = list.get(i);
            Label index1 = new Label(0, 2 + i * 2, (1 + firstIndex + i) + "", contentFormat);
            sheet.addCell(index1);
            sheet.mergeCells(0, 2 + i * 2, 0, 3 + i * 2);
            Label qun1 = new Label(1, 2 + i * 2, vo.getCostEntity().getDptQun(), contentFormat);
            sheet.addCell(qun1);
            sheet.mergeCells(1, 2 + i * 2, 1, 3 + i * 2);
            String faren = dictService.getDictByTypeAndVlaue("guard_legalperson", vo.getCostEntity().getLegal()).getLabel();
            Label faren1 = new Label(2, 2 + i * 2, faren, contentFormat);
            sheet.addCell(faren1);
            sheet.mergeCells(2, 2 + i * 2, 2, 3 + i * 2);
            Label bumen1 = new Label(3, 2 + i * 2, vo.getCostEntity().getDptBu(), contentFormat);
            sheet.addCell(bumen1);
            sheet.mergeCells(3, 2 + i * 2, 3, 3 + i * 2);
            Label costId1 = new Label(4, 2 + i * 2, vo.getCostEntity().getCostId(), contentFormat);
            sheet.addCell(costId1);
            sheet.mergeCells(4, 2 + i * 2, 4, 3 + i * 2);
            Label post1 = new Label(5, 2 + i * 2, vo.getCostEntity().getPostName(), contentFormat);
            sheet.addCell(post1);
            sheet.mergeCells(5, 2 + i * 2, 5, 3 + i * 2);
            Label day = new Label(6, 2 + i * 2, "白班", contentFormat);
            Label night = new Label(6, 3 + i * 2, "夜班", contentFormat);
            sheet.addCell(day);
            sheet.addCell(night);
            for (int j=0; j<dayCount; j++) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(beginDate);
                calendar.add(Calendar.DATE, j);
                String pernu = vo.getDays().get(DateUtil.format(calendar.getTime(), "yyyyMMdd"));
                String[] strings = pernu.split(",");
                Label day1 = new Label(7+j, 2 + i * 2, strings[0], contentFormat);
                Label night1 = new Label(7+j, 3 + i * 2, strings[1], contentFormat);
                sheet.addCell(day1);
                sheet.addCell(night1);
            }
            // 接岗人力
            String[] pernuAB = vo.getDays().get("perNuAB").split(",");
            Label renli1 = new Label(7+dayCount, 2 + i * 2, pernuAB[0], contentFormat);
            Label renli2 = new Label(7+dayCount, 3 + i * 2, pernuAB[1], contentFormat);
            sheet.addCell(renli1);
            sheet.addCell(renli2);
            // 接岗总人力
            Label zongrenli1 = new Label(8+dayCount, 2 + i * 2, vo.getCostEntity().getPernu().toString(), contentFormat);
            sheet.addCell(zongrenli1);
            sheet.mergeCells(8+dayCount, 2 + i * 2, 8+dayCount, 3 + i * 2);
            // 正常上班费用
            Label normalFei1 = new Label(9+dayCount, 2 + i * 2, vo.getCostEntity().getNormalCost().toString(), contentFormat);
            sheet.addCell(normalFei1);
            sheet.mergeCells(9+dayCount, 2 + i * 2, 9+dayCount, 3 + i * 2);
            // 培训费用
            Label peifei1 = new Label(10+dayCount, 2 + i * 2, vo.getCostEntity().getTrainingCost().toString(), contentFormat);
            sheet.addCell(peifei1);
            sheet.mergeCells(10+dayCount, 2 + i * 2, 10+dayCount, 3 + i * 2);
            // 常规费用
            Label changgui1 = new Label(11+dayCount, 2 + i * 2, vo.getCostEntity().getRegularCost().toString(), contentFormat);
            sheet.addCell(changgui1);
            sheet.mergeCells(11+dayCount, 2 + i * 2, 11+dayCount, 3 + i * 2);
            Label jiashi1 = new Label(12+dayCount, 2 + i * 2, vo.getCostEntity().getOvertimeHours().toString(), contentFormat);
            sheet.addCell(jiashi1);
            sheet.mergeCells(12+dayCount, 2 + i * 2, 12+dayCount, 3 + i * 2);
            Label jiafei1 = new Label(13+dayCount, 2 + i * 2, vo.getCostEntity().getOvertimeCost().toString(), contentFormat);
            sheet.addCell(jiafei1);
            sheet.mergeCells(13+dayCount, 2 + i * 2, 13+dayCount, 3 + i * 2);
            Label allCost1 = new Label(14+dayCount, 2 + i * 2, vo.getCostEntity().getTotalCost().toString(), contentFormat);
            sheet.addCell(allCost1);
            sheet.mergeCells(14+dayCount, 2 + i * 2, 14+dayCount, 3 + i * 2);
        }
    }

    private void fillSummary(WritableSheet sheet, BsServiceDayCostEntity summary, int index) throws WriteException {
        // 内容格式
        WritableFont contentFont = new WritableFont(WritableFont.ARIAL, 11, WritableFont.NO_BOLD, false, UnderlineStyle.NO_UNDERLINE, Colour.BLACK);
        WritableCellFormat contentFormat = new WritableCellFormat(contentFont);
        contentFormat.setAlignment(Alignment.CENTRE);
        contentFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
        contentFormat.setBorder(Border.ALL, BorderLineStyle.THIN, Colour.BLACK);

        Label summaryLabel = new Label(0, index, "總計", contentFormat);
        sheet.addCell(summaryLabel);
        sheet.mergeCells(0, index, 39, index);
        // 正常上班费用
        Label normalFei1 = new Label(40, index, summary.getNormalCost().toString(), contentFormat);
        sheet.addCell(normalFei1);
        // 培训费用
        Label peifei1 = new Label(41, index, summary.getTrainingCost().toString(), contentFormat);
        sheet.addCell(peifei1);
        // 常规费用
        Label changgui1 = new Label(42, index, summary.getRegularCost().toString(), contentFormat);
        sheet.addCell(changgui1);
        Label jiashi1 = new Label(43, index, summary.getOvertimeHours().toString(), contentFormat);
        sheet.addCell(jiashi1);
        Label jiafei1 = new Label(44, index, summary.getOvertimeCost().toString(), contentFormat);
        sheet.addCell(jiafei1);
        Label allCost1 = new Label(45, index, summary.getTotalCost().toString(), contentFormat);
        sheet.addCell(allCost1);
    }

    @RequestMapping(value="listArrangeTrainee", method = RequestMethod.POST)
    @ResponseBody
    public List listArrangeTrainee(@RequestParam Map<String,String> params) {
        return bsArrangePostPersonService.listNormalTrainee(params);
    }

    /**
     * 按日生成報表，調用存儲過程
     * */
    @RequestMapping(value = "generate", method = RequestMethod.POST)
    @ResponseBody
    public Result generate(@RequestParam(value = "date") String date) {
        Result result = new Result();
        try {
            Date genDate = DateUtil.parse(date, "yyyy-MM-dd");
            bsServiceDayCostService.generateServiceCostByDate(genDate);
            result.setCode(Result.SUCCESS);
            result.setMsg("生成成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.setCode(Result.FAILURE);
            result.setMsg("生成失敗："+e.getMessage());
        }
        return result;
    }

    /**
     * 經管報表v2
     */
    @RequestMapping(value = "financial", method = RequestMethod.GET)
    public String financialPage () {
        return "basics/bsservicedaycost/financial";
    }

    /**
     * 經管報表數據v2
     */
    @RequestMapping("financialData")
    @ResponseBody
    public List<FinancialVO> financialData(@RequestParam Map<String, String> params){
        return fixedFincialData(params);
    }

    /**
     * 經管報表數據合成
     * @param params 參數
     * @return
     */
    private List<FinancialVO> fixedFincialData(Map<String, String> params) {
        List<FinancialVO> result = bsServiceDayCostService.financialList(params);
        List<FinancialVO> tempSecurityServiceFinancialList = tempSecurityCostAplyService.tempSecurityCostFinancialList(params);
        List<String> costIdList = new ArrayList<>();
        for (FinancialVO item : result) {
            costIdList.add(item.getCostId());
        }
        // 添加沒有統計到的臨時安保服務費用統計
        for (FinancialVO item : tempSecurityServiceFinancialList) {
            if (!costIdList.contains(item.getCostId())) {
                result.add(item);
            }
        }
        return result;
    }

    /**
     * 經管報表导出excel 2022-09-01
     * @Author: ********
     **/
    @RequestMapping("exportFinancial")
    public void exportExcel(@RequestParam Map<String, String> params, HttpServletRequest request, HttpServletResponse response) throws Exception {
        CustomExcelUtil excelUtil = null;
        try {
            excelUtil = new CustomExcelUtil(response, null);
            excelUtil.createNewSheet("經管報表", 0);
            bsServiceDayCostService.generateExcelHeader(excelUtil.getCurrentSheet());
            List<FinancialVO> gathers = fixedFincialData(params);
            bsServiceDayCostService.fillSheet(excelUtil.getCurrentSheet(), gathers);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (excelUtil != null) {
                try {
                    excelUtil.closeWorkBook();
                } catch (Exception e) {}
            }
        }
    }
}
