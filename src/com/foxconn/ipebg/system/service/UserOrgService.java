package com.foxconn.ipebg.system.service;

import java.util.List;

import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseService;
import com.foxconn.ipebg.system.dao.UserOrgDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foxconn.ipebg.system.entity.UserOrg;

/**
 * 用户机构Service
 * <AUTHOR>
 * @date 2015年5月9日 
 */
@Service
@Transactional(readOnly = true)
public class UserOrgService extends BaseService<UserOrg, Integer> {

	@Autowired
	private UserOrgDao userOrgDao;

	@Override
	public HibernateDao<UserOrg, Integer> getEntityDao() {
		return userOrgDao;
	}

	/**
	 * 添加修改用户机构
	 * 
	 * @param id
	 * @param oldList
	 * @param newList
	 */
	@Transactional(readOnly = false)
	public void updateUserOrg(Integer userId,List<Integer> newList) {
		// 删除老的机构关系
		userOrgDao.deleteUO(userId);
		// 添加新的机构关系
		for (Integer integer : newList) {
			userOrgDao.save(new UserOrg(userId, integer));
		}
	}

	/**
	 * 获取用户拥有机构id集合
	 * 
	 * @param userId
	 * @return 结果集合
	 */
	public List<Integer> getOrgIdList(Integer userId) {
		return userOrgDao.findOrgIds(userId);
	}
	/**
	  * 方法描述: 獲取所有審核節點的人員id
	  * @Author: ********
	  * @CreateDate:   2018/10/11  下午 01:44
	  * @Return
	  **/

    public List<Integer> getUserListByDeptNO(String deptNo){
		return userOrgDao.getUserListByDeptNO(deptNo);
	}
}
