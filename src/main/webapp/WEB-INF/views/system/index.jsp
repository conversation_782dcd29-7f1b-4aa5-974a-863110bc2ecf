<%@ page import="java.util.HashSet" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="fns" uri="/WEB-INF/taglib/fns.tld" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>${fns:getConfig('sysName')}</title>
     <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script type="text/javascript" src="${ctx }/static/plugins/artTemplate/dist/template.js"></script>

    <!--导入首页启动时需要的相应资源文件(首页相应功能的 js 库、css样式以及渲染首页界面的 js 文件)-->
    <script src="${ctx}/static/plugins/easyui/common/index.js" type="text/javascript"></script>
    <link href="${ctx}/static/plugins/easyui/common/index.css" rel="stylesheet"/>
    <script src="${ctx}/static/plugins/easyui/common/index-startup.js"></script>
    <style>
                    #d2 {
                           width: 300px;
                           height: 300px;
                           padding: 20px;
        }

                </style>
</head>
<body>
<!-- 容器遮罩 -->
<div id="maskContainer">
    <div class="datagrid-mask" style="display: block;"></div>
    <div class="datagrid-mask-msg" style="display: block; left: 50%; margin-left: -52.5px;">
        正在加载...
    </div>
</div>
<div id="mainLayout" class="easyui-layout hidden" data-options="fit: true">
    <div id="northPanel" data-options="region: 'north', border: false" style="height: 80px; overflow: hidden;">
        <div id="topbar" class="top-bar">
            <div class="top-bar-left">
                <h1 style="margin-left: 10px; margin-top: 10px;"><span
                        style="color: #3F4752">${fns:getConfig('sysName')}</span>
                </h1>
            </div>
            <div class="top-bar-right">
                <div id="timerSpan"></div>
                <div id="themeSpan">
                    <a id="btnHideNorth" class="easyui-linkbutton"
                       data-options="plain: true, iconCls: 'layout-button-up'"></a>
                </div>
            </div>
        </div>
        <div id="toolbar" class="panel-header panel-header-noborder top-toolbar">
            <div id="infobar">
                    <span class="icon-hamburg-user" style="padding-left: 25px; background-position: left center;">
                       <shiro:principal property="name"/>，您好
                    </span>
                <%--<span>&nbsp;&nbsp;<font color="red">當前在線人數：<% HashSet sessions = (HashSet)application.getAttribute("sessions");%><%=sessions.size()%></font></span>--%>
            </div>

            <div id="buttonbar">
                <span>更换皮肤：</span>
                <select id="themeSelector"></select>
                <a href="javascript:void(0);" class="easyui-menubutton" data-options="menu:'#layout_north_set'"
                   iconCls="icon-standard-cog">系统</a>
                <div id="layout_north_set">
                    <div id="updatePwd" data-options="iconCls:'key'">修改密碼</div>
                    <div id="btnFullScreen" data-options="iconCls:'key'">全屏切换</div>
                    <div id="btnExit" data-options="iconCls:'logout'">退出系统</div>
                </div>
                <a id="btnShowNorth" class="easyui-linkbutton" data-options="plain: true, iconCls: 'layout-button-down'"
                   style="display: none;"></a>
            </div>
        </div>
    </div>

    <div data-options="region: 'west', title: '菜单导航栏', iconCls: 'icon-standard-map', split: true, minWidth: 200, maxWidth: 400"
         style="width: 220px; padding: 1px;">
        <div id="RightAccordion" class="easyui-accordion" data-options="fit:true,border:false">
            <%--<ul id="tree"></ul>--%>
            <%--<script id="menu" type="text/html">
                {{each data as p_permission}}
                {{if (p_permission.pid==null&&p_permission.type=='F')}}
                <div title="{{p_permission.name }}" style="padding: 5px;"
                     data-options="border:false,iconCls:'{{p_permission.icon }}'">
                    <div>
                        <div id="myMenu1" class="easyui-accordion" data-options="fit:true,border:false">
                            {{each data as c_permission}}
                            {{if (c_permission.pid==p_permission.id&&c_permission.type=='F')}}
                            <div title="{{c_permission.name }}" style="padding: 5px;"
                                 data-options="border:false,iconCls:'{{c_permission.icon }}'">
                                <div>
                                    {{each data as d_permission}}
                                    {{if (d_permission.pid==c_permission.id&&d_permission.type=='F')}}
                                    <a id="btn" class="easyui-linkbutton"
                                       data-options="plain:true,iconCls:'{{d_permission.icon }}'"
                                       style="width:98%;margin-bottom:5px;"
                                       onclick="window.mainpage.mainTabs.addModule('{{d_permission.name}}','{{d_permission.url }}','{{d_permission.icon }}')">{{d_permission.name}}</a>
                                    {{/if}}
                                    {{/each}}
                                </div>
                            </div>
                            {{/if}}
                            {{/each}}
                        </div>
                    </div>
                </div>
                {{/if}}
                {{/each}}
            </script>--%>

        </div>
    </div>

    <div data-options="region: 'center'">
        <div id="mainTabs_tools" class="tabs-tool">
            <table>
                <tr>
                    <td><a id="mainTabs_jumpHome" class="easyui-linkbutton easyui-tooltip" title="跳转至主页选项卡"
                           data-options="plain: true, iconCls: 'icon-hamburg-home'"></a></td>
                    <td>
                        <div class="datagrid-btn-separator"></div>
                    </td>
                    <td><a id="mainTabs_toggleAll" class="easyui-linkbutton easyui-tooltip" title="展开/折叠面板使选项卡最大化"
                           data-options="plain: true, iconCls: 'icon-standard-arrow-out'"></a></td>
                    <td>
                        <div class="datagrid-btn-separator"></div>
                    </td>
                    <td><a id="mainTabs_refTab" class="easyui-linkbutton easyui-tooltip" title="刷新当前选中的选项卡"
                           data-options="plain: true, iconCls: 'icon-standard-arrow-refresh'"></a></td>
                    <td>
                        <div class="datagrid-btn-separator"></div>
                    </td>
                    <td><a id="mainTabs_closeTab" class="easyui-linkbutton easyui-tooltip" title="关闭当前选中的选项卡"
                           data-options="plain: true, iconCls: 'icon-standard-application-form-delete'"></a></td>
                </tr>
            </table>
        </div>
        <div id="mainTabs" class="easyui-tabs"
             data-options="fit: true, border: false, showOption: true,enableNewTabMenu: true, tools: '#mainTabs_tools', enableJumpTabMenu: true">
            <div id="homePanel" data-options="title: '主页', iconCls: 'icon-hamburg-home',refreshable: true">
                <div class="easyui-layout" data-options="fit: true">
                    <%--<div data-options="region: 'east', split: false, border: false" style="text-align: center;width: 50%;">
                        111
                    </div>--%>
                    <div data-options="region: 'center', border: false" style="overflow: hidden;">
                        <table id="myTask"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>
   <!--  <div data-options="region: 'east', title: '警衛考勤排班系統（舊）', iconCls: 'icon-standard-date', split: true,collapsed: true, minWidth: 160, maxWidth: 1000"
         style="width: 500px;">
        <div id="eastLayout" class="easyui-layout" data-options="fit: true">
            <div data-options="region: 'south', split: false, border: false" style="height: 220px;">
                <div class="easyui-calendar" data-options="fit: true, border: false"></div>
            </div>
            <div data-options="region: 'center', border: false" style="overflow: hidden;">
                <table id="myTask1"></table>
            </div> -->
            <%--<div id="linkPanel"
                 data-options="region: 'center', border: false, title: '通知', iconCls: 'icon-hamburg-link', tools: [{ iconCls: 'icon-hamburg-refresh', handler: function () { window.link.reload(); } }]">

            </div>--%>
    <!--     </div>
    </div> -->
    <%--<div align="center" data-options="region: 'south'" border="false" class="panel-header panel-header-noborder top-toolbar"
         >
        <font color="#000" face="Microsoft JhengHei,Microsoft YaHei,tahoma, Arial, sans-serif" style="font-size:12px;">CopyRight
            &copy;
            2018 中原資訊總處&nbsp;&nbsp;版權所有</font>
    </div>--%>
    <div id="northPane2" data-options="region: 'south', border: false" style="overflow: hidden;">
        <div id="toolbar1" class="panel-header">
            <div id="infobar1" align="center">
                    <span style="padding-left: 25px; background-position: left center;">
                       CopyRight&copy; ${fns:getConfig('copyRight')}&nbsp;&nbsp;版權所有
                    </span>
            </div>
        </div>
    </div>
</div>
<div id="dlg"></div>
<script>
    var dg;
/*     $(function () {
         dg = $('#myTask').datagrid({
            method: "get",
            url: '${ctx}/eformsign/listMyTask',
            fit: true,
            fitColumns: true,
            border: false,
            striped: true,
            idField: 'id',
            pagination: true,
            rownumbers: true,
            loadFilter : function(data) {
			// 过滤数据
			var value = {
				total : data.total,
				rows : []
			};
			var x = 0;
			for ( var i = 0; i < data.rows.length; i++) {
				// 
				
				if (data.rows[i].wfName == "DWJC") 
				{
					data.rows[i].wfName="太原園區水質檢驗服務申請單"	;
				}
				else if (data.rows[i].wfName == "DWSQ")
				{
						data.rows[i].wfName="新增飲用水/蓄水池（箱）點位審批表"	;
				}
				data.rows[i].workstatus="簽核中";
	
				value.rows[x++] = data.rows[i];
			}
			return value;

		},
            pageNumber: 1,
            pageSize: 20,
            pageList: [10, 20, 30, 40, 50],
            singleSelect: true,
            columns: [[
                {field: 'serialno', title: '表單流水號', width: 50, formatter: operation},
                {field: 'auditAction', title: '流程審核', width: 50, hidden:true},
                {field: 'makerno', title: '填單人工號', width: 20},
                {field: 'makername', title: '填單人', width: 20},
                {field: 'wfName', title: '表單類型', width: 80},
                {field: 'taskName', title: '簽核節點', width: 50},
                {field: 'createtime', title: '填單時間', width: 50},//,formatter:formatDate
                {field: 'workstatus', title: '表單狀態', width: 10},
            ]],
            enableHeaderClickMenu: false,
            enableHeaderContextMenu: false,
            enableRowContextMenu: false,
            toolbar: '#tb'
        });
 
        
    }); */

    function operation(value, row, index) {
        return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('"+row.wfName+"',ctx+'/"+row.auditAction+"/"+row.serialno+"','icon-hamburg-basket')\">"+value+"</a>";
    };
/*     var dg;
    var dg1;
    $(function () {
        dg = $('#myTask').datagrid({
            method: "get",
            url: '${ctx}/wfcontroller/list',
            fit: true,
            async: false,
            fitColumns: true,
            border: false,
            striped: true,
            idField: 'id',
            pagination: false,
//            rownumbers: true,
            scrollbarSize: 0,
            pageNumber: 1,
            pageSize: 20,
            pageList: [10, 20, 30, 40, 50],
            singleSelect: true,
            columns: [[
                {field: 'wfName', title: '任務類別', width: 100},
                {field: 'workflowId', title: 'workflowId', width: 50, hidden: true},
                {field: 'flag', title: '狀態', width: 20, formatter: flagStatus},
                {field: 'taskCount', title: '合計', width: 10, formatter: operation},
            ]],
            rowStyler: function (index, row) {
                if (row.flag == 4) {
                    return 'background-color:pink;color:blue;font-weight:bold;';
                }
            },
            enableHeaderClickMenu: false,
            enableHeaderContextMenu: false,
            enableRowContextMenu: false,
        });

        dg1 = $('#myTask1').datagrid({
            method: "get",
            url: '${ctx}/system/exchange/json',
            fit: true,
            async: false,
            fitColumns: true,
            border: false,
            striped: true,
            scrollbarSize: 0,
            idField: 'id',
            pagination: false,
//            rownumbers: true,
            pageNumber: 1,
            pageSize: 20,
            pageList: [10, 20, 30, 40, 50],
            singleSelect: true,
            columns: [[
                {field: 'wfName', title: '任務類別', width: 100},
                {field: 'workflowId', title: 'workflowId', width: 50, hidden: true},
                {field: 'flag', title: '狀態', width: 20, formatter: flagStatus},
                {field: 'taskCount', title: '合計', width: 10, formatter: operation1},
            ]],
            rowStyler: function (index, row) {
                if (row.flag == 4) {
                    return 'background-color:pink;color:blue;font-weight:bold;';
                }
            },
            onLoadSuccess: function(data) {
                var info=$("#myTask1").datagrid("getData");
                //这里举例获取某列所有数据的和，当然你也可以进行其它处理或遍历操作
                if(info.rows.length>0){
                    $('#mainLayout').layout('expand', 'east');
                }
            },
            enableHeaderClickMenu: false,
            enableHeaderContextMenu: false,
            enableRowContextMenu: false,
        });

//        $('#mainLayout').layout('expand', 'east');

        var url = getQueryString("url");
//        console.log("-------" + url);
        if (url != null) {
            window.parent.mainpage.mainTabs.addModule('我的待辦', '${pageContext.request.contextPath}'+ url, 'icon-hamburg-basket');
        } else {
            if (getQueryString("flag") != null) {
                $.messager.alert("溫馨提示", "您的任務已經審核過", "info");
            }
        }
        var urlFromOld = "${urlFromOld}";
        if (urlFromOld != null&&urlFromOld !="") {
            var titleFromOld = decodeURI(decodeURI("${titleFromOld}"));
            window.parent.mainpage.mainTabs.addModule(titleFromOld, '${pageContext.request.contextPath}'+ urlFromOld, 'icon-hamburg-basket');
        }
    });

    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);//search,查询？后面的参数，并匹配正则
        if (r != null) return unescape(r[2]);
        return null;
    }

    function flagStatus(value, row, index) {
        if (value == '2') {
            return "待簽核";
        } else if (value == '4') {
            return "駁回";
        }
    }

    function operation(value, row, index) {
        if (row.flag == '2') {
            return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('我的待辦(待簽核)','${ctx}/wfcontroller/index?workFlowId=" + row.workflowId + "&status=" + row.flag + "','icon-hamburg-basket')\">" + value + "</a>";
        } else if (row.flag == '4') {
            return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('我的待辦(駁回)','${ctx}/wfcontroller/index?workFlowId=" + row.workflowId + "&status=" + row.flag + "','icon-hamburg-basket')\">" + value + "</a>";
        }
    }; */

    function operation1(value, row, index) {
        var old_url;
        $.ajax({
            async: false,
            type: 'get',
            url: "${ctx}/system/dict/getDictById",
            success: function (data) {
                old_url=data;
            }
        });
        return '<a href="'+old_url+'/frame/indexFrame.action" target="_blank">' + value + "</a>";
    };
    //創建菜單
    jQuery("#RightAccordion").accordion({ //初始化accordion
        fillSpace: true,
        fit: true,
        border: false,
        animate: false
    });
    $.ajax({
        async: false,
        type: 'get',
        url: "${ctx}/system/permission/i/json",
        success: function (data) {
            $.each(data, function (i, e) {//循环创建手风琴的项
                var id = e.id;
                $('#RightAccordion').accordion('add', {
                    title: e.text,
                    content: "<ul id='tree"+id+"' ></ul>",
                    selected: true,
                    iconCls:e.iconCls//e.Icon
                });
                $.parser.parse();
                $.post("${ctx}/system/permission/i/json?pid="+id,  function(data) {//循环创建树的项
                    $("#tree" + id).tree({
                        data: data,
                        onClick : function (node) {
                            if (node.attributes.url) {
                                window.mainpage.mainTabs.addModule(node.text,node.attributes.url,node.attributes.icon)
                            }
                        },
                        onLoadSuccess:function(node,data)
                        {
                            $("#tree" + id).tree("collapseAll");
                            var nodes =$('#tree'+ id).tree('getChildren');
                            for(var i=0;i<nodes.length;i++){
                                var node = $("#tree"+ id).tree("find",nodes[i].id);//找到id对应的节点
                                $(node.target).attr("title",nodes[i].text);//.target得到dom对象，设置title
                            }
                        }
                    });
                });
            });
        }
    });

    $('.easyui-linkbutton').on('click', function () {
        $('.easyui-linkbutton').linkbutton({selected: false});
        $(this).linkbutton({selected: true});
    });
    //修改密碼彈窗
    $("#updatePwd").click(function () {
        updatePwd();
    });
    var d;

    //修改密碼
    function updatePwd() {
        d = $("#dlg").dialog({
            title: '修改密碼',
            width: 380,
            height: 380,
            async: false,
            href: '${ctx}/system/user/updatePwd',
            maximizable: true,
            modal: true,
            buttons: [{
                text: '确认',
                handler: function () {
                    $("#mainform").submit();
                }
            }, {
                text: '取消',
                handler: function () {
                    d.panel('close');
                }
            }]
        });
    }
</script>
</body>
</html>
