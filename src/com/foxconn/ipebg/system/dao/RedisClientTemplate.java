package com.foxconn.ipebg.system.dao;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import redis.clients.jedis.Jedis;

@Repository("redisClientTemplate")
public class RedisClientTemplate {

	private static final Logger log = Logger.getLogger(RedisDao.class);

	@Autowired
	private RedisDao redisDao;

	public void disconnect() {
		Jedis jedis = redisDao.getRedisClient();
		jedis.disconnect();
	}

	/**
	 * 设置单个值
	 * 
	 * @param key
	 * @param value
	 * @return
	 */
	public String set(String key, String value) {
		String result = null;

		Jedis jedis = redisDao.getRedisClient();
		if (jedis == null) {
			return result;
		}
		boolean broken = false;
		try {
			result = jedis.set(key, value);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			broken = true;
		} finally {
			redisDao.returnResource(jedis, broken);
		}
		return result;
	}

	/**
	 * 获取单个值
	 * 
	 * @param key
	 * @return
	 */
	public String get(String key) {
		String result = null;
		Jedis jedis = redisDao.getRedisClient();
		if (jedis == null) {
			return result;
		}

		boolean broken = false;
		try {
			result = jedis.get(key);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			broken = true;
		} finally {
			redisDao.returnResource(jedis, broken);
		}
		return result;
	}
	/**
	 * 获取单个值
	 * 
	 * @param key
	 * @return
	 */
	public byte[] get(byte[] key) {
		byte[] result = null;
		Jedis jedis = redisDao.getRedisClient();
		if (jedis == null) {
			return result;
		}
		boolean broken = false;
		try {
			result = jedis.get(key);

		} catch (Exception e) {
			log.error(e.getMessage(), e);
			broken = true;
		} finally {
			redisDao.returnResource(jedis, broken);
		}
		return result;
	}
	public String getHashMapValue(String redisKey,String mapKey){
		String result = null;
		Jedis jedis = redisDao.getRedisClient();
		if (jedis == null) {
			return result;
		}
		boolean broken = false;
		try {
			result = jedis.hget(redisKey,mapKey);

		} catch (Exception e) {
			log.error(e.getMessage(), e);
			broken = true;
		} finally {
			redisDao.returnResource(jedis, broken);
		}
		return result;
	}
}