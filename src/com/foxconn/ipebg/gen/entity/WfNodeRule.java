package com.foxconn.ipebg.gen.entity;

import java.io.Serializable;

public class WfNodeRule implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 節點編碼
	 */
	private String nodeId;
	/**
	 * 會簽節點參數名稱
	 */
	private String nodeParamName;
	/**
	 * 會簽節點分派人數
	 */
	private String totalPeople;
	/**
	 * 會簽通過率
	 */
	private String passRate;
	/**
	 * 會簽通過規則
	 */
	private String voteRule;
	/**
	 * 會簽節點狀態變量
	 */	
	private String taskStatusVariable;
	/**
	 * 會簽節點完成變量
	 */
	private String taskCompleteVariable;
	public String getNodeId() {
		return nodeId;
	}
	public void setNodeId(String nodeId) {
		this.nodeId = nodeId;
	}
	public String getNodeParamName() {
		return nodeParamName;
	}
	public void setNodeParamName(String nodeParamName) {
		this.nodeParamName = nodeParamName;
	}
	public String getTotalPeople() {
		return totalPeople;
	}
	public void setTotalPeople(String totalPeople) {
		this.totalPeople = totalPeople;
	}
	public String getPassRate() {
		return passRate;
	}
	public void setPassRate(String passRate) {
		this.passRate = passRate;
	}
	public String getVoteRule() {
		return voteRule;
	}
	public void setVoteRule(String voteRule) {
		this.voteRule = voteRule;
	}
	public String getTaskStatusVariable() {
		return taskStatusVariable;
	}
	public void setTaskStatusVariable(String taskStatusVariable) {
		this.taskStatusVariable = taskStatusVariable;
	}
	public String getTaskCompleteVariable() {
		return taskCompleteVariable;
	}
	public void setTaskCompleteVariable(String taskCompleteVariable) {
		this.taskCompleteVariable = taskCompleteVariable;
	}
}
