package com.foxconn.ipebg.buessness.audit.service;

import com.foxconn.ipebg.buessness.audit.entity.EGuardItemAplyEntity;
import com.foxconn.ipebg.buessness.audit.dao.EGuardItemAplyDao;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import com.foxconn.ipebg.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 崗位申請明細表
 *
 * <AUTHOR>
 * @email <EMAIL> 
 * @date 2019-02-23 16:41:23
 */
@Service
@Transactional(readOnly=true)
public class  EGuardItemAplyService extends BaseBusinessService<EGuardItemAplyEntity, String>{
	@Autowired
	private EGuardItemAplyDao eGuardItemAplyDao;

	@Override
	public HibernateDao<EGuardItemAplyEntity, String> getEntityDao() {
		return eGuardItemAplyDao;
	}

	// 雖然有調用，但不會執行 S6114893
	public int batchDelete(String serialno) {
		return eGuardItemAplyDao.batchExecute(
				"delete from EGuardItemAplyEntity t where t.serialno=?0", serialno);
	}


	public List<EGuardItemAplyEntity> findItemEntityes(String serialno) {
		return eGuardItemAplyDao.find("from EGuardItemAplyEntity t where t.serialno=?0",
				serialno);
	}

	// 批量變更派駐保安公司 
	public int auditUpdateGuardSecurityCom(String serialno, String securityCom) {
		String hql = "update EGuardItemAplyEntity t  set "
				+ "t.securityCom = '" + securityCom + "' "
				+ "where  t.serialno='" + serialno + "' ";
		return this.eGuardItemAplyDao.batchExecute(hql);
	}

	// 批量生效崗位信息  撤崗標識（0.增崗  1.崗位變更 2.增崗成功 3.變更崗位成功 4.临时增撤申请 5.临时增撤完成 N駁回）
	public int auditUpdateGuardPostEffectDate(String serialno, String delFlag) {
		String hql = " update EGuardItemAplyEntity t  set "
				+ "t.postEffectDate = ?0 ,t.delFlag='" + delFlag + "' "
				+ "where  t.serialno='" + serialno + "' ";
		return this.eGuardItemAplyDao.batchExecute(hql, new Date());
	}

	// 按申請單號更新軟刪除狀態  撤崗標識（0.增崗  1.崗位變更 2.增崗成功 3.變更崗位成功 4.临时增撤申请 5.临时增撤完成 N駁回）
	public int delFlagUpdateBySerialno(String serialno, String delFlag) {
		String hql = "update EGuardItemAplyEntity t  set " + "t.delFlag = '"
				+ delFlag + "'" + "where  t.serialno='" + serialno + "' ";
		return this.eGuardItemAplyDao.batchExecute(hql);
	}

	// 驗證崗位名稱唯一性
	public boolean isExistPostName(EGuardItemAplyEntity eGuardItemAplyEntity) {
		boolean result = false;
		StringBuffer sb = new StringBuffer();
		sb.append(" select * from v_e_guard_item_aply t ").append(" WHERE 1=1  ");
		if (StringUtils.isNotBlank(eGuardItemAplyEntity.getPostName())) {
			sb.append("  and t.POST_NAME = '" + eGuardItemAplyEntity.getPostName() + "' ");
		}
		if (StringUtils.isNotBlank(eGuardItemAplyEntity.getRecno())) {
			sb.append(" and t.recno <> '"+ eGuardItemAplyEntity.getRecno() + "' ");
		}
		@SuppressWarnings("unchecked")
		List<EGuardItemAplyEntity> list = this.eGuardItemAplyDao
				.createSQLQuery(sb.toString()).addEntity(EGuardItemAplyEntity.class)
				.list();
		if (list.size() > 0) {
			result = true;
		} else {
			result = false;
		}
		return result;
	}

	/**
	 * 判斷是否允許申請臨時崗位增撤（相同時間不允許重複申請）
	 * @param recno 崗位編碼
	 * @param startDate 異動開始時間
	 * @param endDate 異動結束時間
	 * @return 是否允許申請
	 */
	public boolean allowApplyTempModify(String recno, Date startDate, Date endDate) {
		return eGuardItemAplyDao.allowApplyTempModify(recno, startDate, endDate);
	}
}
