package com.foxconn.ipebg.audit.service;

import com.foxconn.ipebg.audit.entity.EFormSignEntity;
import com.foxconn.ipebg.audit.dao.EFormSignDao;
import com.foxconn.ipebg.basics.entity.ESignUserinfoEntity;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.buessness.common.entity.TQhChargelogEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.Dict;

import com.foxconn.ipebg.buessness.workflow.entity.Gtasks;
import com.foxconn.ipebg.buessness.workflow.entity.InterConfig;
import com.foxconn.ipebg.buessness.workflow.entity.InterResult;
import com.foxconn.ipebg.buessness.workflow.entity.WfConifgEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WorkFlowEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.system.service.DictService;
import com.foxconn.ipebg.system.utils.UserUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 申請表單審核記錄表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-12-27 08:45:32
 */
@Service
@Transactional(readOnly = true)
public class EFormSignService extends
		BaseBusinessService<EFormSignEntity, String> {
	@Autowired
	private EFormSignDao eFormSignDao;

	@Autowired
	private UserServiceUtil serviceUtil;
	@Autowired
	private DictService dictService;

	@Override
	public HibernateDao<EFormSignEntity, String> getEntityDao() {
		return eFormSignDao;
	}

	public List<EFormSignEntity> findByApplyId(String applyId) {
		return this.eFormSignDao.findAllByOrder("applyId", applyId, "signOrd",
				true);
	}

	public int batchDelete(String serialno) {
		return eFormSignDao.batchExecute(
				"delete from EFormSignEntity t where t.serialno=?0", serialno);
	}

	public int batchDeleteWait(String serialno) {
		return eFormSignDao
				.batchExecute(
						"delete from EFormSignEntity t where t.applyId=?0 and t.eserStat=?1 ",
						serialno, "W");
	}
     //簽核記錄	NEW 
	  public List<EFormSignEntity> findBySerialno(String serialno) {
	    	 StringBuffer sb = new StringBuffer();
//	         sb.append(" select t.apply_id,t.sign_ord, t.sign_nam, t.eser_id,");
//	         sb.append(" nvl(a.image,t.eser_nam) eser_nam,t.eser_mail, decode(t.eser_stat,'Y','通過','N','駁回',t.eser_stat) eser_stat,t.eser_memo,t.eser_date,t.eser_ip,t.crter,");
//	         sb.append(" t.crtdate,t.mdier,t.mdidate,t.id,t.create_by,t.create_date,t.update_by,t.update_date,t.del_flag ");
//	         sb.append(" from E_FORM_SIGN t,BS_SIGNATURE a WHERE  T.Eser_Id=a.signature_no and t.eser_stat in('Y','N') and t.Apply_Id='"+serialno+"' order by t.eser_date desc");
	         sb.append(" select t.apply_id,t.sign_ord, t.sign_nam, t.eser_id,");
	         sb.append(" coalesce((select a.image from BS_SIGNATURE a where t.eser_id=a.signature_no),t.eser_nam) eser_nam,t.eser_mail, decode(t.eser_stat,'Y','通過','N','駁回',t.eser_stat) eser_stat,t.eser_memo,t.eser_date,t.eser_ip,t.crter,");
	         sb.append(" t.crtdate,t.mdier,t.mdidate,t.id,t.create_by,t.create_date,t.update_by,t.update_date,t.del_flag ");
	         sb.append(" from E_FORM_SIGN t WHERE  t.eser_stat in('Y','N') and t.Apply_Id='"+serialno+"' order by t.eser_date desc");
	 
	    	 	        
	         
	         List<EFormSignEntity> list = this.eFormSignDao.createSQLQuery(sb.toString()).addEntity(EFormSignEntity.class).list();
	         return list;
	    }
	  
	// 待簽核節點
	public EFormSignEntity currentNode(String applyId) {
		EFormSignEntity currentNode = null;
		String sql = "select * from E_FORM_SIGN t where  t.apply_id='"
				+ applyId + "' and t.eser_stat='W'order by t.sign_ord ";
		List<EFormSignEntity> list = this.eFormSignDao.createSQLQuery(sql)
				.addEntity(EFormSignEntity.class).list();
		if (list.size() > 0) {
			currentNode = list.get(0);
		}
		return currentNode;// this.eFormSignDao.findAllByOrder("applyId",
							// applyId,"signOrd",true);
	}

	// 待簽核最大狀態
	public String maxStatWait(String applyId) {
		String currentStat = "";
		String sql = "select max(t.sign_ord) from E_FORM_SIGN t where  t.apply_id='"
				+ applyId + "' and t.eser_stat='W'";
		List list = this.eFormSignDao.createSQLQuery(sql).list();
		if (list.size() > 0) {
			currentStat = list.get(0).toString();
		}
		return currentStat;
	}

	// 審核
	public int auditUpdate(String applyId, String minStatWait, String clientIP,
			String auditStat, String memo) {
		String hql = "update EFormSignEntity t  set " + "t.eserStat = '"
				+ auditStat + "',t.eserMemo = '" + memo + "',"
				+ "t.eserDate = ?0,t.eserIp   = '" + clientIP + "'  "
				+ "where  t.applyId='" + applyId + "' and t.signOrd="
				+ Integer.parseInt(minStatWait) + " ";

		return this.eFormSignDao.batchExecute(hql, new Date());
	}

	// 審核/重新提交/取消申請
	public int auditUpdate(String applyId, String minStatWait, String signName, String clientIP,
						   String auditStat, String memo) {
		String hql = "update EFormSignEntity t  set " + "t.eserStat = '"
				+ auditStat + "',t.eserMemo = '" + memo + "',"
				+ "t.signNam = '"+signName+"',"
				+ "t.eserDate = ?0,t.eserIp   = '" + clientIP + "'  "
				+ "where  t.applyId='" + applyId + "' and t.signOrd="
				+ Integer.parseInt(minStatWait) + " ";

		return this.eFormSignDao.batchExecute(hql, new Date());
	}

	// 待辦任務 S6113712
	public List<Gtasks> getTaskAll(String empNo, String serialno,
			String formName) {
		List<Gtasks> currentTask = new ArrayList<Gtasks>();
		String sql = "select * from v_apply_list t " + "where  t.eser_id='"
				+ empNo + "' ";

		if (serialno != null && !"".equals(serialno)) {
			sql = sql + " AND t.serialno = '" + serialno + "'";
		}
		if (formName != null && !"".equals(formName)) {
			sql = sql + " AND t.form_nam = '" + formName + "'";
		}

		sql = sql + "order by t.createtime desc";
		List list = this.eFormSignDao.createSQLQuery(sql).list();
		// 循環遍歷queryList對象到DTO
		if (list.size() > 0) {
			Object[] objs = new Object[50];
			for (int i = 0; i < list.size(); i++) {
				objs[i] = list.get(i);
				// objs[i]=(Object[])objs[i];
				Object[] obj = new Object[10];
				obj = (Object[]) objs[i];
				Gtasks gtask = new Gtasks();
				gtask.setAuditAction(obj[1].toString());
				gtask.setMakername(obj[3].toString());
				gtask.setMakerno(obj[2].toString());
				gtask.setSerialno(obj[0].toString());
				gtask.setWfName(obj[4].toString());
				gtask.setWorkstatus(obj[7].toString());
				gtask.setTaskName(obj[5].toString());// (Date)obj[6]
				gtask.setCreatetime((Date) obj[6]);// new
													// SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(obj[6].toString())
				currentTask.add(gtask);
				System.out.print(obj[6].toString());
			}
		}
		return currentTask;// this.eFormSignDao.findAllByOrder("applyId",
							// applyId,"signOrd",true);
	}
	//  2019/09/27  S6113712
	public List<Gtasks> getTaskAllMail(String empNo, String serialno,
			String formName) {
		List<Gtasks> currentTask = new ArrayList<Gtasks>();
		String sql = "select t.serialno,"
       +" b.freeloginurl auditAction,"
       +"t.makerno,"
       +"t.makername,"
       +"t.form_nam,"
       +"t.sign_nam,"
       +"t.createtime,"
       +"t.apply_stat,"
       +"t.eser_id,"
       +"t.eser_nam"
       +"  from v_apply_list t, T_PUB_MAILRECORD b"
       +"  WHERE t.apply_stat = b.orderstatus "
       +" and t.serialno = b.serialno " + " and  t.eser_id='"
				+ empNo + "' ";

		if (serialno != null && !"".equals(serialno)) {
			sql = sql + " AND t.serialno = '" + serialno + "'";
		}
		if (formName != null && !"".equals(formName)) {
			sql = sql + " AND t.form_nam = '" + formName + "'";
		}

		sql = sql + "order by t.createtime desc";
		List list = this.eFormSignDao.createSQLQuery(sql).list();
		// 循環遍歷queryList對象到DTO
		if (list.size() > 0) {
			Object[] objs = new Object[50];
			for (int i = 0; i < list.size(); i++) {
				objs[i] = list.get(i);
				// objs[i]=(Object[])objs[i];
				Object[] obj = new Object[10];
				obj = (Object[]) objs[i];
				Gtasks gtask = new Gtasks();
				gtask.setAuditAction(obj[1].toString());
				gtask.setMakername(obj[3].toString());
				gtask.setMakerno(obj[2].toString());
				gtask.setSerialno(obj[0].toString());
				gtask.setWfName(obj[4].toString());
				gtask.setWorkstatus(obj[7].toString());
				gtask.setTaskName(obj[5].toString());// (Date)obj[6]
				gtask.setCreatetime((Date) obj[6]);// new
													// SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(obj[6].toString())
				currentTask.add(gtask);
				System.out.print(obj[6].toString());
			}
		}
		return currentTask;// this.eFormSignDao.findAllByOrder("applyId",
							// applyId,"signOrd",true);
	}
	

	/**
	 * 方法描述: 獲取所有待辦任務
	 * 
	 * @throws ParseException
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018/10/13 上午 08:14
	 * @Return
	 **/

	@Transactional(readOnly = false)
	public Page<Gtasks> getAllMyTask(Page<Gtasks> page,String serialno,
			String formName) {
		List<Gtasks> gtasksList = getTaskAll(UserUtil.getCurrentUser()
				.getLoginName(),serialno, formName);
		page.setResult(gtasksList);
		return page;
	}

	
	
	/**
	 * 方法描述: 獲取簽核路徑
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018/10/10 上午 09:35
	 * @Return
	 **/

	public String getChargeNodeInfo(String serialno) {
		StringBuffer stringBuffer = null;
		Boolean flag = true;
		try {
			stringBuffer = new StringBuffer();
			List<EFormSignEntity> entityList = findByApplyId(serialno);
			int signOrd = 888888;
			if (currentNode(serialno) != null) {
				signOrd = currentNode(serialno).getSignOrd();
			}
			for (EFormSignEntity entity : entityList) {
				if (signOrd != entity.getSignOrd()) {
					stringBuffer.append(entity.getSignNam() + "("
							+ /*entity.getEserId() + "/" +*/ entity.getEserNam()
							+ ")--->");
				} else {
					stringBuffer.append("<font color='red'>"
							+ entity.getSignNam() + "(" + /*entity.getEserId()
							+ "/" +*/ entity.getEserNam() + ")" + "</font>->");
				}
				// <font color="red">廠級主管(S6113712/S6113712)</font>
				// ->製造處級主管(S6113712/S6113712)->製造總處級主管(/)
				// ->環保科技處對應窗口(/)->環保科技處課級主管(/)
				// ->環保科技處部級主管(S6113712/S6113712)->環保科技處處級主管(S6113712/S6113712)
			}
			Assert.hasText(stringBuffer.toString());
		} catch (Exception e) {
			logger.error(serialno, e.getStackTrace(), e);
		}
		return stringBuffer.toString().substring(0, stringBuffer.length() - 4);
	}

}
