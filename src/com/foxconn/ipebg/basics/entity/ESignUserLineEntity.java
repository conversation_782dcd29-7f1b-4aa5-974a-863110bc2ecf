package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * 默認簽核線記錄表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-09-23 11:36:24
 */
@Entity
@Table(name = "E_SIGN_USER_LINE")
@DynamicUpdate
@DynamicInsert
public class ESignUserLineEntity extends DataEntity<ESignUserLineEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    //表單類型
    private String formType;
    //申請人工號
    private String empNo;
    // 節點位置
    private Integer codeId;
    // 簽核人員工號
    private String signerEmpNo;
    // 簽核人員姓名
    private String signerEmpName;

    /**
     * 设置：表單類型
     */
    public void setFormType(String formType) {
        this.formType = formType;
    }

    /**
     * 获取：表單類型
     */

    @Column(name = "FORM_TYPE", nullable = false, length = 20)
    public String getFormType() {
        return formType;
    }

    /**
     * 设置：申請人工號
     */
    public void setEmpNo(String empNo) {
        this.empNo = empNo;
    }

    /**
     * 获取：申請人工號
     */

    @Column(name = "EMP_NO", nullable = false, length = 20)
    public String getEmpNo() {
        return empNo;
    }

    @Column(name = "code_id")
    public Integer getCodeId() {
        return codeId;
    }

    public void setCodeId(Integer codeId) {
        this.codeId = codeId;
    }

    @Column(name = "signer_emp_no")
    public String getSignerEmpNo() {
        return signerEmpNo;
    }

    public void setSignerEmpNo(String signerEmpNo) {
        this.signerEmpNo = signerEmpNo;
    }
    @Column(name = "signer_emp_name")
    public String getSignerEmpName() {
        return signerEmpName;
    }

    public void setSignerEmpName(String signerEmpName) {
        this.signerEmpName = signerEmpName;
    }
}
