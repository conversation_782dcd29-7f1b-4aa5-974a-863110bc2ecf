var dg;
var d;
$(function() {
	dg = $('#dg').datagrid({
		method : "get",
		url : ctx + '/bshandrecords/list',
		// url:'/waterQualityTesting/bsdpt/list',
		fit : true,
		fitColumns : true,
		border : false,
	    idField : 'id',
		striped : true,
		pagination : true,
		rownumbers : true,
		pageNumber : 1,
		pageSize : 20,
		pageList : [ 10, 20, 30, 40, 50 ],
		singleSelect : true,
		sortName: 'cardTime',
		sortOrder: 'desc',
		columns : [ [
		{field : 'id',title : '資料識別ID  Primary',hidden : true},
		{field : 'empNo',title : '工號',sortable : true,width : 100},
		{field : 'empName',title : '姓名',sortable : true,width : 100},
		{field : 'cardTime',title : '刷卡時間',sortable : true,width : 100},
		{field : 'remark',title : '備註',sortable : true,width : 100}
		] ],
		enableHeaderClickMenu : true,
		enableHeaderContextMenu : true,
		enableRowContextMenu : false,
		rowTooltip : true,
		toolbar : '#tb'
	});
	$('#dg').datagrid;
});

// 创建查询对象并查询
function cx() {
	var obj = $("#searchFrom").serializeObject();
	dg.datagrid('load', obj);
}
// 表單進度查詢頁面查詢條件重置
function listSearchReset() {
	$("#searchFrom").form("reset");
}
// 导出excel
function exportExcel() {
	var form = document.getElementById("searchFrom");
	searchFrom.action = ctx + '/bshandrecords/exportExcel';
	form.submit();
}