package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.entity.BsTempServiceEntity;
import com.foxconn.ipebg.basics.dao.BsTempServiceDao;
import com.foxconn.ipebg.basics.entity.PerInfoEntity;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.Dict;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WorkFlowEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.system.service.DictService;

import java.lang.reflect.Array;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 臨時勤務信息表
 *
 * <AUTHOR>
 * @email <EMAIL> 
 * @date 2019-05-04 09:10:27
 */
@Service
@Transactional(readOnly=true)
public class  BsTempServiceService extends BaseBusinessService<BsTempServiceEntity, String>{
    @Autowired
    private BsTempServiceDao bsTempServiceDao;
    @Autowired
    private PerInfoService perInfoService;

    @Override
    public HibernateDao<BsTempServiceEntity, String> getEntityDao() {
        return bsTempServiceDao;
    }

    public BsTempServiceEntity findBySerialno(String serialno) {
        return this.bsTempServiceDao.findUniqueBy("serialno", serialno);
    }


    /**
     * 保存臨時勤務人員
     * @param btsId 臨時勤務ID
     * @param piId 合格人員ID
     * @return
     */
    @Transactional(readOnly=false)
    public int savePerinfo(String btsId, String piId){
        String sql = "insert into bs_temp_service_preinfo  ( bts_id, pi_id) values  ( '"+btsId+"', '"+piId+"')";
        return bsTempServiceDao.createSQLQuery(sql).executeUpdate();
    }

    @Transactional(readOnly=false)
    public int deletePerinfoByBtsId(String btsId){
        String sql = "delete from bs_temp_service_preinfo  where bts_id = '"+btsId+"' ";
        return bsTempServiceDao.createSQLQuery(sql).executeUpdate();
    }

    public List<PerInfoEntity> listPerinfo(Map<String,Object> params){
        String sql = "select a.* from per_info a,bs_temp_service_preinfo b where a.id=b.pi_id and b.bts_id='"+params.get("btsId").toString()+"' ";
        return bsTempServiceDao.createSQLQuery(sql).addEntity(PerInfoEntity.class).list();
    }


}
