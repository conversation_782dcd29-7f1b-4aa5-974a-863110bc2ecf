package com.foxconn.ipebg.buessness.common.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 廠區代碼對應配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-08 09:28:36
 */
@Entity
@Table(name = "T_QH_FACTORYIDCONFIG")
@DynamicUpdate
@DynamicInsert
public class TQhFactoryidconfigEntity extends DataEntity<TQhFactoryidconfigEntity>  implements Serializable {
    private static final long serialVersionUID = 1L;

            //廠區代碼
        private String factoryid;
            //廠區名稱
        private String factoryname;
            //本地廠區代碼
        private String localid;

            /**
         * 设置：廠區代碼
         */
        public void setFactoryid(String factoryid) {
            this.factoryid = factoryid;
        }
    /**
     * 获取：廠區代碼
     */
        @Column(name = "FACTORYID", nullable = false, length = 20)
        public String getFactoryid() {
            return factoryid;
        }
        
            /**
         * 设置：廠區名稱
         */
        public void setFactoryname(String factoryname) {
            this.factoryname = factoryname;
        }
    /**
     * 获取：廠區名稱
     */
                    @Column(name = "FACTORYNAME", nullable = false, length = 20)
            public String getFactoryname() {
                return factoryname;
            }
        
            /**
         * 设置：本地廠區代碼
         */
        public void setLocalid(String localid) {
            this.localid = localid;
        }
    /**
     * 获取：本地廠區代碼
     */
                    @Column(name = "LOCALID", nullable = false, length = 20)
            public String getLocalid() {
                return localid;
            }
        
    }
