package com.foxconn.ipebg.basics.vo;

import javax.persistence.Column;
import javax.persistence.Transient;

/**
 * 經管報表
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
public class FinancialVO {
    //事業群名稱
    private String dptQun;
    //法人
    private String legal;
    //費用代碼
    private String costId;
    //常規費用（RMB）
    private Float regularCost;
    //加班費用（RMB）
    private Float overtimeCost;
    // 臨時勤務費用
    private Float tempCost1;
    // 臨時性安保服務費用
    private Float tempCost2;
    //總費用（RMB）
    private Float totalCost;

    @Column(name = "DPT_QUN", nullable = false, length = 20)
    public String getDptQun() {
        return dptQun;
    }

    public void setDptQun(String dptQun) {
        this.dptQun = dptQun;
    }

    /**
     * 设置：法人
     */
    public void setLegal(String legal) {
        this.legal = legal;
    }

    /**
     * 获取：法人
     */
    @Column(name = "LEGAL", nullable = false, length = 20)
    public String getLegal() {
        return legal;
    }

    /**
     * 设置：費用代碼
     */
    public void setCostId(String costId) {
        this.costId = costId;
    }

    /**
     * 获取：費用代碼
     */
    @Column(name = "COST_ID", nullable = false, length = 20)
    public String getCostId() {
        return costId;
    }

    /**
     * 设置：常規費用（RMB分）
     */
    public void setRegularCost(Float regularCost) {
        this.regularCost = regularCost;
    }

    /**
     * 获取：常規費用（RMB分）
     */

    @Column(name = "REGULAR_COST", nullable = false, length = 20)
    public Float getRegularCost() {
        return regularCost == null ? 0 : regularCost;
    }

    /**
     * 设置：加班費用（RMB分）
     */
    public void setOvertimeCost(Float overtimeCost) {
        this.overtimeCost = overtimeCost;
    }

    /**
     * 获取：加班費用（RMB分）
     */
    @Column(name = "OVERTIME_COST", nullable = false, length = 20)
    public Float getOvertimeCost() {
        return overtimeCost == null ? 0 : overtimeCost;
    }

    /**
     * 设置：總費用（RMB分）
     */
    public void setTempCost1(Float tempCost1) {
        this.tempCost1 = tempCost1;
    }

    /**
     * 获取：總費用（RMB分）
     */

    @Column(name = "TEMP_COST1", nullable = false, length = 20)
    public Float getTempCost1() {
        return tempCost1 == null ? 0 : tempCost1;
    }

    /**
     * 设置：臨時勤務費用（RMB分）
     */
    public void setTempCost2(Float tempCost2) {
        this.tempCost2 = tempCost2;
    }

    /**
     * 获取：臨時勤務費用（RMB分）
     */
    @Column(name = "TEMP_COST1", nullable = false, length = 20)
    public Float getTempCost2() {
        return tempCost2 == null ? 0 : tempCost2;
    }

    @Transient
    public Float getTotalCost() {
        return (regularCost != null ? regularCost : 0) +
                (overtimeCost != null ? overtimeCost : 0) +
                (tempCost1 != null ? tempCost1 : 0) +
                (tempCost2 != null ? tempCost2 : 0);
    }

    public void setTotalCost(Float totalCost) {
        this.totalCost = totalCost;
    }
}
