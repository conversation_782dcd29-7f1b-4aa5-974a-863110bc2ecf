var dg;
var d;
var  isCheckObj={'Y':'是','N':'否'};

$(function () {

    //绑定培訓期下拉框
    $('#trainingPeriodNo').combogrid({
        data: _periodList,
        panelWidth: 200,
        multiple: false,
        idField: 'training_period_no',
        textField: 'training_period_name',
        fitColumns: true,
        columns: [[
            {field: 'training_period_name', title: '培訓期名稱', width: 80}
        ]]
    });


    dg = $('#dg').datagrid({
        method: "get",
        url: ctx + '/bstrainingarrange/list',
        fit: true,
        queryParams:{
            order:'desc',
            sort:'arrangeDate'
        },
        fitColumns: true,
        border: false,
        idField: 'id',
        striped: true,
        pagination: true,
        rownumbers: true,
        pageNumber: 1,
        pageSize: 20,
        pageList: [10, 20, 30, 40, 50],
        singleSelect: true,
        columns: [[
            {field: 'id', title: 'id', hidden: true},
            {field: 'trainingPeriodNo', title: '培訓期編碼', sortable: true, width: 100}
            ,
            {field: 'trainingPeriodName', title: '培訓期名稱', sortable: true, width: 100}
            ,
            {field: 'arrangeDate', title: '排班日期', sortable: true, width: 100,
                formatter:function(value, rowData, rowIndex){
                    return value.substr(0,10);
                }
            }
            ,
            {field: 'arrangeNum', title: '排班人數', sortable: true, width: 100}
            ,
            {field: 'isCheck', title: '是否稽核', sortable: true, width: 100,
                formatter:function(value, rowData, rowIndex){
                    return isCheckObj[value];
                }
            }
            ,
            {field: 'createBy', title: '創建人', sortable: true, width: 100}
            ,
            {field: 'createDate', title: '創建時間', sortable: true, width: 100}
            ,
            {field: 'operator', title: '操作', sortable: true, width: 100,
                formatter:function(value, rowData, rowIndex){
                    var aa='<a href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls:\'icon-search\',plain:true" onclick="add('+rowIndex+')">排班詳情</a>';
                    var bb='';
                    if('Y'==rowData.isCheck){
                        bb='<a href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls:\'icon-tip\',plain:true" onclick="checkPer('+rowIndex+')">稽核查看</a>';
                    }else {
                        bb='<a href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls:\'icon-edit\',plain:true" onclick="checkPer('+rowIndex+')">稽核</a>';
                    }
                    return aa+'&nbsp;&nbsp;'+bb;
                }
            }
        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        rowTooltip: true,
        toolbar: '#tb'
    });
});




function add(idx) {
    if(idx || idx==0){
        var arrangeDate=dg.datagrid("getRows")[idx].arrangeDate.substr(0,10);
        var trainingPeriodNo=dg.datagrid("getRows")[idx].trainingPeriodNo;

        window.parent.mainpage.mainTabs.addModule("培訓期排班", ctx + '/bstrainingarrange/arrangeper?trainingPeriodNo='+trainingPeriodNo+'&arrangeDate='+arrangeDate, 'icon-hamburg-basket');
    }else{
        window.parent.mainpage.mainTabs.addModule("培訓期排班", ctx + '/bstrainingarrange/arrangeper', 'icon-hamburg-basket');
    }
}

function checkPer(idx) {

    if(idx || idx==0){
        var row=dg.datagrid("getRows")[idx];
        if(row){
            var arrangeDate=row.arrangeDate.substr(0,10);
            var trainingPeriodNo=row.trainingPeriodNo;
            var trainingPeriodName=row.trainingPeriodName;
            var isCheck=row.isCheck;
            window.parent.mainpage.mainTabs.addModule("人工稽核", ctx + '/bstrainingarrange/checkper?trainingPeriodNo='+trainingPeriodNo+'&arrangeDate='+arrangeDate+'&trainingPeriodName='+trainingPeriodName+'&isCheck='+isCheck, 'icon-hamburg-basket');
        }
    }
}


//删除
function del() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function (data) {
        if (data) {
            $.ajax({
                type: 'get',
                url: ctx + "/bstrainingarrange/delete/" + row.id,
                success: function (data) {
                    successTip(data, dg);
                }
            });
        }
    });
}

//弹窗修改
function upd() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    d = $("#dlg").dialog({
        title: '修改培訓期日排班表',
        width: 380,
        height: 340,
        href: ctx + '/bstrainingarrange/update/' + row.id,
        maximizable: true,
        modal: true,
        buttons: [{
            text: '修改',
            handler: function () {
                $('#mainform').submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//创建查询对象并查询
function cx() {
    var obj = $("#searchFrom").serializeObject();
    obj.order='desc';
    obj.sort='arrangeDate';
    dg.datagrid('load', obj);
}

//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("reset");
}

//导出excel
function exportExcel() {
    var form = document.getElementById("searchFrom");
    searchFrom.action = ctx + '/bstrainingarrange/exportExcel';
    form.submit();
}