package com.foxconn.ipebg.audit.entity;

        import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;
        import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 申請表單審核記錄表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-12-27 08:45:32
 */
@Entity
@Table(name = "E_FORM_SIGN")
@DynamicUpdate
@DynamicInsert
public class EFormSignEntity extends DataEntity<EFormSignEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

                        //申請單號
            private String applyId;
                                //簽單順序
            private Integer signOrd;
                                //簽核流程節點名稱
            private String signNam;
                                //簽核人工號
            private String eserId;
                                //簽核人姓名
            private String eserNam;
                                //簽核人郵件
            private String eserMail;
                                //審核狀態(W待審核Y審核通過N駁回E結案)
            private String eserStat;
                                //簽核批註
            private String eserMemo;
                                //簽核日期
            private Date eserDate;
                                //簽核電腦IP
            private String eserIp;
                                //建立人員
            private String crter;
                                //建立日期
            private Date crtdate;
                                //修改人員
            private String mdier;
                                //修改日期
            private Date mdidate;
               
                        /**
             * 设置：申請單號
             */
            public void setApplyId(String applyId) {
                this.applyId = applyId;
            }

        /**
         * 获取：申請單號
         */
        
                            @Column(name = "APPLY_ID", nullable = false, length = 20)
                public String getApplyId() {
                    return applyId;
                }
                    
                    /**
             * 设置：簽單順序
             */
            public void setSignOrd(Integer signOrd) {
                this.signOrd = signOrd;
            }

        /**
         * 获取：簽單順序
         */
        
                            @Column(name = "SIGN_ORD", nullable = false, length = 20)
                public Integer getSignOrd() {
                    return signOrd;
                }
                    
                    /**
             * 设置：簽核流程節點名稱
             */
            public void setSignNam(String signNam) {
                this.signNam = signNam;
            }

        /**
         * 获取：簽核流程節點名稱
         */
        
                            @Column(name = "SIGN_NAM", nullable = false, length = 20)
                public String getSignNam() {
                    return signNam;
                }
                    
                    /**
             * 设置：簽核人工號
             */
            public void setEserId(String eserId) {
                this.eserId = eserId;
            }

        /**
         * 获取：簽核人工號
         */
        
                            @Column(name = "ESER_ID", nullable = false, length = 20)
                public String getEserId() {
                    return eserId;
                }
                    
                    /**
             * 设置：簽核人姓名
             */
            public void setEserNam(String eserNam) {
                this.eserNam = eserNam;
            }

        /**
         * 获取：簽核人姓名
         */
        
                            @Column(name = "ESER_NAM", nullable = false, length = 20)
                public String getEserNam() {
                    return eserNam;
                }
                    
                    /**
             * 设置：簽核人郵件
             */
            public void setEserMail(String eserMail) {
                this.eserMail = eserMail;
            }

        /**
         * 获取：簽核人郵件
         */
        
                            @Column(name = "ESER_MAIL", nullable = false, length = 20)
                public String getEserMail() {
                    return eserMail;
                }
                    
                    /**
             * 设置：審核狀態(W待審核Y審核通過N駁回E結案)
             */
            public void setEserStat(String eserStat) {
                this.eserStat = eserStat;
            }

        /**
         * 获取：審核狀態(W待審核Y審核通過N駁回E結案)
         */
        
                            @Column(name = "ESER_STAT", nullable = false, length = 20)
                public String getEserStat() {
                    return eserStat;
                }
                    
                    /**
             * 设置：簽核批註
             */
            public void setEserMemo(String eserMemo) {
                this.eserMemo = eserMemo;
            }

        /**
         * 获取：簽核批註
         */
        
                            @Column(name = "ESER_MEMO", nullable = false, length = 20)
                public String getEserMemo() {
                    return eserMemo;
                }
                    
                    /**
             * 设置：簽核日期
             */
            public void setEserDate(Date eserDate) {
                this.eserDate = eserDate;
            }

        /**
         * 获取：簽核日期
         */
        
                            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
                @Column(name = "ESER_DATE", nullable = false, length = 20)
                public Date getEserDate() {
                return eserDate;
            }
                    
                    /**
             * 设置：簽核電腦IP
             */
            public void setEserIp(String eserIp) {
                this.eserIp = eserIp;
            }

        /**
         * 获取：簽核電腦IP
         */
        
                            @Column(name = "ESER_IP", nullable = false, length = 20)
                public String getEserIp() {
                    return eserIp;
                }
                    
                    /**
             * 设置：建立人員
             */
            public void setCrter(String crter) {
                this.crter = crter;
            }

        /**
         * 获取：建立人員
         */
        
                            @Column(name = "CRTER", nullable = false, length = 20)
                public String getCrter() {
                    return crter;
                }
                    
                    /**
             * 设置：建立日期
             */
            public void setCrtdate(Date crtdate) {
                this.crtdate = crtdate;
            }

        /**
         * 获取：建立日期
         */
        
                            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
                @Column(name = "CRTDATE", nullable = false, length = 20)
                public Date getCrtdate() {
                return crtdate;
            }
                    
                    /**
             * 设置：修改人員
             */
            public void setMdier(String mdier) {
                this.mdier = mdier;
            }

        /**
         * 获取：修改人員
         */
        
                            @Column(name = "MDIER", nullable = false, length = 20)
                public String getMdier() {
                    return mdier;
                }
                    
                    /**
             * 设置：修改日期
             */
            public void setMdidate(Date mdidate) {
                this.mdidate = mdidate;
            }

        /**
         * 获取：修改日期
         */
        
                            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
                @Column(name = "MDIDATE", nullable = false, length = 20)
                public Date getMdidate() {
                return mdidate;
            }
                    
}
