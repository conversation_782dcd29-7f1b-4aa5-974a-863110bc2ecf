package com.foxconn.ipebg.basics.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.foxconn.ipebg.basics.entity.*;
import com.foxconn.ipebg.basics.service.*;
import com.foxconn.ipebg.basics.vo.BsTrainingArrangeVO;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.utils.DateUtils;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.report.entity.AbsentReportEntity;
import com.foxconn.ipebg.report.service.AbsentReportService;
import com.foxconn.ipebg.system.utils.UserUtil;
import com.google.common.annotations.VisibleForTesting;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;
import com.foxconn.ipebg.system.entity.User;
import com.foxconn.ipebg.basics.entity.BsTrainingPeriodEntity;


/**
 * 培訓期日排班表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-09-09 13:50:28
 */
@Controller
@RequestMapping("bstrainingarrange")
public class BsTrainingArrangeController extends BaseController {

    @Autowired
    private BsTrainingArrangeService bsTrainingArrangeService;
    @Autowired
    private BsTrainingArrangePerService bsTrainingArrangePerService;
    @Autowired
    private AbsentReportService absentReportService;
    @Autowired
    private ArtificialAuditService artificialAuditService;
    @Autowired
    private BsTraineeService bsTraineeService;
    @Autowired
    private BsTrainingPeriodService bsTrainingPeriodService;
    @Autowired
    private BsGuardLeaveService guardLeaveService;

    /**
     * 方法描述: 列表信息
     *
     * @Author: ********
     * @CreateDate: 2019-09-09 13:50:28
     * @Return
     **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:bstrainingarrange:list")
    public String list(Model model) {
        model.addAttribute("periodList", JSON.toJSONString(bsTrainingArrangeService.listAllTrainingPeriod()));
        //查询列表数据
        return "basics/bstrainingarrange/list";
    }

    /**
     * 方法描述:  分頁查詢信息
     *
     * @Author: ********
     * @CreateDate: 2019-09-09 13:50:28
     * @Return
     **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:bstrainingarrange:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        List<PropertyFilter> periodFilters = new ArrayList<PropertyFilter>();
        User user = UserUtil.getCurrentUser();
        if (user != null) {
            // 按保安公司查询
            PropertyFilter filter = new PropertyFilter("INS_securityCom", user.getSecurityComs());
            periodFilters.add(filter);
        }
        Page<BsTrainingPeriodEntity> periodPage = new Page<BsTrainingPeriodEntity>();
        periodPage.setPageNo(0);
        periodPage.setPageSize(Page.MAXSIZE);
        periodPage = bsTrainingPeriodService.search(periodPage, periodFilters);
        List<String> periodNoList = new ArrayList<String>();
        for (BsTrainingPeriodEntity period : periodPage.getResult()) {
            periodNoList.add(period.getTrainingPeriodNo());
        }
        if (periodNoList.size() == 0) {
            periodNoList.add("");// 數組不能為空，不然下面的in查詢會報錯
        }
        Page<BsTrainingArrangeEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        PropertyFilter periodFilter = new PropertyFilter("INS_trainingPeriodNo", periodNoList);
        filters.add(periodFilter);
        page = bsTrainingArrangeService.search(page, filters);
        return getEasyUIData(page);
    }

    /**
     * 方法描述: 保存
     *
     * @Author: ********
     * @CreateDate: 2019-09-09 13:50:28
     * @Return
     **/

    @RequestMapping(value = "create", method = RequestMethod.POST)
    @RequiresPermissions("basics:bstrainingarrange:add")
    @ResponseBody
    public String create(@Valid BsTrainingArrangeEntity bsTrainingArrange, Model model) {
        bsTrainingArrange.setNewRecord(true);
        bsTrainingArrange.setCreateDate(new Date());
        bsTrainingArrangeService.save(bsTrainingArrange);
        return "success";
    }

    /**
     * 方法描述: 添加跳轉
     *
     * @Author: ********
     * @CreateDate: 2019-09-09 13:50:28
     * @Return
     **/

    @RequiresPermissions("basics:bstrainingarrange:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(String arrangeDate, String trainingPeriodNo, Model model) {
        model.addAttribute("arrangeDate", arrangeDate);
        model.addAttribute("trainingPeriodNo", trainingPeriodNo);
        model.addAttribute("periodList", JSON.toJSONString(bsTrainingArrangeService.listAllTrainingPeriod()));
        model.addAttribute("action", "create");
        return "basics/bstrainingarrange/listForm";
    }

    /**
     * 方法描述: 修改跳轉
     *
     * @Author: ********
     * @CreateDate: 2018-10-06 11:04:39
     * @Return
     **/

    @RequiresPermissions("basics:bstrainingarrange:update")
    @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
    public String updateForm(@PathVariable("id") String id, Model model) {
        model.addAttribute("bsTrainingArrange", bsTrainingArrangeService.get(id));
        model.addAttribute("periodList", JSON.toJSONString(bsTrainingArrangeService.listAllTrainingPeriod()));
        model.addAttribute("action", "update");
        return "basics/bstrainingarrange/listForm";
    }

    /**
     * 方法描述: 修改
     *
     * @Author: ********
     * @CreateDate: 2018-10-11 10:55:00
     * @Return
     **/

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @RequiresPermissions("basics:bstrainingarrange:update")
    @ResponseBody
    public String update(@Valid BsTrainingArrangeEntity bsTrainingArrange, @RequestParam(value = "ids") String ids) {
        bsTrainingArrange.setId(ids);
        bsTrainingArrange.setUpdateDate(new Date());
        bsTrainingArrangeService.update(bsTrainingArrange);

        return "success";
    }

    /**
     * 方法描述: 根據主鍵刪除
     *
     * @Author: ********
     * @CreateDate: 2019-09-09 13:50:28
     * @Return
     **/

    @RequestMapping("delete/{id}")
    @RequiresPermissions("basics:bstrainingarrange:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        bsTrainingArrangeService.delete(id);
        return "success";
    }

    /**
     * 导出excel
     *
     * @Author: ********
     * @CreateDate: 2019-09-09 13:50:28
     * @Return
     **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {
    }

//   培訓人員Start

    /**
     * 培訓人員排班
     *
     * @param model
     * @return
     */
    @RequestMapping(value = "/arrangeper", method = RequestMethod.GET)
    public String arrangeperson(String arrangeDate, String trainingPeriodNo, Model model) {
        model.addAttribute("arrangeDate", arrangeDate);
        model.addAttribute("trainingPeriodNo", trainingPeriodNo);
        model.addAttribute("periodList", JSON.toJSONString(bsTrainingArrangeService.listAllTrainingPeriod()));
        return "basics/bstrainingarrange/arrangeperson";
    }

    /**
     * 初始化培訓人員數據
     *
     * @param params
     * @return
     */
    @RequestMapping(value = "/arrangeper/initData", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> arrangeperInitData(@RequestParam Map<String, String> params) {
        Map<String, Object> result = new HashMap<>();
        int i = bsTrainingArrangeService.countArrange(params);
        List<Map<String, String>> allTraineeArr = bsTrainingArrangeService.listAllTrainee(params);
        List<Map<String, String>> unvalidTrainee = bsTrainingArrangeService.listUnvalidTrainee(params);
        List<Map<String, String>> leaveTrainee = guardLeaveService.leaveListByParamForTrainArrange(params, allTraineeArr);
        unvalidTrainee.addAll(leaveTrainee);
        if (i == 0) {
            result.put("unvalidTraineeArr", unvalidTrainee);
            result.put("allTraineeArr", allTraineeArr);
            result.put("action", "create");
        } else if (i == 1) {
            result.put("unvalidTraineeArr", unvalidTrainee);
            result.put("arrangeTraineeArr", bsTrainingArrangeService.listArrangeTrainee(params));
            result.put("allTraineeArr", allTraineeArr);
            result.put("action", "update");
        }
        return result;
    }

    /**
     * 培訓人員排班保存
     *
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/arrangeper/save", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> arrangeperSave(@RequestBody String jsonStr) {
        //校驗條件是否一致
        JSONObject jsonObject = JSON.parseObject(jsonStr);
        JSONObject validParams = jsonObject.getJSONObject("validParams");
        Map<String, String> params = new HashMap<>(4);
        params.put("arrangeDate", validParams.getString("arrangeDate"));
        params.put("trainingPeriodNo", validParams.getString("trainingPeriodNo"));

        List<Map<String, String>> arrangedA = bsTrainingArrangeService.listArrangeTrainee(params);
        List<Map<String, String>> allA = bsTrainingArrangeService.listAllTrainee(params);
        List<Map<String, String>> unvalidA = bsTrainingArrangeService.listUnvalidTrainee(params);

        List<Map<String, String>> leaveTrainee = guardLeaveService.leaveListByParamForTrainArrange(params, allA);
        unvalidA.addAll(leaveTrainee);

        JSONObject validValues = jsonObject.getJSONObject("validValues");
        JSONArray unvalidB = validValues.getJSONArray("unvalidTraineeArr");
        JSONArray allB = validValues.getJSONArray("allTraineeArr");
        JSONArray arrangedB = validValues.getJSONArray("traineeArr");

        boolean isSame = true;
        isSame = isSame(unvalidA, unvalidB, isSame);
        isSame = isSame(allA, allB, isSame);
        isSame = isSame(arrangedA, arrangedB, isSame);

        Map<String, Object> result = new HashMap<>();
        if (isSame) {
            BsTrainingArrangeVO arrangeVO = JSON.parseObject(jsonStr, BsTrainingArrangeVO.class);

            BsTrainingArrangeEntity identity = bsTrainingArrangeService.getByEntity(arrangeVO.getTrainingArrange());
            String loginName = UserUtil.getCurrentUser().getLoginName();
            Date date = new Date();

            if (identity == null) {
                //新增排班
                BsTrainingArrangeEntity arrange = arrangeVO.getTrainingArrange();
                arrange.setCreateBy(loginName);
                arrange.setCreateDate(date);
                bsTrainingArrangeService.save(arrange);
                //新增排班人員
                for (BsTrainingArrangePerEntity perEntity : arrangeVO.getTrainingPerArr()) {
                    perEntity.setBstaId(arrange.getId());
                    perEntity.setCreateBy(loginName);
                    perEntity.setCreateDate(date);
                    bsTrainingArrangePerService.save(perEntity);
                }
                result.put("success", true);
                result.put("msg", "新增成功");
            } else {
                BsTrainingArrangeEntity arrange = arrangeVO.getTrainingArrange();
                identity.setArrangeNum(arrange.getArrangeNum());
                identity.setUpdateBy(loginName);
                identity.setUpdateDate(date);
                bsTrainingArrangeService.update(identity);

                //修改排班人員
                bsTrainingArrangePerService.deleteByBstaId(identity.getId());

                if (arrange.getArrangeNum() > 0) {
                    for (BsTrainingArrangePerEntity perEntity : arrangeVO.getTrainingPerArr()) {
                        perEntity.setBstaId(identity.getId());
                        perEntity.setUpdateBy(loginName);
                        perEntity.setUpdateDate(date);
                        bsTrainingArrangePerService.save(perEntity);
                    }
                }

                result.put("success", true);
                result.put("msg", "修改成功！");
            }
        }else{
            result.put("success", false);
            result.put("msg", "數據校驗失敗！請刷新重試！");
        }
        return result;
    }

    private boolean isSame(List<Map<String, String>> alist, JSONArray blist, boolean isSame) {
        if (!isSame) {
            return false;
        }

        if (alist != null && !alist.isEmpty() && blist != null && !blist.isEmpty()) {
            if (alist.size() == blist.size()) {
                for (int i = 0; i < alist.size(); i++) {
                    if (!alist.get(i).get("empno").equals(blist.getJSONObject(i).getString("empno"))) {
                        isSame = false;
                        break;
                    }
                }
            } else {
                isSame = false;
            }
        }else if((alist == null || alist.isEmpty()) && (blist != null && !blist.isEmpty()) ){
            isSame = false;
        }else if((alist != null && !alist.isEmpty()) && (blist == null || blist.isEmpty()) ){
            isSame = false;
        }
        return isSame;
    }

//    稽核

    /**
     * 人工稽核
     *
     * @param model
     * @return
     */
    @RequestMapping(value = "/checkper", method = RequestMethod.GET)
    public String arrangeperson(String arrangeDate, String trainingPeriodNo, String trainingPeriodName, String isCheck, Model model) {
        model.addAttribute("arrangeDate", arrangeDate);
        model.addAttribute("trainingPeriodNo", trainingPeriodNo);
        model.addAttribute("trainingPeriodName", trainingPeriodName);
        model.addAttribute("isCheck", isCheck);
//        model.addAttribute("isCheck", "N");
        return "basics/bstrainingarrange/checkperson";
    }

    /**
     * 人工稽核data
     *
     * @param arrangeDate
     * @param trainingPeriodNo
     * @return
     */
    @RequestMapping(value = "/checkper/list", method = RequestMethod.GET)
    @ResponseBody
    public Map<String, Object> checkperlist(String arrangeDate, String trainingPeriodNo) {
        Map<String, String> params = new HashMap<>();
        params.put("arrangeDate", arrangeDate);
        params.put("trainingPeriodNo", trainingPeriodNo);
        List<Map<String, String>> checkTrainee = bsTrainingArrangeService.listCheckTrainee(params);

        Map<String, Object> map = new HashMap<>();
        map.put("rows", checkTrainee);
        map.put("total", checkTrainee.size());
        return map;
    }

    /**
     * 人工稽核save
     *
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/checkper/save", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> checkpersave(@RequestBody String jsonStr) {
        JSONObject jsonArray = JSON.parseObject(jsonStr);
        String arrangeDate = jsonArray.getString("arrangeDate");
        String trainingPeriodNo = jsonArray.getString("trainingPeriodNo");
        String trainingPeriodName = jsonArray.getString("trainingPeriodName");
        JSONArray checkArr = jsonArray.getJSONArray("checkArr");
        BsTrainingPeriodEntity period = bsTrainingPeriodService.findByTrainingPeriodNo(trainingPeriodNo);//培訓期

        Date startTime = DateUtils.parseDate(arrangeDate + " " + period.getStartTime());
        Date endTime = DateUtils.parseDate(arrangeDate + " " + period.getEndTime1());
        Date shiftDate = DateUtils.parseDate(arrangeDate);
        Date now = new Date();
        String loginName = UserUtil.getCurrentUser().getLoginName();

        if (checkArr != null && !checkArr.isEmpty()) {
            for (int i = 0; i < checkArr.size(); i++) {
                JSONObject check = checkArr.getJSONObject(i);
                //人工稽核-增量保存
                ArtificialAuditEntity saveArtif = new ArtificialAuditEntity();
                BsTraineeEntity trainee = bsTraineeService.findPersonByEmpNo(check.getString("emp_no"));
                saveArtif.setEmpNo(trainee.getEmpNo());
                saveArtif.setEmpName(trainee.getEmpName());
                saveArtif.setCompany(trainee.getCompany());
                saveArtif.setPostRecno(trainingPeriodNo);
                saveArtif.setPostName(trainingPeriodName);
                saveArtif.setShiftDate(shiftDate);
                saveArtif.setShiftName("培訓");
                saveArtif.setShiftNo("P");
                saveArtif.setAbnormalState(check.getString("abnormal_state"));
                saveArtif.setCreateBy(loginName);
                saveArtif.setCreateDate(now);
                artificialAuditService.save(saveArtif);
            }
        }
        int i = bsTrainingArrangeService.updateIsCheck(shiftDate, trainingPeriodNo, "Y");
        Map<String, Object> result = new HashMap<>();
        if (i > 0) {
            result.put("success", true);
            result.put("msg", "保存成功");
        } else {
            result.put("success", false);
            result.put("msg", "保存失敗");
        }

        return result;
    }

//報表

    /**
     * 報表view
     *
     * @param model
     * @return
     */
    @RequestMapping(value = "/report", method = RequestMethod.GET)
    public String report(Model model) {
        model.addAttribute("periodList", JSON.toJSONString(bsTrainingArrangeService.listAllTrainingPeriod()));
        return "basics/bstrainingarrange/report";
    }

    /**
     * 報表list
     *
     * @param params
     * @return
     */
    @RequestMapping(value = "/report/list", method = RequestMethod.GET)
    @ResponseBody
    public Map<String, Object> reportdata(@RequestParam Map<String, String> params) {
        Page page = getPage(params);
        Map<String, Object> report = bsTrainingArrangeService.trainingReport(page, params);
        return report;
    }


    /**
     * 報表詳情data
     *
     * @param trainingPeriodNo
     * @return
     */
    @RequestMapping(value = "/report/detailList", method = RequestMethod.GET)
    @ResponseBody
    public List<Map<String, String>> reportdetaillist(String trainingPeriodNo) {
        return bsTrainingArrangeService.trainingDetailReport(trainingPeriodNo);
    }


}
