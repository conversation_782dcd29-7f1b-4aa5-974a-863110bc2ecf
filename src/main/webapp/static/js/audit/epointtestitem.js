var dg;
var d;
$(function() {
	dg = $('#dg').datagrid({
		method : "get",
		url : ctx + '/epointtestitem/list',
		fit : true,
		fitColumns : true,
		border : false,
		idField : 'id',
		striped : true,
		pagination : true,
		rownumbers : true,
		loadFilter : function(data) {
			// 过滤数据
			var value = {
				total : data.total,
				rows : []
			};
			var x = 0;
			for ( var i = 0; i < data.rows.length; i++) {
				// 檢驗回復結果0.未檢測1.不合格2.合格

				if (data.rows[i].testResult == "0") {
					data.rows[i].testResult = "未檢測";
				} else if (data.rows[i].testResult == "1") {
					data.rows[i].testResult = "不合格";
				} else if (data.rows[i].testResult == "2") {
					data.rows[i].testResult = "合格";
				}
				else if (data.rows[i].testResult == "3") {
					data.rows[i].testResult = "複檢合格 ";
				}
				
				// ：0.新申請1.使用中2.手動停用（需重新申請該點位）3.複檢停用
				if (data.rows[i].useStatus == "0") {
					data.rows[i].useStatus = "新申請";
				} else if (data.rows[i].useStatus == "1") {
					data.rows[i].useStatus = "使用中";
				} else if (data.rows[i].useStatus == "3") {
					data.rows[i].useStatus = "複檢停用";
				}
				// :1.直飲水2.自建飲用水系統3.二次供水池/箱4.飲用水轉運水罐5.其他
				if (data.rows[i].pointLocationType == "1") {
					data.rows[i].pointLocationType = "直飲水";
				} else if (data.rows[i].pointLocationType == "2") {
					data.rows[i].pointLocationType = "自建飲用水系統";
				} else if (data.rows[i].pointLocationType == "3") {
					data.rows[i].pointLocationType = "二次供水池/箱";
				} else if (data.rows[i].pointLocationType == "4") {
					data.rows[i].pointLocationType = "飲用水轉運水罐";
				} else if (data.rows[i].pointLocationType == "5") {
					data.rows[i].pointLocationType = "其他";
				}

				value.rows[x++] = data.rows[i];
			}
			return value;

		},
		rowStyler : function(index, row) {
			if (row.testResult == '不合格') {
				return 'background-color:red;';
			}
		},
		pageNumber : 1,
		pageSize : 20,
		pageList : [ 10, 20, 30, 40, 50 ],
		singleSelect : true,
		columns : [ [ {
			field : 'id',
			title : '主鍵',
			hidden : true
		}, {
			field : 'serialno',
			title : '檢測項目編碼',
			sortable : true,
			width : 100
		}, {
			field : 'itemContent',
			title : '項目內容',
			sortable : true,
			width : 100
		}, {
			field : 'testResult',
			title : '檢驗回復結果',
			sortable : true,
			width : 100
		}, {
			field : 'testMemo',
			title : '備註',
			sortable : true,
			width : 100
		}, {
			field : 'pointno',
			title : '點位編碼',
			sortable : true,
			width : 100
		}, {
			field : 'dptQun',
			title : '事業群',
			sortable : true,
			width : 100
		}, {
			field : 'dptChu',
			title : '事業處',
			sortable : true,
			width : 100
		}, {
			field : 'dptBu',
			title : '部門',
			sortable : true,
			width : 100
		}, {
			field : 'dptKe',
			title : '單位',
			sortable : true,
			width : 100
		}, {
			field : 'manager',
			title : '管理人',
			sortable : true,
			width : 100
		}, {
			field : 'attachids',
			title : '複檢報告',
			sortable : true,
			width : 100
		}, {
			field : 'updateBy',
			title : '更新人',
			sortable : true,
			width : 100
		}, {
			field : 'updateDate',
			title : '檢驗狀態更新時間',
			sortable : true,
			width : 100
		}, {
			field : 'reupdateBy',
			title : '複檢人',
			sortable : true,
			width : 100
		}, {
			field : 'reupdateDate',
			title : '複檢更新時間',
			sortable : true,
			width : 100
		} ] ],
		enableHeaderClickMenu : true,
		enableHeaderContextMenu : true,
		enableRowContextMenu : false,
		rowTooltip : true,
		toolbar : '#tb'
	});
	

	
});

// 弹窗增加
function add() {
	d = $("#dlg").dialog({
		title : '添加點位檢測明細表',
		width : 380,
		height : 380,
		href : ctx + '/epointtestitem/create',
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '确认',
			handler : function() {
				$("#mainform").submit();
			}
		}, {
			text : '取消',
			handler : function() {
				d.panel('close');
			}
		} ]
	});
}

// 删除
function del() {
	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data) {
		if (data) {
			$.ajax({
				type : 'get',
				url : ctx + "/epointtestitem/delete/" + row.id,
				success : function(data) {
					successTip(data, dg);
				}
			});
		}
	});
}

// 回復作業修改
function upd() {
	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;

	if (row.testResult != "未檢測") {
		alert("未檢測狀態才可進行結果回復!");
		return;
	}

	d = $("#dlg")
			.dialog(
					{
						title : '回復作業',
						width : 380,
						height : 340,
						href : ctx + '/epointtestitem/update/' + row.id,
						maximizable : true,
						modal : true,
						buttons : [
								{
									text : '修改',
									handler : function() {
										var testResult = $
												.trim($(
														'input[id="testResult"]:checked')
														.val());
										if (testResult = '1') {
											if ($("#testMemo").val() == null
													|| $("#testMemo").val() == '') {
												// alert("請輸入承辦人工號.");
												$.messager.alert("溫馨提示",
														"備註不能為空", "info");
												return;
											}

										}
										$('#mainform').submit();
									}
								}, {
									text : '取消',
									handler : function() {
										d.panel('close');
									}
								} ]
					});
}
// 弹窗上傳附檔
function uploadFileForm() {
	//console.log("ff333");
	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	if (row.testResult!="不合格")
		{
		alert("不合格點位才可上傳附檔!");
		return;
		}
	if (row.attachids!=null)
	{
	alert("該單號已經上傳預算核准檔！");
	return;
	}
	d = $("#dlg").dialog({
		title : '飲用水檢測預算核准檔上傳',
		width : 380,
		height : 340,
		href : ctx + '/epointtestitem/uploadFileForm/' + row.id,
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '確定',
			handler : function() {
				$('#mainform').submit();
			}
		}, {
			text : '取消',
			handler : function() {
				d.panel('close');
			}
		} ]
	});
}
// 创建查询对象并查询
function cx() {
	var obj = $("#searchFrom").serializeObject();
	dg.datagrid('load', obj);
}
// 表單進度查詢頁面查詢條件重置
function listSearchReset() {
	$("#searchFrom").form("reset");
}
// 导出excel
function exportExcel() {
	var form = document.getElementById("searchFrom");
	searchFrom.action = ctx + '/epointtestitem/exportExcel';
	form.submit();
}
//普通文件上傳
function uploadFile() {
    var attachIds = $.trim($("#attachids").val());
    var attachIdsL = attachIds.split(",").length;
    if (attachIdsL >= 6) {
        $.messager.alert("溫馨提示", "上傳附件個數不能超過五個！", "info");
        return;
    }
    var explorer = window.navigator.userAgent;
    if (!!window.ActiveXObject || "ActiveXObject" in window) {
        ajaxLoading();
        $.ajaxFileUpload({  // Jquery插件上传文件
            url: ctx + "/admin/uploadCompatible",
            secureuri: false,// 是否启用安全提交 默认为false
            fileElementId: "attachidsUpload", // type="file"的id
            dataType: "JSON",  // 返回值类型
            success: function (msg, status) {
                msg = jQuery.parseJSON(msg);
                $("#dowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href=' + ctx + '/admin/download/' + msg.id + '>' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delAtt(\'' + msg.id + '\')"></div></div>');
                $("#attachids").val($("#attachids").val() + msg.id + ",");
                ajaxLoadEnd();
            },
            error: function (data, status, e) {
                ajaxLoadEnd();
            }
        });
    }else{
// 创建
        var form_data = new FormData();
        // 获取文件
// var file = document.getElementById('fileToUpload').files[0];
        var file_data = $("#attachidsUpload").prop("files")[0];
        form_data.append("file", file_data);
        form_data.append("path", "upload");
        // 把所以表单信息
        $.ajax({
            type: "POST",
            url: ctx+"/admin/upload",
            beforeSend: ajaxLoading,
           dataType : "json",
            processData: false,  // 注意：让jQuery不要处理数据
            contentType: false,  // 注意：让jQuery不要设置contentType
            data: form_data
        }).success(function (msg) {
            ajaxLoadEnd();
            $("#dowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href='+ctx+'/admin/download/' + msg.id + '>' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delAtt(\'' + msg.id + '\')"></div></div>');
            $("#attachids").val($("#attachids").val() + msg.id + ",");
            // $("#attachids").val(msg.id);
        }).fail(function (msg) {
            ajaxLoadEnd();
        });
    }
}



function delAtt(fileid) {
    $("#" + fileid).remove();
    var kk = $("#attachids").val();
    kk = kk.replace(fileid + ",", "");
    $("#attachids").val(kk);
    ajaxLoading();
    $.post(ctx + '/admin/delete/' + fileid, {}, function (data) {
        successTip(data);
        ajaxLoadEnd();
    }, 'text').error(function() { ajaxLoadEnd(); });
}