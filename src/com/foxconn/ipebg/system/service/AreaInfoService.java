package com.foxconn.ipebg.system.service;

import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseService;
import com.foxconn.ipebg.system.dao.AreaInfoDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foxconn.ipebg.system.entity.AreaInfo;

/**
 * 区域service
 * <AUTHOR>
 * @date 2015年5月09日
 */
@Service
@Transactional(readOnly=true)
public class AreaInfoService extends BaseService<AreaInfo, Integer> {
	
	@Autowired
	private AreaInfoDao areaInfoDao;
	
	@Override
	public HibernateDao<AreaInfo, Integer> getEntityDao() {
		return areaInfoDao;
	}

}
