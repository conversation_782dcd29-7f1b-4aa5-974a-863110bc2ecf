var dg;
var d;
$(function () {

    //绑定培訓期下拉框
    $('#trainingPeriodNo').combogrid({
        data: _periodList,
        panelWidth: 200,
        multiple: false,
        idField: 'training_period_no',
        textField: 'training_period_name',
        fitColumns: true,
        columns: [[
            {field: 'training_period_name', title: '培訓期名稱', width: 80}
        ]]
    });

    if($("#hideWorkMonth").val()){
        $("#searchFrom").hide();
    }

    dg = $('#dg').datagrid({
        method: "get",
        url: ctx + '/bstrainingcost/list',
        fit: true,
        fitColumns: false,
        border: false,
        idField: 'id',
        striped: true,
        queryParams:{
            sort:'trainingPeriodNo',
            order:'desc',
            filter_EQS_workMonth:$("#hideWorkMonth").val(),
            filter_EQS_postRecno:$("#hidePostRecno").val()
        },
        pagination: true,
        rownumbers: true,
        pageNumber: 1,
        pageSize: 20,
        pageList: [10, 20, 30, 40, 50],
        singleSelect: true,
        frozenColumns:[[
            {field: 'id', title: '資料識別ID  Primary', hidden: true, width: 100},
            {field: 'trainingPeriodName', title: '培訓期', sortable: true, width: 120}
            ,
            {field: 'empNo', title: '工號', sortable: true, width: 80}
            ,
            {field: 'empName', title: '姓名', sortable: true, width: 80}
        ]],
        columns: [[
            {field: 'dates', title: '培訓日期', sortable: true, width: 500}
            ,
            {field: 'days', title: '培訓天數', sortable: true,align: 'right', width: 60}
            ,
            {field: 'trainingCost', title: '培訓費用', sortable: true,align: 'right', width: 60}
            ,
            {field: 'trainingPeriodNo', title: '培訓期編碼',  hidden: true, width: 100}
            ,
            {field: 'workMonth', title: '結算月份', sortable: false, width: 80}
            ,
            {field: 'startDate', title: '培訓開始時間', sortable: true, width: 100,
                formatter:function(value,row,index){
                    return value.substr(0,10);
                }
            }
            ,
            {field: 'endDate', title: '培訓結束時間', sortable: true, width: 100,
                formatter:function(value,row,index){
                    return value.substr(0,10);
                }
            }

        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        rowTooltip: true,
        toolbar: '#tb'
    });
});

//弹窗增加
function add() {
    d = $("#dlg").dialog({
        title: '添加培訓期費用結帳明細表',
        width: 380,
        height: 380,
        href: ctx + '/bstrainingcost/create',
        maximizable: true,
        modal: true,
        buttons: [{
            text: '确认',
            handler: function () {
                $("#mainform").submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//删除
function del() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function (data) {
        if (data) {
            $.ajax({
                type: 'get',
                url: ctx + "/bstrainingcost/delete/" + row.settlementDate,
                success: function (data) {
                    successTip(data, dg);
                }
            });
        }
    });
}

//弹窗修改
function upd() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    d = $("#dlg").dialog({
        title: '修改培訓期費用結帳明細表',
        width: 380,
        height: 340,
        href: ctx + '/bstrainingcost/update/' + row.settlementDate,
        maximizable: true,
        modal: true,
        buttons: [{
            text: '修改',
            handler: function () {
                $('#mainform').submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//创建查询对象并查询
function cx() {
    var obj = $("#searchFrom").serializeObject();
    dg.datagrid('load', obj);
}

//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("reset");
}

//导出excel
function exportExcel() {
    var form = document.getElementById("searchFrom");
    searchFrom.action = ctx + '/bstrainingcost/exportExcel';
    form.submit();
}