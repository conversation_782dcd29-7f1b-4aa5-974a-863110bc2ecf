package com.foxconn.ipebg.system.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * ${comments}
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-17 09:10:19
 */
@Entity
@Table(name = "t_qh_userduty")
@DynamicUpdate
@DynamicInsert
public class TQhUserdutyEntity extends DataEntity<TQhUserdutyEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    //$column.comments
    private String dutyid;
    //$column.comments
    private String userid;
    private String empno;
    //$column.comments
    private String factoryid;
    //$column.comments
    private String modor;
    //$column.comments
    private Date modifytime;
    //$column.comments
    private String area;
    //$column.comments
    private String building;
    //$column.comments
    private String remarks;
    //$column.comments
    private String factorycode;
    //$column.comments
    private String applydeptno;

    /**
     * 设置：${column.comments}
     */
    public void setDutyid(String dutyid) {
        this.dutyid = dutyid;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "dutyid", nullable = false, length = 20)
    public String getDutyid() {
        return dutyid;
    }

    /**
     * 设置：${column.comments}
     */
    public void setUserid(String userid) {
        this.userid = userid;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "userid", nullable = false, length = 20)
    public String getUserid() {
        return userid;
    }

    @Column(name = "empno", nullable = false, length = 20)
    public String getEmpno() {
        return empno;
    }

    public void setEmpno(String empno) {
        this.empno = empno;
    }

    /**
     * 设置：${column.comments}
     */
    public void setFactoryid(String factoryid) {
        this.factoryid = factoryid;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "factoryid", nullable = false, length = 20)
    public String getFactoryid() {
        return factoryid;
    }

    /**
     * 设置：${column.comments}
     */
    public void setModor(String modor) {
        this.modor = modor;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "modor", nullable = false, length = 20)
    public String getModor() {
        return modor;
    }

    /**
     * 设置：${column.comments}
     */
    public void setModifytime(Date modifytime) {
        this.modifytime = modifytime;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "modifytime", nullable = false, length = 20)
    public Date getModifytime() {
        return modifytime;
    }


    /**
     * 设置：${column.comments}
     */
    public void setArea(String area) {
        this.area = area;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "area", nullable = false, length = 20)
    public String getArea() {
        return area;
    }

    /**
     * 设置：${column.comments}
     */
    public void setBuilding(String building) {
        this.building = building;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "building", nullable = false, length = 20)
    public String getBuilding() {
        return building;
    }

    /**
     * 设置：${column.comments}
     */
    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "remarks", nullable = false, length = 20)
    public String getRemarks() {
        return remarks;
    }

    /**
     * 设置：${column.comments}
     */
    public void setFactorycode(String factorycode) {
        this.factorycode = factorycode;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "factorycode", nullable = false, length = 20)
    public String getFactorycode() {
        return factorycode;
    }

    /**
     * 设置：${column.comments}
     */
    public void setApplydeptno(String applydeptno) {
        this.applydeptno = applydeptno;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "applydeptno", nullable = false, length = 20)
    public String getApplydeptno() {
        return applydeptno;
    }


}
