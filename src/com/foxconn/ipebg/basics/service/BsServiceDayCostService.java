package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.dao.BsServiceDayCostDao;
import com.foxconn.ipebg.basics.entity.BsServiceDayCostEntity;
import com.foxconn.ipebg.basics.vo.TempWorkcostGatherVO;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import com.foxconn.ipebg.common.utils.ConvertUtils;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.system.entity.User;
import com.foxconn.ipebg.system.service.DictService;
import com.foxconn.ipebg.basics.vo.FinancialVO;
import com.foxconn.ipebg.system.utils.UserUtil;
import jxl.format.*;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.Colour;
import jxl.format.VerticalAlignment;
import jxl.write.*;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.hibernate.type.StandardBasicTypes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 警衛服務費用結賬明細主表
 *
 * <AUTHOR>
 * @email <EMAIL> 
 * @date 2019-10-07 13:15:24
 */
@Service
@Transactional(readOnly=true)
public class BsServiceDayCostService extends BaseBusinessService<BsServiceDayCostEntity, String>{
    @Autowired
    private BsServiceDayCostDao bsServiceDayCostDao;

    @Autowired
    private UserServiceUtil serviceUtil;
    @Autowired
    private DictService dictService;

    @Override
    public HibernateDao<BsServiceDayCostEntity, String> getEntityDao() {
        return bsServiceDayCostDao;
    }

    public Page<BsServiceDayCostEntity> getGroupedEntityList(Page<BsServiceDayCostEntity> page, String qun, String legal, String securityCom, String startDate, String endDate) {
        String sql = "select sys_guid() id, null create_by, null create_date, null update_by, null update_date, '' del_flag, min(security_com) security_com, null work_date, 0 pernu_a, 0 pernu_b, recno, dpt_qun, legal, dpt_bu, cost_id, post_nam, sum(pernu) pernu, sum(normal_cost) normal_cost, sum(training_cost) training_cost, sum(regular_cost) regular_cost, sum(overtime_hours) overtime_hours, sum(overtime_cost) overtime_cost, sum(total_cost) total_cost " +
                "from bs_service_day_cost where 1=1 ";
        if (StringUtils.isNotBlank(qun)) {
            sql += "and dpt_qun = '"+qun+"' ";
        }
        if (StringUtils.isNotBlank(legal)) {
            sql += "and legal ='"+legal+"' ";
        }
        if (StringUtils.isNotBlank(securityCom)) {
            sql += "and security_com ='"+securityCom+"' ";
        }
        User user = UserUtil.getCurrentUser();
        if (user != null) {
            // 按用戶權限查詢報表
            if (StringUtils.isBlank(user.getSecurityCom())) {
                // 沒有權限
                sql += "and security_com = '' ";
            } else {
                String coms = user.getSecurityCom().replace(",", "','");
                sql += "and security_com in ('"+coms+"') ";
            }
        }
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            sql += "and work_date between to_date('"+startDate+"', 'yyyy-mm-dd') and to_date('"+endDate+"', 'yyyy-mm-dd') ";
        }
        sql += "group by recno, dpt_qun, legal, dpt_bu, cost_id, post_nam " +
                "order by recno, post_nam ";
        if (page.getTotalCount() == -1) {
            String countSql = "select count(1) from ("+sql+") as cnter";
            //Integer count = Integer.valueOf(this.bsArrangePostPersonDao.createSQLQuery(countSql.toString()).uniqueResult().toString());
            Integer count = Integer.valueOf(this.bsServiceDayCostDao.createSQLQuery(countSql).uniqueResult().toString());
            page.setTotalCount(count);
        }
        sql += "limit "+page.getPageSize()+" offset "+page.getPageSize()*(page.getPageNo()-1);
        List<BsServiceDayCostEntity> list = bsServiceDayCostDao.createSQLQuery(sql).addEntity(BsServiceDayCostEntity.class).list();
        page.setResult(list);
        return page;
    }

    public BsServiceDayCostEntity getSummary(String qun, String legal, String securityCom, String beginDate, String endDate) {
        String sql = "select sys_guid() id, '' recno, '' dpt_qun, '' legal, '' dpt_bu, '' cost_id, '' post_nam, '' security_com, null pernu_a, null pernu_b, null pernu, '' create_by, null create_date, null work_date, '' update_by, null update_date, '' del_flag, " +
                "round(sum(normal_cost),2) normal_cost, " +
                "round(sum(training_cost),2) training_cost, " +
                "round(sum(regular_cost),2) regular_cost, " +
                "sum(overtime_hours) overtime_hours, " +
                "round(sum(overtime_cost),2) overtime_cost, " +
                "round(sum(total_cost),2) total_cost " +
                "from BS_SERVICE_DAY_COST " +
                " where 1=1";
        if (StringUtils.isNotBlank(qun)) {
            sql += " and dpt_qun = '" + qun + "' ";
        }
        if (StringUtils.isNotBlank(legal)) {
            sql += " and legal = '" + legal + "' ";
        }
        if (StringUtils.isNotBlank(securityCom)) {
            sql += " and security_com = '"+ securityCom + "' ";
        }
        User user = UserUtil.getCurrentUser();
        if (user != null) {
            // 按用戶權限查詢報表
            if (StringUtils.isBlank(user.getSecurityCom())) {
                // 沒有權限
                sql += "and security_com = '' ";
            } else {
                String coms = user.getSecurityCom().replace(",", "','");
                sql += "and security_com in ('"+coms+"') ";
            }
        }
        if (StringUtils.isNotBlank(beginDate)) {
            sql += " and work_date >= to_date('"+beginDate+"', 'yyyy-mm-dd') ";
        }
        if (StringUtils.isNotBlank(endDate)) {
            sql += " and work_date <= to_date('"+endDate+"', 'yyyy-mm-dd') ";
        }
        List<BsServiceDayCostEntity> list = this.bsServiceDayCostDao.createSQLQuery(sql).addEntity(BsServiceDayCostEntity.class).list();
        return list.get(0);
    }

    @Transactional(readOnly=false)
    public void generateServiceCostByDate(Date date) throws SQLException {
        bsServiceDayCostDao.generateServiceCost(date);
    }

    /**
     * 經管報表數據查詢
     * @param params 查詢參數 dptQun legal costId securityCom startDate endDate
     * @return 列表數據
     */
    public List<FinancialVO> financialList(Map<String, String> params) {
        String dptQun = params.get("dptQun");
        String legal = params.get("legal");
        String costId = params.get("costId");
        String securityCom = params.get("securityCom");
        String startDate = params.get("startDate");
        String endDate = params.get("endDate");
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            return Arrays.asList();
        }
        StringBuffer sql = new StringBuffer();
        sql.append("select dpt_qun as dptQun, legal, cost_id as costId, sum(regular_cost) regularCost, sum(overtime_cost) overtimeCost");
        sql.append(", (select round(COALESCE(sum(expenses), 0), 2) from BS_TEMP_SERVICE where cost_code = a.cost_id and start_time between to_date('"+startDate+"', 'yyyy-mm-dd') and to_date('"+endDate+"', 'yyyy-mm-dd')) tempCost1");
        if (StringUtils.isNotBlank(securityCom)) {
            sql.append(", (select round(COALESCE(sum(b.cost), 0), 2) from e_temp_security_cost_aply b, e_temp_security_aply c where b.service_serialno = c.serialno and b.apply_stat = 'E' and c.apply_stat = 'E' and c.cost_id = a.cost_id and b.security_com = '"+securityCom+"' and c.start_time between to_date('"+startDate+"', 'yyyy-mm-dd') and to_date('"+endDate+"', 'yyyy-mm-dd')) tempCost2");
        } else {
            sql.append(", (select round(COALESCE(sum(b.cost), 0), 2) from e_temp_security_cost_aply b, e_temp_security_aply c where b.service_serialno = c.serialno and b.apply_stat = 'E' and c.apply_stat = 'E' and c.cost_id = a.cost_id and c.start_time between to_date('"+startDate+"', 'yyyy-mm-dd') and to_date('"+endDate+"', 'yyyy-mm-dd')) tempCost2");
        }
        sql.append(" from bs_service_day_cost a");
        sql.append(" where work_date between to_date('"+startDate+"', 'yyyy-mm-dd') and to_date('"+endDate+"', 'yyyy-mm-dd')");
        if (StringUtils.isNotBlank(dptQun)) {
            sql.append(" and dpt_qun = '"+dptQun+"'");
        }
        if (StringUtils.isNotBlank(legal)) {
            sql.append(" and legal = '"+legal+"'");
        }
        if (StringUtils.isNotBlank(costId)) {
            sql.append(" and cost_id = '"+costId+"' ");
        }
        if (StringUtils.isNotBlank(securityCom)) {
            sql.append(" and security_com = '"+securityCom+"'");
        }
        User user = UserUtil.getCurrentUser();
        if (user != null) {
            // 按保安公司查询
            String securityComs = user.getSecurityCom().replace(",", "','");
            sql.append(" and security_com in ('"+securityComs+"') ");
        }
        sql.append(" group by dpt_qun, legal, cost_id");
        sql.append(" order by dpt_qun, legal, cost_id");
        SQLQuery sqlQuery=this.bsServiceDayCostDao.createSQLQuery(sql.toString());
        sqlQuery.addScalar("dptQun", StandardBasicTypes.STRING);
        sqlQuery.addScalar("legal",StandardBasicTypes.STRING);
        sqlQuery.addScalar("costId",StandardBasicTypes.STRING);
        sqlQuery.addScalar("regularCost",StandardBasicTypes.FLOAT);
        sqlQuery.addScalar("overtimeCost",StandardBasicTypes.FLOAT);
        sqlQuery.addScalar("tempCost1",StandardBasicTypes.FLOAT);
        sqlQuery.addScalar("tempCost2",StandardBasicTypes.FLOAT);
        sqlQuery.setResultTransformer(Transformers.aliasToBean(FinancialVO.class));
        List<FinancialVO> resultList = sqlQuery.list();
        ConvertUtils.convertPropertyToDictLabel(resultList, "legal", "guard_legalperson");
        return resultList;
    }

    public void generateExcelHeader(WritableSheet sheet) throws WriteException {
        // 表头样式
        WritableFont headerFont = new WritableFont(WritableFont.ARIAL, 11, WritableFont.BOLD, false, UnderlineStyle.NO_UNDERLINE, Colour.BLACK);
        WritableCellFormat headerFormat = new WritableCellFormat(headerFont);
        headerFormat.setAlignment(Alignment.CENTRE);
        headerFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
        headerFormat.setBorder(Border.ALL, BorderLineStyle.THIN, Colour.BLACK);
        headerFormat.setWrap(true);
        Label index1 = new Label(0, 0, "序號", headerFormat);
        sheet.addCell(index1);
        sheet.mergeCells(0, 0, 0, 1);
        sheet.setColumnView(0, 5);
        Label month1 = new Label(1, 0, "事業群", headerFormat);
        sheet.addCell(month1);
        sheet.mergeCells(1, 0, 1, 1);
        sheet.setColumnView(1, 10);
        Label qun1 = new Label(2, 0, "法人", headerFormat);
        sheet.addCell(qun1);
        sheet.mergeCells(2, 0, 2, 1);
        sheet.setColumnView(2, 15);
        Label faren1 = new Label(3, 0, "費用代碼", headerFormat);
        sheet.addCell(faren1);
        sheet.mergeCells(3, 0, 3, 1);
        sheet.setColumnView(3, 13);
        Label bumen1 = new Label(4, 0, "費用（RMB）", headerFormat);
        sheet.addCell(bumen1);
        sheet.mergeCells(4, 0, 8, 0);
        Label feiyongdaima1 = new Label(4, 1, "常規費用", headerFormat);
        sheet.addCell(feiyongdaima1);
        sheet.setColumnView(4, 13);
        Label gangwei1 = new Label(5, 1, "加班費用", headerFormat);
        sheet.addCell(gangwei1);
        sheet.setColumnView(5, 13);
        Label ls1 = new Label(6, 1, "臨時勤務費用", headerFormat);
        sheet.addCell(ls1);
        sheet.setColumnView(6, 13);
        Label ls2 = new Label(7, 1, "臨時勤務費用", headerFormat);
        sheet.addCell(ls2);
        sheet.setColumnView(7, 13);
        Label shangbanrenyuanxinxi = new Label(8, 1, "合計", headerFormat);
        sheet.addCell(shangbanrenyuanxinxi);
        sheet.setColumnView(8, 13);
    }

    public void fillSheet(WritableSheet sheet, List<FinancialVO> list) throws WriteException {
        // 内容格式
        WritableFont contentFont = new WritableFont(WritableFont.ARIAL, 11, WritableFont.NO_BOLD, false, UnderlineStyle.NO_UNDERLINE, Colour.BLACK);
        WritableCellFormat contentFormat = new WritableCellFormat(contentFont);
        contentFormat.setAlignment(Alignment.CENTRE);
        contentFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
        contentFormat.setBorder(Border.ALL, BorderLineStyle.THIN, Colour.BLACK);
        Float normalCostAll = Float.valueOf(0), otCostAll = Float.valueOf(0), tmpCostAll = Float.valueOf(0), tmpCostAll2 = Float.valueOf(0), allCostAll = Float.valueOf(0);
        for (int i = 0; i < list.size(); i++) {
            FinancialVO gather = list.get(i);
            Label index1 = new Label(0, 2 + i, (1 + i) + "", contentFormat);
            sheet.addCell(index1);
            Label group = new Label(1, 2 + i, gather.getDptQun(), contentFormat);
            sheet.addCell(group);
            Label legal = new Label(2, 2 + i, gather.getLegal(), contentFormat);
            sheet.addCell(legal);
            Label costId = new Label(3, 2 + i, gather.getCostId(), contentFormat);
            sheet.addCell(costId);
            Label normalCost = new Label(4, 2 + i, gather.getRegularCost() + "", contentFormat);
            sheet.addCell(normalCost);
            Label otCost = new Label(5, 2 + i, gather.getOvertimeCost() + "", contentFormat);
            sheet.addCell(otCost);
            Label tmpCost = new Label(6, 2 + i, gather.getTempCost1() + "", contentFormat);
            sheet.addCell(tmpCost);
            Label tmpCost2 = new Label(7, 2 + i, gather.getTempCost2() + "", contentFormat);
            sheet.addCell(tmpCost2);
            Label totalCost = new Label(8, 2 + i, gather.getTotalCost() + "", contentFormat);
            sheet.addCell(totalCost);
            normalCostAll += gather.getRegularCost();
            otCostAll += gather.getOvertimeCost();
            tmpCostAll += gather.getTempCost1();
            tmpCostAll2 += gather.getTempCost2();
            allCostAll += gather.getTotalCost();
        }
        Label totalTitle = new Label(3, list.size() + 2, "合计:", contentFormat);
        sheet.addCell(totalTitle);
        Label normal = new Label(4, list.size() + 2, normalCostAll + "", contentFormat);
        sheet.addCell(normal);
        Label ot = new Label(5, list.size() + 2, otCostAll + "", contentFormat);
        sheet.addCell(ot);
        Label tmp = new Label(6, list.size() + 2, tmpCostAll + "", contentFormat);
        sheet.addCell(tmp);
        Label tmp2 = new Label(7, list.size() + 2, tmpCostAll2 + "", contentFormat);
        sheet.addCell(tmp2);
        Label all = new Label(8, list.size() + 2, allCostAll + "", contentFormat);
        sheet.addCell(all);
        Label presentTitle = new Label(3, list.size() + 3, "百分比：", contentFormat);
        sheet.addCell(presentTitle);
        Label normalpercent = new Label(4, list.size() + 3, (allCostAll > 0 ? Math.round(normalCostAll / allCostAll * 100) : 0) + "%", contentFormat);
        sheet.addCell(normalpercent);
        Label otpercent = new Label(5, list.size() + 3, (allCostAll > 0 ? Math.round(otCostAll / allCostAll * 100) : 0) + "%", contentFormat);
        sheet.addCell(otpercent);
        Label tmppercent = new Label(6, list.size() + 3, (allCostAll > 0 ? Math.round(tmpCostAll / allCostAll *100) : 0) + "%", contentFormat);
        sheet.addCell(tmppercent);
        Label tmppercent2 = new Label(7, list.size() + 3, (allCostAll > 0 ? Math.round(tmpCostAll2 / allCostAll * 100) : 0) + "%", contentFormat);
        sheet.addCell(tmppercent2);
        Label allpercent = new Label(8, list.size() + 3, "100%", contentFormat);
        sheet.addCell(allpercent);
    }
}
