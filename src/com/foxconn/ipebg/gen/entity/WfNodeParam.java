package com.foxconn.ipebg.gen.entity;

import java.io.Serializable;

/**
 * 流程節點參數
 * <AUTHOR>
 *
 */
public class WfNodeParam implements Serializable {
	private static final long serialVersionUID = 1L;
	private String workFlowId;
	private String nodeId;
	/**
	 * 節點參數名稱
	 */
	private String parameName;
	/**
	 * 節點參數值
	 */
	private String paramValue;
	/**
	 * 參數類型
	 */
	private String paramType;
	/**
	 * 節點描述，對應按鈕名稱
	 */
	private String describ;
	/**
	 * 工單通過此節點后的狀態
	 */
	private String toWorkStatus;
	/**
	 * 通過標識
	 */
	private String isPass;
	public String getWorkFlowId() {
		return workFlowId;
	}
	public void setWorkFlowId(String workFlowId) {
		this.workFlowId = workFlowId;
	}
	public String getNodeId() {
		return nodeId;
	}
	public void setNodeId(String nodeId) {
		this.nodeId = nodeId;
	}
	public String getParameName() {
		return parameName;
	}
	public void setParameName(String parameName) {
		this.parameName = parameName;
	}
	public String getParamValue() {
		return paramValue;
	}
	public void setParamValue(String paramValue) {
		this.paramValue = paramValue;
	}
	public String getDescrib() {
		return describ;
	}
	public void setDescrib(String describ) {
		this.describ = describ;
	}
	public String getToWorkStatus() {
		return toWorkStatus;
	}
	public void setToWorkStatus(String toWorkStatus) {
		this.toWorkStatus = toWorkStatus;
	}
	public String getParamType() {
		return paramType;
	}
	public void setParamType(String paramType) {
		this.paramType = paramType;
	}
	public String getIsPass() {
		return isPass;
	}
	public void setIsPass(String isPass) {
		this.isPass = isPass;
	}
}
