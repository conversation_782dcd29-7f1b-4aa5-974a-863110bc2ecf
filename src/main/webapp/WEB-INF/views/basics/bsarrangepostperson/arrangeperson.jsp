<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>正式崗日排班人員安排</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
        var _groupList = ${groupList};
        var _arrangeGroupId="${arrangeGroupId}";
        var _arrangeDate="${arrangeDate}";
        var _allowContinueWork="${allowContinueWork}"=="Y";
    </script>

    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .trainee{
            float: left;
            text-align: center;
            border: 2px solid #e0ecff;
            width: 140px;
            padding: 10px 5px;
            border-radius: 5px;
            margin: 5px 2px;
        }

        .trainee:hover {
            float: left;
            text-align: center;
            border: 2px solid #999dff;
            background-color: #e0ecff;
            cursor:pointer;
            width: 140px;
            padding: 10px 5px;
        }
        .person-both{
            float: left;
            text-align: center;
            border: 2px solid #e0ecff;
            width: 140px;
            padding: 10px 5px;
            border-radius: 5px;
            margin: 5px 2px;
        }

        .person-both:hover {
            float: left;
            text-align: center;
            border: 2px solid #999dff;
            background-color: #e0ecff;
            cursor:pointer;
            width: 140px;
            padding: 10px 5px;
        }

        .person-day{
            float: left;
            text-align: center;
            border: 2px solid #e0ecff;
            background-color: #e2e6c3;
            width: 140px;
            padding: 10px 5px;
            border-radius: 5px;
            margin: 5px 2px;
        }

        .person-day:hover {
            float: left;
            text-align: center;
            border: 2px solid #999dff;
            background-color: #e0ecff;
            cursor:pointer;
            width: 140px;
            padding: 10px 5px;
        }

        .person-night {
            float: left;
            text-align: center;
            border: 2px solid #e0ecff;
            background-color: #d6d6f1;
            width: 140px;
            padding: 10px 5px;
            border-radius: 5px;
            margin: 5px 2px;
        }
        .person-night:hover {
            float: left;
            text-align: center;
            border: 2px solid #999dff;
            background-color: #e0ecff;
            cursor:pointer;
            width: 140px;
            padding: 10px 5px;
        }

        .unenable{
            float: left;
            text-align: center;
            border: 2px solid #e0ecff;
            width: 140px;
            padding: 10px 5px;
            border-radius: 5px;
            margin: 5px 2px;
            background-color: #d6d6d6;
        }

        .post{
            float: left;
            text-align: center;
            border: 2px solid #e0ecff;
            width: 140px;
            padding: 10px 5px;
            border-radius: 5px;
            margin: 5px 2px;
        }

        .post:hover {
            float: left;
            text-align: center;
            border: 2px solid #e0ecff;
            background-color: #e0ecff;
            cursor:pointer;
            width: 140px;
            padding: 10px 5px;
        }

        .post-title {
            height: 40px;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .trainee-title{
            text-align: center;
            background-color: #e0ecff;
            padding: 10px 0px;
            color: #783bc7;
        }

        .post-enable-a{
            padding: 10px 0px;
            background-color: #e2e6c3;
            border: 2px solid #e0ecff;
            border-radius: 3px;
            height: 20px;
        }
        .post-enable-a:hover{
            padding: 10px 0px;
            background-color: #e0ecff;
            border: 2px solid #999dff;
            border-radius: 3px;
            height: 20px;
        }


        .post-enable-b{
            padding: 10px 0px;
            background-color: #d6d6f1;
            border: 2px solid #e0ecff;
            border-radius: 3px;
            height: 20px;
        }
        .post-enable-b:hover{
            padding: 10px 0px;
            background-color: #e0ecff;
            border: 2px solid #999dff;
            border-radius: 3px;
            height: 20px;
        }

        .post-unenable{
            padding: 10px 0px;
            background-color: #d6d6d6;
            border: 2px solid #e0ecff;
            border-radius: 3px;
            height: 20px;
            color: red;
        }

        .post-choosed{
            border: 2px solid #343eff;
        }
        .main{
            border: 2px solid #e0ecff;
            margin: 10px 10px;
            border-radius: 5px;
            font-size: 14px;
        }


        .btn {
            float: left;
            text-align: center;
            width: 80px;
            padding: 10px 5px;
            border-radius: 5px;
            margin: 5px 5px;
            border: 1px solid #999dff;
            cursor:pointer;
            font-size: 14px;
            background-color: #e0ecff;
        }

    </style>
</head>
<body>
<div>
    <table class="formTable">

        <tr>
            <td>排班群組：</td>
            <td>
                <input id="arrangeGroupId" name="arrangeGroupId" class="easyui-combogrid"
                       data-options="width: 200,required:true" />
            </td>
            <td>排班日期：</td>
            <td>
                <input id="arrangeDate" type="text" name="arrangeDate" class="Wdate easyui-validatebox" datefmt="yyyy-MM-dd"
                       data-options="required:true,prompt: '排班日期'" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',onpicked:function(){initData();}})" style="width:150px;"/>

            </td>
            <td>
                <label style="background-color: #e2e6c3;padding: 5px 30px;margin: 5px 10px;"></label><span>白班</span>
            </td>
            <td>
                <label style="background-color: #d6d6f1;padding: 5px 30px;margin: 5px 10px;"></label><span>夜班</span>
            </td>
            <td>
                <label style="background-color: #d6d6d6;padding: 5px 30px;margin: 5px 10px;"></label><span>失效</span>
            </td>
        </tr>
    </table>
    <div class="main">

        <div class="trainee-title">各崗位排班明細</div>
        <div id="traineeArr" >
        </div>
        <div class="trainee-title">可排人員</div>
        <div id="no_traineeArr" >
        </div>
        <div id="msg" style="color: red;text-align: -webkit-center;margin-top: 10px;">
        </div>
        <div id="btns" style="text-align: -webkit-center;margin-top: 10px;">
            <div style="width: 310px;">
            <shiro:hasPermission name="basics:bsarrangepostperson:add">
                <div id="save" class="btn" onclick="save()">保存</div>
            </shiro:hasPermission>
            <shiro:hasPermission name="basics:bsarrangepostperson:add">
                <div id="saveAndContinue" class=" btn" onclick="saveAndContinue()">保存並繼續</div>
            </shiro:hasPermission>
                <div id="cancel" class=" btn" onclick="cancel()">取消</div>
                <div style="clear: both"></div>
            </div>
        </div>
    </div>



</div>
<script src='${ctx}/static/js/basics/bsarrangepostpersonedit.js?"+Math.random()"'></script>

</body>
</html>