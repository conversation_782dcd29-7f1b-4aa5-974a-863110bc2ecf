var dg;
var d;
var _legalPersonObj=arr2obj(_legalPersonDicts,"value","label");

$(function () {
    var now = new Date();
    var startDate = new Date(now.getFullYear(), now.getMonth(), 1);
    var endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() > 7 ? now.getDate() : 7);
    $('#filter_startDate').datebox('setValue', startDate.format('yyyy-MM-dd'));
    $('#filter_endDate').datebox('setValue', endDate.format('yyyy-MM-dd'));

    $("#filter_buNam").combobox({
        data: distinctArrayByProperty(_bsDptList, 'dptQun'),
        valueField: "dptQun",
        textField: "dptQun",
        editable: false,
        panelHeight: 'auto'
    });

    //绑定法人下拉框
    $("#filter_legalId").combobox({
        data: _legalPersonDicts,
        valueField: "value",
        textField: "label",
        editable: false,
        panelHeight: 'auto'
    });

    dg = $('#dg').datagrid({
        method: "get",
        url: ctx + '/bsservicedaycost/list',
        fit: true,
        fitColumns: false,
        border: false,
        queryParams:$("#searchFrom").serializeObject(),
        idField: 'id',
        striped: true,
        pagination: true,
        rownumbers: true,
        pageNumber: 1,
        pageSize: 20,
        pageList: [10, 20, 30, 40, 50],
        singleSelect: true,
        frozenColumns:[[
            {field: 'id', title: 'PKID', sortable: false, width: 100, hidden: true}
            ,
            {field: 'dptQun', title: '事業群', sortable: false, width: 80,halign: 'center',
                formatter:function (value, row, index) {
                    return getCostColumn(row,'dptQun');
                }}
            ,
            {field: 'legal', title: '法人', sortable: false, width: 80,halign: 'center',
                formatter:function (value, row, index) {
                    return _legalPersonObj[getCostColumn(row,'legal')];
                }}
            ,
            {field: 'dptBu', title: '部門', sortable: false, width: 80,halign: 'center',
                formatter:function (value, row, index) {
                    return getCostColumn(row,'dptBu');
                }}
            ,
            {field: 'costId', title: '費用代碼', sortable: false, width: 80,halign: 'center',
                formatter:function (value, row, index) {
                    return getCostColumn(row,'costId');
                }}
            ,
            {field: 'postName', title: '崗位', sortable: false, width: 80,halign: 'center',
                formatter:function (value, row, index) {
                    return getCostColumn(row,'postName');
                }}
        ]],
        columns: getDataGridColumns(),
        toolbar: '#tb',
        loadFilter: function (data) {
            var len = data.rows.length;
            var beginDate = new Date($('#filter_startDate').datebox('getValue').replaceAll('-', '/'));
            var endDate = new Date($('#filter_endDate').datebox('getValue').replaceAll('-', '/'));
            for (var i=0;i<len;i++) {
                var curDate = beginDate;
                while (curDate <= endDate) {
                    var date = curDate.format('yyyyMMdd');
                    if (data.rows[i].days[date] == undefined) {
                        data.rows[i][date] = "0,0,"+date;
                    } else {
                        data.rows[i][date] = data.rows[i].days[date]+","+date;
                    }
                    curDate = new Date(curDate.getTime() + 24*60*60*1000);
                }
                data.rows[i]["perNuAB"] = data.rows[i].days["perNuAB"];
            }
            return data;
        }
    });
});

// 动态生成列表列
var getDataGridColumns = function () {
    var columns1 = new Array();
    columns1.push({field: 'tbShiftNo', title: '班別', sortable: false, width: 40, rowspan: 2,halign: 'center',
        formatter:function (value, row, index) {
            var day='<div class="grid-colunm grid-colunm-border">白班</div>';
            var night='<div class="grid-colunm"> 夜班</div>';
            return day+night;
    }});
    var beginDate = new Date($('#filter_startDate').datebox('getValue').replaceAll('-', '/'));
    var endDate = new Date($('#filter_endDate').datebox('getValue').replaceAll('-', '/'));
    var dayCnt = (endDate.getTime()-beginDate.getTime())/1000/24/60/60 + 1;
    columns1.push({field: '', title: '上班人員信息', sortable: false, halign: 'center', width: 100, colspan: dayCnt});
    columns1.push({field: 'perNuAB', title: '接崗人力', sortable: false, width: 80, rowspan: 2,halign: 'center',
        formatter:function (value, row, index) {
            return getDayTbColumn(value, index);
        }});
    columns1.push({field: 'pernu', title: '接岗总人力', sortable: false, width: 100, rowspan: 2,halign: 'center',align: 'right',
        formatter:function (value, row, index) {
            return getCostColumn(row,'pernu');
        }});
    columns1.push({field: 'normalCost', title: '正常上班費用</br>（RMB/元）', sortable: false, width: 100, rowspan: 2,halign: 'center',align: 'right',
        formatter:function (value, row, index) {
            return getCostColumn(row,'normalCost');
        }});
    columns1.push({field: 'trainingCost', title: '培訓費用</br>（RMB/元）', sortable: false, width: 100, rowspan: 2,halign: 'center',align: 'right',
        formatter:function (value, row, index) {
            var res="<a href='javascript:void(0);' onclick='openTrainingModule("+index+")'>"+getCostColumn(row,'trainingCost')+"</a>";
            return res;
        }});
    columns1.push({field: 'regularCost', title: '常規費用</br>（RMB/元）', sortable: false, width: 100, rowspan: 2,halign: 'center',align: 'right',
        formatter:function (value, row, index) {
            return getCostColumn(row,'regularCost');
        }});
    columns1.push({field: 'overtimeHours', title: '加班時數</br>（小時）', sortable: false, width: 100, rowspan: 2,halign: 'center',align: 'right',
        formatter:function (value, row, index) {
            return getCostColumn(row,'overtimeHours');
        }});
    columns1.push({field: 'overtimeCost', title: '加班費用</br>（RMB/元）', sortable: false, width: 100, rowspan: 2,halign: 'center',align: 'right',
        formatter:function (value, row, index) {
            return getCostColumn(row,'overtimeCost');
        }});
    columns1.push({field: 'totalCost', title: '總費用</br>（RMB/元）', sortable: false, width: 100, rowspan: 2,halign: 'center',align: 'right',
        formatter:function (value, row, index) {
            return getCostColumn(row,'totalCost');
        }});

    var columns2 = new Array();
    var curDate = beginDate;
    while (curDate <= endDate) {
        var date = curDate.format('yyyyMMdd');
        columns2.push({field: date, title: ''+curDate.getDate(), sortable: false, width: 25, halign: 'center',
            formatter:function (value, row, index) {
            return getDayTbColumn(value, index);
        }});
        curDate = new Date(curDate.getTime() + 24*60*60*1000);
    }
    return [columns1, columns2];
}

var getCostColumn=function (row,attr) {
    return row.costEntity[attr]?row.costEntity[attr]:0;
}

var getDayTbColumn = function (value, idx) {
    var array = value.split(",");
    var dayVal = array[0];
    var nightVal = array[1];
    var date = array.length == 3 ? array[2] : undefined;
    var dayDiv;
    if(dayVal>0){
        if (date != undefined) {
            dayDiv='<div class="grid-colunm grid-colunm-border" onclick="reloadModel('+idx+','+date+',0)">'+dayVal+'</div>';
        } else {
            dayDiv = '<div class="grid-colunm grid-colunm-border">' + dayVal + '</div>';
        }
    }else {
        dayDiv='<div class="grid-colunm grid-colunm-border" >'+dayVal+'</div>';
    }
    var nightDiv;
    if(nightVal>0){
        if (date != undefined) {
            nightDiv='<div class="grid-colunm" onclick="reloadModel('+idx+','+date+',1)">'+nightVal+'</div>';
        } else {
            nightDiv='<div class="grid-colunm">'+nightVal+'</div>';
        }
    }else {
        nightDiv='<div class="grid-colunm">'+nightVal+'</div>';
    }
    return dayDiv+nightDiv;
}

function openTrainingModule(idx) {
    debugger;
    var row=dg.datagrid("getRows")[idx];
    var postRecno=row.costEntity.recno;
    var queryParams = dg.datagrid('options').queryParams;
    var startDate = queryParams.filter_GED_workDate;
    var endDate = queryParams.filter_LED_workDate;
    window.parent.mainpage.mainTabs.addModule("培訓費用明細", ctx + '/bstrainingdaycost/costDetail?startDate=' + startDate + '&endDate=' + endDate + '&postRecno=' + postRecno, 'icon-hamburg-basket');
}

//數組轉對象
function arr2obj(arr,key,value) {
    var obj={};
    for (var i=0 ;i< arr.length ;i++) {
        var o=arr[i];
        obj[o[key]]=o[value];
    }
    return obj;
}

function reloadModel(idx,date,type) {
    var row=dg.datagrid("getRows")[idx];
    var params={
        recno:row.costEntity.recno,
        arrangeDate:date,
        shiftNo:type==1?'B':'A'
    }
    $.ajax({
        type: 'post',
        url: ctx + "/bsservicedaycost/listArrangeTrainee",
        beforeSend: ajaxLoading,
        data: params,
        success: function (data) {
            ajaxLoadEnd();
            reloadModelView(data);
            $('#modal').window('open');
        },
        error: function (e) {
            ajaxLoadEnd();
            parent.$.messager.show({title: "提示", msg: "操作失敗！", position: "bottomRight"});
        }
    });

}

function closeModel() {
    $('#modal').window('close');
}

function reloadModelView(data) {
    $("#personArr").empty();
    if(data){
        $("#title").empty();
        $("#title").text(data[0].arrangeDate.substr(0,10)+'  '+data[0].postName+ '  '+(data[0].shiftNo=='A'?'白班':'晚班') );
        for(var i=0;i<data.length;i++){
            if (data[i].resign) {
                $("#personArr").append("<div class='resign-trainee'>"+data[i].empNo+"/"+data[i].empName+"</div> ");
            } else {
                $("#personArr").append("<div class='trainee'>" + data[i].empNo + "/" + data[i].empName + "</div> ");
            }
        }
    }
}

//创建查询对象并查询
function cx() {
    var beginDate = new Date($('#filter_startDate').datebox('getValue').replaceAll('-', '/'));
    var endDate = new Date($('#filter_endDate').datebox('getValue').replaceAll('-', '/'));
    if (beginDate > endDate) {
        $.messager.alert("溫馨提示", "開始日期不能大於結束日期", "info");
        return;
    }
    var days = parseInt(endDate - beginDate) / 1000 / 60 / 60 / 24;
    if (days > 60) {
        $.messager.alert("溫馨提示", "查詢時段不能長於60天", "info");
        return;
    }
    if (days < 6) {
        $.messager.alert("溫馨提示", "查詢時段不能短於7天", "info");
        return;
    }
    var obj = $("#searchFrom").serializeObject();
    dg.datagrid({
        columns: getDataGridColumns(),
        queryParams: obj
    });
    // dg.datagrid('load', obj);
}

//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("reset");
    var now = new Date();
    var startDate = new Date(now.getFullYear(), now.getMonth(), 1);
    var endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() > 7 ? now.getDate() : 7);
    $('#filter_startDate').datebox('setValue', startDate.format('yyyy-MM-dd'));
    $('#filter_endDate').datebox('setValue', endDate.format('yyyy-MM-dd'));
}

// 報表數據生成
function generate() {
    var date = $('#gen_date').my97('getValue');
    if (date === '') {
        $.messager.alert("溫馨提示", "請選擇生成日期", "info");
        return;
    }
    $.ajax({
        url: ctx+'/bsservicedaycost/generate',
        type: 'POST',
        beforeSend: ajaxLoading,
        data: { date: date },
        success: function (response) {
            ajaxLoadEnd();
            if (response.code === 1) {
                // 生成成功
                $.messager.alert("溫馨提示", response.msg, "info");
            } else {
                // 生成失敗
                $.messager.alert("溫馨提示", response.msg, "info");
            }
            cx();
        }
    });
}

//导出excel
function exportExcel() {
    var form = document.getElementById("searchFrom");
    searchFrom.action = ctx + '/bsservicedaycost/exportExcel';
    form.submit();
}

// 去重
function distinctArrayByProperty(array, property) {
    var propArray = new Array();
    var resultArray = new Array();
    $.each(array, function (idx, item) {
        if (propArray.indexOf(item[property]) == -1) {
            propArray.push(item[property]);
            resultArray.push(item);
        }
    });
    return resultArray;
}

// 查看匯總
function showSummary() {
    _qun = $("#filter_buNam").combobox("getValue");
    _legal = $("#filter_legalId").combobox("getValue");
    _securityCom = $("#filter_securityCom").combobox("getValue");
    _beginDate = $("#filter_startDate").datebox("getValue");
    _endDate = $("#filter_endDate").datebox("getValue");
    d = $("#dlg").dialog({
        title: '匯總信息查看',
        width: 280,
        height: 350,
        href: ctx + '/bsservicedaycost/summary',
        maximizable: true,
        modal: true,
        buttons: [{
            text: '關閉',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}
