package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.entity.BsServiceCostTbEntity;
import com.foxconn.ipebg.basics.dao.BsServiceCostTbDao;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.Dict;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WorkFlowEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.system.service.DictService;

import java.util.Date;
import java.util.List;

/**
 * 警衛服務費用結賬明細主表
 *
 * <AUTHOR>
 * @email <EMAIL> 
 * @date 2019-10-07 13:15:26
 */
@Service
@Transactional(readOnly=true)
public class  BsServiceCostTbService extends BaseBusinessService<BsServiceCostTbEntity, String>{
    @Autowired
    private BsServiceCostTbDao bsServiceCostTbDao;

    @Autowired
    private UserServiceUtil serviceUtil;
    @Autowired
    private DictService dictService;

    @Override
    public HibernateDao<BsServiceCostTbEntity, String> getEntityDao() {
        return bsServiceCostTbDao;
    }

    public BsServiceCostTbEntity findBySerialno(String serialno) {
        return this.bsServiceCostTbDao.findUniqueBy("serialno", serialno);
    }


    public List<BsServiceCostTbEntity> getByScId(String scId) {
        String hsql="from  BsServiceCostTbEntity where  scId= ?0";
        return this.bsServiceCostTbDao.createQuery(hsql,scId).list();
    }



}
