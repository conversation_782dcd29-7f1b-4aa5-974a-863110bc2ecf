var dg;
var d;
$(function(){
    dg=$('#dg').datagrid({
        method: "get",
        url:ctx+'/wfnodeinfo/list',
        fit : true,
        fitColumns : true,
        border : false,
        idField : 'id',
        striped:true,
        pagination:true,
        rownumbers:true,
        pageNumber:1,
        pageSize : 20,
        pageList : [ 10, 20, 30, 40, 50 ],
        singleSelect:true,
        columns:[[
							                    { field: 'id', title: '主鍵',hidden:true},
											                    { field: 'createBy', title: '創建人',sortable:true,width:100}
					,
																                    { field: 'createDate', title: '創建時間',sortable:true,width:100}
					,
																                    { field: 'updateBy', title: '更新者',sortable:true,width:100}
					,
																                    { field: 'updateDate', title: '更新時間',sortable:true,width:100}
					,
																                    { field: 'delFlag', title: '刪除標識',sortable:true,width:100}
					,
																                    { field: 'nodeid', title: '節點編碼',sortable:true,width:100}
					,
																                    { field: 'workflowid', title: '流程編碼',sortable:true,width:100}
					,
																                    { field: 'nodename', title: '節點名稱',sortable:true,width:100}
					,
																                    { field: 'orderby', title: '排序號',sortable:true,width:100}
					,
																                    { field: 'signtype', title: '簽核節點類型 0 普通簽核 1會簽',sortable:true,width:100}
					,
																                    { field: 'required', title: '節點簽核人是否必填 Y 必填 N非必填',sortable:true,width:100}
					,
																                    { field: 'colname', title: '表單表保存簽核主管的字段名稱',sortable:true,width:100}
					,
																                    { field: 'canbatch', title: '該結點是否可以參與批量簽核',sortable:true,width:100}
					,
																                    { field: 'dynfield01', title: '擴展字段',sortable:true,width:100}
					,
																                    { field: 'dynfield02', title: '擴展字段',sortable:true,width:100}
					,
																                    { field: 'dynfield03', title: '擴展字段',sortable:true,width:100}
					,
																                    { field: 'dynfield04', title: '擴展字段',sortable:true,width:100}
					,
																                    { field: 'dynfield05', title: '擴展字段',sortable:true,width:100}
												        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        toolbar:'#tb'
    });
});

//弹窗增加
function add() {
    d=$("#dlg").dialog({
        title: '添加流程節點配置表',
        width: 380,
        height: 380,
        href:ctx+'/wfnodeinfo/create',
        maximizable:true,
        modal:true,
        buttons:[{
            text:'确认',
            handler:function(){
                $("#mainform").submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}

//删除
function del(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data){
        if (data){
	$.ajax({
                type:'get',
                url:ctx+"/wfnodeinfo/delete/"+row.id,
                success: function(data){
                    successTip(data,dg);
                }
            });
        }
    });
}

//弹窗修改
function upd(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    d=$("#dlg").dialog({
        title: '修改流程節點配置表',
        width: 380,
        height: 340,
        href:ctx+'/wfnodeinfo/update/'+row.id,
        maximizable:true,
        modal:true,
        buttons:[{
            text:'修改',
            handler:function(){
                $('#mainform').submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}
//创建查询对象并查询
function cx(){
    var obj=$("#searchFrom").serializeObject();
    dg.datagrid('load',obj);
}
