<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>排班群組匹配合格人員</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>

<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/bsarrangegroupperson/${action}" method="post">
		<table class="formTable">
		    			<tr>
				<td>id：</td>
				<td>
				    					<input type="hidden" name="id" value="${bsArrangeGroupPerson.id}"/>
															<input id="id" name="id" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPerson.id }" />
									</td>
			</tr>
						<tr>
				<td>排班群組編碼：</td>
				<td>
				    										<input id="arrangeGroupId" name="arrangeGroupId" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPerson.arrangeGroupId }" />
									</td>
			</tr>
						<tr>
				<td>排班群組：</td>
				<td>
				    										<input id="arrangeGroupName" name="arrangeGroupName" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPerson.arrangeGroupName }" />
									</td>
			</tr>
						<tr>
				<td>身份證號：</td>
				<td>
				    										<input id="psnId" name="psnId" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPerson.psnId }" />
									</td>
			</tr>
						<tr>
				<td>工號：</td>
				<td>
				    										<input id="empNo" name="empNo" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPerson.empNo }" />
									</td>
			</tr>
						<tr>
				<td>姓名：</td>
				<td>
				    										<input id="empName" name="empName" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPerson.empName }" />
									</td>
			</tr>
						<tr>
				<td>硬卡號：</td>
				<td>
				    										<input id="cardId" name="cardId" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPerson.cardId }" />
									</td>
			</tr>
						<tr>
				<td>公司：</td>
				<td>
				    										<input id="company" name="company" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPerson.company }" />
									</td>
			</tr>
						<tr>
				<td>考試合格日期：</td>
				<td>
				    										<input id="passDate" name="passDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${bsArrangeGroupPerson.passDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>創建人：</td>
				<td>
				    										<input id="createBy" name="createBy" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPerson.createBy }" />
									</td>
			</tr>
						<tr>
				<td>創建時間：</td>
				<td>
				    										<input id="createDate" name="createDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${bsArrangeGroupPerson.createDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>更新人：</td>
				<td>
				    										<input id="updateBy" name="updateBy" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPerson.updateBy }" />
									</td>
			</tr>
						<tr>
				<td>更新時間：</td>
				<td>
				    										<input id="updateDate" name="updateDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${bsArrangeGroupPerson.updateDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>刪除標示：</td>
				<td>
				    										<input id="delFlag" name="delFlag" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPerson.delFlag }" />
									</td>
			</tr>
					</table>
	</form>
  </div>
<script src='${ctx}/static/js/basics/bsarrangegroupperson.js?"+Math.random()"'></script>
<script type="text/javascript">
   //提交表单
$('#mainform').form({
    onSubmit: function(){
        var isValid = $(this).form('validate');
        return isValid;	// 返回false终止表单提交
    },
    success:function(data){ 
        successTip(data,dg,d);
    }
});
</script>
</body>
</html>