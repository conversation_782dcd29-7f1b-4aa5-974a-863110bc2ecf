var dg;
var d;
$(function() {
	dg = $('#dg').datagrid({
		method : "get",
		url : ctx + '/epointaply/list',
		fit : true,
		fitColumns : true,
		border : false,
		idField : 'id',
		striped : true,
		pagination : true,
		rownumbers : true,
		loadFilter : function(data) {
			// 过滤数据
			var value = {
				total : data.total,
				rows : []
			};
			var x = 0;
			for ( var i = 0; i < data.rows.length; i++) {
				// if (data.rows[i].tranModel.eserStat == "W")
				if (data.rows[i].applyStat == "N") 
				{
					data.rows[i].applyStat="駁回"	;
				}
				else if (data.rows[i].applyStat == "E")
				{
						data.rows[i].applyStat="簽核完成"	;
				}
				else
				{
						data.rows[i].applyStat="簽核中"	;
				}
				value.rows[x++] = data.rows[i];
			}
			return value;

		},
		pageNumber : 1,
		pageSize : 20,
		pageList : [ 10, 20, 30, 40, 50 ],
		singleSelect : true,
		columns : [ [ {
			field : 'id',
			title : '主鍵',
			hidden : true
		}, {
			field : 'serialno',
			title : '任務編號',
			sortable : true,
			width : 100,
			formatter: operation
		}, {
			field : 'makerno',
			title : '填單人工號',
			sortable : true,
			width : 100
		}, {
			field : 'makername',
			title : '填單人名稱',
			sortable : true,
			width : 100
		}, {
			field : 'makerip',
			title : '填單人IP',
			sortable : true,
			width : 100
		}, {
			field : 'createtime',
			title : '填單時間',
			sortable : true,
			width : 100
		}, {
			field : 'applyStat',
			title : '申請單狀態',
			sortable : true,
			width : 100
		}, {
			field : 'signEmpNo',
			title : '待審核人工號',
			sortable : true,
			width : 100
		}, {
			field : 'signEmpNam',
			title : '待審核人姓名',
			sortable : true,
			width : 100
		}] ],
		enableHeaderClickMenu : true,
		enableHeaderContextMenu : true,
		enableRowContextMenu : false,
		rowTooltip : true,
		toolbar : '#tb'
	});
	
	
	
	// 初始化去除驗證
	$(":input").removeClass("validatebox-invalid");
	// 批量上傳模板下載
	$("#btnBatchImportTpl").click(batchImportTpl);
	// 添加一筆
	$("#add").click(function(){
		 var len = parseInt($("#buildprojectApplyItemTableIndex").val());
	        var b = $("#buildprojectApplyItemTable tr").length;
	$("#buildprojectApplyItemTable").append("<tr align='center' id=Item"+len+">"
	    +"<td>"+b+"</td>"
	    +"<td><input id='building"+len+"' name='pointItemEntity["+(len-1)+"].building' class='easyui-validatebox' required='true' style='width:150px;;'></td>"
	    +"<td><input id='floor"+len+"' name='pointItemEntity["+(len-1)+"].floor' class='easyui-validatebox' required='true' style='width:150px;;'></td>"

	    +"<td><input id='location"+len+"' name='pointItemEntity["+(len-1)+"].location' class=\"easyui-combobox rayOptionitemclass\" data-options=\"panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadLocation("+len+");}\" style='width:150px;'/></td>"
	    	 
	    +"<td><input id='equipmentNumber"+len+"' name='pointItemEntity["+(len-1)+"].equipmentNumber' class='easyui-validatebox' required='true' style='width:150px;;'></td>"
	    +"<td><input id='installLocation"+len+"' name='pointItemEntity["+(len-1)+"].installLocation' class='easyui-validatebox' required='true' style='width:150px;;'></td>"

	  
	    +"<td><input type='image' src='"+ctx+"/static/images/deleteRow.png' onclick='deltr("+len+");return false;'/></td>"+"</tr>");

	 $.parser.parse($("#Item" + len));
     $("#buildprojectApplyItemTableIndex").val(len + 1);
	$("#building"+len).val('');
	$("#floor"+len).val('');
	$("#location"+len).val('');

	$("#equipmentNumber"+len).val('');
	$("#installLocation"+len).val('');


	$("#batchImport").linkbutton('disable');
	});
});
// *****************************自動加載結束


// 表單進度查詢頁面查詢條件重置
function listSearchReset() {
$("#searchFrom").form("clear");
}
// 刪除一筆
function deltr(index) {
/*
 * debugger; var b=$("#buildprojectApplyItemTable tr").length;
 * $("#Item"+index).remove(); for(var i=index+1,j=index;i<=b;i++,j++){
 * $("#"+i).replaceWith("<tr align='center' id="+(i-1)+">" +"<td>"+(i-1)+"</td>" +"<td><input
 * id='building"+(i-1)+"' name='tQhWfnoequipmentEntity["+(i-2)+"].building'
 * class='easyui-validatebox' required='true' style='width:150px;'></td>" +"<td><input
 * id='floor"+(i-1)+"' name='tQhWfnoequipmentEntity["+(i-2)+"].floor'
 * class='easyui-validatebox' required='true' style='width:150px;'></td>" +"<td><input
 * id='equipmentNumber"+(i-1)+"'
 * name='tQhWfnoequipmentEntity["+(i-2)+"].equipmentNumber'
 * class='easyui-validatebox' required='true' style='width:150px;'></td>" +"<td><input
 * id='location"+(i-1)+"' name='tQhWfnoequipmentEntity["+(i-2)+"].location'
 * class='easyui-my97' datefmt='yyyy-MM-dd' data-options='width:
 * 100,required:true' ></td>" +"<td><input id='installLocation"+(i-1)+"'
 * name='tQhWfnoequipmentEntity["+(i-2)+"].installLocation'
 * class='easyui-validatebox' required='true' style='width:150px;'></td>"
 *  +"<td><input type='image' src='/static/images/deleteRow.png'
 * onclick='deltr("+(i-1)+");return false;'/></td>"+"</tr>"); } if (b == 2) {
 * $("#batchImport").linkbutton('enable'); }
 */	
// debugger;
// if(confirm("確定刪除此筆記錄？")) {
// if ($("#buildprojectApplyItemTable tr").length == 2) {
// alert("至少保留一筆！")
// return;
// }
// }
$("#Item" + index).remove();
var len=parseInt($("#buildprojectApplyItemTableIndex").val());
$("#buildprojectApplyItemTableIndex").val(len-1);
var b = $("#buildprojectApplyItemTable tr").length;
for (var i = 1 ; i <= b; i++) {

$("#buildprojectApplyItemTable").find("tr").eq(i).attr("id","Item"+(i));
$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(0).html(i);
$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(1).find("input[id^='building']").attr("id","building"+i);
$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(2).find("input[id^='floor']").attr("id","floor"+i);
$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(3).find("input[id^='location']").attr("id","location"+i);
$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(4).find("input[id^='equipmentNumber']").attr("id","equipmentNumber"+i);
$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(5).find("input[id^='installLocation']").attr("id","installLocation"+i);

$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(3).find("input[id^='location']").attr("comboname","pointItemEntity["+(i-1) +"].location");
$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(3).find("input[id^='location']").attr("data-options","panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadLocation("+i+");}");
// $("#zaItemTable").find("tr:not(#addbutton_za)").eq(i).find("td").eq(1).find("input[id^='za_goodsname']").attr("data-options","panelHeight:'auto',valueField:'value',
// textField:'label',editable:false,onBeforeLoad:function(){loadzaGoodsname("+i+");},onSelect:function(){onchangezaGoodsname("+i+");}");

$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(1).find("input[name^='pointItemEntity']").attr("name","pointItemEntity["+(i-1) +"].building");
$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(2).find("input[name^='pointItemEntity']").attr("name","pointItemEntity["+(i-1) +"].floor");
$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(3).find("input[name^='pointItemEntity']").attr("name","pointItemEntity["+(i-1)+"].location");
$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(4).find("input[name^='pointItemEntity']").attr("name","pointItemEntity["+(i-1) +"].equipmentNumber");
$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(5).find("input[name^='pointItemEntity']").attr("name","pointItemEntity["+(i-1) +"].installLocation");

$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(6).find("input[type='image']").attr("onclick","deltr("+(i) +");return false;");


}
if (b == 2) {
    $("#batchImport").linkbutton('enable');
}
}

function operation(value, row, index) {
return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('詳情',ctx+'/epointaply/view/" + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
};
// 弹窗增加
function add() {
	d = $("#dlg").dialog({
		title : '添加飲用水點位申請主表',
		width : 380,
		height : 380,
		href : ctx + '/epointaply/create',
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '确认',
			handler : function() {
				$("#mainform").submit();
			}
		}, {
			text : '取消',
			handler : function() {
				d.panel('close');
			}
		} ]
	});
}

// 删除
function del() {
	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data) {
		if (data) {
			$.ajax({
				type : 'get',
				url : ctx + "/epointaply/delete/" + row.id,
				success : function(data) {
					successTip(data, dg);
				}
			});
		}
	});
}

// 弹窗修改
function upd() {
	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	d = $("#dlg").dialog({
		title : '修改飲用水點位申請主表',
		width : 380,
		height : 340,
		href : ctx + '/epointaply/update/' + row.id,
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '修改',
			handler : function() {
				$('#mainform').submit();
			}
		}, {
			text : '取消',
			handler : function() {
				d.panel('close');
			}
		} ]
	});
}

// 创建查询对象并查询
function cx() {
	var obj = $("#searchFrom").serializeObject();
	dg.datagrid('load', obj);
}
// 表單進度查詢頁面查詢條件重置
function listSearchReset() {
	$("#searchFrom").form("reset");
}
// 导出excel
function exportExcel() {
	var form = document.getElementById("searchFrom");
	searchFrom.action = ctx + '/epointaply/exportExcel';
	form.submit();
}
function saveInfo(flag) {
    if (flag == '2') {
        var isValid = $("#mainform").form('validate');
        if (!isValid) {
            return;
        }
// if(($("#zchargeno").val()==null||$("#zchargeno").val()=='')&&($("#zcchargeno").val()==null||$("#zcchargeno").val()=='')){
// alert("至少簽核至處級主管及以上主管");
// return;
// }
    }
    $.ajax({
        url: ctx+'/epointaply/create/' + flag,
        type: 'POST',
        beforeSend: ajaxLoading,
        // dataType: 'json',
        data: $('#mainform').serialize(),
        success: function (data) {
            ajaxLoadEnd();
            successTip(data, dg, d);
            window.parent.mainpage.mainTabs.closeCurrentTab();
        }
    });
}
function auditInfo(flag) {
    if (flag == 'Y') {
       
// if(($("#zchargeno").val()==null||$("#zchargeno").val()=='')&&($("#zcchargeno").val()==null||$("#zcchargeno").val()=='')){
// alert("至少簽核至處級主管及以上主管");
// return;
// }
    }
    else if(flag == 'N') {
    	 var isValid = $("#mainform").form('validate');
         if (!isValid) {
             return;
         }	
    }
    $.ajax({
        url: ctx+'/epointaply/auditSave/' + flag,
        type: 'POST',
        beforeSend: ajaxLoading,
        // dataType: 'json',
        data: $('#mainform').serialize(),
        success: function (data) {
            ajaxLoadEnd();
            successTip(data, dg, d);
            window.parent.mainpage.mainTabs.closeCurrentTab();
        }
    });
}
// validatebox驗證擴展
$.extend($.fn.validatebox.defaults.rules, {
    endTime: {
        validator: function (value, param) {
            var startDate = $('#buildcyclebegindate').datebox('getValue');
            var startTmp = new Date(startDate.replace(/-/g, "/"));
            var endTmp = new Date(value.replace(/-/g, "/"));
            return startTmp <= endTmp;
        },
        message: '结束时间要大于开始时间！'
    }
});

// 驗證申請人聯繫電話
function valdApplyTel(obj){
    var phoneL=$.trim($("#dealtel").val()).split("").length;
    var tel = obj.value;
    if(tel!=""){
        var temp = tel;
        var regex  = /^[0-9]{3}\+[0-9]{5}$/;
        var patt1=new RegExp(regex);
        var result = patt1.test(tel);
        if(!result){
            alert("分機格式有誤!");
            obj.value="";
            obj.focus();
            return;
        }
    }
}
// 驗證手機號碼
function checkMobile(obj) {
    var
        re = /^1\d{10}$/

    if (obj.value!="")
    {
        if (re.test(obj.value)) {
            // alert("正确");
        } else {
            alert("聯繫方式格式有誤！");
            obj.value="";
            obj.focus();
            return;
        }

    }
}

// 驗證郵箱
function valdEmail(obj){
    var email = obj.value;
    var temp = email;
    if(email=="")return;
    var regex = /\w+([-/+.]\w+)*@(mail.foxconn.com|foxconn.com|fii-foxconn.com)$/;
    var patt1=new RegExp(regex);
    var result = patt1.test(email);
    if(!result){
        alert("郵箱的格式不正確!");
        obj.value="";
        // obj.focus();
        obj.value =temp;
        return;
    };
}
// 驗證數字
function valdMoney(obj){
    var money = obj.value;
    var temp = money;
    if(money=="")return;
    var regex = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
    var patt1=new RegExp(regex);
    var result = patt1.test(money);
    if(!result){
        alert("格式不正確!");
        obj.value="";
        // obj.focus();
        obj.value =temp;
        return;
    };
}
// 根據工號查出當前申請人的信息
function queryUserInfo() {
    var empno = $.trim($("#dealno").val().toUpperCase());
    if (empno != null && empno != "") {
        $.ajax({
            url: ctx+'/system/user/getUserInfo/',
            type: 'POST',
            beforeSend: ajaxLoading,
            // dataType: 'json',
            data: {empno: empno},
            success: function (data) {
                ajaxLoadEnd();
                if (!data) {
                    $.messager.alert("溫馨提示", "工號不存在", "error");
                    $('#dealno').val('');
                    $('#dealname').val('');
                    $('#dptQun').val('');
                    $('#dptChu').val('');
                    $('#dptBu').val('');
                    $('#dptKe').val('');
                    $('#dealemail').val('');
                } else {
                    $('#dealname').val(data.name);
                    $('#dptQun').val(data.dptQun);
                    $('#dptChu').val(data.dptChu);
                    $('#dptBu').val(data.dptBu);
                    $('#dptKe').val(data.dptKe);
//                    $('#dealfactoryid').combobox('setValue', data.factoryid);
                    $('#dealemail').val(data.email);
                }
            }
        });
    }
}
// 附件上傳
function uploadFile() {
    var attachIds = $.trim($("#attachids").val());
    var attachIdsL = attachIds.split(",").length;
    if (attachIdsL >= 6) {
        alert("上傳附件個數不能超過五個！");
        return;
    }
    // 创建
    var form_data = new FormData();
    // 获取文件
// var file = document.getElementById('fileToUpload').files[0];
    var file_data = $("#attachidsUpload").prop("files")[0];
    form_data.append("file", file_data);
    form_data.append("path", "test");
    // 把所以表单信息
    $.ajax({
        type: "POST",
        url: ctx+"/admin /upload",
        beforeSend: ajaxLoading,
// dataType : "json",
        processData: false,  // 注意：让jQuery不要处理数据
        contentType: false,  // 注意：让jQuery不要设置contentType
        data: form_data
    }).success(function (msg) {
        ajaxLoadEnd();
        $("#dowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href='+ctx+'"/admin /download/' + msg.id + '">' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delAtt(\'' + msg.id + '\')"></div></div>');
        $("#attachids").val($("#attachids").val() + msg.id + ",");
        // $("#attachids").val(msg.id);
    }).fail(function (msg) {
    });
}

function delAtt(fileid) {
    $("#" + fileid).remove();
    var kk = $("#attachids").val();
    kk = kk.replace(fileid + ",", "");
    $("#attachids").val(kk);
    $.post(ctx+'/admin /delete/' + fileid, {}, function (data) {
        console.log(data);
        successTip(data);
    });
}

function reUploadFile() {
    var attachIds = $.trim($("#reattachids").val());
    var attachIdsL = attachIds.split(",").length;
    if (attachIdsL >= 6) {
        alert("上傳附件個數不能超過五個！");
        return;
    }
    // 创建
    var form_data = new FormData();
    // 获取文件
// var file = document.getElementById('fileToUpload').files[0];
    var file_data = $("#attachidsUpload").prop("files")[0];
    form_data.append("file", file_data);
    form_data.append("path", "test");
    // 把所以表单信息
    $.ajax({
        type: "POST",
        url: ctx+"/admin /upload",
        beforeSend: ajaxLoading,
// dataType : "json",
        processData: false,  // 注意：让jQuery不要处理数据
        contentType: false,  // 注意：让jQuery不要设置contentType
        data: form_data
    }).success(function (msg) {
        ajaxLoadEnd();
        $("#reDowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href='+ctx+'"/admin /download/' + msg.id + '">' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delReAtt(\'' + msg.id + '\')"></div></div>');
        $("#reattachids").val($("#reattachids").val() + msg.id + ",");
        // $("#attachids").val(msg.id);
    }).fail(function (msg) {
    });
}

function delReAtt(fileid) {
    $("#" + fileid).remove();
    var kk = $("#reattachids").val();
    kk = kk.replace(fileid + ",", "");
    $("#reattachids").val(kk);
    $.post(ctx+'/admin /delete/' + fileid, {}, function (data) {
        successTip(data);
    });
}
// 導入
function btnUploadExcel() {
    // deleteAllRow("buildprojectApplyItemTable");
    var table = document.getElementById("buildprojectApplyItemTable");
    var len = table.rows.length;
    if (len>1)
    {
        for (var i = 0; i < len-1 ; i++) {
                table.deleteRow(len-i-1);
        }
    }


    var file_data = $("#batchFile").prop("files")[0];
    if (file_data.name == "") {
        $.messager.alert('系統提示', '請選擇文件，再進行導入！', 'info');
        return;
    }
    var suffix = /\.[^\.]+$/.exec(file_data.name);
    var isExcel2003 = "";
    if (suffix == ".xls") {
        isExcel2003 = "true";
    } else if (suffix == ".xlsx") {
        isExcel2003 = "false";
    } else {
        $.messager.alert('系統提示', '導入文件格式不正確！', 'info');
        return;
    }
    // 创建
    var form_data = new FormData();
    // 获取文件
    form_data.append("file", file_data);
    form_data.append("isExcel2003", isExcel2003);

    // 把所以表单信息
    $.ajax({
        type: "POST",
        url: ctx+"/epointaply/upload",
// dataType : "json",
        processData: false,  // 注意：让jQuery不要处理数据
        contentType: false,  // 注意：让jQuery不要设置contentType
        data: form_data
    }).success(function (msg) {
        // console.log(msg)
        if (msg == 'success') {
            $("#labelListAddResult").html("導入成功");


            document.getElementById("add").setAttribute("disabled", true);
                // 加載導入成功的數據
            $.ajax({
                type: "get",
                url:ctx+'/epointaply/importView',
                cache: false,
                dataType : "json",
                success:function(data){
                    var item;
                    $.each(data,function(i,result){

                        item =
                            "<tr><td>"+(i+1)+"</td>"
                             +"<td>"+result['building']+"</td>"
                             +"<td>"+result['floor']+"</td>"
                             +"<td>"+result['location']+"</td>"                            
                             +"<td>"+result['equipmentNumber']+"</td>"
                             +"<td>"+result['installLocation']+"</td>"
// +"<td>"+result['rayspecies']+"</td>"
// +"<td>"+crtTimeFtt(result['stopdate'])+"</td>"
// +"<td>"+crtTimeFtt(result['reusedate'])+"</td>"
// +"<td>"+result['reusereason']+"</td>"
// +"<td>"+result['personofsafe']+"</td>"
// +"<td>"+result['telephoneofsafe']+"</td>"
                             +"<td>  </td>"
                            +"</tr>";
                        $('#buildprojectApplyItemTable').append(item);
                    });
                }
            });




            $("#downloadError").hide();
        }else{
            if(msg =='2') {
                $("#labelListAddResult").html("導入失敗, ");
                $("#downloadError").show();
            }else{
                $("#labelListAddResult").html(msg);
            }
        }
    }).fail(function (msg) {
        $("#labelListAddResult").html("導入失敗");
    });
}

// 查看錯誤信息
function downloadError(){
    // document.location.href = '/epointaply/downLoad/errorExcel';
}

// 批量導入模板下載
function batchImportTpl(){
    document.location.href = ctx+'/epointaply/downLoad/batchImportTpl';
}

function openBatchImportWin(){
    var optionWin = $("#optionWin").window();

    optionWin.window({
        center : true
    });
    // 顯示於畫面中
    optionWin.slideDown();
    // 清空optionWin頁面中所有元件的值
    /*
	 * $("#pointForm .easyui-combobox").each(function(){
	 * $(this).combobox("clear"); });
	 */
    $("#batchImportForm").form("clear");
    $("#labelListAddResult").html("");
    $("#downloadError").hide();
    // optionWin.find("input").val("");
    optionWin.window("open");
}
// 點擊添加一筆射線裝置加載設備類別
function loadLocation(flag) {
    $.ajax({
        url: ctx+"/system/dict/getDictByType/water_location",
        type: "GET",
        success: function (data) {
            var themecombo2 = [{'label': '請選擇', 'value': ''}];
            for (var i = 0; i < data.length; i++) {
                themecombo2.push({"label": data[i].label, "value": data[i].value});
            }
            
            if (null != flag) {
                // 重新載入所有明細
                $("#location" + flag).combobox("loadData", themecombo2);
                // $(".rayOptionitemclass").combobox("loadData", themecombo2);
            } else {

                var len = $("#buildprojectApplyItemTable tr").length - 1;
                // for (var i = 1; i <= len; i++) {
                $("#location" + len).combobox("loadData", themecombo2);
                // }
            }
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });

}

// 刪除table處表頭之外的所有行
function deleteAllRow(tableName){
	 var table = document.getElementById(tableName);
	    var len = table.rows.length;
	    if (len>1)
	    {
	        for (var i = 0; i < len-1 ; i++) {
	                table.deleteRow(len-i-1);
	        }
	    }	
}
//刪除所有行包括表頭
function deleteAllRowHead(tableName){
var table = document.getElementById(tableName);
var len = table.rows.length;
for(var i = 0;i < len-1;i++){
table.deleteRow(i+1);
}
}
// 重新啟用
function reusecheck(){

	deleteAllRow("buildprojectApplyItemTable");  
	 $("#add").attr("disabled", true);
	 selectTestPoint();

}
// 新申請
function newcheck(){
	
	 $("#add").attr("disabled", false);
	 $("#reattachids").val("");
 	deleteAllRow("buildprojectApplyItemTable"); 
 	$('#buildprojectApplyItemTable1').datagrid({
    	data:[],
		columns : [ [{
			field : 'id',
			title : '主鍵',
			hidden : true
		},
	
		{
			field : 'pointno',
			title : '系統編碼',
			sortable : true,
			width : 100
		}, {
			field : 'building',
			title : '樓棟',
			sortable : true,
			width : 100
		}, {
			field : 'floor',
			title : '樓層',
			sortable : true,
			width : 100
		}, {
			field : 'location',
			title : '方位',
			sortable : true,
			width : 100
		}, {
			field : 'equipmentNumber',
			title : '設備序號',
			sortable : true,
			width : 100
		}, {
			field : 'installLocation',
			title : '安裝位置',
			sortable : true,
			width : 100
		}] ],
		rowTooltip : true,
		toolbar : '#tb'
			
    });
	// $("#add").button({ disabled:true });

}
// 格式化日期
function crtTimeFtt(val) {
    if (val != null) {
        var date = new Date(val);
        return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
    }
}
// 選擇檢測項目

function selectTestPoint() {    
    d = $("#win").dialog({
        title: '重新啟用點位選擇',
        width: 1000,
        height: 800,
        async: false,
        href: ctx + '/pointitem/reusePointList',
        maximizable: false,
        modal: true,
        onOpen: function () {
// $('#dutyId').val("55");
// $('#factoryId').val("66");
        },
        buttons: [{
            text: '確定',
            handler: function () {              
// $("#" + colNo).val("");
// $("#" + colName).val("");
            	
                var rows = $('#reusePointList').datagrid('getSelections');
                // var dg1;
                $('#buildprojectApplyItemTable1').datagrid({
                	data:rows,
// fit : true,
// fitColumns : true,
// border : false,
// idField : 'id',
// striped : true,
// pagination : true,
// rownumbers : true,
					columns : [ [{
						field : 'id',
						title : '主鍵',
						hidden : true
					},
				
					{
						field : 'pointno',
						title : '系統編碼',
						sortable : true,
						width : 150
					}, {
						field : 'building',
						title : '樓棟',
						sortable : true,
						width : 150
					}, {
						field : 'floor',
						title : '樓層',
						sortable : true,
						width : 150
					}, {
						field : 'location',
						title : '方位',
						sortable : true,
						width : 150
					}, {
						field : 'equipmentNumber',
						title : '設備序號',
						sortable : true,
						width : 150
					}, {
						field : 'installLocation',
						title : '安裝位置',
						sortable : true,
						width : 150
					}] ],
// enableHeaderClickMenu : true,
// enableHeaderContextMenu : true,
// enableRowContextMenu : false,
					rowTooltip : true,
					toolbar : '#tb'
						
                });


                var ids = "";
                for (var i = 0, j = rows.length; i < j; i++) {
                    // ids.push(rows[i].empno + ',' + rows[i].username);
                	ids=ids+rows[i].pointno+',';
                }
                ids=ids.substring(0,ids.length-1);
                      	
            	$("#reattachids").val(ids);

                d.panel('close');
            }
        }, {
            text: '置空檢測點位',
            handler: function () {
          	
            	$("#reattachids").val("");
            	deleteAllRow("buildprojectApplyItemTable"); 
            	$('#buildprojectApplyItemTable1').datagrid({
                	data:[],
					columns : [ [{
						field : 'id',
						title : '主鍵',
						hidden : true
					},
				
					{
						field : 'pointno',
						title : '系統編碼',
						sortable : true,
						width : 100
					}, {
						field : 'building',
						title : '樓棟',
						sortable : true,
						width : 100
					}, {
						field : 'floor',
						title : '樓層',
						sortable : true,
						width : 100
					}, {
						field : 'location',
						title : '方位',
						sortable : true,
						width : 100
					}, {
						field : 'equipmentNumber',
						title : '設備序號',
						sortable : true,
						width : 100
					}, {
						field : 'installLocation',
						title : '安裝位置',
						sortable : true,
						width : 100
					}] ],
					rowTooltip : true,
					toolbar : '#tb'
						
                });
                d.panel('close');
            }
        }]
    });
}
