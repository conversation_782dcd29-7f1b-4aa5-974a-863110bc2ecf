package com.foxconn.ipebg.system.web;

import com.alibaba.druid.pool.DruidDataSource;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Controller
@RequestMapping("system/status")
public class StatusController {
    @Resource(name="dataSource")
    private DruidDataSource dataSource;

    @RequestMapping("getConnPoolInfo")
    public void getConnPoolInfo(HttpServletRequest request,HttpServletResponse response) throws Exception{
        response.setContentType("text/xml");
        response.setCharacterEncoding("UTF-8");
        StringBuffer xmlDoc = new StringBuffer();
        try {
            xmlDoc.append("<?xml version=\"1.0\" encoding=\"UTF-8\" ?>");
            xmlDoc.append("<connPool>");
            xmlDoc.append("<dataSource>");
            //xmlDoc.append("<applyName>"+PropertyParse.readData("SERVER-NODE")+"</applyName>");
            xmlDoc.append("<dataSourceName>sgas-test</dataSourceName>");
            xmlDoc.append("<poolSize>"+dataSource.getMaxActive()+"</poolSize>");//连接池最大数
            xmlDoc.append("<poolUsed>"+(dataSource.getActiveCount()+dataSource.getPoolingCount())+"</poolUsed>");//连接池的连接数
            xmlDoc.append("<busy>"+dataSource.getActiveCount()+"</busy>");//连接池繁忙中的连接数
            xmlDoc.append("<idle>"+dataSource.getPoolingCount()+"</idle>");//连接池空闲的连接数
            xmlDoc.append("</dataSource>");
            xmlDoc.append("</connPool>");
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        response.getWriter().write(xmlDoc.toString());
    }
}
