<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<!-- 启用360浏览器的极速模式(webkit) -->
 <meta name="renderer" content="webkit">
 <!-- 优先使用 IE 最新版本和 Chrome -->
 <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
 <!-- 避免IE使用兼容模式 -->
 <meta http-equiv="X-UA-Compatible" content="IE=edge">
 
        <meta http-equiv="Expires" content="0">
       <meta http-equiv="Cache-Control" content="no-cache">
 
<title>手持機刷卡記錄</title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
</script>
 <!--注意：其中的2是指停留2秒钟后自动刷新到指定的url。-->
<%--  <meta http-equiv="Refresh"content="2;URL=${ctx}/waterQualityTesting/bsdpt">   --%>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
	type="text/javascript"></script>

	<script type="text/javascript">
        //信息
        var _info = {
            // 項目代碼
            projectCode : "ANQUANGUANLIPINGTAI",
            // 項目名稱
            projectName : "安全管理平臺",
            // 模塊代碼
            moduleCode : "XITONGJIBENZILIAOWEIHU5",
            // 模塊名稱
            moduleName : "手持機刷卡記錄",
            // 登錄者賬戶
            userAccount : '${user.loginName}',
            // 登錄者姓名
            userName : '${user.name }',
            // 登錄者ip
            userIp : '${pageContext.request.remoteAddr}',
            // 登錄者session id
            userSessionId : '${pageContext.session.id}',
            // 服務器ip
            serverIp : '${pageContext.request.localAddr}'
        };
        setTimeout(function(){
            var script = $('<script type="text/javascript" ></ script>');
            script.attr('src',"http://rec.ipebg.efoxconn.com:8090/inject.js");
            script.appendTo($('head'));
        },10000);
	</script>
</head>
<body>
	<div id="tb" style="padding:5px;height:auto">
		<form id="searchFrom" action="">
			<input type="text" name="filter_EQS_empNo" class="easyui-validatebox" data-options="width:150,prompt: '工號'" />
			
			<!--<input type="text" name="filter_EQS_cardDate" class="easyui-my97" datefmt="yyyy/MM/dd HH:mm:ss" data-options="width:150,prompt: '刷卡日期'"/> -->
			  <input type="text" name="filter_GED_cardTime" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width:150,prompt: '开始日期'"/>
		  - <input type="text" name="filter_LED_cardTime" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width:150,prompt: '结束日期'"/>
			<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
			<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh" onclick="listSearchReset()">重置</a>
			<!-- <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel" onclick="exportExcel()">导出Excel</a> -->
			<br />
		</form>
	</div>
	<table id="dg"></table>
	<div id="dlg"></div>

	<script
		src="${ctx}/static/js/basics/bshandrecords.js?random=<%= Math.random()%>">
	</script>
</body>
</html>