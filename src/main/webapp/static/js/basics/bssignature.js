var dg;
var d;
$(function () {
    $("#image").change(function () {
        imageDeal(this, dealChange);
    });

    dg = $('#dg').datagrid({
        method: "get",
        url: ctx + '/bssignature/list',
        fit: true,
        fitColumns: true,
        border: false,
        idField: 'id',
        striped: true,
        pagination: true,
        rownumbers: true,
        pageNumber: 1,
        pageSize: 20,
        pageList: [10, 20, 30, 40, 50],
        singleSelect: true,
        columns: [[
            {field: 'id', title: 'id', hidden: true},
            {field: 'signatureNo', title: '工號', sortable: true, width: 100}
            ,
            {field: 'signatureName', title: '姓名', sortable: true, width: 100}
            ,
            {field: 'image', title: '簽名', sortable: true, width: 100,
                formatter:function (value, row, index) {
                    if(value){
                        return '<img style="max-width:80px;max-height:80px;" src="'+value+'"/>';
                    }
                    return '';
                }
            }
            ,
            {field: 'signatureDesc', title: '備註', sortable: true, width: 100}
            ,
            {field: 'createBy', title: '創建人', sortable: true, width: 100}
            ,
            {field: 'createDate', title: '創建時間', sortable: true, width: 100}
        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        rowTooltip: true,
        toolbar: '#tb'
    });
});

var dealChange = function(blob, base64) {
    $("#showImg").attr("src",base64);
    $("#imageValue").val(base64);
}

var imageDeal = function(ele, returnBase64) {
    //获取file，转成base64
    var file = ele.files[0]; //取传入的第一个文件
    if(undefined == file) { //如果未找到文件，结束函数，跳出
        return;
    }
    console.log("fileSize" + file.size);
    console.log(file.type);

    var r = new FileReader();
    r.readAsDataURL(file);
    r.onload = function(e) {
        var base64 = e.target.result;
        var bili = 1.5;
        console.log("压缩前：" + base64.length);
        suofang(base64, bili, returnBase64);
    }
}
var suofang = function(base64, bili, callback) {
    console.log("执行缩放程序,bili=" + bili);
    //处理缩放，转格式
    var _img = new Image();
    _img.src = base64;
    _img.onload = function() {
        var _canvas = document.createElement("canvas");
        var w = this.width / bili;
        var h = this.height / bili;
        _canvas.setAttribute("width", w);
        _canvas.setAttribute("height", h);
        _canvas.getContext("2d").drawImage(this, 0, 0, w, h);
        var base64 = _canvas.toDataURL("image/jpeg");
        _canvas.toBlob(function(blob) {
            console.log(blob.size);

            if(blob.size > 1024*1024){
                suofang(base64, bili, callback);
            }else{
                callback(blob, base64);
            }
        }, "image/jpeg");
    }
}


//弹窗增加
function add() {
    d = $("#dlg").dialog({
        title: '添加簽名檔',
        width: 320,
        height: 270,
        href: ctx + '/bssignature/create',
        maximizable: true,
        modal: true,
        buttons: [{
            text: '确认',
            handler: function () {
                $("#mainform").submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//删除
function del() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function (data) {
        if (data) {
            $.ajax({
                type: 'get',
                url: ctx + "/bssignature/delete/" + row.id,
                success: function (data) {
                    successTip(data, dg);
                }
            });
        }
    });
}

//弹窗修改
function upd() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    d = $("#dlg").dialog({
        title: '修改簽名檔',
        width: 320,
        height: 270,
        href: ctx + '/bssignature/update/' + row.id,
        maximizable: true,
        modal: true,
        buttons: [{
            text: '修改',
            handler: function () {
                $('#mainform').submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//创建查询对象并查询
function cx() {
    var obj = $("#searchFrom").serializeObject();
    dg.datagrid('load', obj);
}

//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("reset");
}

//导出excel
function exportExcel() {
    var form = document.getElementById("searchFrom");
    searchFrom.action = ctx + '/bssignature/exportExcel';
    form.submit();
}