package com.foxconn.ipebg.buessness.audit.entity;

import com.foxconn.ipebg.audit.entity.EFormSignEntity;

public class TempSecurityCostAplyDto {
    // 臨時性安保服務單主表
    TempSecurityCostAplyEntity aplyEntity;
    // 签核节点
    private EFormSignEntity[] eFormSignEntity;

    public TempSecurityCostAplyEntity getAplyEntity() {
        return aplyEntity;
    }

    public void setAplyEntity(TempSecurityCostAplyEntity aplyEntity) {
        this.aplyEntity = aplyEntity;
    }

    public EFormSignEntity[] geteFormSignEntity() {
        return eFormSignEntity;
    }

    public void seteFormSignEntity(EFormSignEntity[] eFormSignEntity) {
        this.eFormSignEntity = eFormSignEntity;
    }
}
