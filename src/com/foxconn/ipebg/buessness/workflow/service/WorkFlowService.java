package com.foxconn.ipebg.buessness.workflow.service;

import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.buessness.common.entity.TQhChargelogEntity;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.buessness.common.service.TQhChargelogService;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;
import com.foxconn.ipebg.buessness.workflow.entity.*;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Global;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.Mail;
import com.foxconn.ipebg.system.entity.TPubMailrecordEntity;
import com.foxconn.ipebg.system.entity.User;
import com.foxconn.ipebg.system.service.DictService;
import com.foxconn.ipebg.system.service.TPubMailrecordService;
import com.foxconn.ipebg.system.service.UserService;
import com.foxconn.ipebg.system.utils.SendMailUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.*;

/**
 * Company foxconn Copyright (C) 2004-2018 All Rights Reserved.
 */
@Service
@Transactional(readOnly = true)
public class WorkFlowService {
	private static final Logger logger = Logger
			.getLogger(WorkFlowService.class);
	@Autowired
	private ProcessService processService;
	@Autowired
	private TQhAllRelationService allRelationService;
	@Autowired
	private WfConifgService conifgService;
	@Autowired
	private TQhChargelogService chargelogService;
	@Autowired
	private WfNodeinfoService wfNodeinfoService;
	@Autowired
	private WfConfigparamService wfConfigparamService;
	@Autowired
	private WfNoderuleService wfNoderuleService;
	@Autowired
	private TQhChargelogService tQhChargelogService;
	@Autowired
	private DictService dictService;
	@Autowired
	private WfNodeparamService nodeparamService;
	@Autowired
	private TQhUserformhsService tQhUserformhsService;
	@Autowired
	private TPubMailrecordService mailrecordService;
	@Autowired
	private UserService userService;

	/**
	 * 方法描述: 獲取待辦
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018/10/8 下午 03:09
	 * @Return
	 **/
//	@Transactional(readOnly = false)
//	public List<MyTaskCount> myTask(String empno) {
//		User user = null;
//		if (StringUtils.isNotEmpty(empno)) {
//			user = new User();
//			user.setLoginName(empno);
//		} else {
//			user = UserUtil.getCurrentUser();
//		}
//		List<MyTaskCount> entityList = new ArrayList<MyTaskCount>();
//		List<MyTaskCount> entityBackList = new ArrayList<MyTaskCount>();
//		List<String> processIds = this.getProcessIds(user.getLoginName());
//		// 查詢中間表
//		if (processIds.size() > 0) {
//			entityList = allRelationService.queryIndexTask(processIds);
//			entityBackList = allRelationService.queryIndexBackTask(processIds);
//			for (MyTaskCount taskCount : entityBackList) {
//				entityList.add(taskCount);
//			}
//			logger.info(String.format(
//					"user %s falcon found %s,system found %s",
//					user.getLoginName(), processIds.size(), entityList.size()));
//		}
//		return entityList;
//	}

	/**
	 * 方法描述: 獲取所有待辦任務
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018/10/13 上午 08:14
	 * @Return
	 **/

//	@Transactional(readOnly = false)
//	public Page<Gtasks> getAllMyTask(String workFlowId, String status,
//			Page page, List<PropertyFilter> filters) {
//		List<Gtasks> gtasksList = new ArrayList<Gtasks>();
//		Gtasks gtasks = null;
//		List<String> processIds = this.getProcessIds(UserUtil.getCurrentUser()
//				.getLoginName());
//		if (processIds.size() > 0) {
//			// processIds.add("101789953");
//			PropertyFilter filter = new PropertyFilter("INS_processid",
//					processIds);
//			filters.add(filter);
//			WfConifgEntity conifgEntity = null;
//			List<TQhAllRelationEntity> dataList = null;
//			if (StringUtils.isNotBlank(workFlowId)) {
//				PropertyFilter filter_status = new PropertyFilter(
//						"EQS_workstatus", status);
//				PropertyFilter filter_workFlowId = new PropertyFilter(
//						"EQS_workflowid", workFlowId);
//				filters.add(filter_status);
//				filters.add(filter_workFlowId);
//				dataList = allRelationService.search(page, filters).getResult();
//			} else {
//				dataList = allRelationService.search(page, filters).getResult();
//			}
//			for (TQhAllRelationEntity entity : dataList) {
//				gtasks = allRelationService.queryMyGtasks(entity.getSerialno(),
//						entity.getDtoName());
//				gtasks.setWfName(entity.getWfName());
//				conifgEntity = conifgService.findUnique(entity.getWorkflowid());
//				if ("4".equals(entity.getWorkstatus())) {
//					gtasks.setAuditAction(conifgEntity.getModaction());
//				} else if ("2".equals(entity.getWorkstatus())) {
//					gtasks.setAuditAction(conifgEntity.getAction());
//				}
//				gtasks.setSerialno(entity.getSerialno());
//				InterConfig config = new InterConfig();
//				config.setProcessId(entity.getProcessid());
//				// 獲取流程當前節點處理人
//				InterResult taskInfo = processService.currentTaskInfo(config);
//				gtasks.setTaskName(taskInfo.getTaskInfoList().get(0)
//						.getTaskName());
//				gtasksList.add(gtasks);
//			}
//		}
//		page.setResult(gtasksList);
//		return page;
//	}

//	@Transactional(readOnly = false)
//	public Page<Gtasks> getAllMyDownTask(Page page) {
//		List<Gtasks> gtasksList = new ArrayList<Gtasks>();
//		Gtasks gtasks = null;
//		WfConifgEntity conifgEntity = null;
//		List<TQhAllRelationEntity> dataList = allRelationService.getMyDownTask(
//				UserUtil.getCurrentUser().getLoginName(), page);
//		for (TQhAllRelationEntity entity : dataList) {
//			gtasks = allRelationService.queryMyGtasks(entity.getSerialno(),
//					entity.getDtoName());
//			gtasks.setWfName(entity.getWfName());
//			conifgEntity = conifgService.findUnique(entity.getWorkflowid());
//			// if ("4".equals(entity.getWorkstatus())) {
//			// gtasks.setAuditAction(conifgEntity.getModaction());
//			// } else if ("2".equals(entity.getWorkstatus())) {
//			// gtasks.setAuditAction(conifgEntity.getAction());
//			// }else {
//			gtasks.setAuditAction(conifgEntity.getDetailaction());
//			// }
//			gtasks.setSerialno(entity.getSerialno());
//			InterConfig config = new InterConfig();
//			config.setProcessId(entity.getProcessid());
//			// 獲取流程當前節點處理人
//			InterResult taskInfo = processService.currentTaskInfo(config);
//			gtasks.setTaskName(taskInfo.getTaskInfoList() == null ? ""
//					: taskInfo.getTaskInfoList().get(0).getTaskName());
//			gtasksList.add(gtasks);
//		}
//		page.setResult(gtasksList);
//		page.setTotalCount(allRelationService.getMyDownTaskCount(UserUtil
//				.getCurrentUser().getLoginName()));
//		return page;
//	}

	/**
	 * 方法描述: 通過流程標識獲取對應的任務
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018/10/13 上午 08:09
	 * @Return
	 **/

//	public List<Gtasks> getMyTaskByWorkId(String workFlowId, String status) {
//		List<Gtasks> gtasksList = new ArrayList<Gtasks>();
//		Gtasks gtasks = null;
//		List<String> processIds = this.getProcessIds(UserUtil.getCurrentUser()
//				.getLoginName());
//		// processIds.add("101789953");
//		// workFlowId="dzqh_xinzengxiangmuhuanbaoshouxubanlishenqingdan";
//		WfConifgEntity conifgEntity = null;
//		List<TQhAllRelationEntity> dataList = allRelationService
//				.getMyTaskByWorkId(workFlowId, processIds, status);
//		for (TQhAllRelationEntity entity : dataList) {
//			gtasks = allRelationService.queryMyGtasks(entity.getProcessid(),
//					entity.getDtoName());
//			gtasks.setWfName(entity.getWfName());
//			gtasks.setSerialno(entity.getSerialno());
//			InterConfig config = new InterConfig();
//			config.setProcessId(entity.getProcessid());
//			conifgEntity = conifgService.findUnique(entity.getWorkflowid());
//			gtasks.setAuditAction(conifgEntity.getAction());
//			// 獲取流程當前節點處理人
//			InterResult taskInfo = processService.currentTaskInfo(config);
//			gtasks.setTaskName(taskInfo.getTaskInfoList().get(0).getTaskName());
//			gtasksList.add(gtasks);
//		}
//		return gtasksList;
//	}

	private List<String> getProcessIds(String empno) {
		List<String> processIds = new ArrayList<String>();
		try {
			InterConfig config = new InterConfig();
			config.setUserId(empno);
			// 查詢流程引擎的個人待辦
			InterResult interResult = processService.queryTaskList(config);
			List<WfConifgEntity> conifgEntityList = conifgService.getAll();
			if (interResult != null) {
				List<TaskInfo> taskList = interResult.getTaskInfoList();
				if (taskList != null && taskList.size() > 0) {
					for (TaskInfo t : taskList) {
						for (WfConifgEntity w : conifgEntityList) {
							if (t.getPdId().startsWith(w.getWorkflowid())) {
								processIds.add(t.getProcessId());
								break;
							}
						}
					}
				} else {
					logger.info("not found user task");
				}
			}
			// 查詢個人會簽待辦任務
			config = new InterConfig();
			config.setUserId(empno);
			interResult = processService.groupTasks(config);
			if (interResult != null) {
				List<TaskInfo> taskList = interResult.getTaskInfoList();
				if (taskList != null && taskList.size() > 0) {
					for (TaskInfo t : taskList) {
						for (WfConifgEntity w : conifgEntityList) {
							if (t.getPdId().startsWith(w.getWorkflowid())) {
								processIds.add(t.getProcessId());
								break;
							}
						}
					}
				} else {
					logger.info("not found user group task");
				}
			}
		} catch (Exception e) {
			logger.error(e.getStackTrace(), e);
		}
		return processIds;
	}

	/**
	 * 方法描述: 完成任務
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018/10/10 下午 01:20
	 * @Return
	 **/
//	@Transactional(readOnly = false)
//	public String completeTask(String serialno, String ip, String status,
//			String attachidsremark, String reattachids) {
//		// status 2 重新提交 3 取消申請 0 通過 1 駁回
//		try {
//			TQhAllRelationEntity relationEntity = allRelationService
//					.queryByEntity(serialno);
//			Assert.notNull(relationEntity);
//			CompleteTaskParameter parameter = new CompleteTaskParameter();
//			parameter.setSerialno(serialno);
//			parameter.setProcessId(relationEntity.getProcessid());
//			parameter.setWorkFlowId(relationEntity.getWorkflowid());
//			parameter.setIp(ip);
//			parameter.setAttachidsremark(attachidsremark);
//
//			InterConfig config = new InterConfig();
//			config.setProcessId(parameter.getProcessId());
//			// 獲取流程當前節點處理人
//			InterResult taskInfo = processService.currentTaskInfo(config);
//			TaskInfo info = taskInfo.getTaskInfoList().get(0);
//			WfNodeinfoEntity entity = wfNodeinfoService.findByTaskName(
//					info.getTaskName(), parameter.getWorkFlowId(),
//					relationEntity.getVersion());
//
//			String result = "";
//			// 防止不是自己的單子審核通過
//			if (UserUtil.getCurrentUser().getLoginName()
//					.equals(info.getAssignee())) {
//				if ("0".equals(entity.getSigntype())) {
//					result = this.completeTask(parameter, status);
//				} else if ("1".equals(entity.getSigntype())) {
//					result = this.completeHqTask(parameter, status);
//				}
//				if (result.equals(Constant.RESULT.CODE_NO.getValue())) {
//					return Constant.RESULT.CODE_NO.getValue();
//				}
//			} else {
//				return Constant.RESULT.CODE_NO.getValue();
//			}
//			// 對應窗口審核上傳的附件
//			if (StringUtils.isNotEmpty(reattachids)) {
//				allRelationService.updateFormReattachids(serialno, reattachids,
//						relationEntity.getDtoName());
//			}
//			// 如果是完成則更新表單狀態及中間表狀態
//			if (result == Constant.RESULT.CODE_COMPLETE.getValue()) {
//				allRelationService.updateFormStaticComplete(serialno, result,
//						relationEntity.getDtoName());
//				// relationEntity.setWorkstatus(result);
//				// allRelationService.save(relationEntity);
//				String userEmpno = allRelationService.findUserEmpno(
//						relationEntity.getDtoName(), serialno);
//				User user = userService.getUser(userEmpno);
//				if (user.getEmail() != null) {
//					// WfConifgEntity conifgEntity =
//					// conifgService.findUnique(relationEntity.getWorkflowid());
//					// String validStr =
//					// UUID.randomUUID().toString().replace("-","");
//
//					Gtasks gtasks = allRelationService.queryMyGtasks(
//							relationEntity.getSerialno(),
//							relationEntity.getDtoName());
//					Mail mail = new Mail();
//					mail.setUsername(userService.getUser(info.getAssignee())
//							.getName());
//					mail.setSystemname(dictService.getDictByTypeAndVlaue(
//							"sys_property", "sys_name").getLabel());
//					mail.setDusername(gtasks.getMakername());
//					mail.setChargerman(UserUtil.getCurrentUser().getName());
//					mail.setOrdertype(relationEntity.getWfName());
//					mail.setSerialno(serialno);
//					mail.setOrderstatus(result);
//					mail.setUrl(dictService.get(560).getValue());
//					mail.setUrlip(dictService.get(561).getValue());
//					mail.setUsermail(user.getEmail());
//					// mail.setFreeloginurl("");
//					// 保存發送記錄
//					TPubMailrecordEntity mailrecordEntity = new TPubMailrecordEntity();
//					mailrecordEntity.setChargerman(mail.getChargerman());
//					mailrecordEntity.setDusername(mail.getDusername());
//					mailrecordEntity.setOrderstatus(mail.getOrderstatus());
//					mailrecordEntity.setOrdertype(mail.getOrdertype());
//					mailrecordEntity.setSerialno(mail.getSerialno());
//					mailrecordEntity.setUsername(mail.getUsername());
//					mailrecordEntity.setUsermail(mail.getUsermail());
//					mailrecordEntity.setSendStatus("0");
//					mailrecordEntity.setEmpno(info.getAssignee());
//					mailrecordEntity.setUrl(mail.getUrl());
//					mailrecordEntity.setUrlip(mail.getUrlip());
//					// mailrecordEntity.setValidStr(validStr);
//					mailrecordService.save(mailrecordEntity);
//					String sendResult = new SendMailUtil().sendMail(mail);
//					if ("0".equals(sendResult)) {
//						// 發送成功，更新標誌
//						mailrecordEntity.setSendStatus("1");
//						mailrecordService.save(mailrecordEntity);
//					}
//
//				}
//			} else {
//				// 獲取審核人員郵箱，發送郵件信息用
//				taskInfo = processService.currentTaskInfo(config);
//				info = taskInfo.getTaskInfoList().get(0);
//				User user = userService.getUser(info.getAssignee());
//				if (user.getEmail() != null) {
//					WfConifgEntity conifgEntity = conifgService
//							.findUnique(relationEntity.getWorkflowid());
//					String validStr = UUID.randomUUID().toString()
//							.replace("-", "");
//
//					Gtasks gtasks = allRelationService.queryMyGtasks(
//							relationEntity.getSerialno(),
//							relationEntity.getDtoName());
//					Mail mail = new Mail();
//					mail.setUsername(userService.getUser(info.getAssignee())
//							.getName());
//					mail.setChargerman(UserUtil.getCurrentUser().getName());
//					mail.setSystemname(dictService.getDictByTypeAndVlaue(
//							"sys_property", "sys_name").getLabel());
//					mail.setDusername(gtasks.getMakername());
//					mail.setOrdertype(relationEntity.getWfName());
//					mail.setSerialno(serialno);
//					mail.setUrl(dictService.get(560).getValue());
//					mail.setUrlip(dictService.get(561).getValue());
//					if ("0".equals(status)) {
//						// 通過
//						mail.setOrderstatus("2");
//						mail.setFreeloginurl(dictService.get(558).getValue()
//								+ Global.getConfig("sendMailAuditPath")
//								+ "/audit/login?username=" + info.getAssignee()
//								+ "&loginType=1&utoken=" + validStr + "&url=/"
//								+ conifgEntity.getAction() + "/" + serialno);
//						mail.setFreeloginurlip(dictService.get(559).getValue()
//								+ Global.getConfig("sendMailAuditPath")
//								+ "/audit/login?username=" + info.getAssignee()
//								+ "&loginType=1&utoken=" + validStr + "&url=/"
//								+ conifgEntity.getAction() + "/" + serialno);
//
//					} else {
//						// 駁回
//						mail.setOrderstatus("4");
//						mail.setFreeloginurl(dictService.get(558).getValue()
//								+ Global.getConfig("sendMailAuditPath")
//								+ "/audit/login?username=" + info.getAssignee()
//								+ "&loginType=1&utoken=" + validStr + "&url=/"
//								+ conifgEntity.getModaction() + "/" + serialno);
//						mail.setFreeloginurlip(dictService.get(559).getValue()
//								+ Global.getConfig("sendMailAuditPath")
//								+ "/audit/login?username=" + info.getAssignee()
//								+ "&loginType=1&utoken=" + validStr + "&url=/"
//								+ conifgEntity.getModaction() + "/" + serialno);
//
//					}
//					mail.setUsermail(user.getEmail());
//					// 保存發送記錄
//					TPubMailrecordEntity mailrecordEntity = new TPubMailrecordEntity();
//					mailrecordEntity.setChargerman(mail.getChargerman());
//					mailrecordEntity.setDusername(mail.getDusername());
//					mailrecordEntity.setOrderstatus(mail.getOrderstatus());
//					mailrecordEntity.setOrdertype(mail.getOrdertype());
//					mailrecordEntity.setSerialno(mail.getSerialno());
//					mailrecordEntity.setUsername(mail.getUsername());
//					mailrecordEntity.setUsermail(mail.getUsermail());
//					mailrecordEntity.setSendStatus("0");
//					mailrecordEntity.setEmpno(info.getAssignee());
//					mailrecordEntity.setUrl(mail.getUrl());
//					mailrecordEntity.setUrlip(mail.getUrlip());
//					mailrecordEntity.setFreeloginurl(mail.getFreeloginurl());
//					mailrecordEntity
//							.setFreeloginurlip(mail.getFreeloginurlip());
//					mailrecordEntity.setValidStr(validStr);
//					mailrecordService.save(mailrecordEntity);
//					String sendResult = new SendMailUtil().sendMail(mail);
//					if ("0".equals(sendResult)) {
//						// 發送成功，更新標誌
//						mailrecordEntity.setSendStatus("1");
//						mailrecordService.save(mailrecordEntity);
//					}
//
//				}
//			}
//		} catch (Exception e) {
//			logger.error(e.getStackTrace(), e);
//			return Constant.RESULT.CODE_NO.getValue();
//		}
//		return Constant.RESULT.CODE_YES.getValue();
//	}

//	@Transactional(readOnly = false)
//	public String processStart(String serialno) {
//		TQhAllRelationEntity relationEntity = allRelationService
//				.queryByEntity(serialno);
//		// List<Map<String, Object>> mapList=
//		// allRelationService.findToMapBySql(" from "+relationEntity.getDtoName()+" where serialno=:serialno",serialno);
//		List<Map<String, Object>> mapList = allRelationService
//				.findToMapBySql(serialno);
//		return "";
//	}

	/**
	 * 方法描述: 啟動流程
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018/10/8 上午 10:32
	 * @Return
	 **/
//	@Transactional(readOnly = false)
//	public String processStart(WorkFlowEntity obj, Object entity) {
//		try {
//			InterConfig config = new InterConfig();
//			// 設置工單發啟人
//			// config.setApplyUserId("admin");
//			config.setApplyUserId(UserUtil.getCurrentUser().getLoginName());
//			// 設置流程id
//			config.setWorkFlowId(obj.getWorkflowId());
//			// 初始化工單各節點處理人
//			config.setTaskUsers(obj.getTaskUsers());
//			// 設置會簽簽核人信息
//			config.setHuiqian(obj.getHuiqian());
//			// 設置流程整體參數
//			String varStr = this.getWfVariables(obj.getWorkflowId());
//			// String userGrade =
//			// conifgService.callProcWithResult("{CALL p_qh_getUserGrade('?0')}",
//			// obj.getEmpNo()).get(0).toString();
//			varStr = varStr.replace("$USERGRADE$", "1");
//			logger.info(varStr);
//			config.setVariables(varStr);
//			InterResult interResult = processService.processStart(config);
//			// 創建會簽節點
//			this.createHuiqianTaskInfor(obj, interResult.getProcessId(), entity);
//
//			AutoCompleteTask(interResult.getProcessId());
//			// 發送郵件
//
//			config = new InterConfig();
//			config.setProcessId(interResult.getProcessId());
//			InterResult taskInfo = processService.currentTaskInfo(config);
//			TaskInfo info = taskInfo.getTaskInfoList().get(0);
//			User user = userService.getUser(info.getAssignee());
//			if (user.getEmail() != null) {
//				WfConifgEntity conifgEntity = conifgService.findUnique(obj
//						.getWorkflowId());
//				String validStr = UUID.randomUUID().toString().replace("-", "");
//				Gtasks gtasks = allRelationService.queryMyGtasks(
//						obj.getSerialNo(), entity.getClass().getName());
//				Mail mail = new Mail();
//				mail.setUsername(userService.getUser(info.getAssignee())
//						.getName());
//				mail.setChargerman(UserUtil.getCurrentUser().getName());
//				mail.setSystemname(dictService.getDictByTypeAndVlaue(
//						"sys_property", "sys_name").getLabel());
//				mail.setDusername(gtasks.getMakername());
//				mail.setOrdertype(conifgService.findUnique(obj.getWorkflowId())
//						.getWorkflowname());
//				mail.setSerialno(obj.getSerialNo());
//				mail.setOrderstatus("2");
//				mail.setFreeloginurl(dictService.get(558).getValue()
//						+ Global.getConfig("sendMailAuditPath")
//						+ "/audit/login?username=" + info.getAssignee()
//						+ "&loginType=1&utoken=" + validStr + "&url=/"
//						+ conifgEntity.getAction() + "/" + obj.getSerialNo());
//				mail.setFreeloginurlip(dictService.get(559).getValue()
//						+ Global.getConfig("sendMailAuditPath")
//						+ "/audit/login?username=" + info.getAssignee()
//						+ "&loginType=1&utoken=" + validStr + "&url=/"
//						+ conifgEntity.getAction() + "/" + obj.getSerialNo());
//				mail.setUsermail(user.getEmail());
//				mail.setUrl(dictService.get(560).getValue());
//				mail.setUrlip(dictService.get(561).getValue());
//				// 保存發送記錄
//				TPubMailrecordEntity mailrecordEntity = new TPubMailrecordEntity();
//				mailrecordEntity.setChargerman(mail.getChargerman());
//				mailrecordEntity.setDusername(mail.getDusername());
//				mailrecordEntity.setOrderstatus(mail.getOrderstatus());
//				mailrecordEntity.setOrdertype(mail.getOrdertype());
//				mailrecordEntity.setSerialno(mail.getSerialno());
//				mailrecordEntity.setUsername(mail.getUsername());
//				mailrecordEntity.setUsermail(mail.getUsermail());
//				mailrecordEntity.setSendStatus("0");
//				mailrecordEntity.setEmpno(info.getAssignee());
//				mailrecordEntity.setUrl(mail.getUrl());
//				mailrecordEntity.setUrlip(mail.getUrlip());
//				mailrecordEntity.setFreeloginurl(mail.getFreeloginurl());
//				mailrecordEntity.setFreeloginurlip(mail.getFreeloginurlip());
//				mailrecordEntity.setValidStr(validStr);
//				mailrecordService.save(mailrecordEntity);
//				String sendResult = new SendMailUtil().sendMail(mail);
//				if ("0".equals(sendResult)) {
//					// 發送成功，更新標誌
//					mailrecordEntity.setSendStatus("1");
//					mailrecordService.save(mailrecordEntity);
//				}
//
//			}
//			// createHuiqianTaskInfor(obj, interResult.getProcessId());
//			return interResult.getProcessId();
//		} catch (Exception e) {
//			logger.error(e.getStackTrace(), e);
//			return Constant.RESULT.CODE_NO.getValue();
//		}
//	}

	/**
	 * 方法描述: 創建會簽節點
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018/10/8 上午 10:32
	 * @Return
	 **/
	@Transactional(readOnly = false)
	public void createHuiqianTaskInfor(WorkFlowEntity obj, String processId,
			Object o) {
		try {
			List<WfNodeinfoEntity> entityList = wfNodeinfoService
					.findForHuiqian(obj.getWorkflowId());
			if (entityList != null && entityList.size() > 0) {
				InterConfig config = null;
				for (WfNodeinfoEntity n : entityList) {
					if ("1".equals(n.getSigntype())) {
						config = new InterConfig();
						config.setProcessId(processId);
						config.setTaskName(n.getNodename());
						String hQRule = getHuiQianRule(obj.getWorkflowId(),
								n.getNodeid());
						Object cstr = Reflections.getFieldValue(o,
								n.getColname());
						String hChargeStr = (cstr == null ? "" : cstr
								.toString());
						if (StringUtils.isNotEmpty(hChargeStr)) {
							String[] cs = hChargeStr.split(",");
							hQRule = hQRule.replace("$PERSONNUM$", cs.length
									+ "");
						}
						logger.info(hQRule);
						config.setOtherParam(hQRule);
						processService.createHuiqianTaskInfor(config);
					}
				}
			}
		} catch (Exception e) {
			logger.error(e.getStackTrace(), e);
		}
	}

	/**
	 * 方法描述: 獲取流程級配置參數
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018/10/8 上午 11:51
	 * @Return
	 **/

	public String getWfVariables(String workFlowId) {
		StringBuffer sb = new StringBuffer();
		try {
			WfConifgEntity wf = conifgService.findUnique(workFlowId);
			Assert.notNull(wf);
			List<WfConfigparamEntity> ps = wfConfigparamService
					.findByFilters(workFlowId);
			Assert.notEmpty(ps);
			// 組織流程級別的參數
			for (WfConfigparamEntity p : ps) {
				sb.append(p.getParamename() + ",")
						.append(p.getParamvalue() == null ? "" : p
								.getParamvalue() + ",")
						.append(p.getParamtype() == null ? "" : p
								.getParamtype() + ";");
			}
		} catch (Exception e) {
			logger.error(e.getStackTrace(), e);
		}
		return sb.toString();
	}

	/**
	 * 方法描述:
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018/10/10 上午 10:42
	 * @Return
	 **/

	public String getHuiQianRule(String workflowId, String nodeId) {
		StringBuffer sb = new StringBuffer();
		try {
			WfNoderuleEntity ne = wfNoderuleService.findUniqByNodeId(nodeId,
					workflowId);
			Assert.notNull(ne);
			sb.append(ne.getTotalpeople()).append("&").append(ne.getPassrate())
					.append("&").append(ne.getVoterule()).append("&")
					.append(ne.getTaskstatusvariable()).append("&")
					.append(ne.getTaskcompletevariable()).append("&");
		} catch (Exception e) {
			logger.error(e.getStackTrace(), e);
		}
		return sb.toString();
	}

	/**
	 * 方法描述: 完成任務
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018/10/9 上午 10:21
	 * @Return
	 **/
//	@Transactional(readOnly = false)
//	private String completeTask(CompleteTaskParameter parameter, String status) {
//		try {
//			InterConfig config = new InterConfig();
//			config.setProcessId(parameter.getProcessId());
//			// 獲取流程當前節點處理人
//			InterResult taskInfo = processService.currentTaskInfo(config);
//			config = new InterConfig();
//			// config.setTaskId(parameter.getTaskId());
//			// InterResult taskInfoTmp=((ProcessService)
//			// SpringContextUtil.getBean("processService")).queryTaskById(config);
//			config = new InterConfig();
//			TaskInfo info = taskInfo.getTaskInfoList().get(0);
//			config.setTaskId(info.getTaskId());
//			TQhAllRelationEntity entityR = allRelationService
//					.queryByEntity(parameter.getSerialno());
//			config.setVariables(this.getNodeTaskVarivles(
//					parameter.getWorkFlowId(), info.getTaskName(), status,
//					entityR.getVersion()));
//			InterResult interResult = processService.comleteTask(config);
//			if ("SUCCESS".equals(interResult.getStatus())) {
//				// 添加簽核記錄
//				TQhChargelogEntity entity = new TQhChargelogEntity();
//				entity.setChargename(UserUtil.getCurrentUser().getName());
//				// entity.setChargename("S6112942");
//				WfNodeinfoEntity entity_nodeinfo = wfNodeinfoService
//						.findByTaskName(info.getTaskName(),
//								entityR.getWorkflowid(), entityR.getVersion());
//				entity.setChargenode(entity_nodeinfo.getNodealain());
//				entity.setSerialno(parameter.getSerialno());
//				entity.setChargeno(info.getAssignee());
//				entity.setWorkflowid(parameter.getWorkFlowId());
//				entity.setOperateip(parameter.getIp());
//				entity.setDecrib(parameter.getAttachidsremark());
//				if ("0".equals(status)) {
//					entity.setIspass("通過");
//				} else if ("1".equals(status)) {
//					entity.setIspass("駁回");
//				} else if ("2".equals(status)) {
//					entity.setIspass("重新提交");
//				} else if ("3".equals(status)) {
//					entity.setIspass("取消申請");
//				}
//				tQhChargelogService.save(entity);
//				String result = AutoCompleteTask(parameter.getProcessId());
//				// 查詢工作流中的狀態
//				InterConfig configStatus = new InterConfig();
//				configStatus.setProcessId(parameter.getProcessId());
//				InterResult interResults = processService
//						.processStatus(configStatus);
//				if (interResults.getRunning().equals("false")) {
//					return Constant.RESULT.CODE_COMPLETE.getValue();
//				}
//				// 如果是駁回更新中間表狀態
//				if ("1".equals(status)) {
//					entityR.setWorkstatus(Constant.RESULT.CODE_REJECT
//							.getValue());
//					allRelationService.update(entityR);
//					allRelationService.updateFormStatic(
//							parameter.getSerialno(),
//							Constant.RESULT.CODE_REJECT.getValue(),
//							entityR.getDtoName());
//				}
//				// 如果是完成，返回完成標識
//				if (result.equals(Constant.RESULT.CODE_COMPLETE)) {
//					allRelationService.updateFormStaticComplete(
//							parameter.getSerialno(),
//							Constant.RESULT.CODE_COMPLETE.getValue(),
//							entityR.getDtoName());
//					return result;
//				}
//				return Constant.RESULT.CODE_YES.getValue();
//			}
//		} catch (Exception e) {
//			logger.error(e.getStackTrace(), e);
//		}
//		return Constant.RESULT.CODE_NO.getValue();
//	}

	/**
	 * 方法描述: 會簽任務完成
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018/10/15 下午 03:25
	 * @Return
	 **/
//	@Transactional(readOnly = false)
//	public String completeHqTask(CompleteTaskParameter parameter, String status) {
//		try {
//			InterConfig config = new InterConfig();
//			config.setProcessId(parameter.getProcessId());
//			// 獲取流程當前節點處理人
//			InterResult taskInfo = processService.currentTaskInfo(config);
//			TaskInfo info = taskInfo.getTaskInfoList().get(0);
//			config = new InterConfig();
//			config.setTaskId(info.getTaskId());
//			config.setTaskName(info.getTaskName());
//			TQhAllRelationEntity entityR = allRelationService
//					.queryByEntity(parameter.getSerialno());
//			config.setVoteResult(this.getNodeHqTaskVarivles(
//					parameter.getWorkFlowId(), info.getTaskName(), status,
//					entityR.getVersion()));
//			config.setAssignee(UserUtil.getCurrentUser().getLoginName());
//			config.setProcessId(parameter.getProcessId());
//			InterResult interResult = processService
//					.completeHuiqianTask(config);
//			if ("SUCCESS".equals(interResult.getStatus())) {
//				// 添加簽核記錄
//				TQhChargelogEntity entity = new TQhChargelogEntity();
//				entity.setChargename(UserUtil.getCurrentUser().getName());
//				// entity.setChargename("S6112942");
//				WfNodeinfoEntity entity_nodeinfo = wfNodeinfoService
//						.findByTaskName(info.getTaskName(),
//								entityR.getWorkflowid(), entityR.getVersion());
//				entity.setChargenode(entity_nodeinfo.getNodealain());
//				entity.setSerialno(parameter.getSerialno());
//				entity.setChargeno(info.getAssignee());
//				entity.setWorkflowid(parameter.getWorkFlowId());
//				entity.setOperateip(parameter.getIp());
//				entity.setDecrib(parameter.getAttachidsremark());
//				if ("0".equals(status)) {
//					entity.setIspass("通過");
//				} else if ("1".equals(status)) {
//					entity.setIspass("駁回");
//				} else if ("2".equals(status)) {
//					entity.setIspass("重新提交");
//				} else if ("3".equals(status)) {
//					entity.setIspass("取消申請");
//				}
//				tQhChargelogService.save(entity);
//				String result = AutoCompleteTask(parameter.getProcessId());
//
//				InterConfig configStatus = new InterConfig();
//				configStatus.setProcessId(parameter.getProcessId());
//				InterResult interResults = processService
//						.processStatus(configStatus);
//				if (interResults.getRunning().equals("false")) {
//					return Constant.RESULT.CODE_COMPLETE.getValue();
//				}
//				// 如果是駁回更新中間表狀態
//				if ("1".equals(status)) {
//					entityR.setWorkstatus(Constant.RESULT.CODE_REJECT
//							.getValue());
//					allRelationService.update(entityR);
//					allRelationService.updateFormStatic(
//							parameter.getSerialno(),
//							Constant.RESULT.CODE_REJECT.getValue(),
//							entityR.getDtoName());
//				}
//				// 如果是完成，返回完成標識
//				if (result.equals(Constant.RESULT.CODE_COMPLETE)) {
//					allRelationService.updateFormStaticComplete(
//							parameter.getSerialno(),
//							Constant.RESULT.CODE_COMPLETE.getValue(),
//							entityR.getDtoName());
//					return result;
//				}
//				return Constant.RESULT.CODE_YES.getValue();
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return Constant.RESULT.CODE_NO.getValue();
//	}

	private String getNodeTaskVarivles(String workflowid, String taskName,
			String status, String version) {
		WfNodeinfoEntity entity = wfNodeinfoService.findByTaskName(taskName,
				workflowid, version);
		WfNodeparamEntity nodeparamEntity = nodeparamService.getInfoById(
				entity, status);
		return nodeparamEntity.getParamename() + ","
				+ nodeparamEntity.getParamvalue() + ","
				+ nodeparamEntity.getParamtype() + ";";
	}

	private String getNodeHqTaskVarivles(String workflowid, String taskName,
			String status, String version) {
		WfNodeinfoEntity entity = wfNodeinfoService.findByTaskName(taskName,
				workflowid, version);
		WfNodeparamEntity nodeparamEntity = nodeparamService.getInfoById(
				entity, status);
		return nodeparamEntity.getParamvalue();
	}

	/**
	 * 方法描述: 自動推動任務進行
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018/10/9 下午 06:15
	 * @Return
	 **/

	public String AutoCompleteTask(String processId) {
		try {
			String auto = dictService.get(6).getValue();
			// 查詢流程狀態
			InterConfig config = new InterConfig();
			config.setProcessId(processId);
			InterResult interResult = processService.processStatus(config);
			// 如果是運行狀態
			if (interResult.getRunning().equals("true")) {
				config = new InterConfig();
				config.setProcessId(processId);
				InterResult taskInfo = processService.currentTaskInfo(config);
				while (auto.equals(taskInfo.getTaskInfoList().get(0)
						.getAssignee())) {
					config = new InterConfig();
					config.setUserId(auto);
					processService.completeAutoTask(config);
					// 再查詢下個節點
					config = new InterConfig();
					config.setProcessId(processId);
					taskInfo = processService.currentTaskInfo(config);
				}
			} else {
				// 返回完成標識
				return Constant.RESULT.CODE_COMPLETE.getValue();
			}
		} catch (Exception e) {
			logger.error(e.getStackTrace(), e);
		}
		return Constant.RESULT.CODE_YES.getValue();
	}

	public void setBeanAutorBlank(Object obj, String workFlowId) {
		try {
			String auto = dictService.get(6).getValue();
			List<WfNodeinfoEntity> entityList = wfNodeinfoService
					.findByFilters(workFlowId);
			for (WfNodeinfoEntity entity : entityList) {
				String value = (String) Reflections.getFieldValue(obj,
						entity.getColname());
				if (auto.equals(value)) {
					Reflections.setFieldValue(obj, entity.getColname(), "");
				}
			}
		} catch (Exception e) {
			logger.error(e.getStackTrace(), e);
		}
	}

	/**
	 * 方法描述: 取消表單
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018/10/10 上午 08:27
	 * @Return
	 **/
//	@Transactional(readOnly = false)
//	public String cancelTask(String serialNo, String ip) {
//		try {
//			TQhAllRelationEntity relationEntity = allRelationService
//					.queryByEntity(serialNo);
//			// return Constant.RESULT.CODE_YES.getValue();
//			InterConfig config = new InterConfig();
//			config.setProcessId(relationEntity.getProcessid());
//			InterResult interResult = processService.processEnd(config);
//			if ("SUCCESS".equals(interResult.getStatus())) {
//
//				// 添加簽核記錄
//				// 獲取流程當前節點處理人
//				TQhChargelogEntity entity = new TQhChargelogEntity();
//				entity.setChargename(UserUtil.getCurrentUser().getName());
//				// entity.setChargename("S6112942");
//				entity.setChargenode("填單人修改");
//				entity.setSerialno(serialNo);
//				entity.setChargeno(UserUtil.getCurrentUser().getName());
//				entity.setWorkflowid(relationEntity.getWorkflowid());
//				entity.setOperateip(ip);
//				entity.setIspass("取消申請");
//				tQhChargelogService.save(entity);
//
//				relationEntity.setWorkstatus(Constant.RESULT.CODE_CANCLE
//						.getValue());
//				allRelationService.update(relationEntity);
//				allRelationService.updateFormStatic(
//						relationEntity.getSerialno(),
//						Constant.RESULT.CODE_CANCLE.getValue(),
//						relationEntity.getDtoName());
//				return Constant.RESULT.CODE_YES.getValue();
//			}
//		} catch (Exception e) {
//			logger.error(e.getStackTrace(), e);
//			return Constant.RESULT.CODE_NO.getValue();
//		}
//		return Constant.RESULT.CODE_NO.getValue();
//	}

	/**
	 * 方法描述: 自動設置為空的審核人為AUTOFIN_DZQH
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018/10/10 上午 08:36
	 * @Return
	 **/

	public void setWfAutorBlank(Object obj, String workFlowId) {
		try {
			String auto = dictService.get(6).getValue();
			List<WfNodeinfoEntity> entityList = wfNodeinfoService
					.findByFilters(workFlowId);
			for (WfNodeinfoEntity entity : entityList) {
				String value = (String) Reflections.getFieldValue(obj,
						entity.getColname());
				if (StringUtils.isBlank(value)) {
					Reflections.setFieldValue(obj, entity.getColname(), auto);
				}
			}
		} catch (Exception e) {
			logger.error(e.getStackTrace(), e);
		}
	}

	/**
	 * 方法描述: 獲取簽核路徑
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018/10/10 上午 09:35
	 * @Return
	 **/

	public String getChargeNodeInfo(Object obj, String workFlowId,
			String processId) {
		StringBuffer stringBuffer = null;
		Boolean flag = true;
		try {
			String auto = dictService.get(6).getValue();
			stringBuffer = new StringBuffer();
			List<WfNodeinfoEntity> entityList = wfNodeinfoService
					.findByFilters(workFlowId);
			InterConfig config = new InterConfig();
			config.setProcessId(processId);
			InterResult taskInfo = processService.currentTaskInfo(config);
			for (WfNodeinfoEntity entity : entityList) {
				if ("makerno".equals(entity.getColname())) {
					continue;
				}
				String value = (String) Reflections.getFieldValue(obj,
						entity.getColname());
				if (taskInfo.getTaskInfoList() != null
						&& taskInfo.getTaskInfoList().get(0) != null
						&& taskInfo.getTaskInfoList().get(0).getTaskName()
								.equals(entity.getNodename()) && flag) {
					stringBuffer.append("<font color=red>"
							+ entity.getNodealain() + "(");
					if (auto.equals(value)) {
						stringBuffer.append("/" + ")->");
					} else {
						String valueName = (String) Reflections.getFieldValue(
								obj, entity.getColname().replace("no", "name"));
						stringBuffer.append(value + "/" + valueName
								+ ")</font>->");
					}
					flag = false;
				} else {
					stringBuffer.append(entity.getNodealain() + "(");
					if (auto.equals(value)) {
						stringBuffer.append("/" + ")->");
					} else {
						String valueName = (String) Reflections.getFieldValue(
								obj, entity.getColname().replace("no", "name"));
						stringBuffer.append(value + "/" + valueName + ")->");
					}
				}
			}
			Assert.hasText(stringBuffer.toString());
		} catch (Exception e) {
			logger.error(e.getStackTrace(), e);
		}
		return stringBuffer.toString().substring(0, stringBuffer.length() - 2);
	}

	/**
	 * 方法描述: 返回流程圖片
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018/10/13 下午 01:20
	 * @Return
	 **/

	public String getImgUrl(String processId) {
		String imgPath = "";
		imgPath = dictService.get(301005).getValue() + "?processId="
				+ processId + "&t=" + UUID.randomUUID();
		return imgPath;
	}

	/**
	 * 方法描述: 查詢簽核節點參數信息，顯示按鈕
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018/10/13 下午 04:53
	 * @Return
	 **/

	public List<WfNodeparamEntity> queryNodeparam(String serialNo) {
		TQhAllRelationEntity relationEntity = allRelationService
				.queryByEntity(serialNo);
		InterConfig config = new InterConfig();
		config.setProcessId(relationEntity.getProcessid());
		// 獲取流程當前節點處理人
		InterResult taskInfo = processService.currentTaskInfo(config);
		TaskInfo info = taskInfo.getTaskInfoList().get(0);
		WfNodeinfoEntity entity = wfNodeinfoService.findByTaskName(
				info.getTaskName(), relationEntity.getWorkflowid(),
				relationEntity.getVersion());
		List<WfNodeparamEntity> entityList = nodeparamService
				.queryNodeInfos(entity.getNodeid());
		return entityList;
	}

	/**
	 * 方法描述: 獲取簽核記錄
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018/10/16 上午 08:20
	 * @Return
	 **/

	public Page<TQhChargelogEntity> queryChargeLog(
			Page<TQhChargelogEntity> page, List<PropertyFilter> filters) {
		Page<TQhChargelogEntity> pageResult = chargelogService.search(page,
				filters);
		return pageResult;
	}

	/**
	 * 方法描述: 獲取當前記錄的簽核節點和簽核人
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018/10/19 下午 04:58
	 * @Return
	 **/

	public TaskNode getNodeInfo(String serialNo) {
		TaskNode node = new TaskNode();
		TQhAllRelationEntity relationEntity = allRelationService
				.queryByEntity(serialNo);
		// 只獲取簽核中和駁回的
		if (relationEntity != null
				&& ("2".equals(relationEntity.getWorkstatus()) || "4"
						.equals(relationEntity.getWorkstatus()))) {
			InterConfig config = new InterConfig();
			config.setProcessId(relationEntity.getProcessid());
			// 獲取流程當前節點處理人
			InterResult taskInfo = processService.currentTaskInfo(config);
			if (taskInfo.getTaskInfoList() != null
					&& taskInfo.getTaskInfoList().size() > 0) {
				TaskInfo info = taskInfo.getTaskInfoList().get(0);
				if (info != null) {
					WfNodeinfoEntity entity = wfNodeinfoService.findByTaskName(
							info.getTaskName(), relationEntity.getWorkflowid(),
							relationEntity.getVersion());
					if (entity != null) {
						node.setNodeName(entity.getNodealain());
					}
					node.setAuditUser(info.getAssignee()
							+ "/"
							+ tQhUserformhsService.findByEmpno(
									info.getAssignee()).getEmpname());
				} else {
					node.setAuditUser("");
					node.setNodeName("");
				}
			}
		}
		return node;
	}

	/**
	 * 方法描述: 通過workflowid查詢配置信息
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018/10/22 下午 04:40
	 * @Return
	 **/

	public WfConifgEntity findByWorkFlowId(String workflowid) {
		return conifgService.findUnique(workflowid);
	}

	/**
	 * 方法描述: 獲取當前節點名稱
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018/10/29 下午 03:32
	 * @Return
	 **/

	public String getNodeName(String processId) {
		InterConfig config = new InterConfig();
		config.setProcessId(processId);
		// 獲取流程當前節點處理人
		InterResult taskInfo = processService.currentTaskInfo(config);
		TaskInfo info = taskInfo.getTaskInfoList().get(0);
		return info.getTaskName();
	}

	/**
	 * 方法描述: 獲取當前簽核人
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018/11/9 上午 10:28
	 * @Return
	 **/

	public String getAssigneeInfo(String serialNo) {
		TQhAllRelationEntity relationEntity = allRelationService
				.queryByEntity(serialNo);
		// 只獲取簽核中和駁回的
		if ("2".equals(relationEntity.getWorkstatus())
				|| "4".equals(relationEntity.getWorkstatus())) {
			InterConfig config = new InterConfig();
			config.setProcessId(relationEntity.getProcessid());
			// 獲取流程當前節點處理人
			InterResult taskInfo = processService.currentTaskInfo(config);
			if (taskInfo.getTaskInfoList() != null
					&& taskInfo.getTaskInfoList().size() > 0) {
				TaskInfo info = taskInfo.getTaskInfoList().get(0);
				if (info != null) {
					WfNodeinfoEntity entity = wfNodeinfoService.findByTaskName(
							info.getTaskName(), relationEntity.getWorkflowid(),
							relationEntity.getVersion());
					return info.getAssignee();
				}
			}
		}
		return null;
	}
}
