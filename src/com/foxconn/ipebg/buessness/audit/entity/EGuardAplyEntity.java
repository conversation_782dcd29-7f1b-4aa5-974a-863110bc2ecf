package com.foxconn.ipebg.buessness.audit.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * 警衛服務申請主表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-02-23 13:46:08
 */
@Entity
@Table(name = "E_GUARD_APLY")
@DynamicUpdate
@DynamicInsert
public class EGuardAplyEntity extends DataEntity<EGuardAplyEntity> implements
		Serializable {
	private static final long serialVersionUID = 1L;

	// 任務編號
	private String serialno;
	// 填單人工號
	private String makerno;
	// 填單人名稱
	private String makername;
	// 填單人IP
	private String makerip;
	// 填單時間
	private Date createtime;
	// 簽核完成時間
	private Date complettime;
	// 表單狀態
	private String workstatus;
	// 填單人廠區Id
	private String makerfactoryid;
	// 承辦人工號
	private String dealno;
	// 承辦人
	private String dealname;
	// 事業群
	private String dptQun;
	// 事業處
	private String dptChu;
	// 事業處代碼
	private String dptChuCode;
	// 部門
	private String dptBu;
	// 費用代碼
	private String dptCode;
	// 法人
	private String legalPerson;
	// 手機
	private String mobilePhone;
	// 承辦人郵箱
	private String dealemail;
	// 分機
	private String dealtel;
	// 聯繫人
	private String linkman;
	// 需求類型（0.增崗1.撤崗）
	private String requirememtType;
	// 需求原因說明
	private String applyExplain;
	// 安全管理必要性(0.符合1.不符合)
	private String safeManagement;
	// 人力需求合理性(0.符合1.不符合)
	private String manpowerDemand;
	// 安全硬件設施(0.完善設施1.加強項目)
	private String safetyFacilities;
	// 審核類別：0.新啟用1.重新啟用
	private String auditType;
	// 申請人IP地址
	private String applyIp;
	// 申請單狀態(E：結案，0：申請，O：，駁回 Q：異常結案)
	private String applyStat;
	// 審核人工號
	private String signEmpNo;
	// 審核人姓名
	private String signEmpNam;
	// 審核時間
	private Date signDate;
	// 附件Id
	private String attachids;
	// (撤崗的崗位編號，以英文逗號分隔開)
	private String reattachids;

	/**
	 * 设置：任務編號
	 */
	public void setSerialno(String serialno) {
		this.serialno = serialno;
	}

	/**
	 * 获取：任務編號
	 */

	@Column(name = "SERIALNO", nullable = false, length = 20)
	public String getSerialno() {
		return serialno;
	}

	/**
	 * 设置：填單人工號
	 */
	public void setMakerno(String makerno) {
		this.makerno = makerno;
	}

	/**
	 * 获取：填單人工號
	 */

	@Column(name = "MAKERNO", nullable = false, length = 20)
	public String getMakerno() {
		return makerno;
	}

	/**
	 * 设置：填單人名稱
	 */
	public void setMakername(String makername) {
		this.makername = makername;
	}

	/**
	 * 获取：填單人名稱
	 */

	@Column(name = "MAKERNAME", nullable = false, length = 20)
	public String getMakername() {
		return makername;
	}

	/**
	 * 设置：填單人IP
	 */
	public void setMakerip(String makerip) {
		this.makerip = makerip;
	}

	/**
	 * 获取：填單人IP
	 */

	@Column(name = "MAKERIP", nullable = false, length = 20)
	public String getMakerip() {
		return makerip;
	}

	/**
	 * 设置：填單時間
	 */
	public void setCreatetime(Date createtime) {
		this.createtime = createtime;
	}

	/**
	 * 获取：填單時間
	 */

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Column(name = "CREATETIME", nullable = false, length = 20)
	public Date getCreatetime() {
		return createtime;
	}

	/**
	 * 设置：簽核完成時間
	 */
	public void setComplettime(Date complettime) {
		this.complettime = complettime;
	}

	/**
	 * 获取：簽核完成時間
	 */

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Column(name = "COMPLETTIME", nullable = false, length = 20)
	public Date getComplettime() {
		return complettime;
	}

	/**
	 * 设置：表單狀態
	 */
	public void setWorkstatus(String workstatus) {
		this.workstatus = workstatus;
	}

	/**
	 * 获取：表單狀態
	 */

	@Column(name = "WORKSTATUS", nullable = false, length = 20)
	public String getWorkstatus() {
		return workstatus;
	}

	/**
	 * 设置：填單人廠區Id
	 */
	public void setMakerfactoryid(String makerfactoryid) {
		this.makerfactoryid = makerfactoryid;
	}

	/**
	 * 获取：填單人廠區Id
	 */

	@Column(name = "MAKERFACTORYID", nullable = false, length = 20)
	public String getMakerfactoryid() {
		return makerfactoryid;
	}

	/**
	 * 设置：承辦人工號
	 */
	public void setDealno(String dealno) {
		this.dealno = dealno;
	}

	/**
	 * 获取：承辦人工號
	 */

	@Column(name = "DEALNO", nullable = false, length = 20)
	public String getDealno() {
		return dealno;
	}

	/**
	 * 设置：承辦人
	 */
	public void setDealname(String dealname) {
		this.dealname = dealname;
	}

	/**
	 * 获取：承辦人
	 */

	@Column(name = "DEALNAME", nullable = false, length = 20)
	public String getDealname() {
		return dealname;
	}

	/**
	 * 设置：事業群
	 */
	public void setDptQun(String dptQun) {
		this.dptQun = dptQun;
	}

	/**
	 * 获取：事業群
	 */

	@Column(name = "DPT_QUN", nullable = false, length = 20)
	public String getDptQun() {
		return dptQun;
	}

	/**
	 * 设置：事業處
	 */
	public void setDptChu(String dptChu) {
		this.dptChu = dptChu;
	}

	/**
	 * 获取：事業處
	 */

	@Column(name = "DPT_CHU", nullable = false, length = 20)
	public String getDptChu() {
		return dptChu;
	}

	/**
	 * 设置：事業處代碼
	 */
	public void setDptChuCode(String dptChuCode) {
		this.dptChuCode = dptChuCode;
	}

	/**
	 * 获取：事業處代碼
	 */

	@Column(name = "DPT_CHU_CODE", nullable = false, length = 20)
	public String getDptChuCode() {
		return dptChuCode;
	}

	/**
	 * 设置：部門
	 */
	public void setDptBu(String dptBu) {
		this.dptBu = dptBu;
	}

	/**
	 * 获取：部門
	 */

	@Column(name = "DPT_BU", nullable = false, length = 20)
	public String getDptBu() {
		return dptBu;
	}

	/**
	 * 设置：費用代碼
	 */
	public void setDptCode(String dptCode) {
		this.dptCode = dptCode;
	}

	/**
	 * 获取：費用代碼
	 */

	@Column(name = "DPT_CODE", nullable = false, length = 20)
	public String getDptCode() {
		return dptCode;
	}

	/**
	 * 设置：法人
	 */
	public void setLegalPerson(String legalPerson) {
		this.legalPerson = legalPerson;
	}

	/**
	 * 获取：法人
	 */

	@Column(name = "LEGAL_PERSON", nullable = false, length = 20)
	public String getLegalPerson() {
		return legalPerson;
	}

	/**
	 * 设置：手機
	 */
	public void setMobilePhone(String mobilePhone) {
		this.mobilePhone = mobilePhone;
	}

	/**
	 * 获取：手機
	 */

	@Column(name = "MOBILE_PHONE", nullable = false, length = 20)
	public String getMobilePhone() {
		return mobilePhone;
	}

	/**
	 * 设置：承辦人郵箱
	 */
	public void setDealemail(String dealemail) {
		this.dealemail = dealemail;
	}

	/**
	 * 获取：承辦人郵箱
	 */

	@Column(name = "DEALEMAIL", nullable = false, length = 20)
	public String getDealemail() {
		return dealemail;
	}

	/**
	 * 设置：分機
	 */
	public void setDealtel(String dealtel) {
		this.dealtel = dealtel;
	}

	/**
	 * 获取：分機
	 */

	@Column(name = "DEALTEL", nullable = false, length = 20)
	public String getDealtel() {
		return dealtel;
	}

	/**
	 * 设置：聯繫人
	 */
	public void setLinkman(String linkman) {
		this.linkman = linkman;
	}

	/**
	 * 获取：聯繫人
	 */

	@Column(name = "LINKMAN", nullable = false, length = 20)
	public String getLinkman() {
		return linkman;
	}

	/**
	 * 设置：需求類型（0.增崗1.撤崗）
	 */
	public void setRequirememtType(String requirememtType) {
		this.requirememtType = requirememtType;
	}

	/**
	 * 获取：需求類型（0.增崗1.撤崗）
	 */

	@Column(name = "REQUIREMEMT_TYPE", nullable = false, length = 20)
	public String getRequirememtType() {
		return requirememtType;
	}

	/**
	 * 设置：需求原因說明
	 */
	public void setApplyExplain(String applyExplain) {
		this.applyExplain = applyExplain;
	}

	/**
	 * 获取：需求原因說明
	 */

	@Column(name = "APPLY_EXPLAIN", nullable = false, length = 20)
	public String getApplyExplain() {
		return applyExplain;
	}

	/**
	 * 设置：安全管理必要性(0.符合1.不符合)
	 */
	public void setSafeManagement(String safeManagement) {
		this.safeManagement = safeManagement;
	}

	/**
	 * 获取：安全管理必要性(0.符合1.不符合)
	 */

	@Column(name = "SAFE_MANAGEMENT", nullable = false, length = 20)
	public String getSafeManagement() {
		return safeManagement;
	}

	/**
	 * 设置：人力需求合理性(0.符合1.不符合)
	 */
	public void setManpowerDemand(String manpowerDemand) {
		this.manpowerDemand = manpowerDemand;
	}

	/**
	 * 获取：人力需求合理性(0.符合1.不符合)
	 */

	@Column(name = "MANPOWER_DEMAND", nullable = false, length = 20)
	public String getManpowerDemand() {
		return manpowerDemand;
	}

	/**
	 * 设置：安全硬件設施(0.完善設施1.加強項目)
	 */
	public void setSafetyFacilities(String safetyFacilities) {
		this.safetyFacilities = safetyFacilities;
	}

	/**
	 * 获取：安全硬件設施(0.完善設施1.加強項目)
	 */

	@Column(name = "SAFETY_FACILITIES", nullable = false, length = 20)
	public String getSafetyFacilities() {
		return safetyFacilities;
	}

	/**
	 * 设置：審核類別：0.新啟用1.重新啟用
	 */
	public void setAuditType(String auditType) {
		this.auditType = auditType;
	}

	/**
	 * 获取：審核類別：0.新啟用1.重新啟用
	 */

	@Column(name = "AUDIT_TYPE", nullable = false, length = 20)
	public String getAuditType() {
		return auditType;
	}

	/**
	 * 设置：申請人IP地址
	 */
	public void setApplyIp(String applyIp) {
		this.applyIp = applyIp;
	}

	/**
	 * 获取：申請人IP地址
	 */

	@Column(name = "APPLY_IP", nullable = false, length = 20)
	public String getApplyIp() {
		return applyIp;
	}

	/**
	 * 设置：申請單狀態(E：結案，0：申請，O：，駁回 Q：異常結案)
	 */
	public void setApplyStat(String applyStat) {
		this.applyStat = applyStat;
	}

	/**
	 * 获取：申請單狀態(E：結案，0：申請，O：，駁回 Q：異常結案)
	 */

	@Column(name = "APPLY_STAT", nullable = false, length = 20)
	public String getApplyStat() {
		return applyStat;
	}

	/**
	 * 设置：審核人工號
	 */
	public void setSignEmpNo(String signEmpNo) {
		this.signEmpNo = signEmpNo;
	}

	/**
	 * 获取：審核人工號
	 */

	@Column(name = "SIGN_EMP_NO", nullable = false, length = 20)
	public String getSignEmpNo() {
		return signEmpNo;
	}

	/**
	 * 设置：審核人姓名
	 */
	public void setSignEmpNam(String signEmpNam) {
		this.signEmpNam = signEmpNam;
	}

	/**
	 * 获取：審核人姓名
	 */

	@Column(name = "SIGN_EMP_NAM", nullable = false, length = 20)
	public String getSignEmpNam() {
		return signEmpNam;
	}

	/**
	 * 设置：審核時間
	 */
	public void setSignDate(Date signDate) {
		this.signDate = signDate;
	}

	/**
	 * 获取：審核時間
	 */

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Column(name = "SIGN_DATE", nullable = false, length = 20)
	public Date getSignDate() {
		return signDate;
	}

	/**
	 * 设置：附件Id
	 */
	public void setAttachids(String attachids) {
		this.attachids = attachids;
	}

	/**
	 * 获取：附件Id
	 */

	@Column(name = "ATTACHIDS", nullable = false, length = 20)
	public String getAttachids() {
		return attachids;
	}

	/**
	 * 设置：(撤崗的崗位編號，以英文逗號分隔開)
	 */
	public void setReattachids(String reattachids) {
		this.reattachids = reattachids;
	}

	/**
	 * 获取：(撤崗的崗位編號，以英文逗號分隔開)
	 */

	@Column(name = "REATTACHIDS", nullable = false, length = 20)
	public String getReattachids() {
		return reattachids;
	}

}