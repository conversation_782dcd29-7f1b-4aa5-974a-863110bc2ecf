package com.foxconn.ipebg.basics.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Date;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.basics.entity.TestitemEntity;
import com.foxconn.ipebg.basics.service.TestitemService;
import com.foxconn.ipebg.system.entity.Dict;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;


/**
 * 水質檢測項目維護
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-12-26 08:40:25
 */
@Controller
@RequestMapping("testitem")
public class TestitemController extends BaseController {

    @Autowired
    private TestitemService testitemService;
    @Autowired
    UserServiceUtil serviceUtil;
   
    @RequestMapping(value = "test", method = RequestMethod.GET)
    //@RequiresPermissions("basics:testitem:list")
    @ResponseBody
    public void test() {
    	
    	serviceUtil.createSerialno("xjrn");
    }
    /**
      * 方法描述: 列表信息
      * @Author: S6113712
      * @CreateDate:   2018-12-26 08:40:25
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:testitem:list")
    public String list() {
        //查询列表数据
        return "basics/testitem/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6113712
      * @CreateDate:   2018-12-26 08:40:25
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:testitem:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<TestitemEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        page = testitemService.search(page, filters);
        return getEasyUIData(page);
    }
    /**
     * 方法描述: 保存
     * @Author: S6113712
     * @CreateDate:   2018-12-26 08:40:25
     * @Return
     **/
   
    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("basics:testitem:add")
    @ResponseBody
    public String create(@Valid TestitemEntity testitem, Model model) {
        testitem.setNewRecord(true);
    	testitem.setCreateDate(new Date());
        testitemService.save(testitem);
        return "success";
    }
    
    /**
      * 方法描述: 添加跳轉
      * @Author: S6113712
      * @CreateDate:   2018-12-26 08:40:25
      * @Return
      **/

    @RequiresPermissions("basics:testitem:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("testitem", new TestitemEntity());
        model.addAttribute("action", "create");
        return "basics/testitem/listForm";
    }
    
     /**
     * 方法描述: 修改跳轉
     * @Author: S6113712
     * @CreateDate:   2018-10-06 11:04:39
     * @Return
     **/

   @RequiresPermissions("basics:testitem:update")
   @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
   public String updateForm(@PathVariable("id") String id, Model model) {
       model.addAttribute("testitem", testitemService.get(id));
       model.addAttribute("action", "update");
      return "basics/testitem/listForm";
   }
   
	/**
	 * 方法描述: 修改
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-11 10:55:00
	 * @Return
	 **/

   @RequestMapping(value = "update", method = RequestMethod.POST)
   @RequiresPermissions("basics:testitem:update")
   @ResponseBody
   public String update(@Valid TestitemEntity testitem,@RequestParam(value = "ids") String ids) {
	   testitem.setId(ids);
	   testitem.setUpdateDate(new Date());
	   testitemService.update(testitem);
	   
       return "success";
   }
    /**
      * 方法描述: 根據主鍵刪除
      * @Author: S6113712
      * @CreateDate:   2018-12-26 08:40:25
      * @Return
      **/

    @RequestMapping("delete/{id}")
    @RequiresPermissions("basics:testitem:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        testitemService.delete(id);
        return "success";
    }
    /**
      * 导出excel
      * @Author: S6113712
      * @CreateDate:   2018-12-26 08:40:25
      * @Return
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {}
    /**
     * 下拉框
     * @Author: S6113712
     * @CreateDate:   2018-01-14 08:40:25
     * @Return
     **/
	@ResponseBody
	@RequestMapping(value = "getTestItemsByType/{sampleTyp}")
	public List<TestitemEntity> getTestItemsByType(@PathVariable("sampleTyp") String sampleTyp,Model model,HttpServletRequest request) {
	  List<TestitemEntity> rst=testitemService.getTestItemsByType(sampleTyp);
      return rst;
      
	}
    /**
     * 下拉框
     * @Author: S6113712
     * @CreateDate:   2018-01-14 08:40:25
     * @Return
     **/
	@ResponseBody
	@RequestMapping(value = "getTestCost/{sampleTyp}")
	public List<TestitemEntity> getTestCost(@PathVariable("sampleTyp") String sampleTyp,Model model,HttpServletRequest request,	@RequestParam("testItems") String testItems) {
	  List<TestitemEntity> rst=testitemService.getTestCost(sampleTyp,testItems);
      return rst;
      
	}

}
