package com.foxconn.ipebg.common.exception;

import com.foxconn.ipebg.common.utils.ResultEnum;

/**
 * Company foxconn
 * Copyright (C) 2004-2018 All Rights Reserved.
 */
public class BusinessException extends RuntimeException {

    /** serialVersionUID*/
    private static final long serialVersionUID = -5212079010855161498L;

    public BusinessException(ResultEnum resultEnum){
        super(resultEnum.getMsg());
        this.message = resultEnum.getMsg();
        this.code = resultEnum.getCode();
    }

    //异常信息
    private String message;
    //異常編碼
    private int code;
    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }
}
