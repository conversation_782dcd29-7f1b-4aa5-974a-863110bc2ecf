package com.foxconn.ipebg.system.entity;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;

/**
 * 字典 entity
 *
 * <AUTHOR>
 * @date 2015年1月13日
 */
@Entity
@Table(name = "sys_dict")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE, region = "hibernateCache")
@DynamicUpdate
@DynamicInsert
public class Dict implements java.io.Serializable {

    // Fields
    private static final long serialVersionUID = 1L;
    private Integer id;
    private String label;
    private String value;
    private String type;
    private String description;
    private Integer sort;
    private String remark;
    private String delFlag;

    // Constructors

    /**
     * default constructor
     */
    public Dict() {
    }

    /**
     * full constructor
     */
    public Dict(String label, String value, String type, String description,
                Integer sort, String remark, String delFlag) {
        this.label = label;
        this.value = value;
        this.type = type;
        this.description = description;
        this.sort = sort;
        this.remark = remark;
        this.delFlag = delFlag;
    }

    // Property accessors
    @Id
    @SequenceGenerator(name = "generator", sequenceName = "sys_dict_qequence", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "generator")
    @Column(name = "id", unique = true, nullable = false)
    public Integer getId() {
        return this.id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "label")
    public String getLabel() {
        return this.label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    @Column(name = "value")
    public String getValue() {
        return this.value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Column(name = "type")
    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Column(name = "description")
    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Column(name = "sort")
    public Integer getSort() {
        return this.sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    @Column(name = "remark")
    public String getRemark() {
        return this.remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Column(name = "del_flag")
    public String getDelFlag() {
        return this.delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        return "Employee [id=" + id + ", label=" + label + ", value=" + value + "]";
    }
}