var dg;
var d;
$(function(){
    dg=$('#dg').datagrid({
        method: "get",
        url: ctx + '/ebscode/getListData',
        fit : true,
        fitColumns : true,
        border : false,
        idField : 'id',
        striped:true,
        pagination:true,
        rownumbers:true,
        pageNumber:1,
        pageSize : 20,
        sortName: 'codeTyp,codeId',
        sortOrder: 'asc,asc',
        pageList : [ 10, 20, 30, 40, 50 ],
        singleSelect:true,
        columns:[[
            {field:'id',title:'id',hidden:true},
            {field:'codeTyp',title:'流程類型',sortable:true,width:100},
            {field:'codeId',title:'排序',sortable:false,width:100},
            {field:'codeNam',title:'簽核節點名稱',sortable:false,width:100},
            {field:'required',title:'是否必選節點',sortable:false,width:100, formatter: function (value, row, index) {
                if (value == '1') {
                    return "必選";
                } else {
                    return "可選";
                }
            }},
            {field:'codeMemo',title:'備註',sortable:false,width:100}
        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        toolbar:'#tb'
    });
});

//弹窗增加
function add() {
    d=$("#dlg").dialog({
        title: '添加簽核節點',
        width: 300,
        height: 230,
        href: ctx + '/ebscode/create',
        maximizable:true,
        modal:true,
        buttons:[{
            text:'确认',
            handler:function(){
                $("#mainform").submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}

//删除
function del(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data){
        if (data){
            $.ajax({
                type:'get',
                url: ctx + "/ebscode/delete/"+row.id,
                success: function(data){
                    successTip(data,dg);
                }
            });
        }
    });
}

//弹窗修改
function upd(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    d=$("#dlg").dialog({
        title: '修改簽核節點',
        width: 300,
        height: 230,
        href: ctx + '/ebscode/update/'+row.id,
        maximizable:true,
        modal:true,
        buttons:[{
            text:'修改',
            handler:function(){
                $('#mainform').submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}

// 查詢流程類型下拉框
$("#filterCodeType").combobox({
    url:ctx+'/ebscode/getCodeTypes',
    valueField: "codeTyp",
    textField: "codeTyp",
    editable: false,
    method: 'get',
    panelHeight: 'auto'
});

//创建查询对象并查询
function cx(){
    var obj=$("#searchFrom").serializeObject();
    dg.datagrid('load',obj);
}