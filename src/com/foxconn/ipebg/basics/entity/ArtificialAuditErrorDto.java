package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import java.util.Date;

public class ArtificialAuditErrorDto implements Serializable {
	private static final long serialVersionUID = 1L;
	// 工號
	private String empNo;
	// 姓名
	private String empName;
	// 公司名稱
	private String company;
	// 崗位編號
	private String postRecno;
	// 崗位名稱
	private String postName;
	// 排班日期
	private Date shiftDate;
	// 班別代碼
	private String shiftNo;
	// 班別
	private String shiftName;
	// 上班時間
	private Date cardTime;
	// 異常狀態
	private String abnormalState;
	// 第一次異常結果
	private String abnormalResult1;
	// 第一次異常原因
	private String abnormalCauses1;
	// 第二次異常結果
	private String abnormalResult2;
	// 第二次異常原因
	private String abnormalCauses2;
	// 失敗原因
	private String errorLog;

	public String getEmpNo() {
		return empNo;
	}

	public void setEmpNo(String empNo) {
		this.empNo = empNo;
	}

	public String getEmpName() {
		return empName;
	}

	public void setEmpName(String empName) {
		this.empName = empName;
	}

	public String getCompany() {
		return company;
	}

	public void setCompany(String company) {
		this.company = company;
	}

	public String getPostRecno() {
		return postRecno;
	}

	public void setPostRecno(String postRecno) {
		this.postRecno = postRecno;
	}

	public String getPostName() {
		return postName;
	}

	public void setPostName(String postName) {
		this.postName = postName;
	}

	public Date getShiftDate() {
		return shiftDate;
	}

	public void setShiftDate(Date shiftDate) {
		this.shiftDate = shiftDate;
	}

	public String getShiftNo() {
		return shiftNo;
	}

	public void setShiftNo(String shiftNo) {
		this.shiftNo = shiftNo;
	}

	public String getShiftName() {
		return shiftName;
	}

	public void setShiftName(String shiftName) {
		this.shiftName = shiftName;
	}

	public Date getCardTime() {
		return cardTime;
	}

	public void setCardTime(Date cardTime) {
		this.cardTime = cardTime;
	}

	public String getAbnormalState() {
		return abnormalState;
	}

	public void setAbnormalState(String abnormalState) {
		this.abnormalState = abnormalState;
	}

	public String getAbnormalResult1() {
		return abnormalResult1;
	}

	public void setAbnormalResult1(String abnormalResult1) {
		this.abnormalResult1 = abnormalResult1;
	}

	public String getAbnormalCauses1() {
		return abnormalCauses1;
	}

	public void setAbnormalCauses1(String abnormalCauses1) {
		this.abnormalCauses1 = abnormalCauses1;
	}

	public String getAbnormalResult2() {
		return abnormalResult2;
	}

	public void setAbnormalResult2(String abnormalResult2) {
		this.abnormalResult2 = abnormalResult2;
	}

	public String getAbnormalCauses2() {
		return abnormalCauses2;
	}

	public void setAbnormalCauses2(String abnormalCauses2) {
		this.abnormalCauses2 = abnormalCauses2;
	}

	public String getErrorLog() {
		return errorLog;
	}

	public void setErrorLog(String errorLog) {
		this.errorLog = errorLog;
	}

}
