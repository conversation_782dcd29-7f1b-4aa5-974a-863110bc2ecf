<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>崗位申請明細表</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/eguarditemaply/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${EGuardItemAplyEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${EGuardItemAplyEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">崗位申請明細表</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${EGuardItemAplyEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${EGuardItemAplyEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${EGuardItemAplyEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${EGuardItemAplyEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        <div class="clear"></div>
		<table class="formList">
		   <tr>
                <td>
                    <table class="formList">
                     			<tr>
				<td>需求時間-開始時間：</td>
				<td>
				    					<input type="hidden" name="postStartDate" value="${eGuardItemAply.postStartDate}"/>
															<input id="postStartDate" name="postStartDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${eGuardItemAply.postStartDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>需求時間-結束時間：</td>
				<td>
				    										<input id="postEndDate" name="postEndDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${eGuardItemAply.postEndDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>崗位級別：</td>
				<td>
				    										<input id="postLevel" name="postLevel" class="easyui-validatebox" data-options="width: 150" value="${eGuardItemAply.postLevel }" />
									</td>
			</tr>
						<tr>
				<td>派駐保安公司：</td>
				<td>
				    										<input id="securityCom" name="securityCom" class="easyui-validatebox" data-options="width: 150" value="${eGuardItemAply.securityCom }" />
									</td>
			</tr>
						<tr>
				<td>實到崗人數：</td>
				<td>
				    										<input id="postPerNuR" name="postPerNuR" class="easyui-validatebox" data-options="width: 150" value="${eGuardItemAply.postPerNuR }" />
									</td>
			</tr>
						<tr>
				<td>生效時間：</td>
				<td>
				    										<input id="postEffectDate" name="postEffectDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${eGuardItemAply.postEffectDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>創建人：</td>
				<td>
				    										<input id="createBy" name="createBy" class="easyui-validatebox" data-options="width: 150" value="${eGuardItemAply.createBy }" />
									</td>
			</tr>
						<tr>
				<td>創建時間：</td>
				<td>
				    										<input id="createDate" name="createDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${eGuardItemAply.createDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>更新者：</td>
				<td>
				    										<input id="updateBy" name="updateBy" class="easyui-validatebox" data-options="width: 150" value="${eGuardItemAply.updateBy }" />
									</td>
			</tr>
						<tr>
				<td>更新時間：</td>
				<td>
				    										<input id="updateDate" name="updateDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${eGuardItemAply.updateDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>刪除標識：</td>
				<td>
				    										<input id="delFlag" name="delFlag" class="easyui-validatebox" data-options="width: 150" value="${eGuardItemAply.delFlag }" />
									</td>
			</tr>
						<tr>
				<td>主鍵：</td>
				<td>
				    										<input id="id" name="id" class="easyui-validatebox" data-options="width: 150" value="${eGuardItemAply.id }" />
									</td>
			</tr>
						<tr>
				<td>主表任務單號：</td>
				<td>
				    										<input id="serialno" name="serialno" class="easyui-validatebox" data-options="width: 150" value="${eGuardItemAply.serialno }" />
									</td>
			</tr>
						<tr>
				<td>編號：</td>
				<td>
				    										<input id="recno" name="recno" class="easyui-validatebox" data-options="width: 150" value="${eGuardItemAply.recno }" />
									</td>
			</tr>
						<tr>
				<td>廠區：</td>
				<td>
				    										<input id="site" name="site" class="easyui-validatebox" data-options="width: 150" value="${eGuardItemAply.site }" />
									</td>
			</tr>
						<tr>
				<td>區域：</td>
				<td>
				    										<input id="area" name="area" class="easyui-validatebox" data-options="width: 150" value="${eGuardItemAply.area }" />
									</td>
			</tr>
						<tr>
				<td>棟：</td>
				<td>
				    										<input id="block" name="block" class="easyui-validatebox" data-options="width: 150" value="${eGuardItemAply.block }" />
									</td>
			</tr>
						<tr>
				<td>層：</td>
				<td>
				    										<input id="floor" name="floor" class="easyui-validatebox" data-options="width: 150" value="${eGuardItemAply.floor }" />
									</td>
			</tr>
						<tr>
				<td>方位：</td>
				<td>
				    										<input id="position" name="position" class="easyui-validatebox" data-options="width: 150" value="${eGuardItemAply.position }" />
									</td>
			</tr>
						<tr>
				<td>詳細位置：</td>
				<td>
				    										<input id="location" name="location" class="easyui-validatebox" data-options="width: 150" value="${eGuardItemAply.location }" />
									</td>
			</tr>
						<tr>
				<td>崗位類別：</td>
				<td>
				    										<input id="postType" name="postType" class="easyui-validatebox" data-options="width: 150" value="${eGuardItemAply.postType }" />
									</td>
			</tr>
						<tr>
				<td>班制：</td>
				<td>
				    										<input id="postShift" name="postShift" class="easyui-validatebox" data-options="width: 150" value="${eGuardItemAply.postShift }" />
									</td>
			</tr>
						<tr>
				<td>崗位名稱：</td>
				<td>
				    										<input id="postName" name="postName" class="easyui-validatebox" data-options="width: 150" value="${eGuardItemAply.postName }" />
									</td>
			</tr>
						<tr>
				<td>人數(不含調休)：</td>
				<td>
				    										<input id="postPerNu" name="postPerNu" class="easyui-validatebox" data-options="width: 150" value="${eGuardItemAply.postPerNu }" />
									</td>
			</tr>
			                    </table>
                </td>
           </tr>
		   <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td>附件<font color="red">*</font></td>
                            <td colspan="9" class="td_style1"><span
                                    class="sl-custom-file"> <input type="button"
                                                                   value="点击上传文件" class="btn-file"/>
								<input id="attachidsUpload" name="attachidsUpload" type="file"
                                       onchange="uploadFile();" class="ui-input-file"/>
								</span> <input type="hidden" id="attachids"
                                               name="attachids" value="${EGuardItemAplyEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/newEsign/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="delAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${workFlowId}','崗位申請明細表','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                    </table>
                </td>
            </tr>
		    <tr>
                <td colspan="10" style="text-align:left;">
                    <table class="flowList" style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                        <tr>
                            <td>簽核時間</td>
                            <td>簽核節點</td>
                            <td>簽核主管</td>
                            <td>簽核意見</td>
                            <td>批註</td>
                            <td>簽核電腦IP</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                    <a href="javascript:;" id="btnSave" class="easyui-linkbutton" data-options="iconCls:'icon-add'"
                       style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                    <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                       style="width: 100px;" onclick="saveInfo(2);">提交</a>
                </td>
            </tr>
        </table>
	</div>
	<input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
	</form>
  </div>
<script src='${ctx}/static/js/audit/eguarditemaply.js?random=<%= Math.random()%>'></script>
</body>
</html>