package com.foxconn.ipebg.gen.service;

import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseService;
import com.foxconn.ipebg.gen.dao.SysGeneratorDao;
import com.foxconn.ipebg.gen.entity.TableEntity;
import com.foxconn.ipebg.gen.util.GenUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipOutputStream;

/**
 * 类SysGeneratorService的功能描述:
 * 代码生成器
 * @auther hxy
 * @date 2017-08-25 16:17:08
 */
@Service
@Transactional(readOnly=true)
public class SysGeneratorService extends BaseService<Map<String, Object>, Integer> {
	@Autowired
	private SysGeneratorDao generatorDao;

	public List<TableEntity> queryList(Map<String, Object> map){
		return generatorDao.queryList(map);
	}
	
	public int queryTotal(Map<String, Object> map){
      return generatorDao.queryTotal(map);
	}
	
	public Map<String, String> queryTable(String tableName){
		return generatorDao.queryTable(tableName);
	}
	
	public List<Map<String, String>> queryColumns(String tableName){
		return generatorDao.queryColumns(tableName);
	}

	/**
	 * 生成代码
	 * @param tableNames 表名集
	 * @param genType 生成方式，详见Constant枚举类 0本地 1 web形式
	 * @return
	 */
	public byte[] generatorCode(String[] tableNames, int genType){
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		ZipOutputStream zip = new ZipOutputStream(outputStream);

		for(String tableName : tableNames){
			//查询表信息
			Map<String, String> table = queryTable(tableName);
			//查询列信息
			List<Map<String, String>> columns = queryColumns(tableName);
			//取得根目录路径
			String rootPath=getClass().getResource("/").getFile().toString();
			//生成代码
			try {
				GenUtils.generatorCode(table, columns, zip, genType);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		IOUtils.closeQuietly(zip);
		return outputStream.toByteArray();
	}

	@Override
	public HibernateDao<Map<String, Object>, Integer> getEntityDao() {
		return generatorDao;
	}
}
