<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>臨時性安保服務申請單主表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
            type="text/javascript"></script>
    <script src='${ctx}/static/js/audit/tempSecurityAply.js?random=<%= Math.random()%>'></script>

    <script type="text/javascript">
        //信息
        var _info = {
            // 項目代碼
            projectCode: "ANQUANGUANLIPINGTAI",
            // 項目名稱
            projectName: "安全管理平臺",
            // 模塊代碼
            moduleCode: "BIAODANSHENQING1",
            // 模塊名稱
            moduleName: "臨時性安保服務單申請",
            // 登錄者賬戶
            userAccount: '${user.loginName}',
            // 登錄者姓名
            userName: '${user.name }',
            // 登錄者ip
            userIp: '${pageContext.request.remoteAddr}',
            // 登錄者session id
            userSessionId: '${pageContext.session.id}',
            // 服務器ip
            serverIp: '${pageContext.request.localAddr}'
        };
        setTimeout(function () {
            var script = $('<script type="text/javascript" ></ script>');
            script.attr('src', "http://rec.ipebg.efoxconn.com:8090/inject.js");
            script.appendTo($('head'));
        }, 10000);
    </script>
</head>
<body>
<form id="mainform" action="${ctx}/tempSecurityAply/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${aply.id}"/>
    <input id="serialno" name="aplyEntity.serialno" type="hidden"
           value="${aply.serialno }"/> <input id="createtime"
                                              name="aplyEntity.createDate" type="hidden"
                                              value="${aply.createDate }"/>
    <div class="commonW" style="width: 1000px">
        <div class="headTitle">臨時性安保服務申請單</div>
        <div class="position_L">
            任務編碼：
            <span id="" style="color:#999;">
                <c:choose>
                    <c:when test="${aply.serialno==null}">
                        提交成功后自動編碼
                    </c:when>
                    <c:otherwise>
                        ${aply.serialno}
                    </c:otherwise>
                </c:choose>
            </span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：
            <span style="color:#999;">
                <c:choose>
                    <c:when test="${aply.createDate==null}">
                        YYYY/MM/DD
                    </c:when>
                    <c:otherwise>
                        <fmt:formatDate value="${aply.createDate }" pattern="yyyy-MM-dd HH:mm:ss"/>
                    </c:otherwise>
                </c:choose>
            </span>
        </div>
        <div class="position_R margin_R">
            填單人：${aply.makerno}/${aply.makername}</div>
        <div class="clear"></div>
        <table class="formList" id="buildprojecttable">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="6%">申請單位&nbsp;<font color="red">*</font></td>
                            <td width="14%" class="td_style1"><input id="dpt"
                                                                     name="aplyEntity.dpt"
                                                                     class="easyui-validatebox"
                                                                     data-options="required:true"
                                                                     value="${aply.dpt }" readonly/></td>

                            <td width="6%">費用代碼&nbsp;<font color="red">*</font></td>
                            <td width="14%" class="td_style1"><input id="costId"
                                                                     name="aplyEntity.costId"
                                                                     class="easyui-validatebox"
                                                                     data-options="required:true"
                                                                     value="${aply.costId }" readonly/></td>
                            <td width="6%">聯繫方式&nbsp;<font color="red">*</font></td>
                            <td width="14%" class="td_style1"><input id="tel"
                                                                     name="aplyEntity.tel"
                                                                     class="easyui-validatebox"
                                                                     data-options="required:true"
                                                                     value="${aply.tel }" readonly/></td>
                        </tr>
                        <tr align="center">
                            <td width="6%">需求開始時間&nbsp;<font color="red">*</font></td>
                            <td width="14%" class="td_style1">
                                <input id="startTime" name="aplyEntity.startTime"
                                       class="easyui-datetimebox"
                                       required="required"
                                       data-options="showSeconds:false, width:140, disabled:true"
                                       value="${aply.startTime }"/>
                            </td>
                            <td>需求結束時間&nbsp;<font color="red">*</font></td>
                            <td colspan="1" class="td_style1">
                                <input id="endTime" name="aplyEntity.endTime"
                                       class="easyui-datetimebox"
                                       required="required"
                                       data-options="showSeconds:false, width:140, disabled:true"
                                       value="${aply.endTime}"/>
                            </td>

                            <td>需求人數&nbsp;<font color="red">*</font></td>
                            <td class="td_style1"><input id="demandPerNumber"
                                                                     name="aplyEntity.demandPerNumber"
                                                                     class="easyui-validatebox"
                                                                     data-options="required:true"
                                                                     value="${aply.demandPerNumber }" readonly/></td>
                        </tr>
                        <tr align="center">
                            <td>所屬法人</td>
                            <td colspan="1" class="td_style1"><input id="legalPerson"
                                                                     name="aplyEntity.legalPerson"
                                                                     class="easyui-combobox"
                                                                     data-options="panelHeight:'auto',editable:false,valueField:'value', textField:'label',url: '${ctx}/system/dict/getDictByType/guard_legalperson',width: 150,required:true,disabled:true"
                                                                     value="${aply.legalPerson }" readonly/></td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td colspan="5">安保服務原因</td>
                            <td colspan="5">
                                <textarea id="serviceReason"
                                          name="aplyEntity.serviceReason"
                                          class="easyui-validatebox" style="width:800px;height:60px;"
                                          rows="4" cols="4"
                                          data-options="required:true" readonly>${aply.serviceReason }</textarea>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">需服務地點</td>
                            <td colspan="8">
                                <textarea id="servicePlace"
                                          name="aplyEntity.servicePlace"
                                          class="easyui-validatebox" style="width:800px;height:60px;"
                                          rows="4" cols="4"
                                          data-options="required:true" readonly>${aply.servicePlace }</textarea>
                            </td>
                        </tr>
                        <tr>
                            <td>服務公司</td>
                            <td colspan="9" class="td_style1">
                                <input id="securityCom1" disabled
                                     name="aplyEntity.securityCom"
                                     class="easyui-combobox"
                                     data-options="panelHeight:'auto',valueField:'value', textField:'label',url: '${ctx}/system/dict/getDictByType/guard_securityCom',width: 150"
                                     style="width:150px;" value="${aply.securityCom}" />
                            </td>
                        </tr>
                        <tr>
                            <td>是否產生費用</td>
                            <td colspan="9" class="td_style1">
                                <input type="radio" disabled
<%--                                       id="manpowerDemand" --%>
                                       name="aplyEntity.manpowerDemand"
                                       value="1" onclick="kanchaCheck()"
                                    <c:if test="${aply.hasCost=='1'}">
                                        checked="checked"
                                    </c:if> />是
                                <input type="radio" disabled
<%--                                       id="manpowerDemand"--%>
                                    name="aplyEntity.manpowerDemand"
                                    value="0" onclick="kanchaCheck()"
                                    <c:if test="${aply.hasCost=='0'}">
                                         checked="checked"
                                    </c:if> />否
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄

                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}</td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;"><iframe
                                    id="qianheLogFrame" name="qianheLogFrame"
                                    src="${ctx}/eformsign/goChargeLog?serialNo=${aply.serialno}"
                                    width="100%"></iframe></td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnCancel" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'" style="width: 100px;"
                                   onclick="cancelApply();">取消申請</a>&nbsp;&nbsp;&nbsp;&nbsp; <a
                                    href="#" id="btnPrint" class="easyui-linkbutton"
                                    data-options="iconCls:'icon-redo'" style="width: 100px;"
                                    onclick="resubmit();">修改重提</a>
                                <div id="win"></div> <input type="hidden" id="itemNoDTO"
                                                            name="itemNoDTO" value="" />

                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="UserNo" name="UserNo" value="${user.loginName}"/>
    <input type="hidden" id="eserTyp" name="eserTyp" value=""/>
    <input type="hidden" id="eserNodeName" name="eserNodeName" value=""/>
    <div id="win" style="overflow:hidden"></div>
</form>
</body>
</html>
