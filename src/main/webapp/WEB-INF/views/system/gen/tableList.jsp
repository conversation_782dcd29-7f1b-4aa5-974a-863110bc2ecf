<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>代碼生成</title>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script type="text/javascript">
        //信息
        var _info = {
            // 項目代碼
            projectCode : "ANQUANGUANLIPINGTAI",
            // 項目名稱
            projectName : "安全管理平臺",
            // 模塊代碼
            moduleCode : "XITONGJIANKONG2",
            // 模塊名稱
            moduleName : "代碼生成",
            // 登錄者賬戶
            userAccount : '${user.loginName}',
            // 登錄者姓名
            userName : '${user.name }',
            // 登錄者ip
            userIp : '${pageContext.request.remoteAddr}',
            // 登錄者session id
            userSessionId : '${pageContext.session.id}',
            // 服務器ip
            serverIp : '${pageContext.request.localAddr}'
        };
        setTimeout(function(){
            var script = $('<script type="text/javascript" ></ script>');
            script.attr('src',"http://rec.ipebg.efoxconn.com:8090/inject.js");
            script.appendTo($('head'));
        },10000);
    </script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
    <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="gen();">生成</a>
</div>
<table id="dg"></table>
<div id="dlg"></div>
</body>
<script type="text/javascript">
    var dg;
    var d;
    $(function () {
        dg = $('#dg').datagrid({
            method: "get",
            url: '${ctx}/sys/generator/list',
            fit: true,
            fitColumns: true,
            border: false,
            striped: true,
            pagination: true,
            rownumbers: true,
            pageNumber: 1,
            pageSize: 20,
            pageList: [10, 20, 30, 40, 50],
            singleSelect: false,
            columns: [[
                {field: 'tableName', title: '表名', sortable: true, width: 100}
                ,
                {field: 'comments', title: '注釋', sortable: true, width: 100}
            ]],
            enableHeaderClickMenu: true,
            enableHeaderContextMenu: true,
            enableRowContextMenu: false,
            toolbar: '#tb'
        });
    });

    function gen() {
        var row = dg.datagrid('getSelections');
        if (rowIsNull(row)) return;
        var ids = new Array();
        for(var i=0;i<row.length;i++){
            ids.push(row[i].tableName);
        }
        window.location.href = "${ctx}/sys/generator/code?tables=" + JSON.stringify(ids);
    }
</script>
</html>
