package com.foxconn.ipebg.basics.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Date;

import com.foxconn.ipebg.basics.entity.*;
import com.foxconn.ipebg.basics.service.BsDptService;
import com.foxconn.ipebg.basics.service.BsPostHistoryService;
import com.foxconn.ipebg.system.service.UserRoleService;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;

import com.foxconn.ipebg.basics.service.BsPostService;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;
import com.foxconn.ipebg.system.entity.User;
import com.foxconn.ipebg.buessness.audit.service.EGuardAplyService;
import com.foxconn.ipebg.buessness.audit.entity.EGuardAplyEntity;

/**
 * 崗位明細表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-03-07 09:57:22
 */
@Controller
@RequestMapping("bspost")   
public class BsPostController extends BaseController {

    @Autowired
    private BsPostService bsPostService;

	@Autowired
	private BsPostHistoryService historyService;

	@Autowired
	private UserRoleService userRoleService;

	@Autowired
	private EGuardAplyService eGuardAplyService;

	@Autowired
	private BsDptService dptService;
   
    /**
      * 方法描述: 列表信息
      * @Author: ********
      * @CreateDate:   2019-03-07 09:57:22
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:bspost:list")
    public String list() {
        //查询列表数据
        return "basics/bspost/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: ********
      * @CreateDate:   2019-09-28 09:57:22
      * @Return
      **/
    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:bspost:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
		Page<BsPostEntity> page = getPage(request);
		List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
		String queryQun = request.getParameter("qun");
		List<Integer> roleList = userRoleService.getRoleIdList(UserUtil.getCurrentUser().getId());
		boolean hasQryAllPermission = roleList != null && roleList.contains(281);
		if (hasQryAllPermission) {
			// 拥有查询全部数据的权限
			if (StringUtils.isNotEmpty(queryQun)) {
				// 查询参数包含事业群
				//(select SERIALNO from E_GUARD_APLY where DPT_QUN = '
				List<String> serialNoList = new ArrayList<String>();
				List<PropertyFilter> aplyFilters = new ArrayList<PropertyFilter>();
				aplyFilters.add(new PropertyFilter("EQS_dptQun", queryQun));
				List<EGuardAplyEntity> aplyList = eGuardAplyService.search(aplyFilters);
				for (EGuardAplyEntity aply : aplyList) {
					serialNoList.add(aply.getSerialno());
				}
				if (serialNoList.size() == 0) {
					// 避免查詢的事業群沒有申請過時產生異常
					serialNoList.add("");
				}
				PropertyFilter serialNoFilter = new PropertyFilter("INS_serialno", serialNoList);
				filters.add(serialNoFilter);
			}
		} else {
			// 只能查询自己部门申请的岗位
			User user = UserUtil.getCurrentUser();
			if (user != null) {
				PropertyFilter filter = new PropertyFilter("EQS_dptId", user.getDelFlag());
				filters.add(filter);
			}
		}
		page = bsPostService.search(page, filters);
		//bsPostService.searchPosts(request.getParameter("filter_EQS_recno"), request.getParameter("filter_EQS_postName"), defaultQun, page);
        return getEasyUIData(page);
    }
    
    /**
	 * 方法描述: 獲取崗位（匹配培训期）
	 * 
	 * @Author: ********
	 * @CreateDate: 2019-01-15 18:34:27
	 * @Return
	 **/
	@RequestMapping(value = "getPostList", method = RequestMethod.GET)
	@ResponseBody
	public List<BsPostEntity> getPostList(
			@RequestParam("postName") String postName/*,
			@RequestParam("empName") String empName*/) {

		// BsTraineeEntity bsTrainee
		// HttpServletRequest request
		// String empNo= request.getParameter("empNo");
		// String empName=request.getParameter("empName");
		BsPostEntity post = new BsPostEntity();
		post.setPostName(postName);
		//post.setEmpNo(empNo);
		String com = "";
		User user = UserUtil.getCurrentUser();
		if (user != null && user.getSecurityCom() != null) {
			com = user.getSecurityCom().replaceAll(",", "','");
		}
		List<BsPostEntity> postList = bsPostService
				.findPostList(post, com);
		return postList;
	}
    /**
     * 方法描述: 保存
     * @Author: ********
     * @CreateDate:   2019-03-07 09:57:22
     * @Return
     **/
   
    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("basics:bspost:add")
    @ResponseBody
    public String create(@Valid BsPostEntity bsPost, Model model) {
        bsPost.setNewRecord(true);
    	bsPost.setCreateDate(new Date());
        bsPostService.save(bsPost);
        return "success";
    }
    
    /**
      * 方法描述: 添加跳轉
      * @Author: ********
      * @CreateDate:   2019-03-07 09:57:22
      * @Return
      **/
    @RequiresPermissions("basics:bspost:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("bsPost", new BsPostEntity());
        model.addAttribute("action", "create");
        return "basics/bspost/listForm";
    }
    
     /**
     * 方法描述: 修改跳轉
     * @Author: ********
     * @CreateDate:   2018-10-06 11:04:39
     * @Return
     **/
   @RequiresPermissions("basics:bspost:update")
   @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
   public String updateForm(@PathVariable("id") String id, Model model) {
       model.addAttribute("bsPost", bsPostService.get(id));
       model.addAttribute("action", "update");
      return "basics/bspost/listForm";
   }
   
	/**
	 * 方法描述: 修改
	 * 
	 * @Author: ********
	 * @CreateDate: 2018-10-11 10:55:00
	 * @Return
	 **/
   @RequestMapping(value = "update", method = RequestMethod.POST)
   @RequiresPermissions("basics:bspost:update")
   @ResponseBody
   public String update(@Valid BsPostEntity bsPost,@RequestParam(value = "ids") String ids) {
	   bsPost.setId(ids);
	   bsPost.setUpdateDate(new Date());
	   bsPostService.update(bsPost);
	   // 保存修改記錄
	   BsPostHistoryEntity record = bsPost.getHistoryEntity();
	   record.setCreateBy(getCurUser().getLoginName());
	   record.setApplyEmpNo(getCurUser().getLoginName());
	   record.setApplyEmpName(getCurUser().getName());
	   BsDptEntity dpt = dptService.findById(getCurUser().getDelFlag());
	   if (dpt != null) {
		   record.setApplyEmpBu(dpt.getDptBu());
	   }
	   record.setApplyType("手動修改");
	   historyService.save(record);
       return "success";
   }
    /**
      * 方法描述: 根據主鍵刪除
      * @Author: ********
      * @CreateDate:   2019-03-07 09:57:22
      * @Return
      **/

    @RequestMapping("delete/{id}")
    @RequiresPermissions("basics:bspost:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        bsPostService.delete(id);
        return "success";
    }
    /**
      * 导出excel
      * @Author: ********
      * @CreateDate:   2019-03-07 09:57:22
      * @Return
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {}
	/**
	 * 方法描述: 獲取班別信息
	 * 
	 * @Author: ********
	 * @CreateDate: 2019-03-08 18:34:27
	 * @Return
	 **/
    @RequestMapping(value = "getBsPost/{recno}", method = RequestMethod.GET)///{shiftNo}
    //@RequestMapping("getShiftList")
	@ResponseBody
	public BsPostEntity getBsPost(@PathVariable("recno") String recno) {
		BsPostEntity bsPostEntity = bsPostService.findByRecno(recno);
		return bsPostEntity;
	}
    /**
	 * 方法描述: 獲取撤崗崗位信息
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2019-01-15 18:34:27
	 * @Return
	 **/
	@RequestMapping(value = "getCancelPostList", method = RequestMethod.GET)
	@ResponseBody
	public List<BsPostEntity> getCancelPostList(HttpServletRequest request) {
		Page<BsPostEntity> page = new Page<BsPostEntity>();
		page.setPageNo(1);
		page.setPageSize(Page.MAXSIZE);
		page.setOrderBy("recno");
		page.setOrder(Page.ASC);
		List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
		List<Integer> roleList = userRoleService.getRoleIdList(UserUtil.getCurrentUser().getId());
		String serialno = request.getParameter("serialnoQry");
		if (StringUtils.isNotEmpty(serialno)) {
			filters.add(new PropertyFilter("EQS_recno", serialno));
		}
		boolean hasQryAllPermission = roleList != null && roleList.contains(281);
		if (!hasQryAllPermission) {
			// 只能查询自己部门申请的岗位
			User user = UserUtil.getCurrentUser();
			if (user != null) {
				PropertyFilter filter = new PropertyFilter("EQS_dptId", user.getDelFlag());
				filters.add(filter);
			}
		}
		page = bsPostService.search(page, filters);
		return page.getResult();
	}
	/**
	 * 方法描述: 添加選擇撤崗崗位   跳轉
	 * 
	 * @Author: ********
	 * @CreateDate: 2019-01-15 18:34:27
	 * @Return
	 **/

	@RequestMapping(value = "cancelPostList", method = RequestMethod.GET)
	public String createForm4(Model model) {
		return "basics/bspost/cancelPostList";
		
	}
}
