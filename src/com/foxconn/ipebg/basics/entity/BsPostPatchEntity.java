package com.foxconn.ipebg.basics.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;


/**
 * 崗位临时增撤记录
 *
 * <AUTHOR>
 * @date 2022-07-20
 */
@Entity
@Table(name = "V_BS_POST_PATCH")
@DynamicUpdate
@DynamicInsert
public class BsPostPatchEntity extends DataEntity<BsPostPatchEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    //主表任務單號
    private String serialno;
    //編號
    private String recno;
    //班制
    private String postShift;
    //人數(不含調休)
    private Integer postPerNu;
    //開始時間
    private Date startDate;
    //結束時間
    private Date endDate;

    /**
     * 设置：主表任務單號
     */
    public void setSerialno(String serialno) {
        this.serialno = serialno;
    }

    /**
     * 获取：主表任務單號
     */

    @Column(name = "SERIALNO", nullable = false, length = 20)
    public String getSerialno() {
        return serialno;
    }

    /**
     * 设置：編號
     */
    public void setRecno(String recno) {
        this.recno = recno;
    }

    /**
     * 获取：編號
     */

    @Column(name = "RECNO", nullable = false, length = 20)
    public String getRecno() {
        return recno;
    }

    /**
     * 设置：班制
     */
    public void setPostShift(String postShift) {
        this.postShift = postShift;
    }

    /**
     * 获取：班制
     */

    @Column(name = "POST_SHIFT", nullable = false, length = 20)
    public String getPostShift() {
        return postShift;
    }

    /**
     * 设置：人數(不含調休)
     */
    public void setPostPerNu(Integer postPerNu) {
        this.postPerNu = postPerNu;
    }

    /**
     * 获取：人數(不含調休)
     */
    @Column(name = "POST_PER_NU", nullable = false, length = 20)
    public Integer getPostPerNu() {
        return postPerNu;
    }

    /**
     * 设置：需求時間-開始時間
     */
    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    /**
     * 获取：需求時間-開始時間
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "POST_START_DATE", nullable = false, length = 20)
    public Date getStartDate() {
        return startDate;
    }

    /**
     * 设置：需求時間-結束時間
     */
    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    /**
     * 获取：需求時間-結束時間
     */

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "POST_END_DATE", nullable = false, length = 20)
    public Date getEndDate() {
        return endDate;
    }
}
