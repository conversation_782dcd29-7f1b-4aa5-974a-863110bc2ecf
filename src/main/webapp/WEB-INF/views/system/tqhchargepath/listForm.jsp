<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>簽核路徑表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/tqhchargepath/${action}" method="post">
    <table class="formTable">
        <input id="ids" name="ids" type="hidden" value="${tQhChargepath.id }" />
        <tr>
            <td>廠區：</td>
            <td>
                <input id="factoryid" name="factoryid" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhChargepath.factoryid }"/>
            </td>
            <td></td>
        </tr>
        <tr>
            <td>單位代碼：</td>
            <td>
                <input id="deptno" name="deptno" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhChargepath.deptno }"/>
            </td>
        </tr>
        <tr>
            <td>課級主管：</td>
            <td>
                <input id="kchargeno" name="kchargeno" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhChargepath.kchargeno }" onblur="getUserNameByEmpno(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="kchargename" name="kchargename" class="easyui-validatebox" data-options="width: 150" readonly="true"
                       value="${tQhChargepath.cchargename }"/></td>
        </tr>
        <%--<tr>
            <td>課級主管名稱：</td>
            <td>
                <input id="kchargename" name="kchargename" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhChargepath.kchargename }"/>
            </td>
        </tr>--%>
        <tr>
            <td>部級主管：</td>
            <td>
                <input id="bchargeno" name="bchargeno" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhChargepath.bchargeno }" onblur="getUserNameByEmpno(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="bchargename" name="bchargename" class="easyui-validatebox" data-options="width: 150" readonly="true"
                       value="${tQhChargepath.bchargename }"/></td>
        </tr>
        <%--<tr>
            <td>部級主管名稱：</td>
            <td>
                <input id="bchargename" name="bchargename" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhChargepath.bchargename }"/>
            </td>
        </tr>--%>
        <tr>
            <td>廠級主管：</td>
            <td>
                <input id="cchargeno" name="cchargeno" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhChargepath.cchargeno }" onblur="getUserNameByEmpno(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="cchargename" name="cchargename" class="easyui-validatebox" data-options="width: 150" readonly="true"
                       value="${tQhChargepath.cchargename }"/></td>
        </tr>
        <%--<tr>
            <td>廠級主管名稱：</td>
            <td>
                <input id="cchargename" name="cchargename" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhChargepath.cchargename }"/>
            </td>
        </tr>--%>
        <tr>
            <td>製造處級主管：</td>
            <td>
                <input id="zchargeno" name="zchargeno" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhChargepath.zchargeno }" onblur="getUserNameByEmpno(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="zchargename" name="zchargename" class="easyui-validatebox" data-options="width: 150" readonly="true"
                       value="${tQhChargepath.zchargename }"/></td>
        </tr>
        <%--<tr>
            <td>製造處級主管名稱：</td>
            <td>
                <input id="zchargename" name="zchargename" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhChargepath.zchargename }"/>
            </td>
        </tr>--%>
        <tr>
            <td>製造總處級主管：</td>
            <td>
                <input id="zcchargeno" name="zcchargeno" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhChargepath.zcchargeno }" onblur="getUserNameByEmpno(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="zcchargename" name="zcchargename" class="easyui-validatebox" data-options="width: 150" readonly="true"
                       value="${tQhChargepath.zcchargename }"/></td>
        </tr>
        <%--<tr>
            <td>製造總處級主管名稱：</td>
            <td>
                <input id="zcchargename" name="zcchargename" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhChargepath.zcchargename }"/>
            </td>
        </tr>--%>
        <tr>
            <td>產品處級主管：</td>
            <td>
                <input id="pcchargeno" name="pcchargeno" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhChargepath.pcchargeno }" onblur="getUserNameByEmpno(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="pcchargename" name="pcchargename" class="easyui-validatebox" data-options="width: 150" readonly="true"
                       value="${tQhChargepath.pcchargename }"/></td>
        </tr>
        <%--<tr>
            <td>產品處級主管名稱：</td>
            <td>
                <input id="pcchargename" name="pcchargename" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhChargepath.pcchargename }"/>
            </td>
        </tr>--%>
        <tr>
            <td>事業群級主管：</td>
            <td>
                <input id="sychargeno" name="sychargeno" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhChargepath.sychargeno }" onblur="getUserNameByEmpno(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="sychargename" name="sychargename" class="easyui-validatebox" data-options="width: 150" readonly="true"
                       value="${tQhChargepath.sychargename }"/></td>
        </tr>
        <%--<tr>
            <td>事業群級主管名稱：</td>
            <td>
                <input id="sychargename" name="sychargename" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhChargepath.sychargename }"/>
            </td>
        </tr>--%>
        <%--<tr>
            <td>是否有效：</td>
            <td>
                <input id="isvalid" name="isvalid" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhChargepath.isvalid }"/>
            </td>
        </tr>--%>
        <%--<tr>
            <td>是否鎖定：</td>
            <td>
                <input id="islock" name="islock" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhChargepath.islock }"/>
            </td>
        </tr>--%>
        <tr>
            <td>創建人工號：</td>
            <td>
                <input id="creator" name="creator" class="easyui-validatebox" data-options="width: 150"
                       value="${tQhChargepath.creator }" onblur="getUserNameByEmpno(this);"/>
            </td>
            <td>姓名：</td>
            <td><input id="creatorname" name="creatorname" class="easyui-validatebox" data-options="width: 150" readonly="true"
                       value=""/></td>
        </tr>
        <%--<tr>
            <td>創建時間：</td>
            <td>
                <input id="createtime" name="createtime" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
                       data-options="width: 150" value="<fmt:formatDate value="${tQhChargepath.createtime}"/>"/>
            </td>
        </tr>--%>
    </table>
</form>
</div>
<script src='${ctx}/static/js/system/tqhchargepath.js?"+Math.random()"'></script>
<script type="text/javascript">
    //提交表单
    $('#mainform').form({
        onSubmit: function () {
            var isValid = $(this).form('validate');
            return isValid;	// 返回false终止表单提交
        },
        success: function (data) {
            successTip(data, dg, d);
        }
    });

    //獲取廠區
    $.get('${ctx}/tqhfactoryidconfig/allFactorys/',
        function (result) {
            $("#factoryid").combobox({
                data: result,
                valueField: "factoryid",
                textField: "factoryname",
                editable: false,
                panelHeight: "auto",
                loadFilter: function (data) {
                    data.unshift({factoryid: '', factoryname: '請選擇'});
                    return data;
                }
            });
        });
</script>
</body>
</html>