package com.foxconn.ipebg.basics.controller;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;


import com.foxconn.ipebg.basics.entity.*;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.system.entity.User;
import com.foxconn.ipebg.system.utils.Excel2007Utils;
import com.foxconn.ipebg.system.utils.FileDownloadUtil;
import com.foxconn.ipebg.system.utils.ImportExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;

import com.foxconn.ipebg.basics.service.BsSecPerService;
import org.springframework.web.multipart.MultipartFile;


/**
 * 警衛人員基本資料
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-12-21 14:54:46
 */
@Controller
@RequestMapping("bssecper") 
public class BsSecPerController extends BaseController {

    @Autowired
    private BsSecPerService bsSecPerService;

    private static final String TPLFILEPATH = "/static/resources/templet";

    private static final String BATCHIMPORTTPL = "guardinfo.xlsx";

    private static final String filePath = "/static/resources/download";

    /**
      * 方法描述: 列表信息
      * @Author: S6113712
      * @CreateDate:   2018-12-21 14:54:46
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:bsdpt:list")
    public String list() {
        //查询列表数据
        return "basics/bssecper/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6113712
      * @CreateDate:   2018-12-21 14:54:46
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:bsdpt:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<BsSecPerEntity> page = getPage(request);
        //name = name == null ? "" : URLDecoder.decode(name, "UTF-8");
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
//        User user = UserUtil.getCurrentUser();
//        if (user != null) {
//            // 按保安公司查询
//            PropertyFilter filter = new PropertyFilter("INS_company", user.getSecurityComs());
//            filters.add(filter);
//        }
        page = bsSecPerService.search(page, filters);
        return getEasyUIData(page);
    }
    /**
      * 导出excel
      * @Author: S6113712
      * @CreateDate:   2018-12-21 14:54:46
      * @Return
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {}

    /**
     * List<Object>轉換為List<Map<String, Object>>
     * @param strs strs
     * @param result result
     * @return result
     */
    public static List<Map<String, Object>> listToMapList(String[] strs, List<?> result) {
        List<Map<String, Object>> mapList = new ArrayList<Map<String, Object>>();
        for (int i = 0; i < result.size(); i++) {
            Object[] objs = (Object[]) result.get(i);
            Map<String, Object> map = new HashMap<String, Object>();
            for (int j = 0; j < strs.length; j++) {
                String[] strss = strs[j].split(";");
                if (strss.length == 1) {
                    map.put(strss[0], objs[j].toString());
                } else if (strss.length == 2) {
                    if (strss[1].toUpperCase().equals("D")) {
                        map.put(strss[0], getDateStr((Date) objs[j], null));
                    }
                }
            }
            mapList.add(map);
        }
        return mapList;
    }
    /**
     * @param date date
     * @param dateFormat dateFormat
     * @return dateFormat
     */
    public static String getDateStr(Date date, String dateFormat) {
        String df = dateFormat;
        String strNow;
        if (df == null || df.equals("")) {
            df = "yyyy/MM/dd HH:mm:ss";
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(df);
            strNow = sdf.format(date);
        } catch (Exception e) {
            strNow = "";
        }
        return strNow;
    }
	/**
	 * 方法描述: 獲取警衛人員信息
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-03-08 10:34:27
	 * @Return
	 **/
    @RequestMapping(value = "getBsSecPer/{empNo}", method = RequestMethod.GET)
    //@RequestMapping("getShiftList")
	@ResponseBody
	public BsSecPerEntity getBsSecPer(@PathVariable("empNo") String empNo) {
		BsSecPerEntity bsSecPer = bsSecPerService.findByEmpNo(empNo);
		return bsSecPer;
	}

    /**
     * 方法描述: 添加跳轉
     * @Author: S6114893
     * @CreateDate:   2020-08-26 10:12:01
     * @Return
     **/
    @RequiresPermissions("basics:secper:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("perInfo", new PerInfoEntity());
        model.addAttribute("action", "create");
        return "basics/bssecper/listForm";
    }

    /**
     * 方法描述: 保存
     * @Author: S6113712
     * @CreateDate:   2019-04-16 10:12:01
     * @Return
     **/

    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("basics:secper:add")
    @ResponseBody
    public String create(@Valid BsSecPerEntity perInfo, Model model) {
        perInfo.setNewRecord(true);
        perInfo.setCreateDate(new Date());
        bsSecPerService.save(perInfo);
        return "success";
    }

    /**
     * 方法描述: 修改跳轉
     * @Author: S6114893
     * @CreateDate:   2020-08-26 10:12:01
     * @Return
     **/
    @RequiresPermissions("basics:secper:update")
    @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
    public String updateForm(@PathVariable("id") String id, Model model) {
        model.addAttribute("perInfo", bsSecPerService.get(id));
        model.addAttribute("action", "update");
        return "basics/bssecper/listForm";
    }

    /**
     * 方法描述: 修改
     *
     * @Author: S6114893
     * @CreateDate: 2020-08-26 10:12:01
     * @Return
     **/

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @RequiresPermissions("basics:secper:update")
    @ResponseBody
    public String update(@Valid BsSecPerEntity perInfo,@RequestParam(value = "ids") String ids) {
        perInfo.setId(ids);
        perInfo.setUpdateDate(new Date());
        bsSecPerService.update(perInfo);
        perInfo.setUpdateBy(UserUtil.getCurrentUser().getLoginName());
        bsSecPerService.update(perInfo);

        return "success";
    }

    /**
     * 方法描述: 根據主鍵刪除
     * @Author: S6114893
     * @CreateDate:   2020-08-26 10:12:01
     * @Return
     **/
    @RequestMapping("delete/{id}")
    @RequiresPermissions("basics:secper:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        bsSecPerService.delete(id);
        return "success";
    }

    @RequestMapping(value = "downLoad/batchImportTpl", method = RequestMethod.GET)
// @RequiresPermissions("other:tqhraymachinescrap:add")
//@ResponseBody
    public void batchImportTpl(HttpServletRequest request,
                               HttpServletResponse response) {
        fileDownload(request, response, BATCHIMPORTTPL);
    }

    private void fileDownload(HttpServletRequest request,
                              HttpServletResponse response, String fileName) {
        String path = request.getSession().getServletContext()
                .getRealPath(TPLFILEPATH);
        try {
            FileDownloadUtil.fileDownload(request, response, path, fileName);// 下載點位信息模板
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 批量導入
     *
     * @param file
     *            文檔
     * @param isExcel2003
     *            是否2003
     * @return
     */
    @RequestMapping(value = "upload", method = RequestMethod.POST)
    @ResponseBody
    public String upload(MultipartFile file, boolean isExcel2003, Model model,
                         HttpServletRequest request) {

        if (file.equals("") || file.getSize() <= 0) {
            return Constant.FAIL;
        } else {
            String result = null;
            try {

//			result = artificialAuditService.batchImport(
//					file.getInputStream(), isExcel2003);
                result = this.batchImport(
                        file.getInputStream(), isExcel2003);

            } catch (IOException e) {
                e.printStackTrace();
            }

            if (Constant.RESULT.CODE_YES.getValue().equals(result)) {
                return Constant.SUCCESS;
            } else {
                return result;
            }
        }

    }

// *******************************導入相關邏輯**********
    /**
     * 批量導入
     *
     * @param in
     *            excel文件輸入流
     * @param isExcel2003
     *            是否excel2003
     * @return 是否成功
     */
    public String batchImport(InputStream in, boolean isExcel2003) {
        try {
            List<BsSecPerErrorDto> perinfoErrorDtos=null;
            ImportExcelUtil poi = new ImportExcelUtil();
            int success = 0, failed = 0;
            // 讀取上傳的excel file
            List<List<String>> list = poi.read(in, isExcel2003);
            // 判断需要的字段在Excel中是否都存在
            boolean isExist = true;
            Map<String, String> fieldMap = getFieldMap();
            Map<String, Integer> fieldIndexMap = new HashMap();
            List<String> excelFieldList = list.get(0);
            // 第一列是序號不需要
            for (int i = 1; i < excelFieldList.size(); i++) {
                if (!fieldMap.containsKey(excelFieldList.get(i))) {
                    isExist = false;
                    break;
                }
                fieldIndexMap.put(fieldMap.get(excelFieldList.get(i)), i);
            }
            // 如果有列名不存在，则抛出异常，提示错误
            if (!isExist) {
                return "有列名不存在，請對照模板";
            }
            //導入數量限制
//			if (list.size()-1 > 10) {
//				return "資料導入不能大於100筆;";
//			}
            //開始導入
            perinfoErrorDtos = listToPerInfoErrorDto(
                    list, 1, fieldIndexMap);

            if (perinfoErrorDtos.size()>0)
            {
                UserUtil.getSession().setAttribute(BsSecPerService.PERINFOERRORDTOS,perinfoErrorDtos);
                return "導入成功"+(list.size()-1-perinfoErrorDtos.size())+"筆，失敗"+perinfoErrorDtos.size()+"筆!";
            }


        } catch (Exception e) {
            this.logger.info(e.getMessage(), e);
            return Constant.RESULT.CODE_NO.getValue();
        }
        return Constant.RESULT.CODE_YES.getValue();
    }

    public HashMap<String, String> getFieldMap() {
        HashMap<String, String> fieldMap = new HashMap<String, String>();

        fieldMap.put("工號", "empNo");
        fieldMap.put("姓名", "empName");
        fieldMap.put("所屬公司", "company");
        fieldMap.put("身份證號", "psnId");

        return fieldMap;
    }

    public LinkedHashMap<String, String> getErrorFieldMap() {
        LinkedHashMap<String, String> fieldMap = new LinkedHashMap<String, String>();

        fieldMap.put("empNo", "工號");
        fieldMap.put("empName", "姓名");
        fieldMap.put("company", "所屬公司");
        fieldMap.put("psnId", "身份證號");
        fieldMap.put("errorLog", "導入失敗原因");

        return fieldMap;
    }

    /**
     * @param list
     *            列表
     * @param startIndex
     *            開始的行
     * @param fieldIndexMap
     *            list中的index和类的英文属性的对应关系Map
     * @return
     * @throws IllegalAccessException
     * @throws InstantiationException
     */
    @Transactional
    public List<BsSecPerErrorDto> listToPerInfoErrorDto(
            List<List<String>> list, int startIndex,
            Map<String, Integer> fieldIndexMap) {
        // 定义要返回的list
        List<BsSecPerErrorDto> resultList = new ArrayList<BsSecPerErrorDto>();
        for (int i = startIndex; i < list.size(); i++) {

            String empNo = list.get(i).get(1);
            String empName = list.get(i).get(2);
            String psnID = list.get(i).get(3);
            String company = list.get(i).get(4);
            if (empNo.length() == 0 || empName.length() == 0 || psnID.length() != 18 || company.length() == 0) {
                BsSecPerErrorDto entity = new BsSecPerErrorDto();
                entity.setEmpNo(empNo);
                entity.setEmpName(empName);
                entity.setPsnId(psnID);
                entity.setCompany(company);
                if (empNo.length() == 0) {
                    entity.setErrorLog("工號為空");
                } else if (empName.length() == 0) {
                    entity.setErrorLog("姓名為空");
                } else if (psnID.length() != 18) {
                    entity.setErrorLog("身份證號位數不對");
                } else {
                    entity.setErrorLog("所屬公司為空");
                }
                resultList.add(entity);
            } else {
                BsSecPerEntity perInfoEntity = new BsSecPerEntity();

                perInfoEntity.setEmpNo(empNo);
                perInfoEntity.setEmpName(empName);
                perInfoEntity.setCompany(company);
                perInfoEntity.setPsnId(psnID);

                bsSecPerService.save(perInfoEntity);
            }
        }
        return resultList;
    }

    /**
     * 方法描述: 下載批量上傳異常信息
     *
     * @Author: S6113712
     * @CreateDate: 2019-02-13 18:57:46
     * @Return
     **/

    @RequestMapping(value = "downLoad/errorExcel", method = RequestMethod.GET)
// @RequiresPermissions("other:tqhraymachinescrap:add")
    @ResponseBody
    public void errorExcelDownload(HttpServletRequest request,
                                   HttpServletResponse response) throws IOException {
        List<BsSecPerErrorDto> errorDtos = (List<BsSecPerErrorDto>) request
                .getSession().getAttribute(
                        BsSecPerService.PERINFOERRORDTOS);
        if (errorDtos != null) {
            // 導出excel2007
            LinkedHashMap<String, String> map = this
                    .getErrorFieldMap();

            String name = this.getCurUser().getLoginName() + "In";
            String tplPath = request.getSession().getServletContext()
                    .getRealPath(filePath);
            File file = Excel2007Utils.createFile(errorDtos, map, tplPath,
                    name, null);// 生成xlsx文件
            String fileName = file.getName();
            Excel2007Utils.exportFile(response, tplPath, fileName);// 下载生成的xlsx文件
            request.getSession().removeAttribute(
                    BsSecPerService.PERINFOERRORDTOS);
        }
    }
}
