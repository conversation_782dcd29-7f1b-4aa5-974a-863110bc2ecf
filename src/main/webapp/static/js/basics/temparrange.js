var dg;
var d;
$(function(){
    dg=$('#dg').datagrid({
        method: "get",
        url:ctx+'/temparrange/list',
        fit : true,
        fitColumns : true,
        border : false,
        idField : 'id',
        striped:true,
        pagination:true,
        rownumbers:true,
        pageNumber:1,
        pageSize : 20,
        pageList : [ 10, 20, 30, 40, 50 ],
        singleSelect:true,
        columns:[[
							                    { field: 'id', title: '主鍵',hidden:true},
											                    { field: 'postRecno', title: '崗位編號',sortable:true,width:100}
					,
																                    { field: 'postName', title: '崗位名稱',sortable:true,width:100}
					,
																                    { field: 'postShift', title: '班制',sortable:true,width:100}
					,
																                    { field: 'location', title: '詳細位置（小崗位名稱）',sortable:true,width:100}
					,
																                    { field: 'shiftDate', title: '排班日期',sortable:true,width:100}
					,
																                    { field: 'shiftNo', title: '班別代碼',sortable:true,width:100}
					,
																                    { field: 'shiftName', title: '班別',sortable:true,width:100}
					,
																                    { field: 'startTime', title: '開始時間',sortable:true,width:100}
					,
																                    { field: 'endTime', title: '結束時間',sortable:true,width:100}
					,
																                    { field: 'empNo', title: '工號',sortable:true,width:100}
					,
																                    { field: 'empName', title: '姓名',sortable:true,width:100}
					,
																                    { field: 'createBy', title: '創建人',sortable:true,width:100}
					,
																                    { field: 'createDate', title: '創建時間',sortable:true,width:100}
					,
																                    { field: 'updateBy', title: '更新者',sortable:true,width:100}
					,
																                    { field: 'updateDate', title: '更新時間',sortable:true,width:100}
					,
																                    { field: 'delFlag', title: '刪除標識',sortable:true,width:100}
					,
																                    { field: 'checkState', title: '比對-狀態：正常為0',sortable:true,width:100}
					,
																                    { field: 'checkCauses', title: '比對-異常原因',sortable:true,width:100}
					,
																                    { field: 'createIp', title: '創建人IP',sortable:true,width:100}
												        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        rowTooltip: true,
        toolbar:'#tb'
    });
});

//弹窗增加
function add() {
    d=$("#dlg").dialog({
        title: '添加異常人員信息',
        width: 380,
        height: 380,
        href:ctx+'/temparrange/create',
        maximizable:true,
        modal:true,
        buttons:[{
            text:'确认',
            handler:function(){
                $("#mainform").submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}

//删除
function del(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data){
        if (data){
	$.ajax({
                type:'get',
                url:ctx+"/temparrange/delete/"+row.id,
                success: function(data){
                    successTip(data,dg);
                }
            });
        }
    });
}

//弹窗修改
function upd(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    d=$("#dlg").dialog({
        title: '修改異常人員信息',
        width: 380,
        height: 340,
        href:ctx+'/temparrange/update/'+row.id,
        maximizable:true,
        modal:true,
        buttons:[{
            text:'修改',
            handler:function(){
                $('#mainform').submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}
//创建查询对象并查询
function cx(){
    var obj=$("#searchFrom").serializeObject();
    dg.datagrid('load',obj);
}
//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("reset");
}
//导出excel
function exportExcel(){
    var form = document.getElementById("searchFrom");
    searchFrom.action=ctx+'/temparrange/exportExcel';
    form.submit();
}