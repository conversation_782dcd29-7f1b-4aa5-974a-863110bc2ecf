package com.foxconn.ipebg.basics.service;


import com.foxconn.ipebg.basics.entity.TempArrangeEntity;
import com.foxconn.ipebg.basics.entity.TempArrangeErrorDto;
import com.foxconn.ipebg.basics.dao.TempArrangeDao;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;

import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.Dict;

import com.foxconn.ipebg.system.service.DictService;
import com.foxconn.ipebg.system.utils.ImportExcelUtil;
import com.foxconn.ipebg.system.utils.MyBeanUtils;
import com.foxconn.ipebg.system.utils.UserUtil;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 異常人員信息
 *
 * <AUTHOR>
 * @email <EMAIL> 
 * @date 2019-03-11 14:46:49
 */
@Service
@Transactional(readOnly=true)
public class  TempArrangeService extends BaseBusinessService<TempArrangeEntity, String>{
    @Autowired
    private TempArrangeDao tempArrangeDao;

    @Autowired
    private UserServiceUtil serviceUtil;
    @Autowired
    private DictService dictService;
    
    //在session中存儲的驗證通過后的崗位明細Key
    public static final String TEMPARRANGEENTITIES = "tempArrangeEntities";
    //在session中存儲的驗證失敗后的崗位明細Key
    public static final String TEMPARRANGEERRORDTOS = "tempArrangeErrorDtos";

    @Override
    public HibernateDao<TempArrangeEntity, String> getEntityDao() {
        return tempArrangeDao;
    }

    public TempArrangeEntity findBySerialno(String serialno) {
        return this.tempArrangeDao.findUniqueBy("serialno", serialno);
    }
    public List<TempArrangeEntity> findByIp(String ip) {
        return this.tempArrangeDao.find("from TempArrangeEntity t where t.createIp=?0 and checkState<>'0' ", ip);
       
    }
	public int batchDelete(String ip) {
		return tempArrangeDao.batchExecute(
				"delete from TempArrangeEntity t where t.createIp=?0", ip);
	}

	//*******************************導入相關方法**********
	 /**
    * 批量導入
    * @param in excel文件輸入流
    * @param isExcel2003 是否excel2003
    * @return 是否成功
    */
   public String batchImport(InputStream in, boolean isExcel2003) {
       try {

           String[] uniqueFields = {};
           ImportExcelUtil poi = new ImportExcelUtil();
           int success = 0, failed = 0;
           // 讀取上傳的excel file
           List<List<String>> list = poi.read(in, isExcel2003);
           // 判断需要的字段在Excel中是否都存在
           boolean isExist = true;
           Map<String, String> fieldMap = getFieldMap();
           Map<String, Integer> fieldIndexMap = new HashMap();
           List<String> excelFieldList = list.get(0);
           //第一列是序號不需要
           for(int i = 1; i< excelFieldList.size();i++){
               if(!fieldMap.containsKey(excelFieldList.get(i))){
                   isExist = false;
                   break;
               }
               fieldIndexMap.put(fieldMap.get(excelFieldList.get(i)),i);
           }
           // 如果有列名不存在，则抛出异常，提示错误
           if (!isExist) {
               return "有列名不存在，請對照模板";
           }
           List<TempArrangeErrorDto> tempArrangeErrorDtos = listToTempArrangeErrorDto(list,1,fieldIndexMap);
           if(tempArrangeErrorDtos.size() > 100){
               return "資料導入不能大於100筆;";
           }
           for (int i=0;i<tempArrangeErrorDtos.size();i++ ){
               if(null != tempArrangeErrorDtos.get(i).getErrorLog()) {
                   UserUtil.getSession().removeAttribute(TEMPARRANGEENTITIES);
                   UserUtil.getSession().setAttribute(TEMPARRANGEERRORDTOS,tempArrangeErrorDtos);
                   return Constant.RESULT.CODE_EXCEL_ERROR.getValue();
               }
           }
           List<TempArrangeEntity> tempArrangeEntities = new ArrayList<>();
           for (TempArrangeErrorDto pointItemErrorDto: tempArrangeErrorDtos) {
               TempArrangeEntity pointItemEntity = new TempArrangeEntity();
               MyBeanUtils.copyProperties(pointItemErrorDto,pointItemEntity);
               tempArrangeEntities.add(pointItemEntity);
           }
           UserUtil.getSession().removeAttribute(TEMPARRANGEERRORDTOS);
           UserUtil.getSession().setAttribute(TEMPARRANGEENTITIES,tempArrangeEntities);

       } catch (Exception e) {
           this.logger.info(e.getMessage(), e);
           return Constant.RESULT.CODE_NO.getValue();
       }
       return Constant.RESULT.CODE_YES.getValue();
   }


   public HashMap<String, String> getFieldMap() {
       HashMap<String, String> fieldMap = new HashMap<String, String>();
       
       fieldMap.put("崗位編號","postRecno");
       fieldMap.put( "詳細信息(小崗)","location");
       fieldMap.put( "班別代碼","shiftNo");
       fieldMap.put( "工號","empNo");
       fieldMap.put( "姓名","empName");
       //fieldMap.put( "姓名","empName");
       fieldMap.put( "排班日期","shiftDate");
       
      
//   	// 創建人IP
//   	private String createIp;
      

       return fieldMap;
   }

   public LinkedHashMap<String, String> getErrorFieldMap() {
       LinkedHashMap<String, String> fieldMap = new LinkedHashMap<String, String>();
       fieldMap.put("postRecno", "崗位編號");
       fieldMap.put("location", "詳細信息(小崗)");
       fieldMap.put("shiftNo", "班別代碼");
       fieldMap.put("empNo", "工號");
       fieldMap.put("empName", "姓名");
       fieldMap.put("shiftDate", "排班日期");

       fieldMap.put("errorLog", "導入失敗原因");
       
       
    
       return fieldMap;
   }
   /**
    * @param list          列表
    * @param startIndex    開始的行
    * @param fieldIndexMap list中的index和类的英文属性的对应关系Map
    * @return
    * @throws IllegalAccessException
    * @throws InstantiationException
    */
   public List<TempArrangeErrorDto> listToTempArrangeErrorDto(List<List<String>> list,
                                                                          int startIndex,
                                                                          Map<String, Integer> fieldIndexMap){
       // 定义要返回的list
       List<TempArrangeErrorDto> resultList = new ArrayList<TempArrangeErrorDto>();
//将sheet转换为list
       for (int i = startIndex; i < list.size(); i++) {
           // 新建要转换的对象
           TempArrangeErrorDto entity = new TempArrangeErrorDto();

           // 给对象中的字段赋值
           for (Map.Entry<String, Integer> entry : fieldIndexMap.entrySet()) {
               // 获取英文字段名
				String enNormalName = entry.getKey();
//
//               // 获取当前List中的内容
               String content = list.get(i).get(fieldIndexMap.get(enNormalName));
               if("".equals(content) || null == content){
                   entity.setErrorLog("內容有空值");
                   break;
               }
//
//               // 给对象赋值
               try {
                   MyBeanUtils.setFieldValueByName(enNormalName, content, entity);
               } catch (Exception e) {
                   e.printStackTrace();
                   entity.setErrorLog(list.get(0).get(fieldIndexMap.get(enNormalName)) + "值異常，請檢查");
               }
           }
           resultList.add(entity);
       }
       return resultList;
   }
 //*******************************導入相關方法**********
    
}
