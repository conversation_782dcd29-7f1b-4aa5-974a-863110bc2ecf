package com.foxconn.ipebg.basics.controller;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Date;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.foxconn.ipebg.basics.service.BsStatutoryHolidaysService;
import com.foxconn.ipebg.system.entity.Dict;
import com.foxconn.ipebg.system.service.DictService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.basics.entity.BsTraineeEntity;
import com.foxconn.ipebg.basics.entity.BsTrainingPeriodEntity;
import com.foxconn.ipebg.basics.service.BsTrainingPeriodService;
import com.foxconn.ipebg.basics.service.BsTrainingPersonService;
import com.foxconn.ipebg.system.entity.User;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;


/**
 * 培訓期基本資料
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-08-29 10:42:47
 */
@Controller
@RequestMapping("bstrainingperiod")
public class BsTrainingPeriodController extends BaseController {

    @Autowired
    private BsTrainingPeriodService bsTrainingPeriodService;
	@Autowired
	private UserServiceUtil serviceUtil;
    @Autowired
    private BsTrainingPersonService bsTrainingPersonService;
    @Autowired
    private DictService dictService;
    @Autowired
    private BsStatutoryHolidaysService holidaysService;
   
    /**
      * 方法描述: 列表信息
      * @Author: ********
      * @CreateDate:   2019-08-29 10:42:47
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:bstrainingperiod:list")
    public String list() {
        //查询列表数据
        return "basics/bstrainingperiod/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: ********
      * @CreateDate:   2019-08-29 10:42:47
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:bstrainingperiod:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<BsTrainingPeriodEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
//        page.setOrderBy("createDate");
//  		page.setOrder("desc");
        User user = UserUtil.getCurrentUser();
        if (user != null) {
            // 按保安公司查询
            PropertyFilter filter = new PropertyFilter("INS_securityCom", user.getSecurityComs());
            filters.add(filter);
        }
        page = bsTrainingPeriodService.search(page, filters);
        return getEasyUIData(page);
    }
    /**
     * 方法描述: 保存
     * @Author: ********
     * @CreateDate:   2019-08-29 10:42:47
     * @Return
     **/
   
    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("basics:bstrainingperiod:add")
    @ResponseBody
    public String create(@Valid BsTrainingPeriodEntity bsTrainingPeriod, Model model) {
        bsTrainingPeriod.setNewRecord(true);
    	bsTrainingPeriod.setCreateDate(new Date());
    	//bsTrainingPeriod.setTrainingPeriodNo(serviceUtil.createSerialno("JWPX"));
    	bsTrainingPeriod.setTrainingPeriodNo(serviceUtil.createNo("JWPX"));
    	
    	bsTrainingPeriod.setTrainingPeriodName(bsTrainingPeriod.getTrainingPeriodNo().substring(4,8)+"警衛培訓"+Integer.parseInt(bsTrainingPeriod.getTrainingPeriodNo().substring(8,12))+"期");
    	
        bsTrainingPeriodService.save(bsTrainingPeriod);
        return "success";
    }
    
    /**
      * 方法描述: 添加跳轉
      * @Author: ********
      * @CreateDate:   2019-08-29 10:42:47
      * @Return
      **/

    @RequiresPermissions("basics:bstrainingperiod:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("bsTrainingPeriod", new BsTrainingPeriodEntity());
        model.addAttribute("action", "create");
        return "basics/bstrainingperiod/listForm";
    }

    @RequestMapping(value = "defaultPeriodEndDate/{startDate}", method = RequestMethod.GET)
    @ResponseBody
    public String defaultPeriodEndDate(@PathVariable("startDate") String startDate) {
        Dict defaultDaysConfig = dictService.getDictByLabelAndType("default_training_period_days", "arrange_setting");
        Integer defaultDays = 3;
        if (defaultDaysConfig != null) {
            defaultDays = Integer.parseInt(defaultDaysConfig.getValue());
        }
        Date start = DateUtil.parse(startDate, "yyyy-MM-dd");
        Date end = new Date(start.getTime() + (defaultDays - 1) * DateUnit.DAY.getMillis());
        Integer holidayCount = holidaysService.holidayCount(start, end);
        if (holidayCount > 0) {
            end = new Date(end.getTime() + holidayCount * DateUnit.DAY.getMillis());
        }
        return DateUtil.format(end, "yyyy-MM-dd");
	}
    
     /**
     * 方法描述: 修改跳轉
     * @Author: ********
     * @CreateDate:   2018-10-06 11:04:39
     * @Return
     **/

   @RequiresPermissions("basics:bstrainingperiod:update")
   @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
   public String updateForm(@PathVariable("id") String id, Model model) {
	   String endStatus="Y";
	   Date endDate=bsTrainingPeriodService.get(id).getEndDate();
//	   SimpleDateFormat formatter= new SimpleDateFormat("yyyy-MM-dd");
//	   Date now=new Date().getDate();
//		try {
//			now = new SimpleDateFormat("yyyy-MM-dd").parse(formatter.format(new Date()));
//		} catch (ParseException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		} 
	  // int end=endDate.getYear()+endDate.getMonth()+endDate.getDay();
	   
	   long end=endDate.getTime();
	   Date nowDate=new Date();  
	   long now =0;
	   SimpleDateFormat formatter= new SimpleDateFormat("yyyy-MM-dd");
	   try {
		 now =new SimpleDateFormat("yyyy-MM-dd").parse(formatter.format(nowDate)).getTime();
	} catch (ParseException e) {
		// TODO Auto-generated catch block
		e.printStackTrace();
	}
	   //int now =nowDate;
	   System.out.print(end+""+now);
	   if (end < now)
	   {
		   endStatus="N";  
	   }
	   
   
	   //>new Date();
       model.addAttribute("bsTrainingPeriod", bsTrainingPeriodService.get(id));
       model.addAttribute("action", "update");
       model.addAttribute("endStatus", endStatus);
      return "basics/bstrainingperiod/listForm";
   }
   
	/**
	 * 方法描述: 修改
	 * 
	 * @Author: ********
	 * @CreateDate: 2018-10-11 10:55:00
	 * @Return
	 **/

   @RequestMapping(value = "update", method = RequestMethod.POST)
   @RequiresPermissions("basics:bstrainingperiod:update")
   @ResponseBody
   public String update(@Valid BsTrainingPeriodEntity bsTrainingPeriod,@RequestParam(value = "ids") String ids) {
	   bsTrainingPeriod.setId(ids);
	   User user = UserUtil.getCurrentUser();
	   bsTrainingPeriod.setUpdateBy(user.getLoginName());
	   bsTrainingPeriod.setUpdateDate(new Date());
	   bsTrainingPeriodService.update(bsTrainingPeriod);
	   
       return "success";
   }
    /**
      * 方法描述: 根據主鍵刪除
      * @Author: ********
      * @CreateDate:   2019-08-29 10:42:47
      * @Return
      **/

    @RequestMapping("delete/{id}")
    @RequiresPermissions("basics:bstrainingperiod:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
    	
    	String trainingPeriodNo=bsTrainingPeriodService.findById(id).getTrainingPeriodNo();
    	if (bsTrainingPersonService.isExistTrainingPeriodNo(trainingPeriodNo)) 
    	{
    		 return "該培訓期已經綁定培訓人員不能刪除！";
    	}
    	else
    	{
            bsTrainingPeriodService.delete(id);
            return "success";
    	}

    }
    /**
      * 导出excel
      * @Author: ********
      * @CreateDate:   2019-08-29 10:42:47
      * @Return
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {}
	/**
	  * 方法描述: 生成下拉數據 培訓期
	  * @Author: ********
	  * @CreateDate:   2018/10/15  上午 08:56
	  * @Return
	  **/

	@ResponseBody
	@RequestMapping(value = "getAllTrainingPeriodList/{isAll}")
	public List<BsTrainingPeriodEntity> getAllTrainingPeriodList(@PathVariable("isAll") String isAll,Model model) {
		List<BsTrainingPeriodEntity> lst=bsTrainingPeriodService.getAllTrainingPeriodList(isAll);
        return bsTrainingPeriodService.getAllTrainingPeriodList(isAll);
	}

}
