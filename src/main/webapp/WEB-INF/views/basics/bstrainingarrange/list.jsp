<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>培訓期日排班表</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
    var _periodList = ${periodList};
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
        <form id="searchFrom" action="">
			<input id="trainingPeriodNo" name="filter_EQS_trainingPeriodNo" class="easyui-combogrid"
				   data-options="width: 200,prompt: '培訓期'" />
		<input type="text" name="filter_GED_arrangeDate" class="easyui-my97" datefmt="yyyy-MM-dd"
			   data-options="width:150,prompt: '开始日期'"/>
		- <input type="text" name="filter_LED_arrangeDate" class="easyui-my97" datefmt="yyyy-MM-dd"
				 data-options="width:150,prompt: '结束日期'"/>

		<span class="toolbar-item dialog-tool-separator"></span>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
		   onclick="listSearchReset()">重置</a>
		   <br/>
		   	<shiro:hasPermission name="basics:bstrainingarrange:add">
	       		<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="add();">添加</a>
	       		<span class="toolbar-item dialog-tool-separator"></span>
	       	</shiro:hasPermission>


	</form>

  </div>
<table id="dg"></table>
<div id="dlg"></div>

<script src="${ctx}/static/js/basics/bstrainingarrange.js?random=<%= Math.random()%>"></script>
</body>
</html>