package com.foxconn.ipebg.basics.controller;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Date;

import com.foxconn.ipebg.system.entity.Dict;
import com.foxconn.ipebg.system.service.DictService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.basics.entity.BsTraineeEntity;
import com.foxconn.ipebg.basics.entity.BsTraineeErrorDto;
import com.foxconn.ipebg.basics.entity.BsTrainingPeriodEntity;

import com.foxconn.ipebg.basics.entity.BsPostEntity;
import com.foxconn.ipebg.basics.entity.BsTraineeEntity;
import com.foxconn.ipebg.basics.service.BsTraineeService;
import com.foxconn.ipebg.basics.service.BsTraineeService;
import com.foxconn.ipebg.basics.service.BsTrainingPersonService;
import com.foxconn.ipebg.system.entity.User;
import com.foxconn.ipebg.system.utils.CommonUtils;
import com.foxconn.ipebg.system.utils.Excel2007Utils;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import com.foxconn.ipebg.system.utils.FileDownloadUtil;
import com.foxconn.ipebg.system.utils.IPUtil;
import com.foxconn.ipebg.system.utils.ImportExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;



/**
 * 培訓人員基本資料表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-08-29 10:42:45
 */
@Controller
@RequestMapping("bstrainee")
public class BsTraineeController extends BaseController {

    @Autowired
    private BsTraineeService bsTraineeService;
    @Autowired
    private BsTrainingPersonService bsTrainingPersonService;
    @Autowired
    private DictService dictService;
    
    
	private static final String TPLFILEPATH = "/static/resources/templet";

	private static final String BATCHIMPORTTPL = "bstrainee.xlsx";

	private static final String filePath = "/static/resources/download";
   
    /**
      * 方法描述: 列表信息
      * @Author: S6113712
      * @CreateDate:   2019-08-29 10:42:45
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:bstrainee:list")
    public String list() {
        //查询列表数据
        return "basics/bstrainee/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6113712
      * @CreateDate:   2019-08-29 10:42:45
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:bstrainee:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<BsTraineeEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        User user = UserUtil.getCurrentUser();
        if (user != null) {
        	// 按保安公司查询
			PropertyFilter filter = new PropertyFilter("INS_company", user.getSecurityComs());
			filters.add(filter);
		}
//        page.setOrderBy("createDate");
//  		page.setOrder("desc");
        page = bsTraineeService.search(page, filters);
        return getEasyUIData(page);
    }

    /**
	 * 方法描述：獲取培訓人員信息
	 *
	 * @Author: S6114893
	 * @CreateDate:   2019-09-21
	 * @Return
	 * */
    @RequestMapping(value = "getBsTrainee/{empNo}", method = RequestMethod.GET)
	@ResponseBody
	public BsTraineeEntity getBsTraineeEntity(@PathVariable("empNo") String empNo) {
    	BsTraineeEntity bsTrainee = bsTraineeService.isExistEmpInfo1(empNo);
    	return bsTrainee;
	}

    /**
     * 方法描述: 保存
     * @Author: S6113712
     * @CreateDate:   2019-08-29 10:42:45
     * @Return
     **/
   
    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("basics:bstrainee:add")
    @ResponseBody
    public String create(@Valid BsTraineeEntity bsTrainee, Model model) {
    	if (bsTraineeService.isExistPsnId(bsTrainee)) {
			return bsTrainee.getPsnId()+"該身份證號已經存在！";
		}else if(bsTraineeService.isExistEmpNo(bsTrainee))
		{
			return bsTrainee.getEmpNo()+"該工號已經存在！";
		}
		else  {	
        bsTrainee.setNewRecord(true);
    	bsTrainee.setCreateDate(new Date());
    	//bsTrainee.setStatus("0");
        bsTraineeService.save(bsTrainee);
        return "success";
		}
    }
    
    /**
      * 方法描述: 添加跳轉
      * @Author: S6113712
      * @CreateDate:   2019-08-29 10:42:45
      * @Return
      **/

    @RequiresPermissions("basics:bstrainee:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("bsTrainee", new BsTraineeEntity());
        model.addAttribute("action", "create");
        return "basics/bstrainee/listForm";
    }
    
     /**
     * 方法描述: 修改跳轉
     * @Author: S6113712
     * @CreateDate:   2018-10-06 11:04:39
     * @Return
     **/

   @RequiresPermissions("basics:bstrainee:update")
   @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
   public String updateForm(@PathVariable("id") String id, Model model) {
       model.addAttribute("bsTrainee", bsTraineeService.get(id));
       model.addAttribute("action", "update");
      return "basics/bstrainee/listForm";
   }
   
	/**
	 * 方法描述: 修改
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-11 10:55:00
	 * @Return
	 **/

   @RequestMapping(value = "update", method = RequestMethod.POST)
   @RequiresPermissions("basics:bstrainee:update")
   @ResponseBody
   public String update(@Valid BsTraineeEntity bsTrainee,@RequestParam(value = "ids") String ids) {
	   bsTrainee.setId(ids);
	   if (bsTraineeService.isExistPsnId(bsTrainee)) {
			return bsTrainee.getPsnId()+"該身份證號已經存在！";
		} else  {
	   User user = UserUtil.getCurrentUser();
	   bsTrainee.setUpdateBy(user.getLoginName());
	   bsTrainee.setUpdateDate(new Date());
	   bsTraineeService.update(bsTrainee);
	   
       return "success";
		}
   }
    /**
      * 方法描述: 根據主鍵刪除
      * @Author: S6113712
      * @CreateDate:   2019-08-29 10:42:45
      * @Return
      **/

    @RequestMapping("delete/{id}")
    @RequiresPermissions("basics:bstrainee:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
    	//bsTraineeService.findById(id).getEmpNo();
    	String empNo=bsTraineeService.findById(id).getEmpNo();
    	if (bsTrainingPersonService.isExistEmpNo(empNo))
    	{
    		 return "該人員已經參加培訓不能刪除！";
    	}
    	else
    	{
        bsTraineeService.delete(id);
        return "success";
    	}
    }
    /**
      * 导出excel
      * @Author: S6113712
      * @CreateDate:   2019-08-29 10:42:45
      * @Return
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {}
    
    
	/**
	 * 方法描述: 獲取培训人员信息（匹配培训期）
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-01-15 18:34:27
	 * @Return
	 **/
	@RequestMapping(value = "getPersonList", method = RequestMethod.GET)
	@ResponseBody
	public List<BsTraineeEntity> getPersonList(
			@RequestParam("empNo") String empNo,
			@RequestParam("empName") String empName) {

		// BsTraineeEntity bsTrainee
		// HttpServletRequest request
		// String empNo= request.getParameter("empNo");
		// String empName=request.getParameter("empName");
		BsTraineeEntity bsTrainee = new BsTraineeEntity();
		bsTrainee.setEmpName(empName);
		bsTrainee.setEmpNo(empNo);
		User user = UserUtil.getCurrentUser();
		if (user != null) {
			bsTrainee.setCompany(user.getSecurityCom());
		}
		List<BsTraineeEntity> personList = bsTraineeService
				.findPersonList(bsTrainee);
		return personList;
	}
	
	/**
	 * 方法描述: 下載批量上傳模板
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-22 18:57:46
	 * @Return
	 **/

	@RequestMapping(value = "downLoad/batchImportTpl", method = RequestMethod.GET)
	// @RequiresPermissions("other:tqhraymachinescrap:add")
	@ResponseBody
	public void batchImportTpl(HttpServletRequest request,
			HttpServletResponse response) {
		fileDownload(request, response, BATCHIMPORTTPL);
	}

	private void fileDownload(HttpServletRequest request,
			HttpServletResponse response, String fileName) {
		String path = request.getSession().getServletContext()
				.getRealPath(TPLFILEPATH);
		try {
			FileDownloadUtil.fileDownload(request, response, path, fileName);// 下載點位信息模板
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	/**
	 * 方法描述: 下載批量上傳異常信息
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-02-13 18:57:46
	 * @Return
	 **/

	@RequestMapping(value = "downLoad/errorExcel", method = RequestMethod.GET)
	// @RequiresPermissions("other:tqhraymachinescrap:add")
	@ResponseBody
	public void errorExcelDownload(HttpServletRequest request,
			HttpServletResponse response) throws IOException {

		List<BsTraineeErrorDto> errorDtos = (List<BsTraineeErrorDto>) request
				.getSession().getAttribute(
						BsTraineeService.BSTRAINEEERRORDTOS);
		if (errorDtos != null) {
			// 導出excel2007
			LinkedHashMap<String, String> map = this.getErrorFieldMap();

			String name = this.getCurUser().getLoginName() + "In";
			String tplPath = request.getSession().getServletContext()
					.getRealPath(filePath);
			File file = Excel2007Utils.createFile(errorDtos, map, tplPath,
					name, null);// 生成xlsx文件
			// long endOfBuildExcel = System.currentTimeMillis();
			// System.out.println("生成excel："+(endOfBuildExcel -
			// endOfLoadData)+"毫秒");
			String fileName = file.getName();
			Excel2007Utils.exportFile(response, tplPath, fileName);// 下载生成的xlsx文件
			UserUtil.getSession().removeAttribute(
					BsTraineeService.BSTRAINEEERRORDTOS);
		}

	}

	/**
	 * 批量導入
	 * 
	 * @param file 文檔
	 * @param isExcel2003 是否2003
	 * @return
	 */
	@RequestMapping(value = "upload", method = RequestMethod.POST)
	@ResponseBody
	public String upload(MultipartFile file, boolean isExcel2003, Model model,
			HttpServletRequest request) {
		if (file.equals("") || file.getSize() <= 0) {
			return Constant.FAIL;
		} else {
			String result = null;
			try {

				// result = bsTraineeService.batchImport(
				// file.getInputStream(), isExcel2003);
				result = this.batchImport(file.getInputStream(), isExcel2003);

			} catch (IOException e) {
				e.printStackTrace();
			}

			if (Constant.RESULT.CODE_YES.getValue().equals(result)) {
				return Constant.SUCCESS;
			} else {
				return result;
			}
		}

	}

	/**
	 * 批量導入
	 * 
	 * @param in excel文件輸入流
	 * @param isExcel2003 是否excel2003
	 * @return 是否成功
	 */
	public String batchImport(InputStream in, boolean isExcel2003) {
		try {
			List<BsTraineeErrorDto> bsTraineeErrorDtos = null;
			ImportExcelUtil poi = new ImportExcelUtil();
			int success = 0, failed = 0;
			// 讀取上傳的excel file
			List<List<String>> list = poi.read(in, isExcel2003);
			// 判断需要的字段在Excel中是否都存在
			boolean isExist = true;
			Map<String, String> fieldMap = getFieldMap();
			Map<String, Integer> fieldIndexMap = new HashMap();
			List<String> excelFieldList = list.get(0);
			// 第一列是序號不需要
			for (int i = 1; i < excelFieldList.size(); i++) {
				if (!fieldMap.containsKey(excelFieldList.get(i))) {
					isExist = false;
					break;
				}
				fieldIndexMap.put(fieldMap.get(excelFieldList.get(i)), i);
			}
			// 如果有列名不存在，则抛出异常，提示错误
			if (!isExist) {
				return "有列名不存在，請對照模板";
			}
			// 導入數量限制
			// if (list.size()-1 > 10) {
			// return "資料導入不能大於100筆;";
			// }
			// 開始導入
			bsTraineeErrorDtos = listToBsTraineeErrorDto(list, 1,
					fieldIndexMap);

			if (bsTraineeErrorDtos.size() > 0) {
				UserUtil.getSession().removeAttribute(
						BsTraineeService.BSTRAINEEERRORDTOS);
				UserUtil.getSession().setAttribute(
						BsTraineeService.BSTRAINEEERRORDTOS,
						bsTraineeErrorDtos);
				return "導入成功"
						+ (list.size() - 1 - bsTraineeErrorDtos.size())
						+ "筆，失敗" + bsTraineeErrorDtos.size() + "筆!";
			}

		} catch (Exception e) {
			this.logger.info(e.getMessage(), e);
			return Constant.RESULT.CODE_NO.getValue();
		}
		return Constant.RESULT.CODE_YES.getValue();
	}

	public HashMap<String, String> getFieldMap() {
		HashMap<String, String> fieldMap = new HashMap<String, String>();


		fieldMap.put("工號", "empNo");
		fieldMap.put("姓名", "empName");
		fieldMap.put("性別", "status");
		fieldMap.put("身份證號", "psnId");
		fieldMap.put("保安公司", "company");


		return fieldMap;
	}

	public LinkedHashMap<String, String> getErrorFieldMap() {
		LinkedHashMap<String, String> fieldMap = new LinkedHashMap<String, String>();

		fieldMap.put("empNo", "工號");
		fieldMap.put("empName", "姓名");
		fieldMap.put("status", "性別");
		fieldMap.put("psnId", "身份證號");
		fieldMap.put("company", "保安公司");


		fieldMap.put("errorLog", "導入失敗原因");

		return fieldMap;
	}

	/**
	 * @param list
	 *            列表
	 * @param startIndex
	 *            開始的行
	 * @param fieldIndexMap
	 *            list中的index和类的英文属性的对应关系Map
	 * @return
	 * @throws IllegalAccessException
	 * @throws InstantiationException
	 */
	@Transactional
	public List<BsTraineeErrorDto> listToBsTraineeErrorDto(
			List<List<String>> list, int startIndex,
			Map<String, Integer> fieldIndexMap) {
		// 定义要返回的list
		List<BsTraineeErrorDto> resultList = new ArrayList<BsTraineeErrorDto>();

		List<Dict> securityComList = dictService.getDictByType("guard_securityCom");
		List<String> securityNameList = new ArrayList<>();
		for (Dict dict : securityComList) {
			securityNameList.add(dict.getLabel());
		}
		// 将sheet转换为list

		BsTraineeErrorDto entity = null;
		 String empNo="", empName="", psnId="",company="",status="";

		 BsTraineeEntity bsTraineeVal=new BsTraineeEntity();
		for (int i = startIndex; i < list.size(); i++) {
			// 新建要转换的对象

			entity = new BsTraineeErrorDto();
			
			 empNo=list.get(i).get(1).trim();
			 empName=list.get(i).get(2);
			 status=list.get(i).get(3);
			 psnId=list.get(i).get(4).trim();
			 company=list.get(i).get(5);
			 
				entity.setEmpNo(empNo);
				entity.setEmpName(empName);
				entity.setCompany(company);
				entity.setPsnId(psnId);
				entity.setStatus(status);
				bsTraineeVal.setEmpNo(empNo);
				bsTraineeVal.setPsnId(psnId);
					// 驗證人員信息
					if (empNo== "") {

						entity.setErrorLog("工號不能為空！");
						resultList.add(entity);
						
					}else if(empName=="")
					{
						entity.setErrorLog("姓名不能為空！");
						resultList.add(entity);
						
					}else if(status=="")
					{
						entity.setErrorLog("性別不能為空！");
						resultList.add(entity);
						
					}else if(psnId=="")
					{
						entity.setErrorLog("身份證號不能為空！");
						resultList.add(entity);
						
					}else if(company=="")
					{
						entity.setErrorLog("保安公司不能為空！");
						resultList.add(entity);
						
					} else if (!securityNameList.contains(company)) {
						entity.setErrorLog("系統中不存在該保安公司");
						resultList.add(entity);
					} else if (bsTraineeService.isExistPsnId(bsTraineeVal)) {
						entity.setErrorLog("身份證號已經存在！");
						resultList.add(entity);
					}else if (bsTraineeService.isExistEmpNo(bsTraineeVal)) {
						entity.setErrorLog("工號已經存在！");
						resultList.add(entity);
					}
					else// 
					{
					
						BsTraineeEntity bsTraineeEntity = new BsTraineeEntity();
						bsTraineeEntity.setNewRecord(true);
						bsTraineeEntity.setEmpNo(empNo);
						bsTraineeEntity.setEmpName(empName);
						bsTraineeEntity.setStatus(status);
						bsTraineeEntity.setPsnId(psnId);
						bsTraineeEntity.setCompany(company);
						
						bsTraineeEntity.setCreateDate(new Date());
						bsTraineeEntity.setCreateBy(UserUtil.getCurrentUser().getLoginName());

						bsTraineeService.save(bsTraineeEntity);
						// this.save(bsTraineeEntity);
					
					}
				
			
			

		}
		return resultList;
	}
	// *******************************導入相關邏輯**********

	/**
	  * 方法描述: 生成下拉數據 
	  * @Author: S6113712
	  * @CreateDate:   2018/10/15  上午 08:56
	  * @Return
	  **/

	@ResponseBody
	@RequestMapping(value = "getAllCompany")
	public List<BsTraineeEntity> getAllCompany(Model model) {
   return bsTraineeService.getAllCompany();
	}
}
