package com.foxconn.ipebg.system.entity;

        import java.io.Serializable;
        import java.util.Date;
        import javax.persistence.*;

        import com.foxconn.ipebg.common.entity.DataEntity;
        import org.hibernate.annotations.DynamicInsert;
        import org.hibernate.annotations.DynamicUpdate;


/**
 * 各廠區樓棟及區域對應關係表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-11-27 09:33:14
 */
@Entity
@Table(name = "t_pub_areabaseinfo")
@DynamicUpdate
@DynamicInsert
public class TPubAreabaseinfoEntity extends DataEntity<TPubAreabaseinfoEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

                                    //區域類型 1 廠區 2 區域 3 樓棟
            private String areatype;
                                //上級ID
            private String superid;
                                //區域代碼
            private String code;
                                //區域名稱
            private String name;
                                //狀態 Y 有效 N 無效
            private String status;
                                //排序號
            private Integer orderby;
                                                                        
            
                        /**
             * 设置：區域類型 1 廠區 2 區域 3 樓棟
             */
            public void setAreatype(String areatype) {
                this.areatype = areatype;
            }

            /**
             * 获取：區域類型 1 廠區 2 區域 3 樓棟
             */
            
            @Column(name = "areatype", nullable = false, length = 20)
            public String getAreatype() {
                return areatype;
            }
                
                        /**
             * 设置：上級ID
             */
            public void setSuperid(String superid) {
                this.superid = superid;
            }

            /**
             * 获取：上級ID
             */
            
            @Column(name = "superid", nullable = false, length = 20)
            public String getSuperid() {
                return superid;
            }
                
                        /**
             * 设置：區域代碼
             */
            public void setCode(String code) {
                this.code = code;
            }

            /**
             * 获取：區域代碼
             */
            
            @Column(name = "code", nullable = false, length = 20)
            public String getCode() {
                return code;
            }
                
                        /**
             * 设置：區域名稱
             */
            public void setName(String name) {
                this.name = name;
            }

            /**
             * 获取：區域名稱
             */
            
            @Column(name = "name", nullable = false, length = 20)
            public String getName() {
                return name;
            }
                
                        /**
             * 设置：狀態 Y 有效 N 無效
             */
            public void setStatus(String status) {
                this.status = status;
            }

            /**
             * 获取：狀態 Y 有效 N 無效
             */
            
            @Column(name = "status", nullable = false, length = 20)
            public String getStatus() {
                return status;
            }
                
                        /**
             * 设置：排序號
             */
            public void setOrderby(Integer orderby) {
                this.orderby = orderby;
            }

            /**
             * 获取：排序號
             */
            
            @Column(name = "orderby", nullable = false, length = 20)
            public Integer getOrderby() {
                return orderby;
            }
                
            
            
            
            
            
    }
