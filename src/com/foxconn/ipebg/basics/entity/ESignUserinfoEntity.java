package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 簽核者信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-12-25 14:52:53
 */
@Entity
@Table(name = "E_SIGN_USERINFO")
@DynamicUpdate
@DynamicInsert
public class ESignUserinfoEntity extends DataEntity<ESignUserinfoEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    //工號
    private String eserId;
    //姓名
    private String eserNam;
    //性別
    private String eserSex;
    //部門
    private String eserDptId;
    //部門名稱
    private String eserDptNam;
    //Mail地址
    private String eserMail;
    //簽核節點名稱
    private String eserNodeName;
    //簽核表單類別代碼
    private String eserTyp;
    //簽核表單名稱
    private String eserDesc;
    //廠區（暫不使用）
    private String siteId;
    //有效否
    private String ynVa;
    //簽核者聯系方式
    private String eserTel;



    /**
     * 设置：工號
     */
    public void setEserId(String eserId) {
        this.eserId = eserId;
    }

    /**
     * 获取：工號
     */

    @Column(name = "ESER_ID", nullable = false, length = 20)
    public String getEserId() {
        return eserId;
    }

    /**
     * 设置：姓名
     */
    public void setEserNam(String eserNam) {
        this.eserNam = eserNam;
    }

    /**
     * 获取：姓名
     */

    @Column(name = "ESER_NAM", nullable = false, length = 20)
    public String getEserNam() {
        return eserNam;
    }

    /**
     * 设置：性別
     */
    public void setEserSex(String eserSex) {
        this.eserSex = eserSex;
    }

    /**
     * 获取：性別
     */

    @Column(name = "ESER_SEX", nullable = false, length = 20)
    public String getEserSex() {
        return eserSex;
    }

    /**
     * 设置：部門
     */
    public void setEserDptId(String eserDptId) {
        this.eserDptId = eserDptId;
    }

    /**
     * 获取：部門
     */

    @Column(name = "ESER_DPT_ID", nullable = false, length = 20)
    public String getEserDptId() {
        return eserDptId;
    }

    /**
     * 设置：部門名稱
     */
    public void setEserDptNam(String eserDptNam) {
        this.eserDptNam = eserDptNam;
    }

    /**
     * 获取：部門名稱
     */

    @Column(name = "ESER_DPT_NAM", nullable = false, length = 20)
    public String getEserDptNam() {
        return eserDptNam;
    }


    /**
     * 设置：Mail地址
     */
    public void setEserMail(String eserMail) {
        this.eserMail = eserMail;
    }

    /**
     * 获取：Mail地址
     */

    @Column(name = "ESER_MAIL", nullable = false, length = 20)
    public String getEserMail() {
        return eserMail;
    }

    /**
     * 设置：簽核節點名稱
     */
    public void setEserNodeName(String eserNodeName) {
        this.eserNodeName = eserNodeName;
    }

    /**
     * 获取：簽核節點名稱
     */

    @Column(name = "ESER_NODE_NAME", nullable = false, length = 20)
    public String getEserNodeName() {
        return eserNodeName;
    }

    /**
     * 设置：簽核表單類別代碼
     */
    public void setEserTyp(String eserTyp) {
        this.eserTyp = eserTyp;
    }

    /**
     * 获取：簽核表單類別代碼
     */

    @Column(name = "ESER_TYP", nullable = false, length = 20)
    public String getEserTyp() {
        return eserTyp;
    }

    /**
     * 设置：簽核表單名稱
     */
    public void setEserDesc(String eserDesc) {
        this.eserDesc = eserDesc;
    }

    /**
     * 获取：簽核表單名稱
     */

    @Column(name = "ESER_DESC", nullable = false, length = 20)
    public String getEserDesc() {
        return eserDesc;
    }

    /**
     * 设置：廠區（暫不使用）
     */
    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }

    /**
     * 获取：廠區（暫不使用）
     */

    @Column(name = "SITE_ID", nullable = false, length = 20)
    public String getSiteId() {
        return siteId;
    }

    /**
     * 设置：有效否
     */
    public void setYnVa(String ynVa) {
        this.ynVa = ynVa;
    }

    /**
     * 获取：有效否
     */

    @Column(name = "YN_VA", nullable = false, length = 20)
    public String getYnVa() {
        return ynVa;
    }


    /**
     * 设置：簽核者聯系方式
     */
    public void setEserTel(String eserTel) {
        this.eserTel = eserTel;
    }

    /**
     * 获取：簽核者聯系方式
     */

    @Column(name = "ESER_TEL", nullable = false, length = 20)
    public String getEserTel() {
        return eserTel;
    }

}
