package com.foxconn.ipebg.buessness.workflow.service;


import com.foxconn.ipebg.buessness.workflow.entity.InterConfig;
import com.foxconn.ipebg.buessness.workflow.entity.InterLog;
import com.foxconn.ipebg.buessness.workflow.entity.InterResult;
import com.foxconn.ipebg.buessness.workflow.entity.TaskInfo;
import com.foxconn.ipebg.system.service.DictService;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpMethod;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.GZIPInputStream;

@Service
@Transactional(readOnly = true)
public class ProcessService {
    private static final Logger logger = Logger.getLogger(ProcessService.class);

    @Autowired
    private DictService dictService;

    public InterResult processStart(InterConfig config) {
        if (StringUtils.isEmpty(config.getServerUrl()) || StringUtils.isBlank(config.getServerUrl())) {
            config.setServerUrl(dictService.get(301001).getValue());
        }
        return call(config);
    }

    public InterResult processEnd(InterConfig config) {
        if (StringUtils.isEmpty(config.getServerUrl()) || StringUtils.isBlank(config.getServerUrl())) {
            config.setServerUrl(dictService.get(301034).getValue());
        }
        return call(config);
    }

    public InterResult queryTaskList(InterConfig config) {
        if (StringUtils.isEmpty(config.getServerUrl()) || StringUtils.isBlank(config.getServerUrl())) {
            config.setServerUrl(dictService.get(301002).getValue());
        }
        return call(config);
    }

    public InterResult queryTaskById(InterConfig config) {
        if (StringUtils.isEmpty(config.getServerUrl()) || StringUtils.isBlank(config.getServerUrl())) {
            config.setServerUrl(dictService.get(301007).getValue());
        }
        return call(config);
    }

    public InterResult comleteTask(InterConfig config) {
        if (StringUtils.isEmpty(config.getServerUrl()) || StringUtils.isBlank(config.getServerUrl())) {
            config.setServerUrl(dictService.get(301004).getValue());
        }
        return call(config);
    }

    public InterResult processStatus(InterConfig config) {
        if (StringUtils.isEmpty(config.getServerUrl()) || StringUtils.isBlank(config.getServerUrl())) {
            config.setServerUrl(dictService.get(301006).getValue());
        }
        return call(config);
    }

    public InterResult claimTask(InterConfig config) {
        if (StringUtils.isEmpty(config.getServerUrl()) || StringUtils.isBlank(config.getServerUrl())) {
            config.setServerUrl(dictService.get(301003).getValue());
        }
        return call(config);
    }

    public InterResult processGraph(InterConfig config) {
        if (StringUtils.isEmpty(config.getServerUrl()) || StringUtils.isBlank(config.getServerUrl())) {
            config.setServerUrl(dictService.get(301005).getValue());
        }
        return call(config);
    }

    public InterResult setProcessVariables(InterConfig config) {
        if (StringUtils.isEmpty(config.getServerUrl()) || StringUtils.isBlank(config.getServerUrl())) {
            config.setServerUrl(dictService.get(301017).getValue());
        }
        return call(config);
    }

    public InterResult updateTaskUsers(InterConfig config) {
        if (StringUtils.isEmpty(config.getServerUrl()) || StringUtils.isBlank(config.getServerUrl())) {
            config.setServerUrl(dictService.get(301018).getValue());
        }
        return call(config);
    }

    public InterResult currentTaskInfo(InterConfig config) {
        if (StringUtils.isEmpty(config.getServerUrl()) || StringUtils.isBlank(config.getServerUrl())) {
            config.setServerUrl(dictService.get(301009).getValue());
        }
        return call(config);
    }

    public InterResult completeAutoTask(InterConfig config) {
        if (StringUtils.isEmpty(config.getServerUrl()) || StringUtils.isBlank(config.getServerUrl())) {
            config.setServerUrl(dictService.get(301014).getValue());
        }
        return call(config);
    }

    /**
     * 會簽節點
     *
     * @param config
     * @return
     */

    public InterResult createHuiqianTaskInfor(InterConfig config) {
        if (StringUtils.isEmpty(config.getServerUrl()) || StringUtils.isBlank(config.getServerUrl())) {
            config.setServerUrl(dictService.get(301018).getValue());
        }
        return call(config);
    }

    public InterResult completeHuiqianTask(InterConfig config) {
        if (StringUtils.isEmpty(config.getServerUrl()) || StringUtils.isBlank(config.getServerUrl())) {
            config.setServerUrl(dictService.get(301019).getValue());
        }
        return call(config);
    }

    public InterResult groupTasks(InterConfig config) {
        if (StringUtils.isEmpty(config.getServerUrl()) || StringUtils.isBlank(config.getServerUrl())) {
            config.setServerUrl(dictService.get(301020).getValue());
        }
        return call(config);
    }

    public InterResult huiqianTaskAssignee(InterConfig config) {
        if (StringUtils.isEmpty(config.getServerUrl()) || StringUtils.isBlank(config.getServerUrl())) {
            config.setServerUrl(dictService.get(301023).getValue());
        }
        return call(config);
    }

    public InterResult getVariables(InterConfig config) {
        if (StringUtils.isEmpty(config.getServerUrl()) || StringUtils.isBlank(config.getServerUrl())) {
            config.setServerUrl(dictService.get(301031).getValue());
        }
        return call(config);
    }

    public InterResult timingSendMail(InterConfig config) {
        if (StringUtils.isEmpty(config.getServerUrl()) || StringUtils.isBlank(config.getServerUrl())) {
            config.setServerUrl(dictService.get(999002).getValue());
        }
        return call(config);
    }

    /**
     * 接口調用
     *
     * @param config
     * @return
     */

    public InterResult call(InterConfig config) {
        if (config == null || StringUtils.isEmpty(config.getServerUrl())) {
            return null;
        }
        InterResult resultObj = null;
        HttpMethod method = null;

        //組織接口交互日誌
        InterLog log = new InterLog();
        log.setEmpNo(config.getEmpNo());
//		log.setRequestIp(config.getRequestIp());
        //操作者的工號及登錄地址
//        User u = UserUtil.getCurrentUser();
//        if (u != null) {
//            log.setEmpNo(u.getLoginName());
//            log.setRequestIp(u.getPhone());
//        }
        String reqtime = "";
        String restime = "";
        try {

            int connTime = 0;
            int readTime = 0;
            try {
                String ct = dictService.get(301021).getValue();
                String rt = dictService.get(301022).getValue();
                connTime = Integer.parseInt(ct);
                readTime = Integer.parseInt(rt);
            } catch (Exception e1) {
                connTime = 1500;
                readTime = 5000;
            } finally {
                if (connTime == 0) {
                    connTime = 1500;
                }
                if (readTime == 0) {
                    readTime = 5000;
                }
            }
            //接口請求串組織
            String pstr = crtParamStr(config);
            String urlStr = config.getServerUrl() + "?" + pstr;

            urlStr = urlStr.replaceAll(" ", "");

            //urlStr= urlStr.replace("***********:8081", "*************:8080");
            //urlStr= urlStr.replace("*************:8080", "*************:8088");

            //請求參數串
            log.setRequestMsg(urlStr);
            HttpClient client = new HttpClient();
            client.setConnectionTimeout(connTime);
            client.setTimeout(readTime);
            method = new GetMethod(urlStr);
            if (urlStr.indexOf("gzip") > 0) {
                method.setRequestHeader("Connection", "close");
            }
            //請求開始時間
            client.executeMethod(method);
            //調用工作流程服務接口
            String jsonStr = "";
//			System.out.println(urlStr);
            if (urlStr.indexOf("gzip") > 0) {
                jsonStr = inputStreamToString(method.getResponseBodyAsStream());
            } else {
                jsonStr = method.getResponseBodyAsString();
            }
//			System.out.println(jsonStr);
            //請求結束時間
//			restime=DateUtil.getNowInMillis();
            log.setRequestBackMsg(jsonStr);
//            log.setRequestCostTime((Long.parseLong(restime) - Long.parseLong(reqtime)) + "");
//            logger.error(log.toString());
            resultObj = parserToObj(jsonStr);
            if ("FAIL".equalsIgnoreCase(resultObj.getStatus())) {
                logger.error("----------------------------------call activiti engine error find!please check---------------------------------------------------");
                logger.error(log.toString());
                logger.error("----------------------------------call activiti engine error find!please check---------------------------------------------------");
                //如果是審核類的操作失敗，保存到數據庫
                log.setProcessid(config.getProcessId());
                log.setStatus("0");
                log.setTimes("0");
//			  processServiceDao.processLog(log);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
//			restime=DateUtil.getNowInMillis();
//			log.setRequestCostTime((Long.parseLong(restime)- Long.parseLong(reqtime))+"");
        } finally {
            //释放连接
            if (method != null) {
                method.releaseConnection();
            }
        }
        return resultObj;
    }

    private static String inputStreamToString(InputStream is) {
        if (is == null) {
            return "";
        }
        /*ByteArrayOutputStream swapStream = new ByteArrayOutputStream();
		try {
			byte[] buf = new byte[1024];
			int len = -1;
			while ((len = is.read(buf)) != -1) {
			  try {
				  swapStream.write(buf);
			  } catch (Exception e) {}
			}
		} catch (IOException e) {
			logger.info(e.getMessage(),e);
		}finally{
			try {
				if(is!=null){
					is.close();
				}
			} catch (IOException e) {				
			}
		}
	    return uncompressToString(swapStream.toByteArray());*/
        /**
         * 修改InputStream轉byte[]的方式
         */
        try {
            return uncompressToString(IOUtils.toByteArray(is));
        } catch (IOException e) {
            logger.error(e);
            logger.info(e.getMessage(), e);
            return "";
        }
    }

    /*
     * 字节数组解压缩后返回字符串
     */
    private static String uncompressToString(byte[] b) {
        if (b == null || b.length == 0) {
            return null;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream in = new ByteArrayInputStream(b);
        try {
            GZIPInputStream gunzip = new GZIPInputStream(in);
            byte[] buffer = new byte[256];
            int n;
            while ((n = gunzip.read(buffer)) >= 0) {
                out.write(buffer, 0, n);
            }
            return out.toString("UTF-8");
        } catch (IOException e) {
            logger.error(e);
            logger.info(e.getMessage(), e);
            return "";
        }
    }

    /**
     * 將工作流服務返回的json串轉化成為本地bean
     *
     * @param jsonStr
     * @return
     */
    private InterResult parserToObj(String jsonStr) {
        if (StringUtils.isEmpty(jsonStr) || StringUtils.isBlank(jsonStr)) {
            return null;
        }
        InterResult resultObj = null;
        try {
            JSONObject myJsonObject = new JSONObject(jsonStr);
            if (myJsonObject == null) {
//				System.out.println("工作流服務返回信息轉json對象失敗.");
                return resultObj;
            }
            //獲取返回信息 成功SUCCESS  失敗FAIL
            resultObj = new InterResult();
            resultObj.setStatus(myJsonObject.getString("status"));
            resultObj.setMsg(myJsonObject.getString("msg"));

            try {
                resultObj.setProcessId(myJsonObject.getString("pid") == null ? "" : myJsonObject.getString("pid"));
            } catch (Exception e) {
            }
            //工單的運行狀態 running=true 運行中 false完成
            try {
                resultObj.setRunning(myJsonObject.getString("running") == null ? "" : myJsonObject.getString("running"));
            } catch (Exception e) {
            }
            //簽核主管工號
            try {
                resultObj.setAssignee(myJsonObject.getString("assignee") == null ? "" : myJsonObject.getString("assignee"));
            } catch (Exception e) {
            }
            try {
                resultObj.setValue(myJsonObject.getString("value") == null ? "" : myJsonObject.getString("value"));
            } catch (Exception e) {
            }
            try {
                JSONArray tasks = (JSONArray) myJsonObject.get("tasks");
                if (tasks != null) {
					List<TaskInfo> taskList=new ArrayList<TaskInfo>();
					TaskInfo task=null;
					int len=tasks.length();
					for(int i=0;i<len;i++){
						JSONObject t=(JSONObject)tasks.get(i);
						if(t!=null){
							task=new TaskInfo();
							//流程信息描述
							task.setPdId(t.getString("pdid"));
							//任務編碼 
							task.setTaskId(t.getString("taskid"));
							//任務處理人
							task.setAssignee(t.getString("assignee"));
							//工單實例 id
							task.setProcessId(t.getString("pid"));
							//工單處環節
							task.setTaskName(t.getString("taskname"));
							
							taskList.add(task);
						}					
					}
					resultObj.setTaskInfoList(taskList);
                }
            } catch (Exception e) {
            }
            try {
				/*if(resultObj.getTaskInfoList() == null){
					JSONObject tasks =(JSONObject)myJsonObject.get("data");
					if(tasks!=null){
						List<TaskInfo> taskList=new ArrayList<TaskInfo>();
						for(String empNo:JSONObject.getNames(tasks)){
							TaskInfo task=new TaskInfo();
							task.setAssignee(empNo);
							task.setTableNum(tasks.getString(empNo));
							taskList.add(task);
						}
						resultObj.setTaskInfoList(taskList);
					}
				}*/
            } catch (Exception e) {
            }


        } catch (Exception e) {
            logger.info(e.getMessage(), e);
        }
        return resultObj;
    }

    /**
     * 組織工作流請求url串
     *
     * @param config
     * @return
     */
    private String crtParamStr(InterConfig config) {
        if (config == null || StringUtils.isEmpty(config.getServerUrl())) {
            return null;
        }
        StringBuffer sb = new StringBuffer();
        try {
            boolean flag = false;
            int taskUserscount = 0;//多少個串簽節點
            int taskUsersAUTOFIN_DZQHcount = 0;//串簽節點中多少個"AUTOFIN_DZQH"
            int huiqiancount = 0;//多少個會簽節點
            int huiqianAUTOFIN_DZQHcount = 0;//會簽節點中多少個"AUTOFIN_DZQH"
            int len = "AUTOFIN_DZQH".length();
            if (StringUtils.isNotBlank(config.getWorkFlowId())) {
                sb.append("workflowId=").append(config.getWorkFlowId());
                flag = true;
            }
            if (StringUtils.isNotBlank(config.getApplyUserId())) {
                if (flag) {
                    sb.append("&applyUserId=").append(config.getApplyUserId());
                } else {
                    sb.append("applyUserId=").append(config.getApplyUserId());
                    flag = true;
                }
            }
            if (StringUtils.isNotBlank(config.getProcessId())) {
                if (flag) {
                    sb.append("&processId=").append(config.getProcessId());
                } else {
                    sb.append("processId=").append(config.getProcessId());
                    flag = true;
                }
            }
            if (StringUtils.isNotBlank(config.getTaskUsers())) {
                if (flag) {
                    sb.append("&taskUsers=").append(java.net.URLEncoder.encode(config.getTaskUsers(), "utf-8"));
                    taskUserscount = config.getTaskUsers().length() - StringUtils.remove(config.getTaskUsers(), ";").length();
                    taskUsersAUTOFIN_DZQHcount = (config.getTaskUsers().length() - StringUtils.remove(config.getTaskUsers(), "AUTOFIN_DZQH").length()) / len;
                } else {
                    sb.append("taskUsers=").append(java.net.URLEncoder.encode(config.getTaskUsers(), "utf-8"));
                    flag = true;
                    taskUserscount = config.getTaskUsers().length() - StringUtils.remove(config.getTaskUsers(), ";").length();
                    taskUsersAUTOFIN_DZQHcount = (config.getTaskUsers().length() - StringUtils.remove(config.getTaskUsers(), "AUTOFIN_DZQH").length()) / len;
                }
            }
            if (StringUtils.isNotBlank(config.getHuiqian())) {
                if (flag) {
                    sb.append("&huiqian=").append(config.getHuiqian());
                    huiqiancount = config.getHuiqian().length() - StringUtils.remove(config.getHuiqian(), ";").length();
                    huiqianAUTOFIN_DZQHcount = (config.getHuiqian().length() - StringUtils.remove(config.getHuiqian(), "AUTOFIN_DZQH").length()) / len;
                } else {
                    sb.append("huiqian=").append(config.getHuiqian());
                    flag = true;
                    huiqiancount = config.getHuiqian().length() - StringUtils.remove(config.getHuiqian(), ";").length();
                    huiqianAUTOFIN_DZQHcount = (config.getHuiqian().length() - StringUtils.remove(config.getHuiqian(), "AUTOFIN_DZQH").length()) / len;
                }
            }
            if (StringUtils.isNotBlank(config.getVariables())) {
                if (flag) {
                    sb.append("&variables=").append(config.getVariables());
                } else {
                    sb.append("variables=").append(config.getVariables());
                    flag = true;
                }
            }
            if (StringUtils.isNotBlank(config.getVariableName())) {
                if (flag) {
                    sb.append("&variableName=").append(config.getVariableName());
                } else {
                    sb.append("variableName=").append(config.getVariableName());
                    flag = true;
                }
            }
            if (StringUtils.isNotBlank(config.getVariableType())) {
                if (flag) {
                    sb.append("&variableType=").append(config.getVariableType());
                } else {
                    sb.append("variableType=").append(config.getVariableType());
                    flag = true;
                }
            }
            if (StringUtils.isNotBlank(config.getUserId())) {
                if (flag) {
                    sb.append("&userId=").append(config.getUserId());
                } else {
                    sb.append("userId=").append(config.getUserId());
                    flag = true;
                }
            }
            if (StringUtils.isNotBlank(config.getTaskId())) {
                if (flag) {
                    sb.append("&taskId=").append(config.getTaskId());
                } else {
                    sb.append("taskId=").append(config.getTaskId());
                    flag = true;
                }
            }
            if (StringUtils.isNotBlank(config.getTaskName())) {
                if (flag) {
                    sb.append("&taskName=").append(java.net.URLEncoder.encode(config.getTaskName(), "utf-8"));
                } else {
                    sb.append("taskName=").append(java.net.URLEncoder.encode(config.getTaskName(), "utf-8"));
                    flag = true;
                }
            }
            if (StringUtils.isNotBlank(config.getAssignee())) {
                if (flag) {
                    sb.append("&assignee=").append(config.getAssignee());
                } else {
                    sb.append("assignee=").append(config.getAssignee());
                    flag = true;
                }
            }
            if (StringUtils.isNotBlank(config.getVoteResult())) {
                if (flag) {
                    sb.append("&voteResult=").append(config.getVoteResult());
                } else {
                    sb.append("voteResult=").append(config.getVoteResult());
                    flag = true;
                }
            }
            if (StringUtils.isNotBlank(config.getCandidateUsers())) {
                if (flag) {
                    sb.append("&candidateUsers=").append(config.getCandidateUsers());
                } else {
                    sb.append("candidateUsers=").append(config.getCandidateUsers());
                    flag = true;
                }
            }
            if (StringUtils.isNotBlank(config.getOtherParam())) {
                if (flag) {
                    sb.append("&").append(config.getOtherParam() + "&");
                } else {
                    sb.append(config.getOtherParam() + "&");
                    flag = true;
                }
            }
            if (config.getServerUrl().indexOf("start") > 0 && taskUserscount == taskUsersAUTOFIN_DZQHcount && huiqiancount == huiqianAUTOFIN_DZQHcount) {
                return null;
            }
        } catch (UnsupportedEncodingException e) {
            logger.info(e.getMessage(), e);
        }
        return sb.toString();
    }

    public static String Inputstr2Str_ByteArrayOutputStream(InputStream in, String encode) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        byte[] b = new byte[1024];
        int len = 0;
        try {
            if (encode == null || encode.equals("")) {
                // 默认以utf-8形式
                encode = "utf-8";
            }
            while ((len = in.read(b)) > 0) {
                out.write(b, 0, len);
            }
            return out.toString(encode);
        } catch (IOException e) {
            logger.info(e.getMessage(), e);
        }
        return "";
    }

    public static void main(String[] args) throws IOException {
/*		for(int i=0;i<3;i++){
			byte [] buffer =null;
			File file=new File("D:\\20170929.zip");
			FileInputStream fis=new FileInputStream(file);
			ByteArrayOutputStream bos=new ByteArrayOutputStream();
			byte [] b=new byte[1024];
			int n;
			while((n=fis.read(b))!=-1){
				bos.write(b,0,n);
			}
			fis.close();
			bos.close();
			buffer=bos.toByteArray();
			System.out.println(buffer.length);
			String out=uncompressToString(buffer);
			System.out.println(out);
		}*/
		/*for (int i=0;i<3;i++){
			HttpMethod method=null;
			HttpClient client = new HttpClient();
			client.setConnectionTimeout(1500);
			client.setTimeout(20000);
			method = new GetMethod("http://*************/falcon/workflow/api/gzipRetrieveTasksStatus?");
			//method = new GetMethod("http://*************/falcon/workflow/api/gzipUserTasks?userId=F7645239");//代辦任務查詢
			//method = new GetMethod("http://*************/falcon/workflow/api/gzipGroupTasks?userId=F7645239");//會辦任務查詢
			method.setRequestHeader("Connection", "close");
			client.executeMethod(method);
			String jsonStr="";
			jsonStr= inputStreamToString(method.getResponseBodyAsStream());
			System.out.println(jsonStr);
		}*/
        /**
         * 獲取工作流中某個簽核節點的待審核單據
         * 工號數組
         * 節點名稱    廠部FF倉負責人    中央FF倉負責人
         */
        String[] empNos = new String[]{"F4596047", "F4436945", "F1624649", "F4558877", "F4556585", "F4408307", "H4365069", "H4109010", "F9266654", "F2832771", "H2006882", "F9696795", "F9416592", "F4559477", "H2202820", "H4155509", "F3854948", "F4712581", "F9713617", "F4536241", "S7112417", "F9562995", "F2302719", "H4165768", "F4466884", "F4490589", "F4551650", "S7113871", "F9444889", "S7167641", "F1611583", "F3705677", "H2853827", "F2805553", "F9737110", "F3425836", "F4853883", "F4899448", "F3823819", "F4414376", "F3202389", "F2820086", "H4315190", "F3839853", "F4676947", "F9266463", "F4101322", "H2893883", "F9619891", "H4480802", "F4939737", "F4986322", "F9516018", "F8020497", "H2004368", "F4929944", "F4849214", "F4904690", "F4707499", "F1634834", "F4236307", "F9622331", "H2203197", "S6804904", "H4847688", "F8765971", "F3832536", "F1007460", "F3808153", "F9290401", "H2001904", "H4312160", "H2006384", "F9917731", "F2141224", "H4559194", "H2202739", "H4046952", "F7706678", "F9589794", "F4954165", "F3844191", "H5425805", "F9878682", "H5355543", "H4165933", "F9675735", "H5665216", "F9466090", "F4327448", "F9803934", "F9914727", "H5294534", "F9238126", "F9642652", "F4941289", "F3805839", "F4100646", "F4437499", "G4669338", "G4669296", "F8719800", "F2143046", "H2837916", "H2820647", "H2849710", "H6802371", "H2812521", "H4492359", "H4114706", "H2822300", "H2813525", "H2813418", "H2819001", "H2814679", "H2819384", "C0103712", "F9576044", "F4261462", "H4026248", "F3837027", "H4973061", "H6809718", "H5383288", "H5477961", "F2805674", "F9917594", "H4112122", "H2888010", "F1636670", "F2300346", "F3106077", "F3181115", "F4434835", "F4511123", "F9304568"};
        for (int i = 0; i < empNos.length; i++) {
            int time = 0;
            HttpMethod method = null;
            HttpClient client = new HttpClient();
            client.setConnectionTimeout(1500);
            client.setTimeout(20000);
            method = new GetMethod("http://*************/falcon/workflow/api/userTasks?userId=" + empNos[i]);
            method.setRequestHeader("Connection", "close");
            client.executeMethod(method);
            String jsonStr;
            jsonStr = Inputstr2Str_ByteArrayOutputStream(method.getResponseBodyAsStream(), "UTF-8");
            //jsonStr=method.getResponseBodyAsString();
            System.out.println(jsonStr);
            try {
                JSONObject jsonObject = new JSONObject(jsonStr);
                Object aa = jsonObject.get("tasks");
                if (!"null".equals(aa.toString())) {
                    JSONArray jsonArray = jsonObject.getJSONArray("tasks");
                    for (int n = 0; n < jsonArray.length(); n++) {
                        JSONObject object = jsonArray.getJSONObject(n);
                        String result = object.getString("taskname");
                        if ("廠部FF倉負責人".equals(result)) {
                            time++;
                        }
                    }
                }
            } catch (JSONException e) {
                logger.info(e.getMessage(), e);
            }
            if (time != 0) {
                System.out.println(empNos[i] + " : " + time);
            }
        }
    }
}
