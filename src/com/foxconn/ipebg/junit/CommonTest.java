package com.foxconn.ipebg.junit;

import cn.hutool.json.JSONUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhChargelogEntity;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.buessness.common.service.TQhChargelogService;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.common.utils.SpringContextUtil;
import com.foxconn.ipebg.system.entity.*;
import com.foxconn.ipebg.system.service.*;
import com.foxconn.ipebg.system.utils.SendMailUtil;
import com.foxconn.ipebg.system.utils.UsernamePasswordCaptchaToken;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;

import java.util.List;

@ContextConfiguration(locations = {"/applicationContext.xml"})
public class CommonTest extends AbstractJUnit4SpringContextTests {

    @Autowired
    private UserService userService;
    @Autowired
    private WorkFlowService flowService;
    @Autowired
    private TQhAllRelationService allRelationService;
    @Autowired
    private DictService dictService;
    @Autowired
    private WfNodeinfoService wfNodeinfoDao;

    @Autowired
    private WfNodeinfoService wfNodeinfoService;
    @Autowired
    private WfNoderuleService noderuleService;
    @Autowired
    private UserServiceUtil serviceUtil;
    @Autowired
    private TQhChargepathService chargeService;
    @Autowired
    private TQhUserdutyService tQhUserdutyService;
    @Autowired
    private TQhChargelogService chargelogService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private TPubMailrecordService mailrecordService;
    @Autowired
    private TPubAreabaseinfoService areabaseinfoService;


//    @Test
//    public void quartzTest() throws Exception {
//        /*String[] names=applicationContext.getBeanDefinitionNames();
//		for(String s:names){
//			System.out.println(s);
//		}*/
//        String empno = allRelationService.findUserEmpno("TQhWfbuildprojectrocessEntity","JCXTOT051071810270006");
//        System.out.println(empno);
//    }

    @Test
    public void test() {
        List<WfNodeinfoEntity> entityList = wfNodeinfoService.findByFilters("dzqh_xinzengxiangmuhuanbaoshouxubanlishenqingdan");
		System.out.println(JSONUtil.toJsonStr(entityList));
    }

    @Test
    public void test1() {
        flowService.AutoCompleteTask("101800241");
//		WorkFlowService  service = (WorkFlowService) SpringContextUtil.getBean("workFlowService");
//		service.myTask();
//		TQhWfbuildprojectrocessEntity tQhWfbuildprojectrocess = tQhWfbuildprojectrocessService.findById("321bf69a2ed6482088d176d880c357af");
//		System.out.println(JSONUtil.toJsonStr(tQhWfbuildprojectrocess));
//		flowService.getMyTaskByWorkId("dzqh_xinzengxiangmuhuanbaoshouxubanlishenqingdan","2");
		/*WorkFlowEntity entity = new WorkFlowEntity();
		entity.setWorkflowId(tQhWfbuildprojectrocess.getWorkflowid());
		entity.setSerialNo(tQhWfbuildprojectrocess.getSerialno());

		List<WfNodeinfoEntity> infoList = wfNodeinfoService.findForUser(entity.getWorkflowId());
		String taskUser = "";
		for (WfNodeinfoEntity nodeinfoEntity : infoList) {
			taskUser = nodeinfoEntity.getNodename() + ":" + Reflections.getFieldValue(tQhWfbuildprojectrocess, nodeinfoEntity.getColname()) + ";";
		}
		entity.setTaskUsers(taskUser);

		List<WfNodeinfoEntity> infoLists = wfNodeinfoService.findForHuiqian(entity.getWorkflowId());
		String huiqian = "";
		WfNoderuleEntity noderuleEntity = null;
		for (WfNodeinfoEntity list : infoLists) {
			noderuleEntity = noderuleService.findUniqByNodeId(list.getNodeid());
			huiqian = noderuleEntity.getNodeparamname() + ":" + Reflections.getFieldValue(tQhWfbuildprojectrocess, list.getColname()) + ";";
		}
		entity.setHuiqian(huiqian);
		flowService.processStart(entity);*/
    }

    @Test
    public void findAuditUser() {
//        List<AuditConnonUser> data = chargeService.findAuditUser("kchargeno", "kchargename", "IPEZBQ", "TOTBC168-1");
//        System.out.println(data);
        UsernamePasswordCaptchaToken token = new UsernamePasswordCaptchaToken();
        token.setUsername("F1858847");
        token.setUtoken("c03b371af6924b9c8ee4f231a85f5c34");
        TPubMailrecordEntity tPubMailrecordEntity = mailrecordService.findByProperty(token);
        System.out.println(JSONUtil.toJsonStr(tPubMailrecordEntity));
    }

    @Test
    public void testtest(){
        List<AuditHqUser> data = tQhUserdutyService.findAuditUser("62","IPEZBQ");
        System.out.println(data);
    }
    @Test
    public void saveChargLog(){
        TQhChargelogEntity entity = new TQhChargelogEntity();
        entity.setIspass("通過");
        entity.setDecrib("test");
        entity.setSerialno("1213456");
        chargelogService.save(entity);
    }
    @Test
    public void testOrganization(){
        System.out.println(JSONUtil.toJsonStr(organizationService.getUserAllDeptPermision()));
    }

    @Test
    public void getAreaInfoBySuperId(){
        System.out.println(JSONUtil.toJsonStr(areabaseinfoService.getAreaInfoBySuperId("IPEJC")));
    }
}