package com.foxconn.ipebg.system.utils;

import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;

public class FileDownloadUtil {
    public static void fileDownload(HttpServletRequest request,HttpServletResponse response, String filePatherPath, String fileName) throws IOException{
        try {
            String fullPath = filePatherPath + File.separator + fileName;
            ServletOutputStream os=null;
            response.reset();
            fileName = URLEncoder.encode(fileName,"UTF-8");
            response.setCharacterEncoding("utf-8");
            response.setContentType(request.getServletContext().getMimeType(fileName));
//            response.setContentType("application/msword"); // word格式
            response.setHeader("Content-Disposition","attachment;filename="+fileName);
            os = response.getOutputStream();   //直接下载导出
            fileDownload(new FileInputStream(new File(fullPath)),os);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 文件下載
     * @param request request
     * @param response response
     * @param in 文件輸入流
     * @param fileName 文件名稱
     * @throws IOException IO異常
     */
    public static void fileDownload(HttpServletRequest request,HttpServletResponse response, InputStream in, String fileName) throws IOException{
        try {
        	ServletOutputStream os=null;
            response.reset();
            fileName = URLEncoder.encode(fileName,"UTF-8");
            response.setCharacterEncoding("utf-8");
            response.setContentType(request.getServletContext().getMimeType(fileName));
//            response.setContentType("application/msword"); // word格式
            response.setHeader("Content-Disposition","attachment;filename="+fileName);
            os = response.getOutputStream();   //直接下载导出
            fileDownload(in,os);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

        public static void fileDownload( InputStream in,ServletOutputStream os) throws IOException{
            try {
                BufferedInputStream bis= new BufferedInputStream(in);
                byte[] b=new byte[bis.available()+1000];
                int i=0;
                while((i=bis.read(b))!=-1) {
                    os.write(b, 0, i);
                }
                os.flush();
                os.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    public static void fileDownloadExcel(HttpServletResponse response, InputStream in, String fileName) throws IOException{
        try {
            OutputStream os=null;
            response.reset();
            fileName = URLEncoder.encode(fileName,"UTF-8");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
//            response.setContentType("application/msword"); // word格式
            response.setHeader("Content-Disposition","attachment;filename="+fileName);
            byte[] b=new byte[in.available()+1000];
            int i=0;
            os = response.getOutputStream();   //直接下载导出
            while((i=in.read(b))!=-1) {
                os.write(b, 0, i);
            }
            os.flush();
            os.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
