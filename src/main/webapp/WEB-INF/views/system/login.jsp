<%@ page contentType="text/html;charset=UTF-8" %>
<%@ page pageEncoding="UTF-8"%>
<%@ page import="org.apache.shiro.web.filter.authc.FormAuthenticationFilter"%>
<%@ page import="org.apache.shiro.authc.ExcessiveAttemptsException"%>
<%@ page import="org.apache.shiro.authc.IncorrectCredentialsException"%>
<%@ page import="com.foxconn.ipebg.common.utils.Global"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fns" uri="/WEB-INF/taglib/fns.tld" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<%
String error = (String) request.getAttribute(FormAuthenticationFilter.DEFAULT_ERROR_KEY_ATTRIBUTE_NAME);
request.setAttribute("error", error);
%>
<html>
<head>
	<title>${fns:getConfig('sysName')}</title>
	<meta http-equiv="X-UA-Compatible" content="IE=8"/>
	<link rel="stylesheet" type="text/css" href="${ctx}/static/css/bglogin.css" />
	<script>
	var captcha;
	function refreshCaptcha(){  
	    document.getElementById("img_captcha").src="${ctx}/static/images/kaptcha.jpg?t=" + Math.random();  
	}
    $(document).ready(function () {
        if (window != top) {
            top.location.href = location.href;
        }
    });
	</script>
</head>
<body>
	<div>
	<form id="loginForm" action="${ctx}/admin/login" method="post">
		<div class="login_top">
			<div class="login_title">
				${fns:getConfig('sysName')}
			</div>
		</div>
		<div style="float:left;width:100%;">
			<div class="login_main">
				<div class="login_main_top"></div>
				<div class="login_main_errortip">&nbsp;</div>
				<div class="login_main_ln">
					<input type="text" id="username" name="username" value="" onblur="toUperCaseString(this);"/>
				</div>
				<div class="login_main_pw">
					<input type="password" id="password" name="password" value=""/>
				</div>
				<div class="login_main_yzm">
					<div>
					<input type="text" id="captcha" name="captcha"/>
					<img alt="验证码" src="${ctx}/static/images/kaptcha.jpg" title="点击更换" id="img_captcha" onclick="javascript:refreshCaptcha();" style="height:45px;width:85px;float:right;margin-right:98px;"/>
					</div>
				</div>
				<div class="login_main_remb">
					<input id="rm" name="rememberMe" type="hidden"/><!-- <label for="rm"><span>记住我</span></label> -->
				</div>
				<div class="login_main_submit">
					<button onclick=""></button>
				</div>
			</div>
		</div>
		<div style="float: left; display: block; width: 100%; height: 60px; text-align: center; padding-top: 10px;">
			<label>CopyRight© ${fns:getConfig('copyRight')}  版權所有</label>
		</div>
	</form>
	</div>
	<p style="position: absolute;right: 20px;bottom: 0;color: lightgray">
		当前IP:${ip}
	</p>
	<c:choose>
		<c:when test="${error eq 'com.foxconn.ipebg.system.utils.CaptchaException'}">
			<script>
				$(".login_main_errortip").html("验证码错误，请重试");
			</script>
		</c:when>
		<c:when test="${error eq 'com.foxconn.ipebg.system.utils.IpException'}">
			<script>
				$(".login_main_errortip").html("您當前的IP未被授權，请聯繫管理員");
			</script>
		</c:when>
		<c:when test="${error eq 'org.apache.shiro.authc.IncorrectCredentialsException'}">
			<script>
				$(".login_main_errortip").html("帐号或密码错误，请重试");
			</script>
		</c:when>
		<c:when test="${error eq 'org.apache.shiro.authc.UnknownAccountException'}">
			<script>
				$(".login_main_errortip").html("用户名不存在，请重试");
			</script>
		</c:when>
		<%--<c:when test="${error eq 'org.apache.shiro.authc.IncorrectCredentialsException'}">
			<script>
				$(".login_main_errortip").html("用户名不存在，请重试");
			</script>
		</c:when>--%>
	</c:choose>
    <script>
		function toUperCaseString(obj) {
			$(obj).val(obj.value.toUpperCase());
        }
	</script>
</body>
</html>
