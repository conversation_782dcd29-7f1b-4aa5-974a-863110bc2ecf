<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>請假資料</title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
</script>

<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
	<form id="mainform" action="${ctx}/guardLeave/${action}" method="post">
		<table class="formTable">
			<tr>
				<input id="ids" name="ids" type="hidden" value="${model.id }" />
				
			</tr>
			<tr>
				<td>工號：</td>
				<td><input id="empNo" name="empNo" class="easyui-validatebox"
					data-options="width: 150, required:true" value="${model.empNo }" onblur="queryGuardInfo(this);" /></td>
			</tr>
			<tr>
				<td>姓名：</td>
				<td><input id="empName" name="empName" class="easyui-validatebox" disabled="disabled"
					data-options="width: 150, required:true" value="${model.empName }" readonly /></td>
			</tr>
			<tr>
				<td>身份證號：</td>
				<td><input id="psnId" name="psnId" class="easyui-validatebox" disabled="disabled"
						   data-options="width: 150, required:true" value="${model.psnId }" readonly /></td>
			</tr>
			<tr>
				<td>假別：</td>
				<td><input id="leaveType" name="leaveType" class="easyui-combobox" value="${model.leaveType }"
						   data-options="width:150,prompt: '假別', required:true, panelHeight:'auto',valueField:'value',textField:'label',
					url: '${ctx}/system/dict/getDictByType/leave_type'"/></td>
			</tr>
			<tr>
				<td>請假開始日期：</td>
				<td>
					<input id="startDate" name="startDate" class="easyui-my97"
						   value="<fmt:formatDate value="${model.startDate}" pattern='yyyy-MM-dd'/>"
						   datefmt="yyyy-MM-dd" minDate="%y-%M-{%d+1}"
						   data-options="width: 150,required:true"
					<c:if test="${model.startDate lt endOfToday}">
						disabled
					</c:if>
					/>
				</td>
			</tr>
			<tr>
				<td>請假結束日期：</td>
				<td>
					<input id="endDate" name="endDate" class="easyui-my97"
						   value="<fmt:formatDate value="${model.endDate}" pattern='yyyy-MM-dd'/>"
						   datefmt="yyyy-MM-dd" minDate="%y-%M-%d"
						   data-options="width: 150,required:true"
					<c:if test="${model.endDate lt beginOfToday}">
						disabled
					</c:if>
					/>
				</td>
			</tr>
			<tr>
				<td>請假日班別：</td>
				<td>
					<input id="shiftNo" name="shiftNo" class="easyui-combobox" value="${model.shiftNo }"
						   data-options="width:150,prompt: '請假日班別', required:true, panelHeight:'auto',valueField:'value',textField:'label',
					url: '${ctx}/system/dict/getDictByType/shift_type'"/>
				</td>
			</tr>
			<tr>
				<td>請假天數：</td>
				<td>
					<input id="dayCount" name="dayCount" class="easyui-validatebox"
						   data-options="width: 150, required:true" value="${model.dayCount }"  />
				</td>
			</tr>
			<tr>
				<td>請假事由：</td>
				<td>
					<input id="reason" name="reason" class="easyui-validatebox"
						   data-options="width: 150, required:true" value="${model.reason }" />
				</td>
			</tr>
			<tr style="display: none">
				<td><input id="createBy" name="createBy" value="${model.createBy }" />
					<input id="createDate" name="createDate"
						   value="<fmt:formatDate value="${model.createDate}" pattern="yyyy-MM-dd HH:mm:ss"/>" />
					<input id="updateBy" name="updateBy" value="${model.updateBy }" />
					<input id="updateDate" name="updateDate"
						   value="<fmt:formatDate value="${model.updateDate}" pattern="yyyy-MM-dd HH:mm:ss"/>" /></td>
			</tr>

		</table>
	</form>
	<script src='${ctx}/static/js/basics/guardLeave.js?"+Math.random()"'></script>
	<script type="text/javascript">
		//提交表单
		$('#mainform').form({
			onSubmit : function() {
				var isValid = $(this).form('validate');
				if (!isValid) {
					return false;
				}
				var startDateString = $("#startDate").my97("getValue");
				var endDateString = $("#endDate").my97("getValue");
				var startDate = new Date(startDateString);
				var endDate = new Date(endDateString);
				if (startDate > endDate) {
					$.messager.alert('提示', '請假開始日期不能大於請假結束日期', 'info');
					return false;
				}
				debugger
				if (endDate.getTime() - startDate.getTime() > 1000 * 60 * 60 * 24 * 14) {
					$.messager.alert('提示', '請假天数不能大於15天', 'info');
					return false;
				}
				return true;
			},
			success : function(data) {
				successTip(data, dg, d);
			}
		});
	</script>
</body>
</html>