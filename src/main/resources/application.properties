#oracle database setting
jdbc.driver=org.postgresql.Driver
#test sgas1227
jdbc.url=***********************************************
jdbc.username=sgas_test
jdbc.password=UIxACS7ykqgxfn1bcYdbH7JeRWXxN7SNThDERD0rvM0FjnbqWVbUCtLaXwGCImmeDzsB90f2xQv4WtQI8n4XmQ==

#sgas-ty sgasty210809 
#jdbc.url=********************************************
#jdbc.username=sgasty
#jdbc.password=mj2cQ04MvNBpAjpd2YpwIgHbYw4HNQ5EBdMTrPhDTFEdVDHvPnhzk8Bv8iJLbx5UnjQCwTqhKEOm9s6PmM1ehg==

#sgas-jc sgas_jc_200916
#jdbc.url=*********************************************
#jdbc.username=sgas_jc
#jdbc.password=HSWzQKTieMVvkDO4qLwDhi9/TsCw8ahoPveGpDbQoLPzQzAu6hrSmwF4YqY1qihJqiIzpOWpk/f/zMmGuvOIfw==

#connection pool settings
jdbc.pool.maxIdle=5
jdbc.pool.maxActive=40

#hibernate settings
hibernate.show_sql=true
hibernate.format_sql=true
hibernate.dialect=org.hibernate.dialect.PostgreSQL82Dialect
hibernate.search.default.indexBase=indexes
#hibernate.hbm2ddl.auto=update

#cache settings
hibernate.ehcache.configFile=cache/ehcache-hibernate-local.xml
ehcache.configFile=cache/ehcache-local.xml

#admin path
adminPath=/admin
#??????
sendMailAuditPath=/newEsign
#FactoryID
#factoryID=JC
factoryID=TY

sysName=????????
copyRight=Fii-iPEBG-iPEG-AI??????
