<!DOCTYPE HTML>
<html>
<head>
<meta charset="UTF-8">
<title>xss-test</title>
<script src="../dist/template-native.js"></script>
</head>

<body>
<div id="content"></div>
<script id="test" type="text/html">
<!--<img title="这是没转义的输出" src="<%=#url_0%>" />-->
<img src="<%=url_1%>" />
<img src="<%=url_2%>" />
<img src="<%=url_3%>" data-index="<%=index%>" />
</script>

<script>
var data = {
	url_0: 'http://mat1.gtimg.com/www/images/qq2012/qqlogo_1x.png?" onload="alert(\'no escape\')"',
	url_1: 'http://mat1.gtimg.com/www/images/qq2012/qqlogo_1x.png?" onload=alert(1)',
	url_2: 'http://mat1.gtimg.com/www/images/qq2012/qqlogo_1x.png?&#34; onload=alert(2)',
	url_3: 'http://mat1.gtimg.com/www/images/qq2012/qqlogo_1x.png?\\',
	index: '\\&quot;&#38;#34; onload=alert(2)'
};
var html = template('test', data);
document.getElementById('content').innerHTML = html;
</script>
</body>
</html>