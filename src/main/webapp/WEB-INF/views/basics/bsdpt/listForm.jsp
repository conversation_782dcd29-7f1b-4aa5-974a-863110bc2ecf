<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>部門基本資料</title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
</script>

<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
	<form id="mainform" action="${ctx}/bsdpt/${action}" method="post">
		<table class="formTable">
			<tr>
				<%-- 	<td>資料識別ID  Primary：</td>
				<td>
				    					<input type="hidden" name="id" value="${bsDpt.id}"/>
															<input id="id" name="id" class="easyui-validatebox" data-options="width: 150" value="${bsDpt.id }" />
									</td> --%>
				<input id="ids" name="ids" type="hidden" value="${bsDpt.id }" />
				
			</tr>
			<tr>
				<td>事業群：</td>
				<td><input id="dptQun" name="dptQun" class="easyui-validatebox"
					data-options="width: 150,required:true" value="${bsDpt.dptQun }" /></td>
			</tr>
			<tr>
				<td>事業處：</td>
				<td><input id="dptChu" name="dptChu" class="easyui-validatebox"
					data-options="width: 150,required:true" value="${bsDpt.dptChu }" /></td>
			</tr>
			<tr>
				<td>部門名稱：</td>
				<td><input id="dptBu" name="dptBu" class="easyui-validatebox"
					data-options="width: 150,required:true" value="${bsDpt.dptBu }" /></td>
			</tr>
			<tr>
				<td>單位：</td>
				<td><input id="dptKe" name="dptKe" class="easyui-validatebox"
					data-options="width: 150" value="${bsDpt.dptKe }" /></td>
			</tr>
			<tr>
				<td>單位代碼：</td>
				<td><input id="dptId" name="dptId" class="easyui-validatebox"
					data-options="width: 150" value="${bsDpt.dptId }" /></td>
			</tr>
			<tr>
				<td>費用代碼：</td>
				<td><input id="costId" name="costId" class="easyui-validatebox"
					data-options="width: 150" value="${bsDpt.costId }" /></td>
			</tr>
			<tr style="display: none">
				<td>
					<input id="createBy" name="createBy"
						   class="easyui-validatebox" data-options="width: 150"
						   readonly="readonly"
						   value="${bsDpt.createBy }" />
					<input id="createDate" name="createDate"
						   class="easyui-my97"
						   data-options="width: 160"
						   disabled="disabled"
						   datefmt="yyyy-MM-dd HH:mm:ss"
						   data-options="width: 160"
						   value="<fmt:formatDate value="${bsDpt.createDate}"/>"
					/>
					<input id="updateBy" name="updateBy"
						   class="easyui-validatebox" data-options="width: 150"
						   readonly="readonly"
						   value="${bsDpt.updateBy }" />
					<input id="updateDate" name="updateDate"
						   class="easyui-my97"
						   data-options="width: 160"
						   disabled="disabled"
						   datefmt="yyyy-MM-dd HH:mm:ss"
						   data-options="width: 160"
						   value="${bsDpt.updateDate}" />
				</td>
			</tr>
			<%-- <tr>
				<td>刪除標識：</td>
				<td><input id="delFlag" name="delFlag"
					class="easyui-validatebox" data-options="width: 150"
					value="${bsDpt.delFlag }" /></td>
			</tr> --%>
		</table>
	</form>
	</div>
	<script src='${ctx}/static/js/basics/bsdpt.js?"+Math.random()"'></script>
	<script type="text/javascript">
		//提交表单
		$('#mainform').form({
			onSubmit : function() {
				var isValid = $(this).form('validate');
				return isValid; // 返回false终止表单提交
			},
			success : function(data) {
				successTip(data, dg, d);
			}
		});
	</script>
</body>
</html>