<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>警衛費用結賬明細表</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>

</head>
<body>
<div id="tb" style="padding:5px;height:auto">
        <form id="searchFrom" action="">
		<input type="text" id="filter_buNam" name="buNam" class="easyui-combobox" />
		<input type="text" id="filter_legalId" name="legalId" class="easyui-combobox" />
		<input type="text" name="costId" class="easyui-validatebox" data-options="width:150,prompt: '費用代碼'"/>
		<%--<input type="text" id="filter_workMonth" name="workMonth" class="easyui-my97" datefmt="yyyy-MM" data-options="width:150,prompt: '月份'"/>--%>
		<input type="text" id="filter_workMonth" name="workMonth" class="easyui-validatebox Wdate"  onClick="WdatePicker({dateFmt:'yyyyMM',alwaysUseStartDate:true})" data-options="width:150,prompt: '月份'"/>

		<span class="toolbar-item dialog-tool-separator"></span>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
		   onclick="listSearchReset()">重置</a>
		<a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel"
			   onclick="exportExcel()">导出Excel</a>
	</form>

  </div>
<table id="dg"></table>
<div id="dlg"></div>

<script src="${ctx}/static/js/basics/tempworkcostdetailgather.js?random=<%= Math.random()%>"></script>
</body>
</html>