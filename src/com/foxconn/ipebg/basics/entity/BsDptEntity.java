package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * 部門基本資料
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-12-21 14:54:46
 */
@Entity
@Table(name = "BS_DPT")
@DynamicUpdate
@DynamicInsert
public class BsDptEntity extends DataEntity<BsDptEntity> implements
		Serializable {
	private static final long serialVersionUID = 1L;

	// 事業群
	private String dptQun;
	// 事業處
	private String dptChu;
	// 部門名稱
	private String dptBu;
	// 課級單位名稱
	private String dptKe;
	// 單位代碼
	private String dptId;
	// 費用代碼
	private String costId;

	/**
	 * 设置：事業群
	 */
	public void setDptQun(String dptQun) {
		this.dptQun = dptQun;
	}

	/**
	 * 获取：事業群
	 */

	@Column(name = "DPT_QUN", nullable = false, length = 20)
	public String getDptQun() {
		return dptQun;
	}

	/**
	 * 设置：事業處
	 */
	public void setDptChu(String dptChu) {
		this.dptChu = dptChu;
	}

	/**
	 * 获取：事業處
	 */

	@Column(name = "DPT_CHU", nullable = false, length = 20)
	public String getDptChu() {
		return dptChu;
	}

	/**
	 * 设置：部門名稱
	 */
	public void setDptBu(String dptBu) {
		this.dptBu = dptBu;
	}

	/**
	 * 获取：部門名稱
	 */

	@Column(name = "DPT_BU", nullable = false, length = 20)
	public String getDptBu() {
		return dptBu;
	}

	/**
	 * 设置：課級單位名稱
	 */
	public void setDptKe(String dptKe) {
		this.dptKe = dptKe;
	}

	/**
	 * 获取：課級單位名稱
	 */

	@Column(name = "DPT_KE", nullable = false, length = 20)
	public String getDptKe() {
		return dptKe;
	}

	/**
	 * 设置：單位代碼
	 */
	public void setDptId(String dptId) {
		this.dptId = dptId;
	}

	/**
	 * 获取：單位代碼
	 */

	@Column(name = "DPT_ID", nullable = false, length = 20)
	public String getDptId() {
		return dptId;
	}

	/**
	 * 设置：費用代碼
	 */
	public void setCostId(String costId) {
		this.costId = costId;
	}

	/**
	 * 获取：費用代碼
	 */

	@Column(name = "COST_ID", nullable = false, length = 20)
	public String getCostId() {
		return costId;
	}

}
