var dg;
var d;
$(function(){
    dg=$('#dg').datagrid({
        method: "get",
        url:ctx+'/epointtestitemtemp/list',
        fit : true,
        fitColumns : true,
        border : false,
        idField : 'id',
        striped:true,
        pagination:true,
        rownumbers:true,
        pageNumber:1,
        pageSize : 20,
        pageList : [ 10, 20, 30, 40, 50 ],
        singleSelect:true,
        columns:[[
							                    { field: 'id', title: '主鍵',hidden:true},
											                    { field: 'empNo', title: '申請人工號',sortable:true,width:100}
					,
																                    { field: 'serialno', title: '任務編號',sortable:true,width:100}
					,
																                    { field: 'pointno', title: '點位編碼',sortable:true,width:100}
					,
																                    { field: 'dptQun', title: '事業群',sortable:true,width:100}
					,
																                    { field: 'dptChu', title: '事業處',sortable:true,width:100}
					,
																                    { field: 'dptBu', title: '部門',sortable:true,width:100}
					,
																                    { field: 'dptKe', title: '單位',sortable:true,width:100}
					,
																                    { field: 'manager', title: '管理人',sortable:true,width:100}
					,
																                    { field: 'pointLocationType', title: '點位類別:1.直飲水2.自建飲用水系統3.二次供水池/箱4.飲用水轉運水罐5.其他',sortable:true,width:100}
					,
																                    { field: 'auditType', title: '審核類別：0.新啟用1.重新啟用',sortable:true,width:100}
					,
																                    { field: 'building', title: '樓棟',sortable:true,width:100}
					,
																                    { field: 'floor', title: '樓層',sortable:true,width:100}
					,
																                    { field: 'location', title: '位置',sortable:true,width:100}
					,
																                    { field: 'equipmentNumber', title: '設備序號',sortable:true,width:100}
					,
																                    { field: 'installLocation', title: '安裝位置',sortable:true,width:100}
					,
																                    { field: 'useStatus', title: '點位狀態：0.新申請1.使用中2.手動停用（需重新申請該點位）3.複檢停用',sortable:true,width:100}
					,
																                    { field: 'attachids', title: '附件Id',sortable:true,width:100}
					,
																                    { field: 'reattachids', title: '補充說明附件Id',sortable:true,width:100}
					,
																                    { field: 'createBy', title: '創建人',sortable:true,width:100}
					,
																                    { field: 'createDate', title: '創建時間',sortable:true,width:100}
					,
																                    { field: 'updateBy', title: '更新者',sortable:true,width:100}
					,
																                    { field: 'updateDate', title: '更新時間',sortable:true,width:100}
					,
																                    { field: 'delFlag', title: '刪除標識',sortable:true,width:100}
												        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        rowTooltip: true,
        toolbar:'#tb'
    });
});

//弹窗增加
function add() {
    d=$("#dlg").dialog({
        title: '添加點位檢測臨時明細表',
        width: 380,
        height: 380,
        href:ctx+'/epointtestitemtemp/create',
        maximizable:true,
        modal:true,
        buttons:[{
            text:'确认',
            handler:function(){
                $("#mainform").submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}

//删除
function del(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data){
        if (data){
	$.ajax({
                type:'get',
                url:ctx+"/epointtestitemtemp/delete/"+row.id,
                success: function(data){
                    successTip(data,dg);
                }
            });
        }
    });
}

//弹窗修改
function upd(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    d=$("#dlg").dialog({
        title: '修改點位檢測臨時明細表',
        width: 380,
        height: 340,
        href:ctx+'/epointtestitemtemp/update/'+row.id,
        maximizable:true,
        modal:true,
        buttons:[{
            text:'修改',
            handler:function(){
                $('#mainform').submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}
//创建查询对象并查询
function cx(){
    var obj=$("#searchFrom").serializeObject();
    dg.datagrid('load',obj);
}
//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("reset");
}
//导出excel
function exportExcel(){
    var form = document.getElementById("searchFrom");
    searchFrom.action=ctx+'/epointtestitemtemp/exportExcel';
    form.submit();
}