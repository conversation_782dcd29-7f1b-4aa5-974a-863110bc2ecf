package com.foxconn.ipebg.buessness.common.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 簽核業務中間表，保存所有簽核業務主信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-08 09:28:35
 */
@Entity
@Table(name = "T_QH_ALL_RELATION")
@DynamicUpdate
@DynamicInsert
public class TQhAllRelationEntity extends DataEntity<TQhAllRelationEntity>  implements Serializable {
    private static final long serialVersionUID = 1L;

            //流程編碼
        private String workflowid;
            //工單流水號
        private String serialno;
            //工單實例ID
        private String processid;
            //流程版本
        private String version;
            //表對應實體名稱
        private String dtoName;
            //字典：DICT_WORKSTATUS，對應表單狀態
        private String workstatus;
        private String wfName;
            /**
         * 设置：流程編碼
         */
        public void setWorkflowid(String workflowid) {
            this.workflowid = workflowid;
        }
    /**
     * 获取：流程編碼
     */
                    @Column(name = "WORKFLOWID", nullable = false, length = 20)
            public String getWorkflowid() {
                return workflowid;
            }
        
            /**
         * 设置：工單流水號
         */
        public void setSerialno(String serialno) {
            this.serialno = serialno;
        }
    /**
     * 获取：工單流水號
     */
                    @Column(name = "SERIALNO", nullable = false, length = 20)
            public String getSerialno() {
                return serialno;
            }
        
            /**
         * 设置：工單實例ID
         */
        public void setProcessid(String processid) {
            this.processid = processid;
        }
    /**
     * 获取：工單實例ID
     */
                    @Column(name = "PROCESSID", nullable = false, length = 20)
            public String getProcessid() {
                return processid;
            }
        
            /**
         * 设置：流程版本
         */
        public void setVersion(String version) {
            this.version = version;
        }
    /**
     * 获取：流程版本
     */
                    @Column(name = "VERSION", nullable = false, length = 20)
            public String getVersion() {
                return version;
            }
        
            /**
         * 设置：表對應實體名稱
         */
        public void setDtoName(String dtoName) {
            this.dtoName = dtoName;
        }
    /**
     * 获取：表對應實體名稱
     */
                    @Column(name = "DTO_NAME", nullable = false, length = 20)
            public String getDtoName() {
                return dtoName;
            }
        
            /**
         * 设置：字典：DICT_WORKSTATUS，對應表單狀態
         */
        public void setWorkstatus(String workstatus) {
            this.workstatus = workstatus;
        }
    /**
     * 获取：字典：DICT_WORKSTATUS，對應表單狀態
     */
                    @Column(name = "WORKSTATUS", nullable = false, length = 20)
            public String getWorkstatus() {
                return workstatus;
            }
    @Column(name = "wf_name", nullable = false, length = 100)
    public String getWfName() {
        return wfName;
    }

    public void setWfName(String wfName) {
        this.wfName = wfName;
    }
}
