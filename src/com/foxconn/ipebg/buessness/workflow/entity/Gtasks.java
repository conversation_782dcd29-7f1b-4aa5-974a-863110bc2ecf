package com.foxconn.ipebg.buessness.workflow.entity;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * Company foxconn
 * Copyright (C) 2004-2018 All Rights Reserved.
 */
public class Gtasks {
    //填單人工號
    private String makerno;
    //填單人名稱
    private String makername;
    //填單時間
    private Date createtime;
    //表單狀態
    private String workstatus;
    //工單流水號
    private String serialno;
    //表單類型
    private String wfName;
    //節點名稱
    private String taskName;
    //跳轉對應審核頁面
    private String auditAction;

    public String getMakerno() {
        return makerno;
    }

    public void setMakerno(String makerno) {
        this.makerno = makerno;
    }

    public String getMakername() {
        return makername;
    }

    public void setMakername(String makername) {
        this.makername = makername;
    }
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public String getWorkstatus() {
        return workstatus;
    }

    public void setWorkstatus(String workstatus) {
        this.workstatus = workstatus;
    }

    public String getSerialno() {
        return serialno;
    }

    public void setSerialno(String serialno) {
        this.serialno = serialno;
    }

    public String getWfName() {
        return wfName;
    }

    public void setWfName(String wfName) {
        this.wfName = wfName;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getAuditAction() {
        return auditAction;
    }

    public void setAuditAction(String auditAction) {
        this.auditAction = auditAction;
    }
}
