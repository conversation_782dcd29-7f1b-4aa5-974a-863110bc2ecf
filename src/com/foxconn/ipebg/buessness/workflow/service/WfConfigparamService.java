package com.foxconn.ipebg.buessness.workflow.service;

import com.foxconn.ipebg.buessness.workflow.entity.WfConfigparamEntity;
import com.foxconn.ipebg.buessness.workflow.dao.WfConfigparamDao;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import com.foxconn.ipebg.common.service.BaseService;
import com.foxconn.ipebg.system.entity.Dict;
import com.foxconn.ipebg.system.service.DictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 流程參數配置表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-06 11:04:36
 */
@Service
@Transactional(readOnly=true)
public class  WfConfigparamService extends BaseBusinessService<WfConfigparamEntity, String> {
    @Autowired
    private WfConfigparamDao wfConfigparamDao;
    @Autowired
    private DictService dictService;
    @Override
    public HibernateDao<WfConfigparamEntity, String> getEntityDao() {
        return wfConfigparamDao;
    }
    public List<WfConfigparamEntity> findByFilters(String workflowid){
        Dict dict = dictService.getDictByTypeAndVlaue("workflow_code",workflowid);
        Assert.notNull(dict);
        String version = dict.getLabel();
        Map param = new HashMap();
        param.put("workflowid",workflowid);
        param.put("version",version);
        return wfConfigparamDao.findAllByCollect(param);
    }
}
