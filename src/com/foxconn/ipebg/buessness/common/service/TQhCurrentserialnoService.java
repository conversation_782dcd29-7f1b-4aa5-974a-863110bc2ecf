package com.foxconn.ipebg.buessness.common.service;

import com.foxconn.ipebg.buessness.common.entity.TQhCurrentserialnoEntity;
import com.foxconn.ipebg.buessness.common.dao.TQhCurrentserialnoDao;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 生成工單流水功能表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-08 09:28:36
 */
@Service
@Transactional(readOnly=true)
public class  TQhCurrentserialnoService extends BaseBusinessService<TQhCurrentserialnoEntity, String>{
    @Autowired
    private TQhCurrentserialnoDao tQhCurrentserialnoDao;
    @Override
    public HibernateDao<TQhCurrentserialnoEntity, String> getEntityDao() {
        return tQhCurrentserialnoDao;
    }

    public TQhCurrentserialnoEntity findByCondition(String localid,String curdate){
        return tQhCurrentserialnoDao.findUnique("from TQhCurrentserialnoEntity t where t.localid=?0 and t.curdate=?1",localid,curdate);
    }
}
