package com.foxconn.ipebg.system.utils;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

import com.foxconn.ipebg.common.utils.Constant;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.web.filter.authc.FormAuthenticationFilter;
import org.apache.shiro.web.util.WebUtils;

/**
 * 扩展认证默认过滤
 * <AUTHOR>
 * @date 2014年12月2日 下午10:47:09
 */
public class FormAuthenticationCaptchaFilter extends FormAuthenticationFilter {

	public static final String DEFAULT_CAPTCHA_PARAM = "captcha";
	public static final String DEFAULT_LOGINTYPE_PARAM = "loginType";
	public static final String DEFAULT_UPHONENUM_PARAM = "uphoneNum";
	public static final String DEFAULT_UTOKEN_PARAM = "utoken";
	private String captchaParam = DEFAULT_CAPTCHA_PARAM;
	private String loginTypeParam = DEFAULT_LOGINTYPE_PARAM;
	private String uphoneNumParam = DEFAULT_UPHONENUM_PARAM;
	private String utokenParam = DEFAULT_UTOKEN_PARAM;

	public String getCaptchaParam() {
		return captchaParam;
	}
	public String getLoginTypeParam() {
		return loginTypeParam;
	}
	public String getUphoneNumParam() {
		return uphoneNumParam;
	}
	public String getUtokenNumParam() {
		return utokenParam;
	}
	protected String getParam(ServletRequest request,String paramName) {
		return WebUtils.getCleanParam(request, paramName);
	}

	protected AuthenticationToken createToken(ServletRequest request, ServletResponse response) {
		String username = getUsername(request);
		String password = getPassword(request);
		String captcha = getParam(request,getCaptchaParam());
		String loginType = Constant.LoginType.NORMAL.getValue();
		String ip = IPUtil.getIpAddress((HttpServletRequest) request);

		if(request.getParameter("loginType")!=null && !"".equals(request.getParameter("loginType").trim())){
			loginType = request.getParameter("loginType");
		}
		boolean rememberMe = isRememberMe(request);
		String uphoneNum = getParam(request,getUphoneNumParam());
		String utoken = getParam(request,getUtokenNumParam());
//		boolean rememberMe = isRememberMe(request);
		String host = getHost(request);
		return new UsernamePasswordCaptchaToken(username,password.toCharArray(), rememberMe, host, captcha,loginType,utoken,uphoneNum,ip);
	}

}