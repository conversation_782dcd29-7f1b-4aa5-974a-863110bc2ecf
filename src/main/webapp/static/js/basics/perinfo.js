var dg;
var d;
$(function(){
    dg=$('#dg').datagrid({
        method: "get",
        url:ctx+'/perinfo/list',
        fit : true,
        fitColumns : true,
        border : false,
        idField : 'id',
        striped:true,
        pagination:true,
        rownumbers:true,
        pageNumber:1,
        pageSize : 20,
        pageList : [ 10, 20, 30, 40, 50 ],
        singleSelect:true,
        columns:[[
					{ field: 'id', title: '資料識別ID  Primary', hidden : true},
							                    
					/*{ field: 'cardId', title: '硬卡號',sortable:true,width:100}
					 * 
					,*/
					{ field: 'empNo', title: '工號',sortable:true,width:100},
					{ field: 'empName', title: '姓名',sortable:true,width:100},
					{ field: 'psnId', title: '身份證號',sortable:true,width:100},
					{ field: 'company', title: '公司',sortable:true,width:100},
					{ field: 'entryDate', title: '入職日期', sortable: true, width: 100 },
					{ field: 'passDate', title: '考試合格日期',sortable:true,width:100},
					{ field: 'createBy', title: '創建人',sortable:true,width:100},
					{ field: 'createDate', title: '創建時間',sortable:true,width:100},
					{ field: 'resign', title: '在職狀態',sortable:true,width:100,
						formatter : function(value, row, index) {
							if (value) {
								return "離職";
							} else {
								return "在職";
							}
						}
					},
					{ field: 'updateBy', title: '更新人',sortable:true,width:100},
					{ field: 'updateDate', title: '更新時間',sortable:true,width:100}
					]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        rowTooltip: true,
        toolbar:'#tb'
    });
    
  
    
 // 0419批量上傳模板下載
	$("#btnBatchImportTpl").click(batchImportTpl);

});



//弹窗增加
function add() {
    d=$("#dlg").dialog({
        title: '添加合格人員',
        width: 280,
        height: 250,
        href:ctx+'/perinfo/create',
        maximizable:true,
        modal:true,
        buttons:[{
            text:'确认',
            handler:function(){
            	//驗證
				var empNo = $.trim($("#empNo").val());
				var entryDate = $('#entryDate').datebox('getValue');
				var passDate= $('#passDate').datebox('getValue');
			    var empName=$.trim($("#empName").val());
			   
				if (empNo == "") {
					$.messager.alert("溫馨提示", "工號不能為空！", "info");
					return;
				}
				if (entryDate == "") {
					$.messager.alert("溫馨提示", "入職日期不能為空！", "info");
					return;
				}
				if (passDate == "") {
					$.messager.alert("溫馨提示", "合格日期不能為空！", "info");
					return;
				}
				if (empName == "") {
					$.messager.alert("溫馨提示", "姓名不能為空！", "info");
					return;
				}
				//test()
                $("#mainform").submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}

//删除
function del(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '是否確定要將該人員標記為離職狀態？', function(data){
        if (data){
	$.ajax({
                type:'get',
                url:ctx+"/perinfo/delete/"+row.id,
                success: function(data){
                    successTip(data,dg);
                }
            });
        }
    });
}

//弹窗修改
function upd(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    d=$("#dlg").dialog({
        title: '修改合格人員',
        width: 280,
        height: 250,
        href:ctx+'/perinfo/update/'+row.id,
        maximizable:true,
        modal:true,
        buttons:[{
            text:'修改',
            handler:function(){
            	
            	//驗證
            	//var postShift = $.trim($("#postShift").val());
				//var shiftNo = $('#shiftNo').combobox('getValue');
				var empNo = $.trim($("#empNo").val());
				var entryDate = $('#entryDate').datebox('getValue');
				var passDate= $('#passDate').datebox('getValue');
			    var company=$.trim($("#company").val());
			    var psnId=$.trim($("#psnId").val());
			    var empName=$.trim($("#empName").val());
				/*if (postShift == "1班制" && shiftNo == "B") {
					$.messager.alert("溫馨提示", "一班制不能排晚班！", "info");
					return;
				}*/
			    
			    var intStrLen =psnId.length;
				  
				    if (intStrLen != 18) {
			            error = "輸入身份證長度不正確！";
			            alert(error);
			           
			            return false;
			        }
			    
			    
				if (empNo == "") {
					$.messager.alert("溫馨提示", "工號不能為空！", "info");
					return;
				}
				if (entryDate == "") {
					$.messager.alert("溫馨提示", "入職日期不能為空！", "info");
					return;
				}
				if (passDate == "") {
					$.messager.alert("溫馨提示", "合格日期不能為空！", "info");
					return;
				}
				if (company == "") {
					$.messager.alert("溫馨提示", "公司不能為空！", "info");
					return;
				}
				if (psnId == "") {
					$.messager.alert("溫馨提示", "身份證號不能為空！", "info");
					return;
				}
				if (empName == "") {
					$.messager.alert("溫馨提示", "姓名不能為空！", "info");
					return;
				}
				//test()
                $('#mainform').submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}
//创建查询对象并查询
function cx(){
    var obj=$("#searchFrom").serializeObject();
  
    dg.datagrid('load',obj);
}
//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("reset");
}


function queryEmpInfo() {
	$("#empNo").val($("#empNo").val().toUpperCase());
	var empNo = $.trim($("#empNo").val().toUpperCase());
	if (empNo != null && empNo != "") {
		$.ajax({
			url : ctx + '/bstrainee/getBsTrainee/' + empNo,
			type : 'GET',
			beforeSend : ajaxLoading,
			// dataType: 'json',
			// data : {
			// 	empNo : empNo
			// },
			success : function(data) {
				ajaxLoadEnd();
				if (!data) {
					$.messager.alert("溫馨提示", "工號不存在或工號重複，請確認！", "error");
					$('#empName').val('');
					$('#company').val('');
					$('#psnId').val('');
				} else {
					$('#empName').val(data.empName);
					$('#company').val(data.company);
					$('#psnId').val(data.psnId);
				}
			}
		});
	}
}
//导出excel
/*function exportExcel(){
    var form = document.getElementById("searchFrom");
    searchFrom.action=ctx+'/perinfo/exportExcel';
    form.submit();
}*/


//019導入
function btnUploadExcel() {

	// var table = document.getElementById("buildprojectApplyItemTable");
	// var len = table.rows.length;
	// if (len>1)
	// {
	// for (var i = 0; i < len-1 ; i++) {
	// table.deleteRow(len-i-1);
	// }
	// }

	var file_data = $("#batchFile").prop("files")[0];
	if (file_data.name == "") {
		$.messager.alert('系統提示', '請選擇文件，再進行導入！', 'info');
		return;
	}
	var suffix = /\.[^\.]+$/.exec(file_data.name);
	var isExcel2003 = "";
	if (suffix == ".xls") {
		isExcel2003 = "true";
	} else if (suffix == ".xlsx") {
		isExcel2003 = "false";
	} else {
		$.messager.alert('系統提示', '導入文件格式不正確！', 'info');
		return;
	}
	// 创建
	var form_data = new FormData();
	// 获取文件
	form_data.append("file", file_data);
	form_data.append("isExcel2003", isExcel2003);

	// 把所以表单信息
	$.ajax({
		type : "POST",
		url : ctx + "/perinfo/upload",
		// dataType : "json",
		processData : false, // 注意：让jQuery不要处理数据
		contentType : false, // 注意：让jQuery不要设置contentType
		data : form_data
	}).success(function(msg) {
		// console.log(msg)
		if (msg == 'success') {
			$("#labelListAddResult").html("導入成功");
			$("#downloadError").hide();
		} else {
			if (msg == '2') {
				$("#labelListAddResult").html("導入失敗, ");
				$("#downloadError").show();
			} else {
				$("#labelListAddResult").html(msg);
				$("#downloadError").show();
			}
		}
	}).fail(function(msg) {
		$("#labelListAddResult").html("導入失敗");
	});
}

// 批量導入模板下載
function batchImportTpl() {
	document.location.href = ctx + '/perinfo/downLoad/batchImportTpl';
}

function openBatchImportWin() {
	var optionWin = $("#optionWin").window();

	optionWin.window({
		center : true
	});
	// 顯示於畫面中
	optionWin.slideDown();
	// 清空optionWin頁面中所有元件的值
	/*
	 * $("#pointForm .easyui-combobox").each(function(){
	 * $(this).combobox("clear"); });
	 */
	$("#batchImportForm").form("clear");
	$("#labelListAddResult").html("");
	$("#downloadError").hide();
	// optionWin.find("input").val("");
	optionWin.window("open");
}