
package com.foxconn.ipebg.common.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.foxconn.ipebg.common.utils.IdGen;
import com.foxconn.ipebg.system.entity.User;
import com.foxconn.ipebg.system.utils.UserUtil;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.persistence.Transient;
import java.util.Date;

/**
 * 数据Entity类
 * <AUTHOR>
 * @version 2014-05-16
 */
@MappedSuperclass
public abstract class DataEntity<T>{
	/**
	 * 删除标记（0：正常；1：删除；2：审核；）
	 */
	public static final String DEL_FLAG_NORMAL = "0";
	public static final String DEL_FLAG_DELETE = "1";
	private static final long serialVersionUID = 1L;
	/**
	 * 实体编号（唯一标识）
	 */
	protected String id;
	protected String createBy;	// 创建者
	protected Date createDate;	// 创建日期
	protected String updateBy;	// 更新者
	protected Date updateDate;	// 更新日期
	protected String delFlag; 	// 删除标记（0：正常；1：删除；2：审核）
	@Transient
	protected boolean isNewRecord = false;
	@Transient
	protected boolean isAssignCreateBy = true;//是否指定創建人 
	
	@Transient
	public boolean isAssignCreateBy() {
		return isAssignCreateBy;
	}

	public void setAssignCreateBy(boolean isAssignCreateBy) {
		this.isAssignCreateBy = isAssignCreateBy;
	}

	public DataEntity() {
		super();
		this.delFlag = DEL_FLAG_NORMAL;
	}
	
	public DataEntity(String id) {

	}
	
	/**
	 * 插入之前执行方法，需要手动调用
	 */
	public void preInsert(){
		// 不限制ID为UUID，调用setIsNewRecord()使用自定义ID
		if (!this.isNewRecord){
			setId(IdGen.uuid());
		}
		User user = UserUtil.getCurrentUser();
		if (user != null && StringUtils.isNotBlank(user.getId()+"")){
			//this.updateBy = user.getLoginName();
			this.createBy = user.getLoginName();
		}
		//this.updateDate = new Date();
		//this.createDate = this.updateDate;
		this.createDate = new Date();
	}
	
	/**
	 * 更新之前执行方法，需要手动调用
	 */
	public void preUpdate(){
		User user = UserUtil.getCurrentUser();
		if (StringUtils.isNotBlank(user.getId()+"")){
			this.updateBy = user.getLoginName();
		}
		this.updateDate = new Date();
	}
	
	@Column(name = "CREATE_BY", nullable = false, length = 20)
	public String getCreateBy() {
		return createBy;
	}

	public void setCreateBy(String createBy) {
		this.createBy = createBy;
	}

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "CREATE_DATE", nullable = false, length = 20)
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "UPDATE_BY", nullable = false, length = 20)
	public String getUpdateBy() {
		return updateBy;
	}

	public void setUpdateBy(String updateBy) {
		this.updateBy = updateBy;
	}

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "UPDATE_DATE", nullable = false, length = 20)
	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	@JsonIgnore
	@Column(name = "DEL_FLAG", nullable = false, length = 20)
	public String getDelFlag() {
		return delFlag;
	}

	public void setDelFlag(String delFlag) {
		this.delFlag = delFlag;
	}
	@Id
	@Column(name = "ID", unique = true, nullable = false)
	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}
    @Transient
	public boolean isNewRecord() {
		return isNewRecord|| com.foxconn.ipebg.common.utils.StringUtils.isBlank(getId());
	}

	public void setNewRecord(boolean newRecord) {
		isNewRecord = newRecord;
	}
}
