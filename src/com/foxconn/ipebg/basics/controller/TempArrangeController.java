package com.foxconn.ipebg.basics.controller;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Date;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.web.BaseController;

import com.foxconn.ipebg.basics.entity.TempArrangeErrorDto;

import com.foxconn.ipebg.basics.entity.TempArrangeEntity;
import com.foxconn.ipebg.basics.service.TempArrangeService;
import com.foxconn.ipebg.system.utils.Excel2007Utils;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import com.foxconn.ipebg.system.utils.FileDownloadUtil;
import com.foxconn.ipebg.system.utils.IPUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;

/**
 * 異常人員信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-03-11 14:46:49
 */
@Controller
@RequestMapping("temparrange")
public class TempArrangeController extends BaseController {

	@Autowired
	private TempArrangeService tempArrangeService;

	private static final String TPLFILEPATH = "/static/resources/templet";

	private static final String BATCHIMPORTTPL = "arrange.xlsx";

	private static final String filePath = "/static/resources/download";

	/**
	 * 方法描述: 列表信息
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-03-11 14:46:49
	 * @Return
	 **/

	@RequestMapping(method = RequestMethod.GET)
	// @RequiresPermissions("basics:temparrange:list")
	public String list() {
		// 查询列表数据
		return "basics/temparrange/list";
	}

	/**
	 * 方法描述: 分頁查詢信息
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-03-11 14:46:49
	 * @Return
	 **/

	@RequestMapping(value = "list", method = RequestMethod.GET)
	// @RequiresPermissions("basics:temparrange:list")
	@ResponseBody
	public Map<String, Object> infoList(HttpServletRequest request) {
		Page<TempArrangeEntity> page = getPage(request);
		List<PropertyFilter> filters = PropertyFilter
				.buildFromHttpRequest(request);
		page = tempArrangeService.search(page, filters);
		return getEasyUIData(page);
	}

	/**
	 * 方法描述: 保存
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-03-11 14:46:49
	 * @Return
	 **/

	@RequestMapping(value = "create", method = RequestMethod.POST)
	@RequiresPermissions("basics:temparrange:add")
	@ResponseBody
	public String create(@Valid TempArrangeEntity tempArrange, Model model) {
		tempArrange.setNewRecord(true);
		tempArrange.setCreateDate(new Date());
		tempArrangeService.save(tempArrange);
		return "success";
	}

	/**
	 * 方法描述: 添加跳轉
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-03-11 14:46:49
	 * @Return
	 **/

	@RequiresPermissions("basics:temparrange:add")
	@RequestMapping(value = "create", method = RequestMethod.GET)
	public String createForm(Model model) {
		model.addAttribute("tempArrange", new TempArrangeEntity());
		model.addAttribute("action", "create");
		return "basics/temparrange/listForm";
	}

	/**
	 * 方法描述: 修改跳轉
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-06 11:04:39
	 * @Return
	 **/

	@RequiresPermissions("basics:temparrange:update")
	@RequestMapping(value = "update/{id}", method = RequestMethod.GET)
	public String updateForm(@PathVariable("id") String id, Model model) {
		model.addAttribute("tempArrange", tempArrangeService.get(id));
		model.addAttribute("action", "update");
		return "basics/temparrange/listForm";
	}

	/**
	 * 方法描述: 修改
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-11 10:55:00
	 * @Return
	 **/

	@RequestMapping(value = "update", method = RequestMethod.POST)
	@RequiresPermissions("basics:temparrange:update")
	@ResponseBody
	public String update(@Valid TempArrangeEntity tempArrange,
			@RequestParam(value = "ids") String ids) {
		tempArrange.setId(ids);
		tempArrange.setUpdateDate(new Date());
		tempArrangeService.update(tempArrange);

		return "success";
	}

	/**
	 * 方法描述: 根據主鍵刪除
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-03-11 14:46:49
	 * @Return
	 **/

	@RequestMapping("delete/{id}")
	@RequiresPermissions("basics:temparrange:delete")
	@ResponseBody
	public String delete(@PathVariable("id") String id) {
		tempArrangeService.delete(id);
		return "success";
	}

	/**
	 * 导出excel
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-03-11 14:46:49
	 * @Return
	 **/
	@RequestMapping("exportExcel")
	public void exportExcel(HttpServletRequest request,
			HttpServletResponse response) throws Exception {
	}

	/**
	 * 方法描述: 導入顯示明細
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018/10/31 下午 02:30
	 * @Return
	 **/

	@RequestMapping(value = "importView", method = RequestMethod.GET)
	@ResponseBody
	public List<TempArrangeEntity> importView(HttpServletResponse response,
			Model model, HttpServletRequest request) {
		String clientIP = IPUtil.getIpAddress(request);
		List<TempArrangeEntity> tempArrangeEntities = tempArrangeService
				.findByIp(clientIP);
		//model.addAttribute("itemEntity", tempArrangeEntities.get(1));
		if (!tempArrangeEntities.equals(null)) {
			return tempArrangeEntities;
		} else {
			return null;
		}

	}

	/**
	 * 方法描述: 下載批量上傳模板
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-22 18:57:46
	 * @Return
	 **/

	@RequestMapping(value = "downLoad/batchImportTpl", method = RequestMethod.GET)
	// @RequiresPermissions("other:tqhraymachinescrap:add")
	@ResponseBody
	public void batchImportTpl(HttpServletRequest request,
			HttpServletResponse response) {
		fileDownload(request, response, BATCHIMPORTTPL);
	}

	private void fileDownload(HttpServletRequest request,
			HttpServletResponse response, String fileName) {
		String path = request.getSession().getServletContext()
				.getRealPath(TPLFILEPATH);
		try {
			FileDownloadUtil.fileDownload(request, response, path, fileName);// 下載點位信息模板
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	/**
	 * 方法描述: 下載批量上傳異常信息
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-02-13 18:57:46
	 * @Return
	 **/

	@RequestMapping(value = "downLoad/errorExcel", method = RequestMethod.GET)
	// @RequiresPermissions("other:tqhraymachinescrap:add")
	@ResponseBody
	public void errorExcelDownload(HttpServletRequest request,
			HttpServletResponse response) throws IOException {
		List<TempArrangeErrorDto> errorDtos = (List<TempArrangeErrorDto>) request
				.getSession().getAttribute(
						TempArrangeService.TEMPARRANGEERRORDTOS);
		if (errorDtos != null) {
			// 導出excel2007
			LinkedHashMap<String, String> map = tempArrangeService
					.getErrorFieldMap();

			String name = this.getCurUser().getLoginName() + "In";
			String tplPath = request.getSession().getServletContext()
					.getRealPath(filePath);
			File file = Excel2007Utils.createFile(errorDtos, map, tplPath,
					name, null);// 生成xlsx文件
			// long endOfBuildExcel = System.currentTimeMillis();
			// System.out.println("生成excel："+(endOfBuildExcel -
			// endOfLoadData)+"毫秒");
			String fileName = file.getName();
			Excel2007Utils.exportFile(response, tplPath, fileName);// 下载生成的xlsx文件
			request.getSession().removeAttribute(
					TempArrangeService.TEMPARRANGEERRORDTOS);
		}
	}

	/**
	 * 批量導入
	 * 
	 * @param file
	 *            文檔
	 * @param isExcel2003
	 *            是否2003
	 * @return
	 */
	@RequestMapping(value = "upload", method = RequestMethod.POST)
	@ResponseBody
	public String upload(MultipartFile file, boolean isExcel2003, Model model,
			HttpServletRequest request) {

		if (file.equals("") || file.getSize() <= 0) {
			return Constant.FAIL;
		} else {
			String result = null;
			try {

				result = tempArrangeService.batchImport(file.getInputStream(),
						isExcel2003);

			} catch (IOException e) {
				e.printStackTrace();
			}

			if (Constant.RESULT.CODE_YES.getValue().equals(result)) {
				String clientIP = IPUtil.getIpAddress(request);
				@SuppressWarnings("unchecked")
				List<TempArrangeEntity> tempArrangeEntities = (List<TempArrangeEntity>) UserUtil
						.getSession().getAttribute(
								TempArrangeService.TEMPARRANGEENTITIES);
				tempArrangeService.batchDelete(clientIP);
				if (null != tempArrangeEntities) {
					for (TempArrangeEntity tempArrangeEntity : tempArrangeEntities) {
						tempArrangeEntity.setCreateIp(clientIP);
						tempArrangeService.save(tempArrangeEntity);

					}
					tempArrangeService.callProc(
							"{CALL pk_workstatus_rpt_bs_arrange_check_sp(?0)}",
							clientIP);
					// conifgService.callProcWithResult("{CALL p_qh_getUserGrade('?0')}",
					// obj.getEmpNo()).get(0).toString();
					// pointItemEntity = new
					// TempArrangeEntity[pointItemEntities.size()];
					// pointItemEntities.toArray(pointItemEntity);
					// UserUtil.getSession().removeAttribute(POINTITEMENTITIES);
				}
				return Constant.SUCCESS;
			} else {
				return result;
			}
		}

	}
}
