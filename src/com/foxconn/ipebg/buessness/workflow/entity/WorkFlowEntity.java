package com.foxconn.ipebg.buessness.workflow.entity;

/**
 * Company foxconn
 * Copyright (C) 2004-2018 All Rights Reserved.
 */
public class WorkFlowEntity {
    /**
     * 流程id
     */
    private String workflowId;
    /**
     * 工單(表單)流水號
     */
    private String serialNo;
    /**
     * 工單實例id
     */
    private String processId;
    /**
     * 申請人工號
     */
    private String applyNo;
    //獲取申請人的級別
    private String userGrade;
    /**
     * 任務處理人信息
     * taskUsers=產工課部級審核:G4247468;
     */
    private String taskUsers;
    /**
     * 會簽任務
     * huiqian=assigneeList:H2201968,F1649647;
     */
    private String huiqian;
    /**
     * 申請人工號
     */
    private String empNo;
    /**
     * 約定的流程可選變量
     * variables=變更名稱,變更值,變更類型
     * 如：variables=assign_agree,true,B;
     */
    private String variables;

    public String getWorkflowId() {
        return workflowId;
    }

    public void setWorkflowId(String workflowId) {
        this.workflowId = workflowId;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    public String getApplyNo() {
        return applyNo;
    }

    public void setApplyNo(String applyNo) {
        this.applyNo = applyNo;
    }

    public String getUserGrade() {
        return userGrade;
    }

    public void setUserGrade(String userGrade) {
        this.userGrade = userGrade;
    }

    public String getTaskUsers() {
        return taskUsers;
    }

    public void setTaskUsers(String taskUsers) {
        this.taskUsers = taskUsers;
    }

    public String getHuiqian() {
        return huiqian;
    }

    public void setHuiqian(String huiqian) {
        this.huiqian = huiqian;
    }

    public String getVariables() {
        return variables;
    }

    public void setVariables(String variables) {
        this.variables = variables;
    }

    public String getEmpNo() {
        return empNo;
    }

    public void setEmpNo(String empNo) {
        this.empNo = empNo;
    }
}
