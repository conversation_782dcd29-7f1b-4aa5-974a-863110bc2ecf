var dg;
var d;
var f = false;
$(function(){
	    dg = $('#dg').datagrid({
		method : "get",
		url : ctx + '/tqhwfelectricaldispatching/list',
		fit : true,
		fitColumns : false,
		border : false,
		idField : 'id',
		striped : true,
		pagination : true,
		rownumbers : true,
		pageNumber : 1,
		pageSize : 20,
		pageList : [ 10, 20, 30, 40, 50 ],
		singleSelect : true,
		columns : [ [ 
		              {field : 'serialno',title : '任務編碼',sortable : true,width : 180,formatter:operation}, 
		              {field : 'dealno',title : '申請人工號',sortable : true,width : 100}, 
		              {field : 'dealname',title : '申請人姓名',sortable : true,width : 100},
		              {field : 'dealtel',title : '申請聯絡分機',sortable : true,width : 120}, 
		              {field : 'dealdeptno',title : '單位代碼',sortable : true,width : 100}, 
		              {field : 'dealdeptname',title : '單位名稱',sortable : true,width : 300}, 
		              {field : 'taskproject',title : '派工項目',sortable : true,width : 100},
		              {field : 'taskcategory',title : '派工需求類別',sortable : true,width : 100,
							formatter : function(value, row, index) {
								var reStr = '';
								$.ajax({
									url : ctx + "/system/dict/getDictByType/elec_category",
									type : "GET",
									async : false,
									success : function(data) {
										for (var i = 0; i < data.length; i++) {
											if (value == data[i].value) {
												reStr = data[i].label;
											}
										}
									}
								});
								return reStr;
							}
						},
		              {field : 'taskplace',title : '派工地點',sortable : true,width : 100}, 
		              {field : 'qchargeaccepttime',title : '接單日期',sortable : true,width : 100,formatter:formatDate}, 
		              {field : 'constrstaettime',title : '開工日期',sortable : true,width : 100,formatter:formatDate}, 
		              {field : 'constrno',title : '施工人員工號',sortable : true,width : 100}, 
		              {field : 'constrname',title : '施工人員姓名',sortable : true,width : 100}, 
		              {field : 'constrrate',title : '施工進度',sortable : true,width : 100},
		              {field : 'workstatus',title : '表單狀態',sortable : true,width : 100,
								formatter : function(value, row, index) {
									var reStr = '';
									$.ajax({
										url : ctx + "/system/dict/getDictByType/audit_status",
										type : "GET",
										async : false,
										success : function(data) {
											for (var i = 0; i < data.length; i++) {
												if (value == data[i].value) {
													reStr = data[i].label;
												}
											}
										}
									});
									return reStr;
								}
							}, 
					{field : 'nodeName',title : '當前簽核節點',sortable : true,width : 150,
							 formatter : function(value, row, index) {
								 var reStr = '';
								 $.ajax({
									 url : ctx + "/wfcontroller/getNodeInfo/" + row.serialno,
									 type : "GET",
									 async : false,
									 success : function(data) {
										 reStr = data.nodeName;
									 }
								 });
								 return reStr;
							 }
						 }, 
					{field : 'auditUser',title : '當前簽核人',sortable : true,width : 120,
							 formatter : function(value, row, index) {
								 var reStr = '';
								 $.ajax({
									 url : ctx + "/wfcontroller/getNodeInfo/" + row.serialno,
									 type : "GET",
									 async : false,
									 success : function(data) {
										 reStr = data.auditUser;
									 }
								 });
								 return reStr;
							 }
						 },
		           /* {field : 'workstatus', title : '表單狀態', sortable : true, width : 100},
		      		{field : 'nodeName', title : '當前簽核節點', sortable : true, width : 100},
		      		{field : 'auditUser', title : '當前簽核人', sortable : true, width : 100} ,*/
				    {field: 'complettime', title: '簽核完成時間', sortable: true, width: 125,formatter:formatDatebox
            } ] ],
		enableHeaderClickMenu : true,
		enableHeaderContextMenu : true,
		enableRowContextMenu : false,
		toolbar : '#tb'
	});
	    
	    //去除紅色下滑線
	    $(":input").removeClass("validatebox-invalid");
		//獲取區域負責人
		//getQycharge();
		//加載時去除生產安管click事件
		//$("#scfwsafeDiv").removeAttr("onclick");
		//加載時去除生產安管click事件
		//$("#cchargeDiv").removeAttr("onclick");
	    //獲取廠區
		$.get(ctx + '/tqhfactoryidconfig/allFactorys/', function(result) {
			$("#dealfactoryid").combobox({
				data : result,
				valueField : "factoryid",
				textField : "factoryname",
				editable : false,
				panelHeight : "auto",
				loadFilter : function(data) {
					data.unshift({
						factoryid : '',
						factoryname : '請選擇'
					});
					return data;
				}
			});
		});
		
		//查詢條件狀態
		$.ajax({
			url : ctx + "/system/dict/getDictByType/audit_status",
			//dataType:"json",
			type : "GET",
			success : function(data) {
				//绑定第一个下拉框
				$("#qysjzt").combobox({
					data : data,
					valueField : "value",
					textField : "label",
					editable : false,
					panelHeight : "auto",
					loadFilter : function(data) {
						data.unshift({
							value : '',
							label : '請選擇'
						});
						return data;
					}
				});
			},
			error : function(error) {
				alert("初始化下拉控件失败");
			}
		});
		
		//獲取需求類別
		$.ajax({
					type : "GET",
					url : ctx + "/system/dict/getDictByType/elec_category",
					success : function(data) {
						var html = '';
						if ($("#taskcategory").val() == null || $("#taskcategory").val() == '') {
							$.each(data,function(i, v) {
							html += "<input type='radio'  onclick = 'scfwsafeShow()'  value=" + v.value
								 + " name='taskcategoryName'/>" + v.label + "&nbsp;&nbsp;&nbsp;&nbsp;";
							});
	
						} else {
							var str = $("#taskcategory").val().split(",");
							var disOrEnabled = $("#disOrEnabled").val();
							$.each(data,function(n, item) {
								var flag = false;
								for (var i = 0; i < str.length; i++) {
								 if (str[i] == item.value) {
								 html += "<input type='radio'  onclick = 'scfwsafeShow()'  checked='checked' "
									  + disOrEnabled + " value=" + item.value + " name='taskcategoryName'/>" + item.label
									  + "&nbsp;&nbsp;&nbsp;&nbsp;";
									  flag = true;
								}
							}
							if (!flag)
								html += "<input type='radio'  onclick = 'scfwsafeShow()' " + disOrEnabled
									 + " value=" + item.value
									 + " name='taskcategoryName'/>" + item.label
									 + "&nbsp;&nbsp;&nbsp;&nbsp;";
								});
						}
						$(".categoryDiv").html(html);
						scfwsafeShow();
					}
				});
		
		//獲取緊急程度
		$.ajax({
					type : "GET",
					url : ctx + "/system/dict/getDictByType/elec_emergency",
					success : function(data) {
						var html = '';
						if ($("#taskemergency").val() == null || $("#taskemergency").val() == '') {
							$.each(data,function(i, v) {
							html += "<input type='radio' onclick = 'cchargeShow()' value=" + v.value
								 + " name='taskemergencyName'/>" + v.label + "&nbsp;&nbsp;&nbsp;&nbsp;";
							});
	
						} else {
							var str = $("#taskemergency").val().split(",");
							var disOrEnabled = $("#disOrEnabled").val();
							$.each(data,function(n, item) {
								var flag = false;
								for (var i = 0; i < str.length; i++) {
								 if (str[i] == item.value) {
								 html += "<input type='radio' onclick = 'cchargeShow()' checked='checked' "
									  + disOrEnabled + " value=" + item.value + " name='taskemergencyName'/>" + item.label
									  + "&nbsp;&nbsp;&nbsp;&nbsp;";
									  flag = true;
								}
							}
							if (!flag)
								html += "<input type='radio' onclick = 'cchargeShow()' " + disOrEnabled
									 + " value=" + item.value
									 + " name='taskemergencyName'/>" + item.label
									 + "&nbsp;&nbsp;&nbsp;&nbsp;";
								});
						}
						$(".emergencyDiv").html(html);
						cchargeShow();
					}
				});
		
		//獲取評價
		$.ajax({
					type : "GET",
					url : ctx + "/system/dict/getDictByType/elec_judge",
					success : function(data) {
						debugger;
						var html = '';
						if ($("#judgeresult").val() == null || $("#judgeresult").val() == '') {
							$.each(data,function(i, v) {
							html += "<input type='radio' value=" + v.value
								 + " name='judgeName'/>" + v.label + "&nbsp;&nbsp;&nbsp;&nbsp;";
							});
	
						} else {
							var str = $("#judgeresult").val().split(",");
							var disOrEnabled = $("#disOrEnabled2").val();
							$.each(data,function(n, item) {
								var flag = false;
								for (var i = 0; i < str.length; i++) {
								 if (str[i] == item.value) {
								 html += "<input type='radio' checked='checked' "
									  + disOrEnabled + " value=" + item.value + " name='judgeName'/>" + item.label
									  + "&nbsp;&nbsp;&nbsp;&nbsp;";
									  flag = true;
								}
							}
							if (!flag)
								html += "<input type='radio' " + disOrEnabled
									 + " value=" + item.value
									 + " name='judgeName'/>" + item.label
									 + "&nbsp;&nbsp;&nbsp;&nbsp;";
								});
						}
						$(".judgeDiv").html(html);
					}
				});
});

//任務編號查看頁面
function operation(value, row, index) {
	return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('詳情',ctx+'/tqhwfelectricaldispatching/view/"
			+ row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
};

//保存0和提交2方法 
function saveInfo(flag) {
	debugger;
	if (flag == '0') {
		var empno = $.trim($("#dealno").val());
		if (empno == '') {
			alert("申請人工號不能為空！");
			return;
		}
		if (!valdCategory(flag)) {
			return;
		}
		if (!valdEmergency(flag)) {
			return;
		}
		var email = $("#dealemail").val();
		if(!valdEmailAuto(email)){
			return;
		}
		$.ajax({
			url : ctx + '/tqhwfelectricaldispatching/create/' + flag,
			asycn:false,
			type : 'POST',
			beforeSend : ajaxLoading,
			data : $('#mainform').serialize(),
			success : function(data) {
				ajaxLoadEnd();
				successTip(data, dg, d);
				window.parent.mainpage.mainTabs.closeCurrentTab();
			}
		});
	} else {
		var isValid = $("#mainform").form('validate');
		if (!isValid) {
			return;
		}
		if (!valdCategory(flag)) {
			return;
		}
		if (!valdEmergency(flag)) {
			return;
		}
		var email = $("#dealemail").val();
		if(!valdEmailAuto(email)){
			return;
		}
		if(!validtaskexceptdealtime()){
			return;
		}
		
		//緊急事件必須簽核廠級主管
		if($("input[name=taskemergencyName]:checked").val() == "elec_emergency_02"){ 
			if("" == $("#cchargename").val() || null == $("#cchargename").val() || "" == $("#cchargeno").val()|| null == $("#cchargeno").val()  ){
			alert("緊急程度為緊急，必須選擇廠級主管！");
			}
		}
		//安全事件必須簽核安管部門
		if ($("input[name=taskcategoryName]:checked").val() == "elec_category_03"){
			if("" == $("#scfwsafeno").val() || null == $("#scfwsafeno").val() || "" == $("#scfwsafename").val()|| null == $("#scfwsafename").val()  ){
				alert("需求類別為安全，必須生產服務部安管審核主管！");
				return;
			}
		}
		var empno = $.trim($("#scfwqychargeno").val());
		if (empno == '') {
			alert("生產服務部區域負責人工號不能為空！");
			return;
		}else if(empno != null && empno != "") {
			$.ajax({
				asycn:false,
				url : ctx + '/system/user/getUserInfo/',
				type : 'POST',
				beforeSend : ajaxLoading,
				data : {
					empno : empno
				},
				success : function(data) {
					ajaxLoadEnd();
					if (!data||data.empno=="") {
						$.messager.alert("溫馨提示", "請重新選擇區域負責人", "error");
						$('#qchargeno').val('');
						$('#qchargename').val('');
						$('#qchargedptno').val('');
						$('#qchargemail').val('');
						return;
					} else {
						$('#qchargeno').val(data.empno);
						$('#qchargename').val(data.empname);
						$('#qchargedptno').val(data.deptno);
						$('#qchargemail').val(data.email);
						$.ajax({
							url : ctx + '/tqhwfelectricaldispatching/create/' + flag,
							asycn:false,
							type : 'POST',
							beforeSend : ajaxLoading,
							data : $('#mainform').serialize(),
							success : function(data) {
								ajaxLoadEnd();
								successTip(data, dg, d);
								window.parent.mainpage.mainTabs.closeCurrentTab();
							}
						});
					}
				}
			});
		}
		
	}
/*	$.ajax({
		url : ctx + '/tqhwfelectricaldispatching/create/' + flag,
		asycn:false,
		type : 'POST',
		beforeSend : ajaxLoading,
		data : $('#mainform').serialize(),
		success : function(data) {
			ajaxLoadEnd();
			successTip(data, dg, d);
			window.parent.mainpage.mainTabs.closeCurrentTab();
		}
	});*/
}

// 弹窗增加
function add() {
    d=$("#dlg").dialog({
        title: '添加機電派工申請單',
        width: 380,
        height: 380,
        href:'tqhwfelectricaldispatching/create',
        maximizable:true,
        modal:true,
        buttons:[{
            text:'确认',
            handler:function(){
                $("#mainform").submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}

// 删除
function del(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data){
        if (data){
	$.ajax({
                type:'get',
                url:"tqhwfelectricaldispatching/delete/"+row.id,
                success: function(data){
                    successTip(data,dg);
                }
            });
        }
    });
}

// 弹窗修改
function upd(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    d=$("#dlg").dialog({
        title: '修改機電派工申請單',
        width: 380,
        height: 340,
        href:'tqhwfelectricaldispatching/update/'+row.id,
        maximizable:true,
        modal:true,
        buttons:[{
            text:'修改',
            handler:function(){
                $('#mainform').submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}
//创建查询对象并查询
function cx(){
    var obj=$("#searchFrom").serializeObject();
    dg.datagrid('load',obj);
}

//导出excel
function exportExcel() {
    var form = document.getElementById("searchFrom");
    searchFrom.action =  ctx + '/tqhwfelectricaldispatching/exportExcel';
    form.submit();
}

//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("reset");
}

//根據工號查出當前申請人的信息
function queryUserInfo1() {
	var empno = $.trim($("#dealno").val()).toUpperCase();
	if (empno != null && empno != "") {
		$.ajax({
			url : ctx + '/system/user/getUserInfo/',
			type : 'POST',
			beforeSend : ajaxLoading,
			data : {
				empno : empno
			},
			success : function(data) {
				ajaxLoadEnd();
				if (!data) {
					$.messager.alert("溫馨提示", "工號不存在", "error");
					$('#dealno').val('');
					$('#dealname').val('');
					$('#dealdeptno').val('');
					$('#dealfactoryid').combobox('setValue','');
					$('#dealdeptname').val('');
					$('#dealemail').val('');
				} else {
					$('#dealname').val(data.empname);
					$('#dealdeptno').val(data.deptno);
					$('#dealfactoryid').combobox('setValue',data.factoryid);
					$('#dealdeptname').val(data.deptname);
					$('#dealemail').val(data.email);
				}
			}
		});
	}
}

//根據所選簽核路徑負責人為區域負責人賦值
function getQycharge(){
	//var quCharge = $("#scfwqychargeno").val();
	//alert(quCharge);
	debugger;
	//alert("onchange");
	//$("#qchargeno").val($("#scfwqychargeno").val());
	
	var scfwqychargeno = $("#scfwqychargeno").val();
	if(scfwqychargeno != null && scfwqychargeno != ""){
	queryUserInfo2(scfwqychargeno);
	}else{
		
	}
}

//根據工號查區域負責人的信息
function queryUserInfo2(empno) {
	debugger;
	if (empno != null && empno != "") {
		$.ajax({
			asycn:false,
			url : ctx + '/system/user/getUserInfo/',
			type : 'POST',
			beforeSend : ajaxLoading,
			data : {
				empno : empno
			},
			success : function(data) {
				ajaxLoadEnd();
				if (!data||data.empno=="") {
					$.messager.alert("溫馨提示", "請重新選擇區域負責人", "error");
					$('#qchargeno').val('');
					$('#qchargename').val('');
					$('#qchargedptno').val('');
					$('#qchargemail').val('');
				} else {
					$('#qchargeno').val(data.empno);
					$('#qchargename').val(data.empname);
					$('#qchargedptno').val(data.deptno);
					$('#qchargemail').val(data.email);
				}
			}
		});
	}
}

//根據工號查表單作業人員信息
function queryUserInfo3() {
	var empno = $.trim($("#constrno").val().toUpperCase());
	if (empno != null && empno != "") {
		$.ajax({
			url : ctx + '/system/user/getUserInfo/',
			type : 'POST',
			beforeSend : ajaxLoading,
			data : {
				empno : empno
			},
			success : function(data) {
				ajaxLoadEnd();
				if (!data) {
					$.messager.alert("溫馨提示", "工號不存在", "error");
					$('#constrno').val('');
					$('#constrname').val('');
					$('#constrdptno').val('');
				} else {
					$('#constrname').val(data.empname);
					$('#constrdptno').val(data.deptno);
				}
			}
		});
	}
}

//根據工號查評價人員信息
function queryUserInfo4() {
	var empno = $.trim($("#judgeno").val().toUpperCase());
	if (empno != null && empno != "") {
		$.ajax({
			url : ctx + '/system/user/getUserInfo/',
			type : 'POST',
			beforeSend : ajaxLoading,
			data : {
				empno : empno
			},
			success : function(data) {
				ajaxLoadEnd();
				if (!data) {
					$.messager.alert("溫馨提示", "工號不存在", "error");
					$('#judgeno').val('');
					$('#judgename').val('');
					$('#judgedeptno').val('');
					$('#judgemail').val('');
				} else {
					$('#judgename').val(data.empname);
					$('#judgedeptno').val(data.deptno);
					$('#judgemail').val(data.email);
				}
			}
		});
	}
}

//審核通過修改修改區域負責人信息-生產服務部派工窗口承接
function updateQcharge(){
	var isValid = $("#mainform").form('validate');
	if (!isValid) {
		return;
	}
	var email = $("#qchargemail").val();
	if(!valdEmailAuto(email)){
		return;
	}
	if(!validqchargeaccepttime()){
		return;
	}
	$.ajax({
		url : ctx + '/tqhwfelectricaldispatching/updateQcharge',
		type : 'POST',
		async: false,
		beforeSend : ajaxLoading,
		data : $('#mainform').serialize(),
		success : function(data) {
			debugger;
			ajaxLoadEnd();
			successTip(data, dg, d);
			f = true;
		}
	});
	return f;
}

//審核通過修改表單作業信息-生產服務部區域負責人安排施工通過
function updateCons(){
	if(!validConstrendTime()){
		return;
	}
	var isValid = $("#mainform").form('validate');
	if (!isValid) {
		return;
	}
	if(!validDate()){
		return;
	}
	if(!validRate()){
		return;
	}
	if(!validconstrstaettime()){
		return;
	}
	$.ajax({
		url : ctx + '/tqhwfelectricaldispatching/updateCons',
		type : 'POST',
		async: false,
		beforeSend : ajaxLoading,
		data : $('#mainform').serialize(),
		success : function(data) {
			debugger;
			ajaxLoadEnd();
			successTip(data, dg, d);
			f = true;
		}
	});
	return f;
}

//僅修改表單作業信息不審核-生產服務部區域負責人安排施工保存
function updateRate(){
	$.ajax({
		url : ctx + '/tqhwfelectricaldispatching/updateCons',
		type : 'POST',
		async: false,
		beforeSend : ajaxLoading,
		data : $('#mainform').serialize(),
		success : function(data) {
			debugger;
			ajaxLoadEnd();
			successTip(data, dg, d);
			window.parent.mainpage.mainTabs.closeCurrentTab();
		}
	});
}

//審核通過修改修改評價信息
function updateJudge(){
	debugger;
	if(!valdJudgeResult()){
		return;
	}
	var isValid = $("#mainform").form('validate');
	if (!isValid) {
		return;
	}
	if(!valdJudgeMemo()){
		return;
	}
	if(!validjudgetime()){
		return;
	}
	var email = $("#judgemail").val();
	if(!valdEmailAuto(email)){
		return;
	}
	$.ajax({
		url : ctx + '/tqhwfelectricaldispatching/updateJudge',
		type : 'POST',
		async: false,
		beforeSend : ajaxLoading,
		data : $('#mainform').serialize(),
		success : function(data) {
			debugger;
			ajaxLoadEnd();
			successTip(data, dg, d);
			f = true;
		}
	});
	return f;
}

//驗證郵箱onblur
function valdEmail(obj) {
    var email = obj.value;
    var temp = email;
    if (email == "") return;
    var regex = /\w+([-/+.]\w+)*@(mail.foxconn.com|foxconn.com|fii-foxconn.com)$/;
    var patt1 = new RegExp(regex);
    var result = patt1.test(email);
    if (!result) {
        alert("郵箱的格式不正確!");
        obj.value = "";
        obj.value = temp;
        return;
    }
    ;
}

//dealemail   qchargemail   judgemail
//提交或審核時驗證郵箱      申請人郵箱         派工窗口承接        服務評價郵箱
   function valdEmailAuto(email) {
	    if (email !=""){
	        var regex = /\w+([-/+.]\w+)*@(mail.foxconn.com|foxconn.com|fii-foxconn.com)$/;
	        var patt1 = new RegExp(regex);
	        var result = patt1.test(email);
	        if (!result) {
	            alert("郵箱的格式不正確!");
	            $("#applyemail").val('');
	            $("#applyemail").focus();
	            return false;
	        }
	    }
	    return true;
	}
   
//驗證分機
function valdApplyTel(obj) {
    var tel = obj.value;
    if (tel != "") {
        var regex = /^[0-9]{3}\+[0-9]{5}$/;
        var patt1 = new RegExp(regex);
        var result = patt1.test(tel);
        if (!result) {
            alert("分機格式有誤!");
            obj.value = "";
            obj.focus();
            return;
        }
    }
}

//驗證數字
function valdNumber(obj){
	debugger;
	var num = obj.value;
    if (num != "") {
        var regex = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
        var patt1 = new RegExp(regex);
        var result = patt1.test(num);
        if (!result) {
            alert("該欄位只能輸入數字!");
            obj.value = "";
            obj.focus();
            return;
        }
    }
}

//驗證radio 派工需求類別是否為空
function valdCategory(flag) {
	var chk_value = [];
	$('input[name="taskcategoryName"]:checked').each(function() {
		chk_value.push($(this).val());
	});
	if (chk_value.length == 0 && flag != '0') {
		alert("請選擇派工需求類別！ ");
		return false;
	} else {
		var chk_values = '';
		$.each(chk_value, function(index, value) {
			chk_values += value + ',';
		});
		var chk_vals = chk_values.substring(0, chk_values.length - 1);
		$('#taskcategory').val(chk_vals);
		return true;
	}
}

//驗證radio 緊急程度是否為空
function valdEmergency(flag) {
	var chk_value = [];
	$('input[name="taskemergencyName"]:checked').each(function() {
		chk_value.push($(this).val());
	});
	if (chk_value.length == 0 && flag != '0') {
		alert("請選擇緊急程度 ！");
		return false;
	} else {
		var chk_values = '';
		$.each(chk_value, function(index, value) {
			chk_values += value + ',';
		});
		var chk_vals = chk_values.substring(0, chk_values.length - 1);
		$('#taskemergency').val(chk_vals);
		return true;
	}
}

//驗證radio 評價是否為空
function valdJudgeResult() {
	var chk_value = [];
	$('input[name="judgeName"]:checked').each(function() {
		chk_value.push($(this).val());
	});
	if (chk_value.length == 0) {
		alert("請選擇評價 ！");
		return false;
	} else {
		var chk_values = '';
		$.each(chk_value, function(index, value) {
			chk_values += value + ',';
		});
		var chk_vals = chk_values.substring(0, chk_values.length - 1);
		$('#judgeresult').val(chk_vals);
		return true;
	}
}

//驗證評價說明是否為空
function valdJudgeMemo(){
	debugger;
	if ($("input[name=judgeName]:checked").val() == "elec_judge_03"){
	if ($("#judgememo").val().trim() =='' ||  null == $("#judgememo").val().trim()){
		    alert("評價結果為“不滿意時”，必須填寫評價說明！");
		    return false;
	}
	}
	return true;
}

//驗證完工日期是否為空
function validConstrendTime() {
	debugger;
	var constrcomplettime = $("input[name='constrcomplettime']").val();
	if (constrcomplettime =='' || constrcomplettime == null){
	alert("請選擇完工日期！");
	
	return false;
	}
    return true;
}

//驗證完工日期是否大於開工日期
function validDate(){
	var kaigongDate = $("input[name='constrstaettime']").val();
	var wangongDate = $("input[name='constrcomplettime']").val();
	    if(kaigongDate >wangongDate){
	    	alert("完工日期不可小於開工日期！");
	    	return false;
	    }
	return true;
}

//驗證施工欄位是否為100
function validRate(){
	var constrrate = $("#constrrate").val();
	    if(constrrate != "100"){
	    	alert("施工進度不為100%，請確認！");
	    	return false;
	    }
	return true;
}

//驗證期望完工日期是否為空
function validtaskexceptdealtime() {
	if ($("#taskexceptdealtime").val() =='' || $("#taskexceptdealtime").val() == null){
	alert("請選擇期望完工日期！");
	return false;
	}
    return true;
}

//驗證接單日期是否為空
function validqchargeaccepttime() {
	if ($("#qchargeaccepttime").val() =='' || $("#qchargeaccepttime").val() == null){
	alert("請選擇接單日期！");
	return false;
	}
    return true;
}

//驗證開工日期是否為空 
function validconstrstaettime() {
	if ($("#constrstaettime").val() =='' || $("#constrstaettime").val() == null){
	alert("請選擇開工日期！");
	return false;
	}
    return true;
}

//驗證完工日期是否為空
/*function validconstrcomplettime() {
	if ($("#constrcomplettime").val() =='' || $("#constrcomplettime").val() == null){
		alert("請選擇完工日期！");
		return false;
		}
    return true;
}*/

//驗證評價是否為空
function validjudgetime() {
	if ($("#judgetime").val() =='' || $("#judgetime").val() == null){
	alert("請選擇評價日期！");
	return false;
	}
    return true;
}

//安管節點點擊事件
function scfwsafeShow(){
	debugger;
if ($("input[name=taskcategoryName]:checked").val() == "elec_category_03"){
	    $("#scfwsafeDiv").attr("onclick","selectRole2(34,'scfwsafeTable','scfwsafeno','scfwsafename',$('#dealfactoryid').combobox('getValue'),'tQhWfelectricaldispatchingEntity')");
	    $("#scfwsafeno").attr("style","background-color:#FFFFFF;width:80px");
		$("#scfwsafename").attr("style","background-color:#FFFFFF;width:80px");
		$("#scfwsafeXing").html("<font color='red'>*</font>");
	}else{
		$("#scfwsafeDiv").removeAttr("onclick");
		$("#scfwsafeno").attr("style","background-color:#D0D0D0;width:80px");
		$("#scfwsafename").attr("style","background-color:#D0D0D0;width:80px");
		$("#scfwsafeXing").html("");
	}
}

//廠級主管點擊事件
function cchargeShow(){
	debugger;
if ($("input[name=taskemergencyName]:checked").val() == "elec_emergency_02"){
	    $("#cchargeDiv").attr("onclick","selectRole($('#dealdeptno').val(),'cchargeno','cchargename',$('#dealfactoryid').combobox('getValue'))");
	    $("#cchargeno").attr("style","background-color:#FFFFFF;width:80px");
		$("#cchargename").attr("style","background-color:#FFFFFF;width:80px");
		$("#cXing").html("<font color='red'>*</font>");
	}else{
		$("#cchargeDiv").removeAttr("onclick");
		$("#cchargeno").attr("style","background-color:#D0D0D0;width:80px");
		$("#cchargename").attr("style","background-color:#D0D0D0;width:80px");
		$("#cXing").html("");
	}
}