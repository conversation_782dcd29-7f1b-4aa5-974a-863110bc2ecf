package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.ColumnTransformer;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * 培訓期培訓人員明細基本資料
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-08-29 10:42:48
 */
@Entity
@Table(name = "V_BS_TRAINING_PERSON")
@DynamicUpdate
@DynamicInsert
public class BsTrainingPersonEntity extends DataEntity<BsTrainingPersonEntity>
		implements Serializable {
	private static final long serialVersionUID = 1L;

	// 工號
	private String empNo;
	// 姓名
	private String empName;
	// 身份證號
	private String psnId;
	// 保安公司
	private String company;
	// 培訓期編碼
	private String trainingPeriodNo;
	// 培訓期名稱
	private String trainingPeriodName;
	// 培訓開始時間
	private Date startDate;
	// 培訓結束時間
	private Date endDate;
	// 開始時間
	private String startTime;
	// 結束時間
	private String endTime;
	// 開始時間下午
	private String startTime1;

	// 結束時間下午
	private String endTime1;

	/**
	 * 设置：工號
	 */
	public void setEmpNo(String empNo) {
		this.empNo = empNo;
	}

	/**
	 * 获取：工號
	 */

	@Column(name = "EMP_NO", nullable = false, length = 20)
	public String getEmpNo() {
		return empNo;
	}

	/**
	 * 设置：姓名
	 */
	public void setEmpName(String empName) {
		this.empName = empName;
	}

	/**
	 * 获取：姓名
	 */

	@Column(name = "EMP_NAME", nullable = false, length = 20)
	public String getEmpName() {
		return empName;
	}

	/**
	 * 设置：身份證號
	 */
	public void setPsnId(String psnId) {
		this.psnId = psnId;
	}

	/**
	 * 获取：身份證號
	 */

	@Column(name = "PSN_ID", nullable = false, length = 20)
	@ColumnTransformer(forColumn = "PSN_ID", read = "CONVERT_FROM(decrypt(decode(PSN_ID, 'base64'), 'sgas', 'aes'), 'UTF-8')", write = "encode(encrypt(cast(? as bytea), 'sgas', 'aes'), 'base64')")
	public String getPsnId() {
		return psnId;
	}

	/**
	 * 设置：保安公司
	 */
	public void setCompany(String company) {
		this.company = company;
	}

	/**
	 * 获取：保安公司
	 */

	@Column(name = "COMPANY", nullable = false, length = 20)
	public String getCompany() {
		return company;
	}

	/**
	 * 设置：培訓期編碼
	 */
	public void setTrainingPeriodNo(String trainingPeriodNo) {
		this.trainingPeriodNo = trainingPeriodNo;
	}

	/**
	 * 获取：培訓期編碼
	 */

	@Column(name = "TRAINING_PERIOD_NO", nullable = false, length = 20)
	public String getTrainingPeriodNo() {
		return trainingPeriodNo;
	}

	/**
	 * 设置：培訓期名稱
	 */
	public void setTrainingPeriodName(String trainingPeriodName) {
		this.trainingPeriodName = trainingPeriodName;
	}

	/**
	 * 获取：培訓期名稱
	 */

	@Column(name = "TRAINING_PERIOD_NAME", nullable = false, length = 20)
	public String getTrainingPeriodName() {
		return trainingPeriodName;
	}

	/**
	 * 设置：培訓開始時間
	 */
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	/**
	 * 获取：培訓開始時間
	 */

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Column(name = "START_DATE", nullable = false, length = 20)
	public Date getStartDate() {
		return startDate;
	}

	/**
	 * 设置：培訓結束時間
	 */
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	/**
	 * 获取：培訓結束時間
	 */

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Column(name = "END_DATE", nullable = false, length = 20)
	public Date getEndDate() {
		return endDate;
	}

	/**
	 * 设置：開始時間
	 */
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	/**
	 * 获取：開始時間
	 */

	@Column(name = "START_TIME", nullable = false, length = 20)
	public String getStartTime() {
		return startTime;
	}

	/**
	 * 设置：結束時間
	 */
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	/**
	 * 获取：結束時間
	 */

	@Column(name = "END_TIME", nullable = false, length = 20)
	public String getEndTime() {
		return endTime;
	}

	@Column(name = "START_TIME1", nullable = false, length = 20)
	public String getStartTime1() {
		return startTime1;
	}

	public void setStartTime1(String startTime1) {
		this.startTime1 = startTime1;
	}

	@Column(name = "END_TIME1", nullable = false, length = 20)
	public String getEndTime1() {
		return endTime1;
	}

	public void setEndTime1(String endTime1) {
		this.endTime1 = endTime1;
	}

}
