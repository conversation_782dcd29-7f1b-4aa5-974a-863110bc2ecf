package com.foxconn.ipebg.report.service;

import com.foxconn.ipebg.report.entity.WorkStatusEntity;
import com.foxconn.ipebg.report.dao.WorkStatusDao;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.Dict;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WorkFlowEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.system.service.DictService;

import java.sql.CallableStatement;
import java.sql.SQLException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 班別資料
 *
 * <AUTHOR>
 * @email <EMAIL> 
 * @date 2019-03-07 09:57:22
 */
@Service
@Transactional(readOnly=true)
public class  WorkStatusService extends BaseBusinessService<WorkStatusEntity, String>{
    @Autowired
    private WorkStatusDao workStatusDao;

    @Autowired
    private UserServiceUtil serviceUtil;
    @Autowired
    private DictService dictService;

    @Override
    public HibernateDao<WorkStatusEntity, String> getEntityDao() {
        return workStatusDao;
    }

    public WorkStatusEntity findBySerialno(String serialno) {
        return this.workStatusDao.findUniqueBy("serialno", serialno);
    }
    
	public List getDptInfoId(String watchDate) {
		
		//String sql = "select t.id,  t.dpt_qun || '---' || t.dpt_chu || '---' || t.dpt_bu || '---' || t.dpt_ke dpt_name  from BS_DPT t";
		
		//List list = this.bsSecPerDao.createSQLQuery(sql).list();
		//return list;
		
		callProc("pk_workstatus_rpt_select_workstatus_detail",watchDate);
	    	
	    String sql="select L1,L2,L3,L4,L5,L6,L7,L8,L9,L10,L11,L12,L13,L14 from TEMP_WORKSTATUS_DETAIL ";

	    List list = this.workStatusDao.createSQLQuery(sql).list();
	    return list;
	    	

	}
    
}
