//?窗增加
/**
 * 單選簽核路徑普通簽核節點
 * @param deptNo
 * @param colNo
 * @param colName
 * @param factoryId
 */
function selectRole(deptNo, colNo, colName, factoryId) {
    if (deptNo == null || deptNo == '') {
        $.messager.alert("溫馨提示", "請輸入承辦人工號.", "warning");
        return;
    }
    // var h = document.documentElement.clientHeight || document.body.clientHeight;
    var w = document.documentElement.clientWidth || document.body.clientWidth;
    var top = ($(window).height() - 450) / 2;
    var left = ($(window).width() - 650) / 2;
    var scrollTop = top + $(document).scrollTop();
    var scrollLeft = $(document).scrollLeft();
    d = $("#win").dialog({
        title: '簽核角色人員選擇',
        width: 650,
        height: 450,
        // async: false,
        href: ctx + '/system/auditingNode/roleList',
        maximizable: false,
        modal: true,
        onOpen: function () {
            $('#deptNo').val(deptNo);
            $('#chargeNo').val(colNo);
            $('#chargeName').val(colName);
            $('#factoryId').val(factoryId);
        },
        buttons: [{
            text: '確定',
            handler: function () {
                var isChecked = document.getElementsByName('isChecked');
                for (var i = 0; i < isChecked.length; i++) {
                    if (isChecked[i].checked == true) {
                        var noAndname = isChecked[i].value;
                        var noAndnames = noAndname.split(",");
                        $("#" + colNo).val(noAndnames[0]);
                        $("#" + colName).val(noAndnames[1]);
                    }
                }
                d.panel('close');
            }
        }, {
            text: '置空該節點主管',
            handler: function () {
                $("#" + colNo).val("");
                $("#" + colName).val("");
                if (colNo == 'kchargeno') {//課級
                    $("#kchargeno").val("");
                    $("#kchargename").val("");
                    $("#bchargeno").val("");
                    $("#bchargename").val("");
                    $("#cchargeno").val("");
                    $("#cchargename").val("");
                    $("#zchargeTable tr:gt(2)").remove();
                    $("#zchargeno").val("");
                    $("#zchargename").val("");
                    $("#zcchargeTable tr:gt(2)").remove();
                    $("#zcchargeno").val("");
                    $("#zcchargename").val("");
                } else if (colNo == 'bchargeno') {//部級
                    $("#bchargeno").val("");
                    $("#bchargename").val("");
                    $("#cchargeno").val("");
                    $("#cchargename").val("");
                    $("#zchargeTable tr:gt(2)").remove();
                    $("#zchargeno").val("");
                    $("#zchargename").val("");
                    $("#zcchargeTable tr:gt(2)").remove();
                    $("#zcchargeno").val("");
                    $("#zcchargename").val("");
                } else if (colNo == 'cchargeno') {//廠級
                    $("#cchargeno").val("");
                    $("#cchargename").val("");
                    $("#zchargeTable tr:gt(2)").remove();
                    $("#zchargeno").val("");
                    $("#zchargename").val("");
                    $("#zcchargeTable tr:gt(2)").remove();
                    $("#zcchargeno").val("");
                    $("#zcchargename").val("");
                }
                d.panel('close');
            }
        }]
    });
}

/**
 * 單選簽核路徑普通簽核節點  hyc
 * @param eserTyp
 * @param eserNodeName
 * @param colNo
 * @param colName
 */
function selectRoleCom(eserTyp,eserNodeName, colNo, colName) {
//    if (deptNo == null || deptNo == '') {
//        $.messager.alert("溫馨提示", "請輸入承辦人工號.", "warning");
//        return;
//    }
    // var h = document.documentElement.clientHeight || document.body.clientHeight;
    var w = document.documentElement.clientWidth || document.body.clientWidth;
    var top = ($(window).height() - 450) / 2;
    var left = ($(window).width() - 650) / 2;
    var scrollTop = top + $(document).scrollTop();
    var scrollLeft = $(document).scrollLeft();
    d = $("#win").dialog({
        title: '簽核角色人員選擇',
        width: 650,
        height: 450,
        // async: false,
        href: ctx + '/system/auditingNode/roleList',
        maximizable: false,
        modal: true,
        onOpen: function () {

			            $('#chargeNo').val(colNo);
            $('#chargeName').val(colName);
            $('#eserTyp').val(eserTyp);
            $('#eserNodeName').val(eserNodeName);
            
        },
        buttons: [{
            text: '確定',
            handler: function () {
              /*  var isChecked = document.getElementsByName('isChecked');
                for (var i = 0; i < isChecked.length; i++) {
                    if (isChecked[i].checked == true) {
                        var noAndname = isChecked[i].value;
                        var noAndnames = noAndname.split(",");
                        $("#" + colNo).val(noAndnames[0]);
                        $("#" + colName).val(noAndnames[1]);
                    }
                }*/
            	var row = dg.datagrid('getSelected');
            
                 $("#" + colNo).val(row.empno);
                 $("#" + colName).val(row.username);
                d.panel('close');
            }
        }, {
            text: '置空該節點主管',
            handler: function () {
                $("#" + colNo).val("");
                $("#" + colName).val("");
                d.panel('close');
            }
        }]
    });
}
/**
 * 多選簽核路徑會簽節點
 * @param tableId
 * @param deptNo
 * @param colNo
 * @param colName
 * @param factoryId
 * @param dtoName
 */
function selectRole3(tableId, deptNo, colNo, colName, factoryId, dtoName) {
    if (deptNo == null || deptNo == '') {
        // alert("請輸入承辦人工號.");
        $.messager.alert("溫馨提示", "請輸入承辦人工號.", "warning");
        return;
    }
    d = $("#win").dialog({
        title: '簽核角色人員選擇',
        width: 650,
        height: 450,
        async: false,
        href: ctx + '/system/auditingNode/roleList3',
        maximizable: false,
        modal: true,
        onOpen: function () {
            $('#deptNo').val(deptNo);
            $('#chargeNo').val(colNo);
            $('#chargeName').val(colName);
            $('#factoryId').val(factoryId);
        },
        buttons: [{
            text: '確定',
            handler: function () {
                $("#" + tableId + " tr:gt(2)").remove();
                $("#" + colNo).val("");
                $("#" + colName).val("");
                var rows = $('#roleListdg3').datagrid('getSelections');
                var ids = [];
                for (var i = 0, j = rows.length; i < j; i++) {
                    ids.push(rows[i].empno + ',' + rows[i].username);
                }
                var html = "";
                $.each(ids, function (i, v) {
                    if (i == 0) {
                        $("#" + colNo).val(v.split(",")[0]);
                        $("#" + colName).val(v.split(",")[1]);
                        $("#" + colNo).removeClass("validatebox-invalid");
                        $("#" + colName).removeClass("validatebox-invalid");
                    } else {
                        debugger;
                        html += "<tr align='center'>"
                        if (dtoName != null && dtoName != undefined&& dtoName != 'undefined') {
                            html+="<td><input name='" + dtoName + "." + colNo + "' style='width: 80px' value='" + v.split(",")[0] + "' />"
                            + "<font color='red'>*</font>/<input name='" + dtoName + "." + colName + "' style='width: 80px'  value='" + v.split(",")[1] + "' /></td>" + "</tr>";
                        } else {
                        	html+="<td><input name='" + colNo + "' style='width: 80px' value='" + v.split(",")[0] + "' />"
                            + "<font color='red'>*</font>/<input name='" + colName + "' style='width: 80px'  value='" + v.split(",")[1] + "' /></td>" + "</tr>";
                        }
                    }
                })
                $("#" + tableId).append(html);
                d.panel('close');
            }
        }, {
            text: '置空該節點主管',
            handler: function () {
                $("#" + tableId + " tr:gt(2)").remove();
                $("#" + colNo).val("");
                $("#" + colName).val("");
                d.panel('close');
            }
        }]
    });
}

/**
 * 多選角色會簽節點
 * @param dutyId
 * @param tableId
 * @param colNo
 * @param colName
 * @param factoryId
 * @param dtoName
 */
function selectRole2(dutyId, tableId, colNo, colName, factoryId, dtoName) {
    if (factoryId == null || factoryId == '') {
        // alert("請輸入承辦人工號.");
        $.messager.alert("溫馨提示", "請輸入承辦人工號.", "info");
        return;
    }
    d = $("#win").dialog({
        title: '簽核角色人員選擇',
        width: 650,
        height: 450,
        async: false,
        href: ctx + '/system/auditingNode/roleList2',
        maximizable: false,
        modal: true,
        onOpen: function () {
            $('#dutyId').val(dutyId);
            $('#factoryId').val(factoryId);
        },
        buttons: [{
            text: '確定',
            handler: function () {
                $("#" + tableId + " tr:gt(2)").remove();
                $("#" + colNo).val("");
                $("#" + colName).val("");
                var rows = $('#roleListdg2').datagrid('getSelections');
                var ids = [];
                for (var i = 0, j = rows.length; i < j; i++) {
                    ids.push(rows[i].empno + ',' + rows[i].username);
                }
                var html = "";
                $.each(ids, function (i, v) {
                    if (i == 0) {
                        $("#" + colNo).val(v.split(",")[0]);
                        $("#" + colName).val(v.split(",")[1]);
                        $("#" + colNo).removeClass("validatebox-invalid");
                        $("#" + colName).removeClass("validatebox-invalid");
                    } else {
                        html += "<tr align='center'>";
                        if (dtoName != null && dtoName != undefined&& dtoName != 'undefined') {
                            html +="<td><input name='" + dtoName + "." + colNo + "' style='width: 80px' value='" + v.split(",")[0] + "' />"
                            + "<font color='red'>*</font>/<input name='" + dtoName + "." + colName + "' style='width: 80px'  value='" + v.split(",")[1] + "' /></td>" + "</tr>";
                        } else {
                            html +="<td><input name='" + colNo + "' style='width: 80px' value='" + v.split(",")[0] + "' />"
                            + "<font color='red'>*</font>/<input name='" + colName + "' style='width: 80px'  value='" + v.split(",")[1] + "' /></td>" + "</tr>";
                        }
                    }
                })
                $("#" + tableId).append(html);
                d.panel('close');
            }
        }, {
            text: '置空該節點主管',
            handler: function () {
                $("#" + tableId + " tr:gt(2)").remove();
                $("#" + colNo).val("");
                $("#" + colName).val("");
                d.panel('close');
            }
        }]
    });
}

/**
 * 單選角色普通簽核節點
 * @param dutyId
 * @param tableId
 * @param colNo
 * @param colName
 * @param factoryId
 * @param dtoName
 */
function selectRole4(dutyId, colNo, colName, factoryId) {
    if (factoryId == null || factoryId == '') {
        // alert("請輸入承辦人工號.");
        $.messager.alert("溫馨提示", "請輸入承辦人工號.", "info");
        return;
    }
    d = $("#win").dialog({
        title: '簽核角色人員選擇',
        width: 650,
        height: 450,
        async: false,
        href: ctx + '/system/auditingNode/roleList4',
        maximizable: false,
        modal: true,
        onOpen: function () {
            $('#dutyId').val(dutyId);
            $('#factoryId').val(factoryId);
        },
        buttons: [{
            text: '確定',
            handler: function () {
                var isChecked = document.getElementsByName('isChecked');
                for (var i = 0; i < isChecked.length; i++) {
                    if (isChecked[i].checked == true) {
                        var noAndname = isChecked[i].value;
                        var noAndnames = noAndname.split(",");
                        $("#" + colNo).val(noAndnames[0]);
                        $("#" + colName).val(noAndnames[1]);
                    }
                }
                d.panel('close');
            }
        }, {
            text: '置空該節點主管',
            handler: function () {
                $("#" + colNo).val("");
                $("#" + colName).val("");
                d.panel('close');
            }
        }]
    });
}

/*通過產區id獲取產區名稱*/
function getFactryName(value, row, index) {
    var reStr = '';
    $.ajax({
        url: ctx + "/tqhfactoryidconfig/getFactryNameById/" + row.factoryid,
        type: "GET",
        async: false,
        success: function (data) {
            // alert(data.factoryname);
            reStr = data.factoryname;
        }
    });
    return reStr;
}

function audit(serialno, status,perCall) {
    if(perCall!=null&&status!='1'){
        if(!perCall()) return;
    }
    /* if($("#mainform").form('validate')){*/
    var attachidsremark = $("#attachidsremark").val();
    var reattachids = $("#reattachids").val();
    if ((attachidsremark == null || attachidsremark == '') && status == "1") {
        $.messager.alert('操作提示', '批註不能為空！', "warning");
        return;
    }
    $.ajax({
        url: ctx + '/wfcontroller/completeTask',
        type: 'POST',
        beforeSend: ajaxLoading,
        // dataType: 'json',
        data: {serialno: serialno, status: status, attachidsremark: attachidsremark, reattachids: reattachids},
        success: function (result) {
            ajaxLoadEnd();
            successTip(result, dg, d);
            if (result == 'success') {
                window.parent.mainpage.mainTabs.closeCurrentTab();
            }
        }
    });
    /*}else{
        $.messager.alert('操作提示','存在校验项未通过！',"warning");
    }*/
}

function showWfImag(processId, title) {
    d = $("#dlg").dialog({
        title: title,
        width: 1200,
        height: 850,
        href: ctx + '/wfcontroller/getImg?processId=' + processId,
        maximizable: true,
        modal: true,
        buttons: [{
            text: '确认',
            handler: function () {
                d.panel('close');
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

// 日期格式化
// 用法：var time1 = new Date().Format('yyyy-MM-dd'); var time2 = new Date().Format('yyyy-MM-dd HH:mm:ss');
Date.prototype.format = function (format) {
    var o = {
        "M+": this.getMonth() + 1, // month
        "d+": this.getDate(), // day
        "h+": this.getHours(), // hour
        "m+": this.getMinutes(), // minute
        "s+": this.getSeconds(), // second
        "q+": Math.floor((this.getMonth() + 3) / 3), // quarter
        "S": this.getMilliseconds()
        // millisecond
    }
    if (/(y+)/.test(format))
        format = format.replace(RegExp.$1, (this.getFullYear() + "")
            .substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp("(" + k + ")").test(format))
            format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
    return format;
}

function formatDatebox(value) {
    if (value == null || value == '') {
        return '';
    }
    var dt;
    if (value instanceof Date) {
        dt = value;
    } else {
        dt = new Date(value);
    }

    return dt.format("yyyy-MM-dd hh:mm:ss"); //扩展的Date的format方法(上述插件实现)
}

function formatDate(value) {
    if (value == null || value == '') {
        return '';
    }
    var dt;
    if (value instanceof Date) {
        dt = value;
    } else if (value instanceof String) {
        value = value.replace(/-/g,':').replace(' ',':');
        dateComponents = value.split(':');
        dt = new Date(dateComponents[0],(dateComponents[1]-1),dateComponents[2],dateComponents[3],dateComponents[4],dateComponents[5]);
    } else {
        dt = new Date(value);
    }

    return dt.format("yyyy-MM-dd"); //扩展的Date的format方法(上述插件实现)
}

function formatWorkStatus(value, row, index) {
    if (value == '2') {
        return '審核中';
    } else if (value == '4') {
        return '駁回';
    }
}


function canelTask(serialno) {
    $.ajax({
        url: ctx + '/wfcontroller/cancelTask',
        type: 'POST',
        // dataType: 'json',
        data: {serialno: serialno},
        success: function (result) {
            successTip(result, dg, d);
            window.parent.mainpage.mainTabs.closeCurrentTab();
        }
    });
}

function closeCurrentTab() {
    window.parent.mainpage.mainTabs.closeCurrentTab();
}

function printPage() {

    window.print()
}

function printDiv(printpage) {
    var headstr = "<html><head><title></title></head><body>";
    var footstr = "</body>";
    var newstr = document.all.item(printpage).innerHTML;
    var oldstr = document.body.innerHTML;
    document.body.innerHTML = headstr + newstr + footstr;
    window.print();
    document.body.innerHTML = oldstr;
    return false;
}

function printWindow(buttonNames) {

    var buttonNameArray = buttonNames.split(",");
    for (var i = 0; i < buttonNameArray.length; i++) {
        $("#" + buttonNameArray[i]).css("display", "none");
    }
    // document.getElementsByTagName('body')[0].style.zoom=0.85;
    window.print();
    for (var i = 0; i < buttonNameArray.length; i++) {
        $("#" + buttonNameArray[i]).css("display", "");
    }
    return false;
}

//上移
function MoveUp(tableName) {
    var row = $("#" + tableName).datagrid('getSelected');
    var index = $("#" + tableName).datagrid('getRowIndex', row);
    mysort(index, 'up', tableName);

}

//下移
function MoveDown(tableName) {
    var row = $("#" + tableName).datagrid('getSelected');
    var index = $("#" + tableName).datagrid('getRowIndex', row);
    mysort(index, 'down', tableName);

}

//置顶
function MoveTop(tableName) {
    var row = $("#" + tableName).datagrid('getSelected');
    var index = $("#" + tableName).datagrid('getRowIndex', row);
    mysort(index, 'top', tableName);

}

//置底
function MoveButtom(tableName) {
    var row = $("#" + tableName).datagrid('getSelected');
    var index = $("#" + tableName).datagrid('getRowIndex', row);
    mysort(index, 'buttom', tableName);

}

//上移
function MoveUp(tableName, index) {
    mysort(index, 'up', tableName);

}

//下移
function MoveDown(tableName, index) {
    mysort(index, 'down', tableName);

}

//置顶
function MoveTop(tableName, index) {
    mysort(index, 'top', tableName);

}

//置底
function MoveButtom(tableName, index) {
    mysort(index, 'buttom', tableName);

}

//置於已選擇的底部
function moveSelectionsButtom(tableName, index) {
    var isSelected = false;
    var selectItems = $('#' + tableName).datagrid('getSelections');
    for (var i = 0; i < selectItems.length; i++) {
        var rowIndex = $('#' + tableName).datagrid('getRowIndex', selectItems[i]);
        // console.log(rowIndex);
        if (rowIndex == index) {
            isSelected = true;
        }
    }
    //判斷是否被選擇
    if (isSelected) {
        var toup = $('#' + tableName).datagrid('getData').rows[index];

        if (selectItems.length == 1) {
            MoveTop(tableName, index);
        } else {

            var rowIndex = $('#' + tableName).datagrid('getRowIndex', selectItems[selectItems.length - 2]);
            console.log(rowIndex);
            $('#' + tableName).datagrid('uncheckAll');
            for (var i = index; i > rowIndex + 1; i--) {
                var todown = $('#' + tableName).datagrid('getData').rows[i - 1];
                $('#' + tableName).datagrid('getData').rows[i] = todown;
                $('#' + tableName).datagrid('refreshRow', i);

            }
            $('#' + tableName).datagrid('getData').rows[rowIndex + 1] = toup;
            $('#' + tableName).datagrid('refreshRow', rowIndex + 1);
            $('#' + tableName).datagrid('unselectAll');
            for (var i = 0; i < selectItems.length; i++) {
                var rowIndex = $('#' + tableName).datagrid('getRowIndex', selectItems[i]);
                // console.log(rowIndex);
                $('#' + tableName).datagrid('selectRow', rowIndex);
            }
        }
    }
}

function sortByClick(tableName, index) {
    var isSelected = false;
    var selectItems = $('#' + tableName).datagrid('getSelections');
    for (var i = 0; i < selectItems.length; i++) {
        var rowIndex = $('#' + tableName).datagrid('getRowIndex', selectItems[i]);
        // console.log(rowIndex);
        if (rowIndex == index) {
            isSelected = true;
        }
    }
    //判斷是否被選擇
    if (isSelected) {
        var toup = $('#' + tableName).datagrid('getData').rows[index];

        if (selectItems.length == 1) {
            MoveTop(tableName, index);
        } else {

            var rowIndex = $('#' + tableName).datagrid('getRowIndex', selectItems[selectItems.length - 2]);
            if (rowIndex < index) {
                // console.log(rowIndex);
                $('#' + tableName).datagrid('uncheckAll');
                for (var i = index; i > rowIndex + 1; i--) {
                    var todown = $('#' + tableName).datagrid('getData').rows[i - 1];
                    $('#' + tableName).datagrid('getData').rows[i] = todown;
                    $('#' + tableName).datagrid('refreshRow', i);

                }
                $('#' + tableName).datagrid('getData').rows[rowIndex + 1] = toup;
                $('#' + tableName).datagrid('refreshRow', rowIndex + 1);
                $('#' + tableName).datagrid('unselectAll');
                for (var i = 0; i < selectItems.length; i++) {
                    var rowIndex = $('#' + tableName).datagrid('getRowIndex', selectItems[i]);
                    $('#' + tableName).datagrid('getRows')[rowIndex]["num"] = i + 1;
                    $('#' + tableName).datagrid('refreshRow', rowIndex);
                    // console.log(rowIndex);
                    $('#' + tableName).datagrid('selectRow', rowIndex);
                }
            }
        }
    } else {
        MoveButtom(tableName, index);
    }
}

//判断向上，向下，置底，置顶的方法
function mysort(index, type, gridname) {
    if ("up" == type) {
        if (index != 0) {
            var toup = $('#' + gridname).datagrid('getData').rows[index];
            var todown = $('#' + gridname).datagrid('getData').rows[index - 1];
            $('#' + gridname).datagrid('getData').rows[index] = todown;
            $('#' + gridname).datagrid('getData').rows[index - 1] = toup;
            $('#' + gridname).datagrid('refreshRow', index);
            $('#' + gridname).datagrid('refreshRow', index - 1);
            $('#' + gridname).datagrid('selectRow', index - 1);
        }
    } else if ("down" == type) {
        var rows = $('#' + gridname).datagrid('getRows').length;
        if (index != rows - 1) {
            var todown = $('#' + gridname).datagrid('getData').rows[index];
            var toup = $('#' + gridname).datagrid('getData').rows[index + 1];
            $('#' + gridname).datagrid('getData').rows[index + 1] = todown;
            $('#' + gridname).datagrid('getData').rows[index] = toup;
            $('#' + gridname).datagrid('refreshRow', index);
            $('#' + gridname).datagrid('refreshRow', index + 1);
            $('#' + gridname).datagrid('selectRow', index + 1);
        }
    } else if ("top" == type) {
        var rows = $('#' + gridname).datagrid('getRows').length;
        var toup = $('#' + gridname).datagrid('getData').rows[index];

        var selectItems = $('#' + gridname).datagrid('getSelections');
        $('#' + gridname).datagrid('uncheckAll');
        for (var i = index; i > 0; i--) {
            var todown = $('#' + gridname).datagrid('getData').rows[i - 1];
            $('#' + gridname).datagrid('getData').rows[i] = todown;
            $('#' + gridname).datagrid('refreshRow', i);

        }
        $('#' + gridname).datagrid('getData').rows[0] = toup;
        $('#' + gridname).datagrid('refreshRow', 0);
        $('#' + gridname).datagrid('unselectAll');
        for (var i = 0; i < selectItems.length; i++) {
            var rowIndex = $('#' + gridname).datagrid('getRowIndex', selectItems[i]);
            $('#' + gridname).datagrid('getRows')[rowIndex]["num"] = i + 1;
            $('#' + gridname).datagrid('refreshRow', rowIndex);
            // console.log(rowIndex);
            $('#' + gridname).datagrid('selectRow', rowIndex);
        }

    } else if ("buttom" == type) {
        var rows = $('#' + gridname).datagrid('getRows').length;
        var todown = $('#' + gridname).datagrid('getData').rows[index];
        var selectItems = $('#' + gridname).datagrid('getSelections');
        $('#' + gridname).datagrid('uncheckAll');
        $('#' + gridname).datagrid('clearChecked');
        for (var i = index; i < rows - 1; i++) {

            var toup = $('#' + gridname).datagrid('getData').rows[i + 1];
            $('#' + gridname).datagrid('getData').rows[i] = toup;
            $('#' + gridname).datagrid('refreshRow', i);
        }
        $('#' + gridname).datagrid('getData').rows[rows - 1] = todown;
        $('#' + gridname).datagrid('getData').rows[rows - 1]["num"] = "";
        $('#' + gridname).datagrid('refreshRow', rows - 1);
        // $('#' + gridname).datagrid('selectRow', rows - 1);
        $('#' + gridname).datagrid('unselectAll');
        for (var i = 0; i < selectItems.length; i++) {
            var rowIndex = $('#' + gridname).datagrid('getRowIndex', selectItems[i]);
            $('#' + gridname).datagrid('getRows')[rowIndex]["num"] = i + 1;
            $('#' + gridname).datagrid('refreshRow', rowIndex);
//                dg.datagrid('refreshRow', index);
//             console.log(rowIndex);
            $('#' + gridname).datagrid('selectRow', rowIndex);
        }

    }
}

function listSearchReset() {
    $("#searchFrom").form("reset");
}

function ajaxLoading() {
    var top = ($(window).height() - $(".mainform").height()) / 2;
    var left = ($(window).width() - $(".mainform").width()) / 2;
    var scrollTop = $(document).scrollTop();
    var scrollLeft = $(document).scrollLeft();
    $("<div class=\"datagrid-mask\"></div>").css({
        display: "block",
        width: "100%",
        height: $(document).height()
    }).appendTo("body");
    $("<div class=\"datagrid-mask-msg\"></div>").html("正在处理，请稍候。。。").appendTo("body").css({
        display: "block",
        left: left + scrollLeft,
        top: top + scrollTop
    });
}

function ajaxLoadEnd() {
    $(".datagrid-mask").remove();
    $(".datagrid-mask-msg").remove();
}