var dg;
var d;
$(function() {
	dg = $('#dg').datagrid({
		method : "get",
		url : ctx + '/etestitem/list',
		fit : true,
		fitColumns : true,
		border : false,
		idField : 'id',
		striped : true,
		pagination : true,
		rownumbers : true,
		loadFilter : function(data) {
			// 过滤数据
			var value = {
				total : data.total,
				rows : []
			};
			var x = 0;
			for ( var i = 0; i < data.rows.length; i++) {
				// 0.駁回1.安排作業2.簽核完成（待安排作業）

				if (data.rows[i].itemStatus == "0") {
					data.rows[i].itemStatus = "駁回";
				} else if (data.rows[i].itemStatus == "1") {
					data.rows[i].itemStatus = "已安排作業";
				} else if (data.rows[i].itemStatus == "2") {
					data.rows[i].itemStatus = "待安排作業";
				}
				// :1.飲用純淨水檢驗2.直飲水檢驗3.二次供水水質檢驗4.自建飲用水系統水質檢測
				if (data.rows[i].waterTestType == "1") {
					data.rows[i].waterTestType = "飲用純淨水檢驗";
				} else if (data.rows[i].waterTestType == "2") {
					data.rows[i].waterTestType = "直飲水檢驗";
				} else if (data.rows[i].waterTestType == "3") {
					data.rows[i].waterTestType = "二次供水水質檢驗";
				} else if (data.rows[i].waterTestType == "4") {
					data.rows[i].waterTestType = "自建飲用水系統水質檢測";
				}
				value.rows[x++] = data.rows[i];
			}
			return value;

		},
		pageNumber : 1,
		pageSize : 20,
		pageList : [ 10, 20, 30, 40, 50 ],
		singleSelect : true,
		columns : [ [ {
			field : 'id',
			title : '主鍵',
			hidden : true
		}, {
			field : 'serialno',
			title : '任務編號',
			sortable : true,
			width : 100
		}, {
			field : 'itemNo',
			title : '項目編碼',
			sortable : true,
			width : 100
		}, {
			field : 'itemContent',
			title : '項目內容',
			sortable : true,
			width : 100
		}, {
			field : 'waterTestType',
			title : '樣品類別',
			sortable : true,
			width : 100
		}, {
			field : 'testTyp',
			title : '檢驗類型',
			sortable : true,
			width : 100
		}, {
			field : 'testItems',
			title : '檢驗項目',
			sortable : true,
			width : 100
		}, {
			field : 'unitPrice',
			title : '單價（元）',
			sortable : true,
			width : 100
		}, {
			field : 'testCount',
			title : '數量',
			sortable : true,
			width : 100
		}, {
			field : 'testCost',
			title : '小計（元）',
			sortable : true,
			width : 100
		}, {
			field : 'itemStatus',
			title : '點位狀態',
			sortable : true,
			width : 100
		} /*
			 * , { field : 'attachids', title : '附件Id', sortable : true, width :
			 * 100 }, { field : 'reattachids', title : '補充說明附件Id', sortable :
			 * true, width : 100 }, { field : 'updateBy', title : '更新者',
			 * sortable : true, width : 100 }, { field : 'updateDate', title :
			 * '更新時間', sortable : true, width : 100 }
			 */] ],
		enableHeaderClickMenu : true,
		enableHeaderContextMenu : true,
		enableRowContextMenu : false,
		rowTooltip : true,
		toolbar : '#tb'
	});
});

// 弹窗增加
function add() {
	d = $("#dlg").dialog({
		title : '添加飲用水檢測申請項目明細表',
		width : 380,
		height : 380,
		href : ctx + '/etestitem/create',
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '确认',
			handler : function() {
				$("#mainform").submit();
			}
		}, {
			text : '取消',
			handler : function() {
				d.panel('close');
			}
		} ]
	});
}

// 删除
function del() {
	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data) {
		if (data) {
			$.ajax({
				type : 'get',
				url : ctx + "/etestitem/delete/" + row.id,
				success : function(data) {
					successTip(data, dg);
				}
			});
		}
	});
}

// 弹窗修改
function upd() {
	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	d = $("#dlg").dialog({
		title : '修改飲用水檢測申請項目明細表',
		width : 380,
		height : 340,
		href : ctx + '/etestitem/update/' + row.id,
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '修改',
			handler : function() {
				$('#mainform').submit();
			}
		}, {
			text : '取消',
			handler : function() {
				d.panel('close');
			}
		} ]
	});
}
//安排作業通過
function pass() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    if (row.itemStatus != "待安排作業")
	{
	alert("非【待安排作業】狀態不能進行進行作業安排!");
	return;
	}
    parent.$.messager.confirm('提示', '確定通過？', function (data) {
        if (data) {
            $.ajax({
                type: 'get',
                url: ctx+'/etestitem/pass/' + row.id+"?itemNo="+row.itemNo,
                success: function (data) {
                    successTip(data, dg);
                }
            });
        }
    });
}
//安排作業駁回
function reject() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    if (row.itemStatus != "待安排作業")
	{
	alert("非【待安排作業】狀態不能進行進行作業安排!");
	return;
	}
    parent.$.messager.confirm('提示', '確定駁回？', function (data) {
        if (data) {
            $.ajax({
                type: 'get',
                url: ctx+'/etestitem/reject/' + row.id+"?itemNo="+row.itemNo,
                success: function (data) {
                    successTip(data, dg);
                }
            });
        }
    });
}
// 创建查询对象并查询
function cx() {
	var obj = $("#searchFrom").serializeObject();
	dg.datagrid('load', obj);
}
// 表單進度查詢頁面查詢條件重置
function listSearchReset() {
	$("#searchFrom").form("reset");
}
// 导出excel
function exportExcel() {
	var form = document.getElementById("searchFrom");
	searchFrom.action = ctx + '/etestitem/exportExcel';
	form.submit();
}