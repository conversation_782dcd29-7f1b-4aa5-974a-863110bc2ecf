package com.foxconn.ipebg.report.entity;

import java.io.Serializable;
import java.util.Date;
//import java.sql.Date;

import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * 警衛人員資料
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-12-21 14:54:46
 */
@Entity
@Table(name = "BS_ABNORMAL_PERSONNEL")
@DynamicUpdate
@DynamicInsert
public class AbsentReportEntity extends DataEntity<AbsentReportEntity> implements
		Serializable {
	private static final long serialVersionUID = 1L;

	//結束時間
	private Date endTime;
	//工號
	private String empNo;
	//姓名
	private String empName;
	//班別
	private String shiftNo;
	//異常日期->shiftDate
	private Date issueDate;
	//異常原因->abnormalCauses
	private String issueReason;
	//公司名稱
	private String company;
	//異常狀態
	private String abnormalState;
	//刷卡時間
	private Date cardTime;
	//崗位編號
	private String postRecno;

	//班別
	private String shiftName;
	//開始時間
	private Date startTime;

	/**
	 * 设置：結束時間
	 */
	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	/**
	 * 获取：結束時間
	 */

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Column(name = "END_TIME", nullable = false, length = 20)
	public Date getEndTime() {
		return endTime;
	}

	/**
	 * 设置：工號
	 */
	public void setEmpNo(String empNo) {
		this.empNo = empNo;
	}

	/**
	 * 获取：工號
	 */

	@Column(name = "EMP_NO", nullable = false, length = 20)
	public String getEmpNo() {
		return empNo;
	}

	/**
	 * 设置：姓名
	 */
	public void setEmpName(String empName) {
		this.empName = empName;
	}

	/**
	 * 获取：姓名
	 */

	@Column(name = "EMP_NAME", nullable = false, length = 20)
	public String getEmpName() {
		return empName;
	}

	/**
	 * 设置：公司名稱
	 */
	public void setCompany(String company) {
		this.company = company;
	}

	/**
	 * 获取：公司名稱
	 */

	@Column(name = "COMPANY", nullable = false, length = 20)
	public String getCompany() {
		return company;
	}

	@Column(name = "SHIFT_DATE")
	public Date getIssueDate() {
		return issueDate;
	}

	public void setIssueDate(Date issueDate) {
		this.issueDate = issueDate;
	}

	/**
	 * 设置：刷卡時間
	 */
	public void setCardTime(Date cardTime) {
		this.cardTime = cardTime;
	}

	/**
	 * 获取：刷卡時間
	 */

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Column(name = "CARD_TIME", nullable = false, length = 20)
	public Date getCardTime() {
		return cardTime;
	}


	@Column(name = "ABNORMAL_CAUSES", nullable = false, length = 20)
	public String getIssueReason() {
		return issueReason;
	}

	public void setIssueReason(String issueReason) {
		this.issueReason = issueReason;
	}


	/**
	 * 设置：崗位編號
	 */
	public void setPostRecno(String postRecno) {
		this.postRecno = postRecno;
	}

	/**
	 * 获取：崗位編號
	 */

	@Column(name = "POST_RECNO", nullable = false, length = 20)
	public String getPostRecno() {
		return postRecno;
	}


	@Column(name = "ABNORMAL_STATE", nullable = false, length = 2)
	public String getAbnormalState() {
		return abnormalState;
	}

	public void setAbnormalState(String abnormalState) {
		this.abnormalState = abnormalState;
	}

	/**
	 * 设置：班別代碼
	 */
	public void setShiftNo(String shiftNo) {
		this.shiftNo = shiftNo;
	}

	/**
	 * 获取：班別代碼
	 */

	@Column(name = "SHIFT_NO", nullable = false, length = 20)
	public String getShiftNo() {
		return shiftNo;
	}

	/**
	 * 设置：班別
	 */
	public void setShiftName(String shiftName) {
		this.shiftName = shiftName;
	}

	/**
	 * 获取：班別
	 */

	@Column(name = "SHIFT_NAME", nullable = false, length = 20)
	public String getShiftName() {
		return shiftName;
	}

	/**
	 * 设置：開始時間
	 */
	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	/**
	 * 获取：開始時間
	 */

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Column(name = "START_TIME", nullable = false, length = 20)
	public Date getStartTime() {
		return startTime;
	}

}
