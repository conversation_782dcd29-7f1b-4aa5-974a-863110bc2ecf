package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.dao.BsTrainingArrangePerDao;
import com.foxconn.ipebg.basics.entity.BsTrainingArrangeEntity;
import com.foxconn.ipebg.basics.dao.BsTrainingArrangeDao;
import com.foxconn.ipebg.basics.entity.BsTrainingArrangePerEntity;
import com.foxconn.ipebg.basics.entity.PerInfoEntity;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import com.foxconn.ipebg.common.utils.*;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.hibernate.type.StandardBasicTypes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foxconn.ipebg.system.entity.Dict;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WorkFlowEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.system.service.DictService;
import com.foxconn.ipebg.system.entity.User;
import com.foxconn.ipebg.system.utils.UserUtil;

import java.util.*;

/**
 * 培訓期日排班表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-09-09 13:50:28
 */
@Service
@Transactional(readOnly = true)
public class BsTrainingArrangeService extends BaseBusinessService<BsTrainingArrangeEntity, String> {
    @Autowired
    private BsTrainingArrangeDao bsTrainingArrangeDao;

    @Autowired
    private DictService dictService;

    @Override
    public HibernateDao<BsTrainingArrangeEntity, String> getEntityDao() {
        return bsTrainingArrangeDao;
    }

    public BsTrainingArrangeEntity findBySerialno(String serialno) {
        return this.bsTrainingArrangeDao.findUniqueBy("serialno", serialno);
    }

    public List<Map<String, String>> listAllTrainingPeriod() {
        User user = UserUtil.getCurrentUser();
        String coms = null;
        if (user != null) {
            coms = user.getSecurityCom();
            if (coms != null) {
                coms = coms.replaceAll(",", "','");
            }
        }
        if (coms == null) coms = "";
        String sql = "select * from bs_training_period where security_com in ('"+coms+"') order by CREATE_DATE desc";
        SQLQuery query = bsTrainingArrangeDao.createSQLQuery(sql);
        query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        return query.list();
    }

    /**
     * 違反六休一及每期排班超上限人員
     *
     * @return
     */
    public List<Map<String, String>> listUnvalidTrainee(Map<String, String> params) {
        if (StringUtils.isBlank(params.get("arrangeDate")) || StringUtils.isBlank(params.get("trainingPeriodNo"))) {
            return null;
        }

        // 最大连续工作天数，太原6天，晋城没限制 zh
        Integer maxContinueWorkDays = Integer.parseInt(dictService.getDictByLabelAndType("max_continue_work_days", "arrange_setting").getValue());
        // 最大培训天数，太原为9天，晋城没限制 zb
        Integer maxTrainingDays = Integer.parseInt(dictService.getDictByLabelAndType("max_training_days", "arrange_setting").getValue());

        StringBuffer sql = new StringBuffer();

        //sql: work six days have a rest
        if (maxContinueWorkDays > 0) {
            for (int i = 0; i <= maxContinueWorkDays; i++) {
                if (i != 0) {
                    sql.append("   union all  ");
                }
                sql.append(" SELECT b.emp_no empNo,max(b.emp_name) empName ");
                sql.append("  FROM bs_training_arrange a, bs_training_arrange_per b  ");
                sql.append(" where a.id = b.bsta_id  ");
                sql.append("   and a.training_period_no='" + params.get("trainingPeriodNo") + "'  ");
                sql.append("   and a.arrange_date between to_date('" + params.get("arrangeDate") + "', 'yyyy-mm-dd')-" + (maxContinueWorkDays - i) + " and to_date('" + params.get("arrangeDate") + "', 'yyyy-mm-dd')+ " + i);
                sql.append(" group by b.emp_no   ");
                sql.append(" having count(a.id)>= "+maxContinueWorkDays+"  ");
            }
        }
        if (maxContinueWorkDays > 0 && maxTrainingDays > 0) {
            //sql:Max Training Days
            sql.append("   union all  ");
        }
        if (maxTrainingDays > 0) {
            sql.append("SELECT b.emp_no empNo,max(b.emp_name) empName   " +
                    "  FROM bs_training_arrange a, bs_training_arrange_per b   " +
                    " where a.id = b.bsta_id   " +
                    "   and a.training_period_no = '" + params.get("trainingPeriodNo") + "'   " +
                    "   group by b.emp_no   " +
                    "   having count(a.id)>=" + maxTrainingDays);
        }
        if (sql.length() > 0) {
            sql.append(" order by empNo");
            SQLQuery query = bsTrainingArrangeDao.createSQLQuery(sql.toString());
            query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
            return query.list();
        } else {
            return new ArrayList<Map<String, String>>();
        }
    }


    /**
     * 培訓期所有人員
     * @param params
     * @return
     */
    public List<Map<String, String>> listAllTrainee(Map<String, String> params) {
        if (StringUtils.isBlank(params.get("arrangeDate")) || StringUtils.isBlank(params.get("trainingPeriodNo")) ) {
            return null;
        }

        String sql="SELECT c.emp_no empNo, c.emp_name empName  " +
                "  FROM bs_training_person a, bs_training_period b, bs_trainee c  " +
                " where a.TRAINING_PERIOD_NO = b.training_period_no and a.EMP_NO=c.EMP_NO " +
                "   and a.training_period_no = '"+params.get("trainingPeriodNo")+"'  " +
                "   and  to_date('" + params.get("arrangeDate") + "','yyyy-mm-dd') between  b.start_date and b.end_date" +
                " order by c.emp_no ";
        
        SQLQuery query = bsTrainingArrangeDao.createSQLQuery(sql);
        query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        return query.list();
    }

    /**
     * 培訓日排班人員
     * @param params
     * @return
     */
    public List<Map<String, String>> listArrangeTrainee(Map<String, String> params) {
        if (StringUtils.isBlank(params.get("arrangeDate")) || StringUtils.isBlank(params.get("trainingPeriodNo")) ) {
            return null;
        }
        String sql="SELECT b.emp_no empNo,b.emp_name empName FROM bs_training_arrange a, bs_training_arrange_per b  where a.id = b.bsta_id " +
                "   and a.arrange_date=to_date('" + params.get("arrangeDate") + "', 'yyyy-mm-dd') and a.training_period_no = '"+params.get("trainingPeriodNo")+"' order by b.emp_no ";
        SQLQuery query = bsTrainingArrangeDao.createSQLQuery(sql);
        query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        return query.list();
    }


    /**
     * count training arrange
     * @param params
     */
    public int countArrange(Map<String, String> params) {
        if (StringUtils.isBlank(params.get("arrangeDate")) || StringUtils.isBlank(params.get("trainingPeriodNo")) ) {
            return -1;
        }
        String sql="select count(*) from bs_training_arrange where training_Period_No = '" +params.get("trainingPeriodNo")+"' and arrange_Date = to_date('"+params.get("arrangeDate")+"','yyyy-mm-dd') ";
        SQLQuery query = bsTrainingArrangeDao.createSQLQuery(sql);
        String count = query.uniqueResult().toString();
        return Integer.valueOf(count);
    }
    /**
     * get training arrange
     * @param entity
     */
    public BsTrainingArrangeEntity getByEntity(BsTrainingArrangeEntity entity) {
        if (entity.getArrangeDate()==null || StringUtils.isBlank(entity.getTrainingPeriodNo()) ) {
            return null;
        }
        String strDate = DateUtils.formatDate(entity.getArrangeDate());
        String sql="select * from bs_training_arrange where training_Period_No = '" +entity.getTrainingPeriodNo()+"' and arrange_Date = to_date('"+strDate+"','yyyy-mm-dd') ";
        List<BsTrainingArrangeEntity> list = bsTrainingArrangeDao.createSQLQuery(sql).addEntity(BsTrainingArrangeEntity.class).list();
        if(list!=null && list.size()==1){
            return list.get(0);
        }
        return null;
    }
    /**
     * get training arrange
     * @param params
     */
    public BsTrainingArrangeEntity getByMap(Map<String,String> params) {
        if (StringUtils.isBlank(params.get("arrangeDate")) || StringUtils.isBlank(params.get("trainingPeriodNo")) ) {
            return null;
        }
        String sql="select * from bs_training_arrange where training_Period_No = '" +params.get("trainingPeriodNo")+"' and arrange_Date = to_date('"+params.get("arrangeDate")+"','yyyy-mm-dd') ";
        List<BsTrainingArrangeEntity> list = bsTrainingArrangeDao.createSQLQuery(sql).addEntity(BsTrainingArrangeEntity.class).list();
        if(list!=null && list.size()==1){
            return list.get(0);
        }
        return null;
    }

    /**
     * 培訓人員稽核信息
     * @param params
     */
    public List<Map<String,String>> listCheckTrainee(Map<String,String> params) {
        if (StringUtils.isBlank(params.get("arrangeDate")) || StringUtils.isBlank(params.get("trainingPeriodNo")) ) {
            return null;
        }

        String sql="SELECT b.emp_no,b.emp_name,(case  when c.abnormal_state='0' then 'N' else 'Y' end ) abnormal_state  " +
                "  FROM bs_training_arrange a  " +
                "  left join bs_training_arrange_per b  " +
                "    on a.id = b.bsta_id  " +
                "  left join bs_abnormal_personnel c  " +
                "    on b.emp_no = c.emp_no  " +
                "   and a.training_period_no = c.post_recno  " +
                "   and a.arrange_date = c.shift_date  " +
                "   where a.training_period_no='"+params.get("trainingPeriodNo")+"'  " +
                "   and a.arrange_date = to_date('"+params.get("arrangeDate")+"','yyyy-mm-dd')";

        SQLQuery query = bsTrainingArrangeDao.createSQLQuery(sql);
        query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        return query.list();
    }


    /**
     * 更新是否稽核字段
     * @param arrangeDate
     * @param trainingPeriodNo
     * @param isCheck
     * @return
     */
    @Transactional(readOnly = false)
    public int updateIsCheck(Date arrangeDate,String trainingPeriodNo,String isCheck ){
        String sql="update BsTrainingArrangeEntity a  set  a.isCheck=?0 where a.arrangeDate = ?1 and a.trainingPeriodNo= ?2 ";
        return bsTrainingArrangeDao.batchExecute(sql,isCheck,arrangeDate,trainingPeriodNo);
    }

    /**
     * 培訓人員匯總表
     * @param page
     * @param params
     * @return
     */
    public Map<String,Object> trainingReport(Page page, Map<String, String> params) {
        //數據
        StringBuffer sql= new StringBuffer("SELECT a.training_period_no training_period_no,  " +
                "                       max(a.training_period_name) training_period_name,  " +
                "                       max(a.start_date) start_arrange_date,  " +
                "                       max(a.end_date) end_arrange_date,  " +
                "                       count(b.id) training_day_num,  " +
                "                       (SELECT count(e.id)  " +
                "                          FROM bs_training_person e  " +
                "                         where e.training_period_no = a.training_period_no) AS trainee_num,  " +
                "                       (SELECT count(c.id)  " +
                "                          FROM bs_training_person c, per_info d  " +
                "                         where c.emp_no = d.emp_no  " +
                "                           and c.training_period_no = a.training_period_no) AS trainee_pass_num  " +
                "                  FROM bs_training_period a, bs_training_arrange b  " +
                "                 where a.training_period_no = b.training_period_no  " );
                if(StringUtils.isNotBlank(params.get("trainingPeriodNo"))){
                    sql.append("                   and a.training_period_no = '"+params.get("trainingPeriodNo")+"'  " );
                }
                if(StringUtils.isNotBlank(params.get("startDate"))){
                    sql.append("                   and a.start_date >= to_date('"+params.get("startDate")+"','yyyy-mm-dd')  " );
                }
                if(StringUtils.isNotBlank(params.get("endDate"))){
                    sql.append("                   and a.end_date <= to_date('"+params.get("endDate")+"','yyyy-mm-dd')  " );
                }

                sql.append("                 group by a.training_period_no  " +
                        "                 order by a.training_period_no desc  ");

                if(page!=null){
                    sql.append(         "LIMIT "+ page.getPageSize() +
                                        " OFFSET "+ (page.getFirst() - 1));
                }

        List list = this.bsTrainingArrangeDao.createSQLQuery(sql.toString()).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();

        //總數
        StringBuffer countSql=new StringBuffer("SELECT count(*)  " +
                "  FROM (SELECT a.training_period_no  " +
                "          FROM bs_training_period a, bs_training_arrange b  " +
                "         where a.training_period_no = b.training_period_no  " );
        if(StringUtils.isNotBlank(params.get("trainingPeriodNo"))){
            countSql.append("                   and a.training_period_no = '"+params.get("trainingPeriodNo")+"'  " );
        }
        if(StringUtils.isNotBlank(params.get("startDate"))){
            countSql.append("                   and a.start_date >= to_date('"+params.get("startDate")+"','yyyy-mm-dd')  " );
        }
        if(StringUtils.isNotBlank(params.get("endDate"))){
            countSql.append("                   and a.end_date <= to_date('"+params.get("endDate")+"','yyyy-mm-dd')  " );
        }
        countSql.append(       "         group by a.training_period_no) AS training_period_no");

        Integer count = Integer.valueOf(this.bsTrainingArrangeDao.createSQLQuery(countSql.toString()).uniqueResult().toString());
        //返回值
        Map<String,Object> result=new HashMap<>();
        result.put("rows",list);
        result.put("total",count);
        return result;
    }

    /**
     * 培訓人員匯總表明細
     * @param trainingPeriodNo
     */
    public List<Map<String,String>> trainingDetailReport(String trainingPeriodNo) {
        String sql="SELECT a.arrange_date arrange_date,  " +
                "       max(a.arrange_num) arrange_num,  " +
                "       sum(case when c.abnormal_state = '1' then 1 else 0 end) TRANIEE_Y_NUM  " +
                "  FROM bs_training_arrange     a,  " +
                "       bs_training_arrange_per b,  " +
                "       bs_abnormal_personnel   c  " +
                " where a.id = b.bsta_id  " +
                "   and b.emp_no = c.emp_no  " +
                "   and a.arrange_date = c.shift_date  " +
                "   and a.is_check = 'Y'  " +
                "   and a.training_period_no = '"+trainingPeriodNo+"'  " +
                " group by a.arrange_date  " +
                " order by a.arrange_date  ";

        return this.bsTrainingArrangeDao.createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }
}
