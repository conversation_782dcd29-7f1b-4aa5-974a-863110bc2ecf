package com.foxconn.ipebg.gen.entity;

import cn.hutool.core.bean.BeanUtil;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 流程信息配置
 * <AUTHOR>
 *
 */
public class WfConfig implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 流程編碼
	 */
	private String workFlowId;
	/**
	 * 流程名稱
	 */
	private String workFlowName;
	/**
	 * 表單處理方法
	 */
	private String action;
	/**
	 * 填單人修改方法
	 */
	private String modAction;
	/**
	 * 臨時表單修改
	 */
	private String saveAction;
	/**
	 * 對應流程所有節點
	 */
	private List<WfNodeInfo> nodeList = new ArrayList<WfNodeInfo>();
	/**
	 * 流程級參數配置
	 */
	private List<WfConfigParam> wfParams = new ArrayList<WfConfigParam>();
	/**
	 * 表單詳情展示
	 */
	private String detailAction;
	private String dynfield01;
	private String dynfield02;
	private String dynfield03;

	public WfNodeInfo getNodeByName(String taskName){
		if(StringUtils.isEmpty(taskName)|| StringUtils.isBlank(taskName)){
			return null;
		}
		if(nodeList==null||nodeList.size()==0){
			return null;
		}
		for(WfNodeInfo n:nodeList){
			if(taskName.equals(n.getNodeName())){
				return n;
			}
		}
		return null;
	}
	public String getWorkFlowId() {
		return workFlowId;
	}
	public void setWorkFlowId(String workFlowId) {
		this.workFlowId = workFlowId;
	}
	public String getWorkFlowName() {
		return workFlowName;
	}
	public void setWorkFlowName(String workFlowName) {
		this.workFlowName = workFlowName;
	}
	public List<WfNodeInfo> getNodeList() {
		return nodeList;
	}
	public void setNodeList(List<WfNodeInfo> nodeList) {
		this.nodeList = nodeList;
	}
	public List<WfConfigParam> getWfParams() {
		return wfParams;
	}
	public void setWfParams(List<WfConfigParam> wfParams) {
		this.wfParams = wfParams;
	}
	public String getAction() {
		return action;
	}
	public void setAction(String action) {
		this.action = action;
	}
	public String getModAction() {
		return modAction;
	}
	public void setModAction(String modAction) {
		this.modAction = modAction;
	}
	public String getSaveAction() {
		return saveAction;
	}
	public void setSaveAction(String saveAction) {
		this.saveAction = saveAction;
	}
	public String getDetailAction() {
		return detailAction;
	}
	public void setDetailAction(String detailAction) {
		this.detailAction = detailAction;
	}
	public String getDynfield01() {
		return dynfield01;
	}
	public void setDynfield01(String dynfield01) {
		this.dynfield01 = dynfield01;
	}
	public String getDynfield02() {
		return dynfield02;
	}
	public void setDynfield02(String dynfield02) {
		this.dynfield02 = dynfield02;
	}
	public String getDynfield03() {
		return dynfield03;
	}
	public void setDynfield03(String dynfield03) {
		this.dynfield03 = dynfield03;
	}
}
