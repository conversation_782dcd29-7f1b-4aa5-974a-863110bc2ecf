<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>培訓人員基本資料表</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>

<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
	<form id="mainform" action="${ctx}/bstrainee/${action}" method="post">
		<table class="formTable">
			<tr>
				<!-- <td>id：</td> -->
				<td><input id="ids" name="ids" type="hidden"
					value="${bsTrainee.id }" /> <%-- <input id="id" name="id" class="easyui-validatebox" data-options="width: 150" value="${perInfo.id }" /> --%>
				</td>
			</tr>
			<tr>
				<td>工號：</td>
				<td><input id="empNo" name="empNo" class="easyui-validatebox"
					data-options="width: 150" 	<c:if test="${bsTrainee.id !=null }">  readonly </c:if> value="${bsTrainee.empNo } " /></td>
			</tr>
			<tr>
				<td>姓名：</td>
				<td><input id="empName" name="empName"
					class="easyui-validatebox" data-options="width: 150"
					value="${bsTrainee.empName }" /></td>
			</tr>
			<%-- <tr>
				<td>性別：</td>
				<td><input id="status" name="status" class="easyui-validatebox"
					data-options="width: 150" value="${bsTrainee.status }" /></td>
			</tr> --%>
				<tr>
				<td>性別：</td>
				<td><select id="status" class="easyui-combobox" name="status"
					style="width:160px;">
						<option value="0">女</option>
						<option value="1"
							<c:if test="${bsTrainee.status=='1'}"> selected="ture" </c:if>>男</option>

				</select></td>
				
			</tr>
			<tr>
				<td>身份證號：</td>
				<td><input id="psnId" name="psnId" class="easyui-validatebox"
					data-options="width: 150,validType:'idcard'" value="${bsTrainee.psnId }" /></td>
			</tr>
		<%-- 	<tr>
				<td>硬卡號：</td>
				<td><input id="cardId" name="cardId" class="easyui-validatebox"
					data-options="width: 150" value="${bsTrainee.cardId }" /></td>
			</tr> --%>
			<tr>
				<td>保安公司：</td>
				<td><input id="company" name="company"
					class="easyui-combobox"
					data-options="width: 150, editable: false, valueField:'value', textField:'label', url: '${ctx}/system/user/userSecurityCom'"
					value="${bsTrainee.company }" /></td>
			</tr>
			
			<tr>
				<td>創建人：</td>
				<td><input id="createBy" name="createBy"  readonly
					class="easyui-validatebox" data-options="width: 150"
					value="${bsTrainee.createBy }" /></td>
			</tr>
			<tr>
				<td>創建時間：</td>
				<td><input id="createDate" name="createDate"
					class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" disabled="disabled"
					data-options="width: 160" 
					value="<fmt:formatDate value="${bsTrainee.createDate}"  pattern="yyyy-MM-dd HH:mm:ss"/>" /></td>
			</tr>
			<tr>
				<td>更新人：</td>
				<td><input id="updateBy" name="updateBy"
					class="easyui-validatebox" data-options="width: 150" readonly
					value="${bsTrainee.updateBy }" /></td>
			</tr>
			<tr>
				<td>更新時間：</td>
				<td><input id="updateDate" name="updateDate"
					class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
					data-options="width: 160" disabled="disabled"
					value="<fmt:formatDate value="${bsTrainee.updateDate}" pattern="yyyy-MM-dd HH:mm:ss"/>" /></td>
			</tr>
	<%-- 		<tr>
				<td>刪除標示：</td>
				<td><input id="delFlag" name="delFlag"
					class="easyui-validatebox" data-options="width: 150"
					value="${bsTrainee.delFlag }" /></td>
			</tr> --%>
		</table>
	</form>
	
	<script src='${ctx}/static/js/basics/bstrainee.js?"+Math.random()"'></script>
	<script type="text/javascript">
   //提交表单
$('#mainform').form({
    onSubmit: function(){
        var isValid = $(this).form('validate');
        return isValid;	// 返回false终止表单提交
    },
    success:function(data){ 
        successTip(data,dg,d);
    }
});
</script>
</body>
</html>