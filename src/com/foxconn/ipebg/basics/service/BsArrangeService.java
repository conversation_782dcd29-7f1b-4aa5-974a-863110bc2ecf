package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.entity.BsArrangeEntity;
import com.foxconn.ipebg.basics.entity.BsTrainingPeriodEntity;
import com.foxconn.ipebg.basics.dao.BsArrangeDao;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.Dict;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WorkFlowEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.system.service.DictService;

import javax.management.Query;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 排班基本資料表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-03-07 09:57:20
 */
@Service
@Transactional(readOnly = true)
public class BsArrangeService extends
		BaseBusinessService<BsArrangeEntity, String> {
	@Autowired
	private BsArrangeDao bsArrangeDao;

	@Autowired
	private UserServiceUtil serviceUtil;
	@Autowired
	private DictService dictService;

	@Override
	public HibernateDao<BsArrangeEntity, String> getEntityDao() {
		return bsArrangeDao;
	}

	public BsArrangeEntity findBySerialno(String serialno) {
		return this.bsArrangeDao.findUniqueBy("serialno", serialno);
	}

	// 當天是否已經排班 abc不能同排
	public boolean isTogetherABC(BsArrangeEntity bsArrange) {
		boolean result = false;
		List<BsArrangeEntity> list = bsArrangeDao
				.find("from BsArrangeEntity t where t.shiftDate=?0 and t.empNo=?1 and id !=?2",
						bsArrange.getShiftDate(),
						bsArrange.getEmpNo(),
						bsArrange.getId() == null ? "888888" : bsArrange
								.getId());
		if (list.size() > 0) {
			result = true;
		} else {
			result = false;
		}
		return result;
	}

	// 1.同一人，不能白晚班連排
	public boolean isContinuous(BsArrangeEntity bsArrange) {

		boolean result = false;

		if (bsArrange.getShiftNo().equals("A")) {
			System.out.println(bsArrange.getShiftDate());
			Date date = new Date(bsArrange.getShiftDate().getTime() - 24 * 60
					* 60 * 1000);
			System.out.println(date);
			List<BsArrangeEntity> list = bsArrangeDao
					.find("from BsArrangeEntity t where t.shiftDate=?0 and t.empNo=?1 and id !=?2 and t.shiftNo='B'",
							date,
							bsArrange.getEmpNo(),
							bsArrange.getId() == null ? "888888" : bsArrange
									.getId());
			if (list.size() > 0) {
				result = true;
			} else {
				result = false;
			}
		}
		return result;
	}

	// 1.同一人，必須六休息一
	public boolean isContinuousSix(BsArrangeEntity bsArrange) {

		boolean result = false;

		if (!bsArrange.getShiftNo().equals("C")) {
			System.out.println(bsArrange.getShiftDate());
			Date minDate = new Date(bsArrange.getShiftDate().getTime() - 7 * 24
					* 60 * 60 * 1000);
			System.out.println(minDate);
			List<BsArrangeEntity> list = bsArrangeDao
					.find("from BsArrangeEntity t where t.shiftDate>?0 and  t.shiftDate<?3 and t.empNo=?1 and id !=?2 and t.shiftNo!='C'",
							minDate,
							bsArrange.getEmpNo(),
							bsArrange.getId() == null ? "888888" : bsArrange
									.getId(), bsArrange.getShiftDate());
			if (list.size() >= 6) {
				result = true;
			} else {
				result = false;
			}
		}
		return result;
	}

	// 培訓崗是否排班超過10天
	public boolean verifyP10(BsArrangeEntity bsArrange) {
		boolean result = false;
		List<BsArrangeEntity> list = bsArrangeDao
				.find("from BsArrangeEntity t where t.shiftNo=?0 and t.empNo=?1 and id !=?2",
						"P",
						bsArrange.getEmpNo(),
						bsArrange.getId() == null ? "888888" : bsArrange
								.getId());
		if (list.size() > 9) {
			result = true;
		} else {
			result = false;
		}
		return result;
	}

	public BsArrangeEntity getEmpInfo(BsArrangeEntity bsArrange) {
//		List<BsArrangeEntity> list = bsArrangeDao.find(
//				"from BsArrangeEntity t where t.shiftDate=?0 and t.empNo=?1 ",
//				bsArrange.getShiftDate(), bsArrange.getEmpNo());
		List<PropertyFilter> filters = new ArrayList<>();
		filters.add(new PropertyFilter("EQS_empNo", bsArrange.getEmpNo()));
		filters.add(new PropertyFilter("EQD_shiftDate", bsArrange.getShiftDate()));
		List<BsArrangeEntity> list = search(filters);
		if (list.size() == 1) {
			return list.get(0);
		} else {
			return null;
		}
	}
	
	public BsArrangeEntity isExistEmpInfo(String empNo, String shiftNo,Date shiftDate) {
		List<PropertyFilter> filters = new ArrayList<>();
		filters.add(new PropertyFilter("EQS_empNo", empNo));
		filters.add(new PropertyFilter("EQS_shiftNo", shiftNo));
		filters.add(new PropertyFilter("EQD_shiftDate", shiftDate));
		List<BsArrangeEntity> list = search(filters);
		if (list.size() == 1) {
			return list.get(0);
		} else {
			return null;
		}
	}

}
