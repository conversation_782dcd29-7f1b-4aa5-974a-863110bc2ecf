package com.foxconn.ipebg.basics.controller;

import com.alibaba.fastjson.JSON;
import com.foxconn.ipebg.basics.entity.BsTrainingCostEntity;
import com.foxconn.ipebg.basics.entity.BsTrainingDayCostEntity;
import com.foxconn.ipebg.basics.service.BsTrainingArrangeService;
import com.foxconn.ipebg.basics.service.BsTrainingCostService;
import com.foxconn.ipebg.basics.service.BsTrainingDayCostService;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.system.entity.User;
import com.foxconn.ipebg.system.utils.UserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 培訓期費用結帳明細表
 * 
 * <AUTHOR>
 * @date 2022-08-24
 */
@Controller
@RequestMapping("bstrainingdaycost")
public class BsTrainingDayCostController extends BaseController {

    @Autowired
    private BsTrainingDayCostService bsTrainingDayCostService;
    @Autowired
    private BsTrainingArrangeService bsTrainingArrangeService;
   
    /**
      * 方法描述: 列表信息
      * <AUTHOR>
      * @date 2022-08-24
      **/
    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:bstrainingcost:list")
    public String list( Model model) {
        model.addAttribute("periodList", JSON.toJSONString(bsTrainingArrangeService.listAllTrainingPeriod()));
        //查询列表数据
        return "basics/bstrainingdaycost/list";
    }

    /**
     * 正式崗報表的培訓費用明細
     * <AUTHOR>
     * @date 2022-08-24
     **/
    @RequestMapping(value="costDetail",method = RequestMethod.GET)
    //@RequiresPermissions("basics:bstrainingcost:list")
    public String costDetail(String startDate, String endDate,String postRecno, Model model) {
        model.addAttribute("periodList", JSON.toJSONString(bsTrainingArrangeService.listAllTrainingPeriod()));
//        model.addAttribute("workMonth", workMonth);
        model.addAttribute("postRecno", postRecno);
        model.addAttribute("startDate", startDate);
        model.addAttribute("endDate", endDate);
        //查询列表数据
        return "basics/bstrainingdaycost/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * <AUTHOR>
      * @date 2022-08-24
      **/
    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:bstrainingcost:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<BsTrainingDayCostEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        User user = UserUtil.getCurrentUser();
        if (user != null) {
            // 按保安公司查询
            PropertyFilter filter = new PropertyFilter("INS_securityCom", user.getSecurityComs());
            filters.add(filter);
        }
        page = bsTrainingDayCostService.search(page, filters);
        return getEasyUIData(page);
    }

    /**
      * 导出excel
      * <AUTHOR>
      * @date 2022-08-24
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {}
}
