package com.foxconn.ipebg.system.web;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.foxconn.ipebg.common.exception.ExcelException;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.system.service.LogService;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.system.entity.Log;

/**
 * 日志controller
 * <AUTHOR>
 * @date 2015年1月14日
 */
@Controller
@RequestMapping("system/log")
public class LogController extends BaseController{

	@Autowired
	private LogService logService;
	
	/**
	 * 默认页面
	 * @return
	 */
	@RequestMapping(method = RequestMethod.GET)
	public String list(){
		return "system/logList";
	}
	
	/**
	 * 获取日志json
	 */
	@RequiresPermissions("sys:log:view")
	@RequestMapping("json")
	@ResponseBody
	public Map<String, Object> list(HttpServletRequest request) {
		Page<Log> logPage=getPage(request);
		List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
		if(filters.size()>0){
			logPage.setPageNo(1);
		}
		logPage = logService.search(logPage, filters);
		//构造easyui表格数据
		Map<String, Object> map=new HashMap<String, Object>();
		map.put("rows", logPage.getResult());
		map.put("total", logPage.getTotalCount());
		return map;
	}
	
	/**
	 * 删除日志
	 * @param id
	 */
	@RequiresPermissions("sys:log:delete")
	@RequestMapping(value = "delete/{id}")
	@ResponseBody
	public String delete(@PathVariable("id") Integer id) {
		logService.delete(id);
		return "success";
	}
	
	/**
	 * 批量删除日志
	 * @param idList
	 */
	@RequestMapping(value = "delete", method = RequestMethod.POST)
	@ResponseBody
	public String delete(@RequestBody List<Integer> idList) {
		logService.deleteLog(idList);
		return "success";
	}

	/**
	 * 导出excel
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@RequestMapping("exportExcel")
	public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception{
		try {
			List<Log> list = logService.getAll();//获取数据
			// excel表格的表头，map
			LinkedHashMap<String, String> fieldMap = new LinkedHashMap<String, String>();
			fieldMap.put("operationCode","操作编码");
			fieldMap.put("description","详细描述");
			fieldMap.put("requestParam","請求參數");
			fieldMap.put("executeTime","执行时间(mm)");
			fieldMap.put("os","操作系统");
			fieldMap.put("browser","浏览器");
			fieldMap.put("ip","IP");
			fieldMap.put("mac","MAC");
			fieldMap.put("creater","操作者");
			fieldMap.put("createDate","操作时间");
			// excel的sheetName
			String sheetName = "日誌";
			// excel要导出的数据
			// 导出
			if (list == null || list.size() == 0) {
				System.out.println("日誌为空");
			}else {
				//将list集合转化为excel
				ExcelUtil.listToExcel(list, fieldMap, sheetName, response);
				System.out.println("导出成功~~~~");
			}
		} catch (ExcelException e) {
			e.printStackTrace();
		}
	}
}
