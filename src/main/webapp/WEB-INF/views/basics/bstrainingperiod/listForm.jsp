<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>培訓期基本資料</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>

<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
	<form id="mainform" action="${ctx}/bstrainingperiod/${action}"
		method="post">
		<table class="formTable">
			<tr>
				<td><input id="ids" name="ids" type="hidden"
					value="${bsTrainingPeriod.id }" /></td>
			</tr>
			<c:if test="${bsTrainingPeriod.id !=null }">
			<tr>
				<td>培訓期編碼：</td>
				<td><input id="trainingPeriodNo" name="trainingPeriodNo"
					class="easyui-validatebox" data-options="width: 150"
					value="${bsTrainingPeriod.trainingPeriodNo }" readonly /></td>
			</tr>
			<tr>
				<td>培訓期名稱：</td>
				<td><input id="trainingPeriodName" name="trainingPeriodName"
					class="easyui-validatebox" data-options="width: 150"
					value="${bsTrainingPeriod.trainingPeriodName }" readonly /></td>
			</tr>
			</c:if>
			<tr>
				<td>培訓開始時間：</td>
				<td><input id="startDate" name="startDate" class="easyui-my97" minDate="%y-%M-%d"
					datefmt="yyyy-MM-dd" data-options="width: 160, onChange:function() {onPeriodStartDateChanged();}"  <c:if test="${bsTrainingPeriod.id !=null }">disabled</c:if>
					value="<fmt:formatDate value="${bsTrainingPeriod.startDate}"/>" onblur="onPeriodStartDateChanged(this);" />
				</td>
			</tr>
			<tr>
				<td>培訓結束時間：</td>
				<td><input id="endDate" name="endDate" class="easyui-my97"  minDate="%y-%M-%d"
					datefmt="yyyy-MM-dd" data-options="width: 160"  <c:if test="${endStatus =='N' }">disabled</c:if>
					value="<fmt:formatDate value="${bsTrainingPeriod.endDate}"/>" />
					
					<input id="endDateOld" name="endDateOld"  type="hidden"
					data-options="width: 160"  <c:if test="${endStatus =='N' }">disabled</c:if>
					value="<fmt:formatDate value="${bsTrainingPeriod.endDate}"/>" /></td>
			</tr>
			<tr>
				<td>上午：</td>
				<%-- <td><input id="startTime" name="startTime"
					class="easyui-validatebox" data-options="width: 150"
					value="${bsTrainingPeriod.startTime }" /></td> --%>
					<td><input id="startTime" name="startTime" class="easyui-my97"
					datefmt="HH:mm" data-options="width: 75" <c:if test="${endStatus =='N' }">disabled</c:if> 
					 <c:if test="${bsTrainingPeriod.id !=null }">value="${bsTrainingPeriod.startTime}" </c:if>
					<c:if test="${bsTrainingPeriod.id ==null }">value="08:00" </c:if> />~
					<input id="endTime" name="endTime" class="easyui-my97"
					datefmt="HH:mm" data-options="width: 75" <c:if test="${endStatus =='N' }">disabled</c:if>
					 <c:if test="${bsTrainingPeriod.id !=null }">value="${bsTrainingPeriod.endTime}" </c:if>
					<c:if test="${bsTrainingPeriod.id ==null }">value="12:00" </c:if>
					 />
				</td>
			</tr>
			<tr>
				<td>下午：</td>
				<%-- <td><input id="endTime" name="endTime"
					class="easyui-validatebox" data-options="width: 150"
					value="${bsTrainingPeriod.endTime }" /></td> --%>
					<td><input id="startTime1" name="startTime1" class="easyui-my97"
					datefmt="HH:mm" data-options="width: 75" <c:if test="${endStatus =='N' }">disabled</c:if>
					<c:if test="${bsTrainingPeriod.id !=null }">value="${bsTrainingPeriod.startTime1}" </c:if>
					<c:if test="${bsTrainingPeriod.id ==null }">value="14:00" </c:if>
					/>~
					<input id="endTime1" name="endTime1" class="easyui-my97"
					datefmt="HH:mm" data-options="width: 75" <c:if test="${endStatus =='N' }">disabled</c:if>
					 <c:if test="${bsTrainingPeriod.id !=null }">value="${bsTrainingPeriod.endTime1}" </c:if>
					<c:if test="${bsTrainingPeriod.id ==null }">value="18:00" </c:if>
					 />
					</td>
			</tr>
			<tr>
				<td>保安公司：</td>
				<td>
					<input id="securityCom" name="securityCom" class="easyui-combobox"
						   data-options="panelHeight:'auto',valueField:'value', textField:'label',
	   						url: '${ctx}/system/user/userSecurityCom',width: 160,value:'${ bsTrainingPeriod.securityCom }'"
						   style="width:150px;" />
				</td>
			</tr>
			<tr style="display: none">
				<td>
					<input id="createBy" name="createBy" readonly
					class="easyui-validatebox" data-options="width: 150"
					value="${bsTrainingPeriod.createBy }" />
					<input id="createDate" name="createDate"
						   class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
						   data-options="width: 160" disabled="disabled"
						   value="<fmt:formatDate value="${bsTrainingPeriod.createDate}"  pattern="yyyy-MM-dd HH:mm:ss"/>" />
					<input id="updateBy" name="updateBy" readonly
						   class="easyui-validatebox" data-options="width: 150"
						   value="${bsTrainingPeriod.updateBy }" />
					<input id="updateDate" name="updateDate"
						   class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
						   data-options="width: 160" disabled="disabled"
						   value="<fmt:formatDate value="${bsTrainingPeriod.updateDate}"  pattern="yyyy-MM-dd HH:mm:ss"/>" />
				</td>
			</tr>
			<%-- 	<tr>
				<td>刪除標示：</td>
				<td><input id="delFlag" name="delFlag"
					class="easyui-validatebox" data-options="width: 150"
					value="${bsTrainingPeriod.delFlag }" /></td>
			</tr> --%>
		</table>
	</form>
	
	<script
		src='${ctx}/static/js/basics/bstrainingperiod.js?"+Math.random()"'></script>
	<script type="text/javascript">
   //提交表单
$('#mainform').form({
    onSubmit: function(){
        var isValid = $(this).form('validate');
        return isValid;	// 返回false终止表单提交
    },
    success:function(data){ 
        successTip(data,dg,d);
    }
});
</script>
</body>
</html>