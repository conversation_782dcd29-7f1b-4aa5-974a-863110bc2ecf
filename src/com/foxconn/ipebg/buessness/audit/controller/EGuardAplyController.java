package com.foxconn.ipebg.buessness.audit.controller;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import com.foxconn.ipebg.audit.entity.EBsCodeEntity;
import com.foxconn.ipebg.audit.service.EBsCodeService;
import com.foxconn.ipebg.basics.entity.BsPostEntity;
import com.foxconn.ipebg.basics.service.BsDptService;
import com.foxconn.ipebg.basics.service.BsPostService;
import com.foxconn.ipebg.buessness.audit.entity.*;
import com.foxconn.ipebg.common.exception.ExcelException;
import com.foxconn.ipebg.common.utils.Global;
import com.foxconn.ipebg.common.utils.Result;
import com.foxconn.ipebg.system.entity.Dict;
import com.foxconn.ipebg.system.entity.User;
import com.foxconn.ipebg.system.utils.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.ConvertUtils;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.basics.entity.ESignUserLineEntity;
import com.foxconn.ipebg.basics.service.ESignUserLineService;
import com.foxconn.ipebg.audit.entity.EFormSignEntity;

import com.foxconn.ipebg.audit.service.EFormSignService;
import com.foxconn.ipebg.buessness.workflow.entity.Gtasks;
import com.foxconn.ipebg.buessness.audit.entity.EGuardAplyEntity;
import com.foxconn.ipebg.buessness.audit.service.EGuardAplyService;
import com.foxconn.ipebg.buessness.audit.service.EGuardItemAplyService;

import com.foxconn.ipebg.system.service.DictService;
import com.foxconn.ipebg.system.service.TPubFileobjectService;
import com.foxconn.ipebg.system.service.TPubMailrecordService;
import com.foxconn.ipebg.system.service.UserRoleService;

/**
 * 警衛服務申請主表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-02-23 13:46:08
 */
@Controller
@RequestMapping("eguardaply")
public class EGuardAplyController extends BaseController {

	@Autowired
	private EGuardAplyService eGuardAplyService;
	@Autowired
	private BsDptService bsDptService;
	@Autowired
	private EFormSignService eFormSignService;
	@Autowired
	private EGuardItemAplyService eGuardItemAplyService;
	@Autowired
	private TPubFileobjectService tPubFileobjectService;
	@Autowired
	private EBsCodeService eBsCodeService;
	@Autowired
	private TPubMailrecordService tPubMailrecordService;
	@Autowired
    private ESignUserLineService eSignUserLineService;
	@Autowired
    private UserRoleService userRoleService;
	@Autowired
	private BsPostService postService;
	@Autowired
	private DictService dictService;

	public static String workFlowId = "";

	private static final String filePath = "/static/resources/download";
	private static final String TPLFILEPATH = "/static/resources/templet";
	private static final String BATCHIMPORTTPL = "postModify.xlsx";

	/**
	 * 方法描述: 列表信息
	 * 
	 * @Author: ********
	 * @CreateDate: 2018-12-27 10:29:50
	 * @Return
	 **/

	@RequestMapping(method = RequestMethod.GET)
	// @RequiresPermissions("audit:eguardaply:list")
	public String list() {
		// 查询列表数据
		return "buessness/audit/eguardaply/list";
	}

	/**
	 * 方法描述: 分頁查詢信息
	 * 
	 * @Author: ********
	 * @CreateDate: 2018-12-27 10:29:50
	 * @Return
	 **/
	@RequestMapping(value = "list", method = RequestMethod.GET)
	// @RequiresPermissions("audit:eguardaply:list")
	@ResponseBody
	public Map<String, Object> infoList(HttpServletRequest request) {
		Page<EGuardAplyEntity> page = getPage(request);
		List<PropertyFilter> filters = PropertyFilter
				.buildFromHttpRequest(request);
		// 查詢權限begin
		List<Integer> roleList =userRoleService.getRoleIdList(UserUtil.getCurrentUser().getId()) ;
		if (roleList == null || roleList.size() == 0 || !roleList.contains(281)) {
			// 只能查到自己申請或者審核的單字
			List<String> serialnoList = new ArrayList<String>();
			// 查自己申請的單子
			List<PropertyFilter> selfApplyFilters = new ArrayList<PropertyFilter>();
			selfApplyFilters.add(new PropertyFilter("EQS_makerno", UserUtil.getCurrentUser().getLoginName()));
			List<EGuardAplyEntity> selfApplyList = eGuardAplyService.search(selfApplyFilters);
			for (EGuardAplyEntity aply : selfApplyList) {
				serialnoList.add(aply.getSerialno());
			}
			// 查自己簽核的單子
			List<PropertyFilter> selfSignFilters = new ArrayList<PropertyFilter>();
			selfSignFilters.add(new PropertyFilter("EQS_eserId", UserUtil.getCurrentUser().getLoginName()));
			List<EFormSignEntity> signFormList = eFormSignService.search(selfSignFilters);
			for (EFormSignEntity sign : signFormList) {
				serialnoList.add(sign.getApplyId());
			}
			if (serialnoList.size() == 0) {
				// 沒有審過單子，也沒提過單子
				serialnoList.add("");
			}
			filters.add(new PropertyFilter("INS_serialno", serialnoList));
		}
		// 審核狀態
		String applyStat = request.getParameter("applyStat");
		if (StringUtils.isNotEmpty(applyStat)) {
			if (applyStat.equals("E") || applyStat.equals("N")) {
				filters.add(new PropertyFilter("EQS_applyStat", applyStat));
			} else {
				// 審核中
				filters.add(new PropertyFilter("NIS_applyStat", Arrays.asList("N", "E")));
			}
		};
//		page.setOrderBy("createtime")
//		page.setOrder("desc");
		page = eGuardAplyService.search(page, filters);

		ConvertUtils.convertPropertyToDictLabel(page, "legalPerson", "guard_legalperson");
		ConvertUtils.convertPropertyToDictLabel(page, "requirememtType", "guard_aply_type");
		ConvertUtils.convertApplyState(page.getResult(), "applyStat");

		return getEasyUIData(page);
	}

	/**
	 * 方法描述: 保存
	 * 
	 * @Author: ********
	 * @CreateDate: 2018-12-27 10:29:50
	 * @Return
	 **/

	@RequestMapping(value = "create/{flag}", method = RequestMethod.POST)
	// @RequiresPermissions("audit:eguardaply:add")
	@ResponseBody
	public String create(@Valid EGuardAplyDto eGuardAplyDto, Model model,
			HttpServletRequest request,
			@RequestParam(value = "ids", required = false) String ids,
			@RequestParam(value = "formType", required = true) String formType,
			@PathVariable("flag") String flag) {
		EGuardAplyEntity eGuardAply = eGuardAplyDto.geteGuardAplyEntity();

		if (ids != null) {
			eGuardAply.setId(ids);
		}

		// 驗證臨時增撤崗時間是否合法
		if (eGuardAply.getRequirememtType().equals("2")) {
			for (EGuardItemAplyEntity item : eGuardAplyDto.getEGuardItemAplyEntity()) {
				if (!eGuardItemAplyService.allowApplyTempModify(item.getRecno(), item.getPostStartDate(), item.getPostEndDate())) {
					return "崗位" + item.getPostName() + "同一時間段已有申請中或已結案的申請";
				}
			}
		}

		eGuardAply.setMakerip(IPUtil.getIpAddress(request));
		eGuardAply.setApplyIp(IPUtil.getIpAddress(request));
		eGuardAply.setMakerno(UserUtil.getCurrentUser().getLoginName());
		eGuardAply.setMakername(UserUtil.getCurrentUser().getName());
		eGuardAply.setMakerfactoryid("IPETY");
		// 删除旧签核线
		eSignUserLineService.deleteByEmpNoAndFormType(UserUtil.getCurrentUser().getLoginName(), formType);
		EFormSignEntity[] nodeList = eGuardAplyDto.geteFormSignEntity();
		for (int i = 0; i < nodeList.length; i++) {
			EFormSignEntity node = nodeList[i];
			// 保存申请人在每个节点选择的签核者信息
			ESignUserLineEntity eSignUserLineEntity=new ESignUserLineEntity();
			eSignUserLineEntity.setEmpNo(UserUtil.getCurrentUser().getLoginName());
			eSignUserLineEntity.setFormType(formType);
			eSignUserLineEntity.setCodeId(i + 1);
			eSignUserLineEntity.setSignerEmpNo(node.getEserId());
			eSignUserLineEntity.setSignerEmpName(node.getEserNam());
			eSignUserLineEntity.setCreateBy(UserUtil.getCurrentUser().getLoginName());
			eSignUserLineEntity.setCreateDate(new Date());
			eSignUserLineService.save(eSignUserLineEntity);
		}
		// 添加或修改默認簽核線記錄***********************
		String result = eGuardAplyService.startProcess(eGuardAply,
				eGuardAplyDto.getEGuardItemAplyEntity(), eGuardAplyDto.geteFormSignEntity(), formType, flag, CommonUtils.getServerBaseURL(request));
		if (result.equals(Constant.RESULT.CODE_YES.getValue())) {
			return Constant.SUCCESS;
		} else {
			return Constant.FAIL;
		}
	}

	/**
	 * 審核頁面 郵件
	 * 
	 * @Author: ********
	 * @CreateDate: 2018-12-27 10:29:50
	 * @Return
	 */
	@RequestMapping(value = "auditMail/{serialno}", method = RequestMethod.GET)
	public String auditFormMail(@PathVariable("serialno") String serialno,
			Model model, HttpServletRequest request) {
		// Subject subject = SecurityUtils.getSubject();
		//
		// Session session = subject.getSession();
		// UserInfo u =(UserInfo)session.getAttribute("loginuser");

		String applyStat = request.getParameter("applyStat");
		String utoken = request.getParameter("utoken");

		EFormSignEntity currentNode = eFormSignService.currentNode(serialno);
		if (currentNode != null) {
			if (currentNode.getSignOrd().toString().equals(applyStat)
					&& currentNode
							.getSignOrd()
							.toString()
							.equals(tPubMailrecordService
									.findByValidStr(utoken).getOrderstatus())) {

				EGuardAplyEntity eGuardAplyEntity = eGuardAplyService
						.findBySerialno(serialno);
				model.addAttribute("eGuardAply", eGuardAplyEntity);
				// 查詢子表信息
				List<EGuardItemAplyEntity> itemEntity = null;
				itemEntity = eGuardItemAplyService.findItemEntityes(serialno);
				 Gtasks nextTask =new Gtasks();
				 Gtasks previousTask =new Gtasks();
					List<Gtasks> listTask = eFormSignService.getTaskAllMail(eFormSignService
							.currentNode(serialno).getEserId(), "", "");
					for (int j = 0; j < listTask.size(); j++) {
						if (listTask.get(j).getSerialno().equals(serialno)&&(j!=listTask.size()-1) ) {
							nextTask=  listTask.get(j+1);  
						}
						if (listTask.get(j).getSerialno().equals(serialno)&&(j!=0) ) {
							previousTask=listTask.get(j-1);  
						}
					}
					model.addAttribute("nextTask", nextTask);
					model.addAttribute("previousTask", previousTask);
				model.addAttribute("itemEntity", itemEntity);

				model.addAttribute("chargeNodeInfo",
						eFormSignService.getChargeNodeInfo(serialno));
				model.addAttribute("applyStat",
						eFormSignService.currentNode(serialno).getSignOrd());
				// 設置現場勘查和崗位確認的序號
				String formType = bsDptService.getJWFWDetailFormTypeForUser(eGuardAplyEntity.getMakerno());
				List<Integer> siteSurveyOrder = eBsCodeService.getConfigedSignOrder(formType, "JWFW_site_survey");
				List<Integer> positionConfirmOrder = eBsCodeService.getConfigedSignOrder(formType, "JWFW_post_confirm");
				model.addAttribute("siteSurveyOrder", siteSurveyOrder);
				model.addAttribute("positionConfirmOrder", positionConfirmOrder);
				model.addAttribute("formType", formType);

				model.addAttribute("auditType", "auditMail");
				model.addAttribute("file", tPubFileobjectService
						.findByIds(eGuardAplyEntity.getAttachids(), request.getContextPath()));
				return "buessness/audit/eguardaply/listFormAudit";
			} else {
				return "system/login";
			}
		} else {
			return "system/login";
		}
	}

	/**
	 * 審核頁面
	 * 
	 * @Author: ********
	 * @CreateDate: 2018-12-27 10:29:50
	 * @Return
	 */
	@RequestMapping(value = "audit/{serialno}", method = RequestMethod.GET)
	public String auditForm(@PathVariable("serialno") String serialno,
			Model model, HttpServletRequest request) {
		EGuardAplyEntity eGuardAplyEntity = eGuardAplyService
				.findBySerialno(serialno);
		model.addAttribute("eGuardAply", eGuardAplyEntity);
		// 查詢子表信息
		List<EGuardItemAplyEntity> itemEntity = null;
		itemEntity = eGuardItemAplyService.findItemEntityes(serialno);
		 Gtasks nextTask =new Gtasks();
		 Gtasks previousTask =new Gtasks();
		List<Gtasks> listTask = eFormSignService.getTaskAll(eFormSignService
				.currentNode(serialno).getEserId(), "", "");
		for (int j = 0; j < listTask.size(); j++) {
			if (listTask.get(j).getSerialno().equals(serialno)&&(j!=listTask.size()-1) ) {
				nextTask=  listTask.get(j+1);  
			}
			if (listTask.get(j).getSerialno().equals(serialno)&&(j!=0) ) {
				previousTask=listTask.get(j-1);  
			}
			
		}
		model.addAttribute("nextTask", nextTask);
		model.addAttribute("previousTask", previousTask);
//		 for(Gtasks task :listTask)
//		 {
//			 task.getMakerno(); 
//		 }
		model.addAttribute("itemEntity", itemEntity);

		model.addAttribute("chargeNodeInfo",
				eFormSignService.getChargeNodeInfo(serialno));
		model.addAttribute("applyStat", eFormSignService.currentNode(serialno)
				.getSignOrd());
		// 設置現場勘查和崗位確認的序號
		String formType = bsDptService.getJWFWDetailFormTypeForUser(eGuardAplyEntity.getMakerno());
		List<Integer> siteSurveyOrder = eBsCodeService.getConfigedSignOrder(formType, "JWFW_site_survey");
		List<Integer> positionConfirmOrder = eBsCodeService.getConfigedSignOrder(formType, "JWFW_post_confirm");
		model.addAttribute("siteSurveyOrder", siteSurveyOrder);
		model.addAttribute("positionConfirmOrder", positionConfirmOrder);
		model.addAttribute("formType", formType);

		model.addAttribute("file", tPubFileobjectService
				.findByIds(eGuardAplyEntity.getAttachids(), request.getContextPath()));
		return "buessness/audit/eguardaply/listFormAudit";
	}

	/**
	 * 審核保存
	 * 
	 * @Author: ********
	 * @CreateDate: 2018-12-27 10:29:50
	 * @Return
	 */
	@RequestMapping(value = "auditSave/{flag}", method = RequestMethod.POST)
	@ResponseBody
	public String auditInfo(@Valid EGuardAplyDto eGuardAplyDto, Model model,
			HttpServletRequest request,
			@RequestParam(value = "ids", required = false) String ids,
			@PathVariable("flag") String flag) {

		String clientIP = IPUtil.getIpAddress(request);
		String memo = request.getParameter("memo");
		String signOrd = request.getParameter("applyStatRe");
		String formType = request.getParameter("formType");
		String result = eGuardAplyService.auditProcess(
				eGuardAplyDto.geteGuardAplyEntity(),
				eGuardAplyDto.getEGuardItemAplyEntity(), flag, signOrd, clientIP, memo, formType, CommonUtils.getServerBaseURL(request));
		if (result.equals(Constant.RESULT.CODE_YES.getValue())) {
			return Constant.SUCCESS;
		} else if (result.equals(Constant.RESULT.CODE_COMMIT.getValue())) {
			return Constant.FAIL_AUDIT_NODE_PROCESSED;
		} else {
			return Constant.FAIL;
		}
	}

	/**
	 * 詳細查詢頁面
	 * 
	 * @Author: ********
	 * @CreateDate: 2018-12-27 10:29:50
	 * @Return
	 */
	@RequestMapping(value = "view/{serialno}", method = RequestMethod.GET)
	public String viewForm(@PathVariable("serialno") String serialno,
			Model model, HttpServletRequest request) {
		EGuardAplyEntity eGuardAplyEntity = eGuardAplyService
				.findBySerialno(serialno);
		model.addAttribute("eGuardAply", eGuardAplyEntity);
		List<EGuardItemAplyEntity> itemEntity = null;
		itemEntity = eGuardItemAplyService.findItemEntityes(serialno);
		model.addAttribute("itemEntity", itemEntity);

		model.addAttribute("chargeNodeInfo",
				eFormSignService.getChargeNodeInfo(serialno));
		model.addAttribute("file", tPubFileobjectService
				.findByIds(eGuardAplyEntity.getAttachids(), request.getContextPath()));
		return "buessness/audit/eguardaply/listFormView";
	}

	/**
	 * 詳細查詢頁面 郵件
	 * 
	 * @Author: ********
	 * @CreateDate: 2018-12-27 10:29:50
	 * @Return
	 */
	@RequestMapping(value = "viewMail/{serialno}", method = RequestMethod.GET)
	public String viewFormMail(@PathVariable("serialno") String serialno,
			Model model, HttpServletRequest request) {
		EGuardAplyEntity eGuardAplyEntity = eGuardAplyService
				.findBySerialno(serialno);
		model.addAttribute("eGuardAply", eGuardAplyEntity);
		List<EGuardItemAplyEntity> itemEntity = null;
		itemEntity = eGuardItemAplyService.findItemEntityes(serialno);

		model.addAttribute("itemEntity", itemEntity);

		model.addAttribute("chargeNodeInfo",
				eFormSignService.getChargeNodeInfo(serialno));
		model.addAttribute("file", tPubFileobjectService
				.findByIds(eGuardAplyEntity.getAttachids(), request.getContextPath()));
		return "buessness/audit/eguardaply/listFormView";
	}

	/**
	 * 方法描述: 添加跳轉
	 * 
	 * @Author: ********
	 * @CreateDate: 2019-09-27 10:29:50
	 * @Return
	 **/

	// @RequiresPermissions("audit:eguardaply:add")
	@RequestMapping(value = "create", method = RequestMethod.GET)
	public String createForm(Model model, HttpServletRequest request) {
		model.addAttribute("eGuardAply", new EGuardAplyEntity());
		model.addAttribute("action", "create");
		// 獲取用戶所屬事業群，獲取對應事業群
		User currentUser = UserUtil.getCurrentUser();
		// 獲取當前用戶所在事業處
		String remark = "JWFW_DEFAULT";
		String factory = Global.getConfig("factoryID");
		if (factory.equalsIgnoreCase("TY")) {
			// 太原廠區
			String bg = bsDptService.businessGroupByID(currentUser.getDelFlag());
			if (bg != null && bg.length() > 0) {
				if (bg.equalsIgnoreCase("iPEBG")) {
					remark = "JWFW_iPEBG";
				} else if (bg.equalsIgnoreCase("iDPBG")) {
					remark = "JWFW_iDPBG";
				}
			}
		} else if (factory.equalsIgnoreCase("JC")) {
			// 晉城廠區
			String chu = bsDptService.businessDivisionByID(currentUser.getDelFlag());
			if (chu != null && chu.length() > 0) {
				if (chu.equals("iPEBG事業處")) {
					remark = "JWFW_iPEBG";
				} else if (chu.equals("晉城周邊處")) {
					remark = "JWFW_JCZB";
				}
			}
		}
		// 獲取當前登錄用戶已保存的簽核線
		List<ESignUserLineEntity> userLineList = eSignUserLineService.findByEmpNoAndFormType(currentUser.getLoginName(), remark);
		Map<Integer, ESignUserLineEntity> userLineInfo = new HashMap<Integer, ESignUserLineEntity>();
		for (ESignUserLineEntity lineNode: userLineList) {
			userLineInfo.put(lineNode.getCodeId(), lineNode);
		}
		model.addAttribute("eSignUserLine", userLineInfo);
		List<EBsCodeEntity> signNodeList = eBsCodeService.getCodeIdByCodeTyp(remark);
		model.addAttribute("formType", remark);
		model.addAttribute("signNodes", signNodeList);

		Dict dict = null;
		try {
			dict = dictService.get(90);
			model.addAttribute("tips", dict.getRemark());
		} catch (Exception e) {
			model.addAttribute("tips", null);
		}
		return "buessness/audit/eguardaply/listForm";
	}

	/**
	 * 方法描述: 下載批量上傳模板
	 *
	 * @Author: S6114893
	 * @CreateDate: 2022-07-18
	 * @Return
	 **/
	@RequestMapping(value = "downLoad/batchImportTpl", method = RequestMethod.GET)
	@ResponseBody
	public void batchImportTpl(HttpServletRequest request,
							   HttpServletResponse response) {
		String path = request.getSession().getServletContext()
				.getRealPath(TPLFILEPATH);
		try {
			FileDownloadUtil.fileDownload(request, response, path, BATCHIMPORTTPL);// 下載點位信息模板
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	/**
	 * 批量導入
	 *
	 * @param file 文檔
	 * @param isExcel2003 是否2003
	 * @return
	 */
	@RequestMapping(value = "upload", method = RequestMethod.POST)
	@ResponseBody
	public Result<List<BsPostEntity>> upload(MultipartFile file, boolean isExcel2003) {
		Result<List<BsPostEntity>> result = new Result<>();
		if (file.equals("") || file.getSize() <= 0) {
			result.setCode(Result.FAILURE);
			result.setMsg("請上傳文件");
		} else {
			try {
				EGuardItemImportDetail importDetail = batchImport(file.getInputStream(), isExcel2003);
				if (importDetail.getErrorDtoList().size() > 0) {
					// 存在錯誤數據
					result.setCode(-1);
					result.setMsg("導入失敗，有"+importDetail.getErrorDtoList().size()+"筆數據存在問題");
					UserUtil.getSession().removeAttribute(
							eGuardAplyService.EGUARDITEMERRORDTOS);
					UserUtil.getSession().setAttribute(
							eGuardAplyService.EGUARDITEMERRORDTOS,
							importDetail.getErrorDtoList());
				} else {
					result.setCode(Result.SUCCESS);
					result.setData(importDetail.getItemList());
				}
			} catch (IOException e) {
				e.printStackTrace();
				result.setCode(Result.FAILURE);
				result.setMsg("讀取文件流失敗");
			} catch (ExcelException e) {
				e.printStackTrace();
				result.setCode(Result.FAILURE);
				result.setMsg(e.getMessage());
			}
		}
		return result;
	}

	// *******************************導入相關邏輯**********
	/**
	 * 批量導入
	 *
	 * @param in
	 *            excel文件輸入流
	 * @param isExcel2003
	 *            是否excel2003
	 * @return 是否成功
	 */
	public EGuardItemImportDetail batchImport(InputStream in, boolean isExcel2003) throws ExcelException {
		EGuardItemImportDetail result = new EGuardItemImportDetail();
		result.setItemList(new ArrayList<BsPostEntity>());
		result.setErrorDtoList(new ArrayList<EGuardItemErrorDto>());

		ImportExcelUtil poi = new ImportExcelUtil();
		int success = 0, failed = 0;
		// 讀取上傳的excel file
		List<List<String>> list = poi.read(in, isExcel2003);
		// 判断需要的字段在Excel中是否都存在
		boolean isExist = true;
		List<String> fieldList = Arrays.asList("崗位編碼","需求開始時間","需求結束時間");
		Map<String, Integer> fieldIndexMap = new HashMap();
		List<String> excelFieldList = list.get(0);
		// 第一列是序號不需要
		for (int i = 1; i < excelFieldList.size(); i++) {
			if (!fieldList.contains(excelFieldList.get(i))) {
				isExist = false;
				break;
			}
			fieldIndexMap.put(excelFieldList.get(i), i);
		}
		// 如果有列名不存在，则抛出异常，提示错误
		if (!isExist) {
			throw new ExcelException("有列名不存在，請對照模板");
		}
		// 查詢當前登錄人能看到的崗位列表
		List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
		List<Integer> roleList = userRoleService.getRoleIdList(UserUtil.getCurrentUser().getId());
		boolean hasQryAllPermission = roleList != null && roleList.contains(281);
		if (!hasQryAllPermission) {
			// 只能查询自己部门申请的岗位
			User user = UserUtil.getCurrentUser();
			if (user != null) {
				PropertyFilter filter = new PropertyFilter("EQS_dptId", user.getDelFlag());
				filters.add(filter);
			}
		}
		List<BsPostEntity> allPostList = postService.search(filters);
		SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd");
		for (int i = 1; i < list.size(); i++) {
			String recno = list.get(i).get(1).trim();
			String startTime = list.get(i).get(2);
			String endTime = list.get(i).get(3);
			// 查找對應的崗位
			BsPostEntity post = null;
			for (BsPostEntity postVar : allPostList) {
				if (postVar.getRecno().equals(recno)) {
					post = postVar;
					break;
				}
			}
			if (post == null) {
				// 沒有找到對應的崗位
				EGuardItemErrorDto item = new EGuardItemErrorDto();
				item.setRecno(recno);
				item.setPostStartDate(startTime);
				item.setPostEndDate(endTime);
				item.setErrorLog("崗位編碼錯誤或無該崗位權限");
				result.getErrorDtoList().add(item);
				continue;
			}
			Date startDate = null, endDate = null;
			try {
				startDate = dateFormatter.parse(startTime);
				endDate = dateFormatter.parse(endTime);
			} catch (ParseException e) {
				// 日期格式錯誤
				EGuardItemErrorDto item = new EGuardItemErrorDto();
				item.setRecno(recno);
				item.setPostStartDate(startTime);
				item.setPostEndDate(endTime);
				if (startDate == null) item.setErrorLog("需求開始時間格式錯誤");
				else item.setErrorLog("需求結束時間格式錯誤");
				result.getErrorDtoList().add(item);
				continue;
			}
			// 判斷同一崗位在該時間段內是否已有申請中/已結案的單子
			if (!eGuardItemAplyService.allowApplyTempModify(post.getRecno(), startDate, endDate)) {
				EGuardItemErrorDto item = new EGuardItemErrorDto();
				item.setRecno(recno);
				item.setPostStartDate(startTime);
				item.setPostEndDate(endTime);
				item.setErrorLog("該崗位在同一時間段已有申請中或已結案的申請單");
				result.getErrorDtoList().add(item);
				continue;
			}
			post.setPostStartDate(startDate);
			post.setPostEndDate(endDate);
			result.getItemList().add(post);
		}
		return result;
	}

	/**
	 * 方法描述: 下載批量上傳異常信息
	 *
	 * @Author: S6114893
	 * @CreateDate: 2022-07-19
	 * @Return
	 **/
	@RequestMapping(value = "downLoad/errorExcel", method = RequestMethod.GET)
	@ResponseBody
	public void errorExcelDownload(HttpServletRequest request,
								   HttpServletResponse response) throws IOException {

		List<EGuardItemErrorDto> errorDtos = (List<EGuardItemErrorDto>) request
				.getSession().getAttribute(
						eGuardAplyService.EGUARDITEMERRORDTOS);
		if (errorDtos != null) {
			// 導出excel2007
			LinkedHashMap<String, String> map = new LinkedHashMap<>();
			map.put("recno", "崗位編碼");
			map.put("postStartDate", "需求開始時間");
			map.put("postEndDate", "需求結束時間");
			map.put("errorLog", "導入失敗原因");

			String name = this.getCurUser().getLoginName() + "In";
			String tplPath = request.getSession().getServletContext()
					.getRealPath(filePath);
			File file = Excel2007Utils.createFile(errorDtos, map, tplPath,
					name, null);// 生成xlsx文件

			String fileName = file.getName();
			Excel2007Utils.exportFile(response, tplPath, fileName);// 下载生成的xlsx文件
			UserUtil.getSession().removeAttribute(
					eGuardAplyService.EGUARDITEMERRORDTOS);
		}
	}

	/**
	 * 方法描述: 根據主鍵刪除
	 * 
	 * @Author: ********
	 * @CreateDate: 2018-12-27 10:29:50
	 * @Return
	 **/

	@RequestMapping("delete/{id}")
	// @RequiresPermissions("audit:eguardaply:delete")
	@ResponseBody
	public String delete(@PathVariable("id") String id) {
		eGuardAplyService.delete(id);
		return "success";
	}

	/**
	 * 导出excel
	 * 
	 * @Author: ********
	 * @CreateDate: 2018-12-27 10:29:50
	 * @Return
	 **/
	@RequestMapping("exportExcel")
	public void exportExcel(HttpServletRequest request,
			HttpServletResponse response) throws Exception {

		List<PropertyFilter> filters = PropertyFilter
				.buildFromHttpRequest(request);
		// 查詢權限begin
		List<Integer> roleList =userRoleService.getRoleIdList(UserUtil.getCurrentUser().getId()) ;
		if (roleList == null || roleList.size() == 0 || !roleList.contains(281)) {
			// 只能查到自己申請或者審核的單字
			List<String> serialnoList = new ArrayList<String>();
			// 查自己申請的單子
			List<PropertyFilter> selfApplyFilters = new ArrayList<PropertyFilter>();
			selfApplyFilters.add(new PropertyFilter("EQS_makerno", UserUtil.getCurrentUser().getLoginName()));
			List<EGuardAplyEntity> selfApplyList = eGuardAplyService.search(selfApplyFilters);
			for (EGuardAplyEntity aply : selfApplyList) {
				serialnoList.add(aply.getSerialno());
			}
			// 查自己簽核的單子
			List<PropertyFilter> selfSignFilters = new ArrayList<PropertyFilter>();
			selfSignFilters.add(new PropertyFilter("EQS_eserId", UserUtil.getCurrentUser().getLoginName()));
			List<EFormSignEntity> signFormList = eFormSignService.search(selfSignFilters);
			for (EFormSignEntity sign : signFormList) {
				serialnoList.add(sign.getApplyId());
			}
			if (serialnoList.size() == 0) {
				serialnoList.add("");
			}
			filters.add(new PropertyFilter("INS_serialno", serialnoList));
		}
		// 審核狀態
		String applyStat = request.getParameter("applyStat");
		if (StringUtils.isNotEmpty(applyStat)) {
			if (applyStat.equals("E") || applyStat.equals("N")) {
				filters.add(new PropertyFilter("EQS_applyStat", applyStat));
			} else {
				// 審核中
				filters.add(new PropertyFilter("NIS_applyStat", Arrays.asList("N", "E")));
			}
		};
		List<EGuardAplyEntity> list = eGuardAplyService.search(filters);
		ConvertUtils.confertDateToStanded(list);// 日期格式化
		ConvertUtils.convertPropertyToDictLabel(list, "legalPerson", "guard_legalperson");
		ConvertUtils.convertPropertyToDictLabel(list, "requirememtType", "guard_aply_type");
		ConvertUtils.convertApplyState(list, "applyStat");

		if (list.size() > 0) {
			// 導出excel2007
			LinkedHashMap<String, String> fieldMap = new LinkedHashMap<String, String>();
			fieldMap.put("serialno", "任務編號");
			fieldMap.put("makerno", "填單人工號");
			fieldMap.put("makername", "填單人姓名");
			fieldMap.put("makerip", "填單人IP");
			fieldMap.put("createtime", "填單時間");
			fieldMap.put("dptQun", "事業群");
			fieldMap.put("legalPerson", "所屬法人");
			fieldMap.put("dealtel", "分機");
			fieldMap.put("linkman", "聯繫人");
			fieldMap.put("requirememtType", "需求類型");
			fieldMap.put("applyStat", "申請單狀態");
			fieldMap.put("signEmpNo", "待審核人工號");
			fieldMap.put("signEmpNam", "待審核人姓名");
			String name = "GuardApply";
			ExcelUtil.listToExcel(list, fieldMap, name, response);
		}
	}
}
