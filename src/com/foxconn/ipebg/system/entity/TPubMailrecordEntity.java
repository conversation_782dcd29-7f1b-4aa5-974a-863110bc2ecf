package com.foxconn.ipebg.system.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 郵件發送記錄表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-25 08:45:27
 */
@Entity
@Table(name = "t_pub_mailrecord")
@DynamicUpdate
@DynamicInsert
public class TPubMailrecordEntity extends DataEntity<TPubMailrecordEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    //用戶
    private String username;
    //表單類型
    private String ordertype;
    //任務編號
    private String serialno;
    //駁回主管
    private String chargerman;
    //審核主管
    private String dusername;
    //表單狀態  2 審核中 3審核完成 4駁回
    private String orderstatus;
    //發送狀態  0 未發送   1 已發送
    private String sendStatus;
    private String usermail;
    private String validStr;
    private String url;
    private String urlip;
    private String freeloginurl;
    private String freeloginurlip;
    private String empno;

    /**
     * 设置：驗證字符串
     */
    public void setValidStr(String validStr) {
        this.validStr = validStr;
    }

    /**
     * 获取：驗證字符串
     */

    @Column(name = "valid_str", nullable = false, length = 20)
    public String getValidStr() {
        return validStr;
    }
    @Column(name = "usermail", nullable = false, length = 20)
    public String getUsermail() {
        return usermail;
    }

    public void setUsermail(String usermail) {
        this.usermail = usermail;
    }

    /**
     * 设置：用戶
     */
    public void setUsername(String username) {
        this.username = username;
    }

    /**
     * 获取：用戶
     */

    @Column(name = "username", nullable = false, length = 20)
    public String getUsername() {
        return username;
    }

    /**
     * 设置：表單類型
     */
    public void setOrdertype(String ordertype) {
        this.ordertype = ordertype;
    }

    /**
     * 获取：表單類型
     */

    @Column(name = "ordertype", nullable = false, length = 20)
    public String getOrdertype() {
        return ordertype;
    }

    /**
     * 设置：任務編號
     */
    public void setSerialno(String serialno) {
        this.serialno = serialno;
    }

    /**
     * 获取：任務編號
     */

    @Column(name = "serialno", nullable = false, length = 20)
    public String getSerialno() {
        return serialno;
    }

    /**
     * 设置：駁回主管
     */
    public void setChargerman(String chargerman) {
        this.chargerman = chargerman;
    }

    /**
     * 获取：駁回主管
     */

    @Column(name = "chargerman", nullable = false, length = 20)
    public String getChargerman() {
        return chargerman;
    }

    /**
     * 设置：審核主管
     */
    public void setDusername(String dusername) {
        this.dusername = dusername;
    }

    /**
     * 获取：審核主管
     */

    @Column(name = "dusername", nullable = false, length = 20)
    public String getDusername() {
        return dusername;
    }

    /**
     * 设置：表單狀態  2 審核中 3審核完成 4駁回
     */
    public void setOrderstatus(String orderstatus) {
        this.orderstatus = orderstatus;
    }

    /**
     * 获取：表單狀態  2 審核中 3審核完成 4駁回
     */

    @Column(name = "orderstatus", nullable = false, length = 20)
    public String getOrderstatus() {
        return orderstatus;
    }

    /**
     * 设置：發送狀態  0 未發送   1 已發送
     */
    public void setSendStatus(String sendStatus) {
        this.sendStatus = sendStatus;
    }

    /**
     * 获取：發送狀態  0 未發送   1 已發送
     */

    @Column(name = "send_status", nullable = false, length = 20)
    public String getSendStatus() {
        return sendStatus;
    }
    @Column(name = "url", nullable = false, length = 20)
    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
    @Column(name = "urlip", nullable = false, length = 20)
    public String getUrlip() {
        return urlip;
    }

    public void setUrlip(String urlip) {
        this.urlip = urlip;
    }
    @Column(name = "freeloginurl", nullable = false, length = 20)
    public String getFreeloginurl() {
        return freeloginurl;
    }

    public void setFreeloginurl(String freeloginurl) {
        this.freeloginurl = freeloginurl;
    }
    @Column(name = "freeloginurlip", nullable = false, length = 20)
    public String getFreeloginurlip() {
        return freeloginurlip;
    }

    public void setFreeloginurlip(String freeloginurlip) {
        this.freeloginurlip = freeloginurlip;
    }
    @Column(name = "empno", nullable = false, length = 20)
    public String getEmpno() {
        return empno;
    }

    public void setEmpno(String empno) {
        this.empno = empno;
    }
}
