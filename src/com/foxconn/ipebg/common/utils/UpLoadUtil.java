package com.foxconn.ipebg.common.utils;

import java.io.File;

import com.google.gson.Gson;
import com.xiaominfo.oss.sdk.OSSClient;
import com.xiaominfo.oss.sdk.OSSClientProperty;
import com.xiaominfo.oss.sdk.client.FileBytesResponse;

public class UpLoadUtil {
  public static void main(String[] args){
	  OSSClientProperty ossClientProperty=new OSSClientProperty();
      ossClientProperty.setRemote("http://**************:18000/");
      ossClientProperty.setAppid("osse29e19");
      ossClientProperty.setAppsecret("3qg9my2a");
      ossClientProperty.setProject("province_IIII");
      OSSClient ossClient=new OSSClient(ossClientProperty);
      File file=new File("E:\\量試樣品擴散單.txt");
      System.out.println(new Gson().toJson(ossClient.uploadFileByForm(file)));
  }
}
