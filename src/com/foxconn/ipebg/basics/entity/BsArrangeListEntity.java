package com.foxconn.ipebg.basics.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

import com.foxconn.ipebg.common.entity.DataEntity;

@Entity
@Table(name = "V_BS_ARRANGE_LIST")
public class BsArrangeListEntity extends DataEntity<BsArrangeListEntity>  implements Serializable {
    private static final long serialVersionUID = 1L;
    // 排班群组编号
    private String arrangeGroupId;
    // 排班群组名称
    private String arrangeGroupName;
    // 排班日期
    private Date arrangeDate;
    // 保安公司
    private String securityCom;

    @Column(name = "arrange_group_id")
    public String getArrangeGroupId() {
        return arrangeGroupId;
    }

    public void setArrangeGroupId(String arrangeGroupId) {
        this.arrangeGroupId = arrangeGroupId;
    }

    @Column(name = "arrange_group_name")
    public String getArrangeGroupName() {
        return arrangeGroupName;
    }

    public void setArrangeGroupName(String arrangeGroupName) {
        this.arrangeGroupName = arrangeGroupName;
    }

    @Column(name = "arrange_date")
    @Temporal(TemporalType.DATE)
    public Date getArrangeDate() {
        return arrangeDate;
    }

    public void setArrangeDate(Date arrangeDate) {
        this.arrangeDate = arrangeDate;
    }

    @Column(name = "security_com")
    public String getSecurityCom() {
        return securityCom;
    }

    public void setSecurityCom(String securityCom) {
        this.securityCom = securityCom;
    }
}
