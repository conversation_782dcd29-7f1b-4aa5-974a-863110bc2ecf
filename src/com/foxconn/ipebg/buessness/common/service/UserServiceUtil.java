package com.foxconn.ipebg.buessness.common.service;

import com.foxconn.ipebg.buessness.common.entity.TQhCurrentserialnoEntity;
import com.foxconn.ipebg.buessness.common.entity.TQhFactoryidconfigEntity;
import com.foxconn.ipebg.buessness.common.entity.TQhOrgaformhsEntity;
import com.foxconn.ipebg.buessness.common.entity.TQhUserformhsEntity;
import com.foxconn.ipebg.common.utils.DateUtil;
import com.foxconn.ipebg.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.text.DecimalFormat;

/**
 * Company foxconn
 * Copyright (C) 2004-2018 All Rights Reserved.
 */
@Service
@Transactional(readOnly = true)
public class UserServiceUtil {
    public Logger logger = LoggerFactory.getLogger(UserServiceUtil.class);
    @Autowired
    private TQhCurrentserialnoService serialnoService;

    /**
     * 方法描述: 生成任務編號
     *
     * @Author: S6113712
     * @CreateDate: 2018/10/16  下午 07:25
     * @Return
     **/

    public String createSerialno(String headStr) {

        //TQhUserformhsEntity entity = null;
        TQhFactoryidconfigEntity configEntity = null;
        String curdate = null;
        String str = null;
        try {
            //entity = tQhUserformhsService.findByEmpno(empNo);
           // Assert.notNull(entity);
            
            
            curdate = DateUtil.getNowTime("yyMMdd");
            TQhCurrentserialnoEntity tQhCurrentserialnoEntity = null;

            tQhCurrentserialnoEntity = serialnoService.findByCondition(headStr, curdate);
            if (tQhCurrentserialnoEntity == null) {
                tQhCurrentserialnoEntity = new TQhCurrentserialnoEntity();
                tQhCurrentserialnoEntity.setCurdate(curdate);
                tQhCurrentserialnoEntity.setLocalid(headStr);
                tQhCurrentserialnoEntity.setCurid(1);
            } else {
                tQhCurrentserialnoEntity.setCurid(tQhCurrentserialnoEntity.getCurid() + 1);
            }
            serialnoService.save(tQhCurrentserialnoEntity);

            DecimalFormat df = new DecimalFormat("0000");
            str = df.format(tQhCurrentserialnoEntity.getCurid());
        } catch (Exception e) {
            logger.info(e.getMessage(), e);
        }
        System.out.println(headStr  + curdate + str);
        return headStr  + curdate + str;//+ StringUtils.leftPad(entity.getDeptcostno(), 8, "X")
    }

    
    /**
     * 方法描述: 生成任務編號
     *
     * @Author: S6113712
     * @CreateDate: 2019/8/30  下午 07:25
     * @Return
     **/

    public String createNo(String headStr) {

        //TQhUserformhsEntity entity = null;
        TQhFactoryidconfigEntity configEntity = null;
        String curdate = null;
        String str = null;
        try {
            //entity = tQhUserformhsService.findByEmpno(empNo);
           // Assert.notNull(entity);
            
            
            curdate = DateUtil.getNowTime("yyyy");
            TQhCurrentserialnoEntity tQhCurrentserialnoEntity = null;

            tQhCurrentserialnoEntity = serialnoService.findByCondition(headStr, curdate);
            if (tQhCurrentserialnoEntity == null) {
                tQhCurrentserialnoEntity = new TQhCurrentserialnoEntity();
                tQhCurrentserialnoEntity.setCurdate(curdate);
                tQhCurrentserialnoEntity.setLocalid(headStr);
                tQhCurrentserialnoEntity.setCurid(1);
            } else {
                tQhCurrentserialnoEntity.setCurid(tQhCurrentserialnoEntity.getCurid() + 1);
            }
            serialnoService.save(tQhCurrentserialnoEntity);

            DecimalFormat df = new DecimalFormat("0000");
            str = df.format(tQhCurrentserialnoEntity.getCurid());
        } catch (Exception e) {
            logger.info(e.getMessage(), e);
        }
        System.out.println(headStr  + curdate + str);
        return headStr  + curdate + str;//+ StringUtils.leftPad(entity.getDeptcostno(), 8, "X")
    }
}
