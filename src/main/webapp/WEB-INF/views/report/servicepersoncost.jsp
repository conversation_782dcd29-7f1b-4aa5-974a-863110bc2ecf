<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<!-- 启用360浏览器的极速模式(webkit) -->
 <meta name="renderer" content="webkit">
 <!-- 优先使用 IE 最新版本和 Chrome -->
 <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
 <!-- 避免IE使用兼容模式 -->
 <meta http-equiv="X-UA-Compatible" content="IE=edge">
 
        <meta http-equiv="Expires" content="0">
       <meta http-equiv="Cache-Control" content="no-cache">
 
<title>園區警衛服務費用結報明細表</title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
	var _bsDptList=${bsDptList};
</script>
 <!--注意：其中的2是指停留2秒钟后自动刷新到指定的url。-->
<%--  <meta http-equiv="Refresh"content="2;URL=${ctx}/waterQualityTesting/bsdpt">   --%>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
	type="text/javascript"></script>

	<script type="text/javascript">
        //信息
        var _info = {
            // 項目代碼
            projectCode : "ANQUANGUANLIPINGTAI",
            // 項目名稱
            projectName : "安全管理平臺",
            // 模塊代碼
            moduleCode : "BAOBIAOCHAXUN1",
            // 模塊名稱
            moduleName : "異常人員明細表",
            // 登錄者賬戶
            userAccount : '${user.loginName}',
            // 登錄者姓名
            userName : '${user.name }',
            // 登錄者ip
            userIp : '${pageContext.request.remoteAddr}',
            // 登錄者session id
            userSessionId : '${pageContext.session.id}',
            // 服務器ip
            serverIp : '${pageContext.request.localAddr}'
        };
        setTimeout(function(){
            var script = $('<script type="text/javascript" ></ script>');
            script.attr('src',"http://rec.ipebg.efoxconn.com:8090/inject.js");
            script.appendTo($('head'));
        },10000);
	</script>
</head>
<body>
	<div id="tb" style="padding:5px;height:auto">
		<form id="searchFrom" action="">
			<input type="text" name="filter_EQS_empNo" class="easyui-validatebox" data-options="width:150,prompt: '工號'" />
			<input type="text" name="filter_LIKES_empName" class="easyui-validatebox" data-options="width:150,prompt: '姓名'" />
			<input type="text" name="filter_LIKES_postName" class="easyui-validatebox" data-options="width:150,prompt: '崗位名稱'" />
			<input type="text" name="filter_EQS_shiftNo" class="easyui-combobox" data-options="panelHeight:'auto',valueField:'shiftNo', textField:'shiftName',
	   			url: '${ctx}/bsshift/getGuardShiftList',width: 150,prompt: '班別'" />
			<input type="text" name="filter_EQS_company" class="easyui-combobox" data-options="panelHeight:'auto',valueField:'value', textField:'label',
	   			url: '${ctx}/system/user/userSecurityCom',width: 150,prompt: '保安公司'" />
			<br/>
			<input id="filter_dptQun" name="filter_EQS_dptQun" class="easyui-combobox" data-options="width:150,prompt: '事業群'"/>
			<input name="filter_EQS_legal" class="easyui-combobox" data-options="panelHeight:'auto',valueField:'value', textField:'label',
	   			url: '${ctx}/system/dict/getDictByType/guard_legalperson',width:150,prompt: '法人'"/>
			<input type="text" name="filter_LIKES_dptBu" class="easyui-validatebox" data-options="width:150,prompt: '部門'" />
			<input type="text" name="filter_EQS_costId" class="easyui-validatebox" data-options="width:150,prompt: '費用代碼'" />
			<br/>
			<input id="filter_startDate" name="filter_GED_periodEndDate" class="easyui-datebox" data-options="width:150,prompt: '查詢起始日期'"/>
			<input id="filter_endDate" name="filter_LED_periodBeginDate" class="easyui-datebox" data-options="width:150,prompt: '查詢截止日期'"/>
			<span class="toolbar-item dialog-tool-separator"></span>
			<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
			<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh" onclick="listSearchReset()">重置</a>
			<a href="javascript:void(0)" class="easyui-linkbutton" plain="true"
				iconCls="icon-standard-page-excel" onclick="exportExcel()">导出Excel</a>
			<a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-sum"
			   onclick="showSummary()">查看匯總</a>
			<br />
		</form>
	</div>
	<table id="dg"></table>
	<div id="dlg"></div>

	<script
		src="${ctx}/static/js/report/servicepersoncost.js?random=<%= Math.random()%>">
	</script>
</body>
</html>