package com.foxconn.ipebg.buessness.workflow.service;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNodeparamEntity;
import com.foxconn.ipebg.buessness.workflow.dao.WfNodeparamDao;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import com.foxconn.ipebg.common.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * 流程節點出口信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-06 11:04:39
 */
@Service
@Transactional(readOnly=true)
public class  WfNodeparamService extends BaseBusinessService<WfNodeparamEntity, String> {
    @Autowired
    private WfNodeparamDao wfNodeparamDao;
    @Override
    public HibernateDao<WfNodeparamEntity, String> getEntityDao() {
        return wfNodeparamDao;
    }

    public WfNodeparamEntity getInfoById(WfNodeinfoEntity entity, String status){
        return this.wfNodeparamDao.findUnique("from WfNodeparamEntity t where t.nodeid=?0 and t.ispass=?1 and t.version=?2",entity.getNodeid(),status,entity.getVersion());
    }

    public List<WfNodeparamEntity> queryNodeInfos(String nodeId){
        return this.wfNodeparamDao.findBy("nodeid",nodeId);
    }
}
