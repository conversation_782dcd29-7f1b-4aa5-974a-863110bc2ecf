var dg;
var d;
$(function () {
    getDictByType();
    dg = $('#dg').datagrid({
        method: "get",
        url: ctx+'/tqhuserduty/list',
        fit: true,
        fitColumns: true,
        border: false,
        idField: 'id',
        striped: true,
        pagination: true,
        rownumbers: true,
        pageNumber: 1,
        pageSize: 20,
        pageList: [10, 20, 30, 40, 50],
        singleSelect: true,
        columns: [[
            {field: 'id', title: '${column.comments}', hidden: true},
            {field: 'factoryid', title: '所在產區', sortable: true, width: 100}
            ,
            {field: 'dutyid', title: '角色名稱', sortable: true, width: 100},
            {field: 'dutyids', title: '角色id', sortable: true, width: 100},
            {field: 'empno', title: '工號', sortable: true, width: 100}
            ,
            {field: 'area', title: '區域', sortable: true, width: 100}
            ,
            {field: 'building', title: '樓棟', sortable: true, width: 100}
            ,
            {field: 'remarks', title: '備註', sortable: true, width: 100}
            ,
            {field: 'factorycode', title: '產區編碼', sortable: true, width: 100}
            ,
            {field: 'applydeptno', title: '申請人', sortable: true, width: 100}
            ,
            {field: 'createBy', title: '創建人', sortable: true, width: 100}
            ,
            {field: 'createDate', title: '創建時間', sortable: true, width: 100}
            ,
            {field: 'updateBy', title: '更新者', sortable: true, width: 100}
            ,
            {field: 'updateDate', title: '更新時間', sortable: true, width: 100}
        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        toolbar: '#tb'
    });
});
//弹窗增加
function add() {
    d = $("#dlg").dialog({
        title: '添加角色權限',
        width: 380,
        height: 380,
        href: ctx+'/tqhuserduty/create',
        maximizable: true,
        modal: true,
        buttons: [{
            text: '确认',
            handler: function () {
                $("#mainform").submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//删除
function del() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function (data) {
        if (data) {
            $.ajax({
                type: 'get',
                url: ctx+"/tqhuserduty/delete/" + row.id,
                success: function (data) {
                    successTip(data, dg);
                }
            });
        }
    });
}

//弹窗修改
function upd() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    d = $("#dlg").dialog({
        title: '修改簽核角色信息',
        width: 380,
        height: 340,
        href: ctx+'/tqhuserduty/update/' + row.id,
        maximizable: true,
        modal: true,
        buttons: [{
            text: '修改',
            handler: function () {
                $('#mainform').submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//创建查询对象并查询
function cx() {
    var obj = $("#searchFrom").serializeObject();
    dg.datagrid('load', obj);
}

function getDictByType() {
    $.ajax({
        url: ctx+"/system/dict/getDictByType/user_duty",
        //dataType:"json",
        type: "GET",
        success: function (data) {
            //绑定第一个下拉框
            $("#dutyid").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({value: '', label: '請選擇'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });
}

$.ajax({
    url: ctx+"/system/dict/getDictByType/user_duty",
    //dataType:"json",
    type: "GET",
    success: function (data) {
        //绑定第一个下拉框
        $("#dutyids").combobox({
            data: data,
            valueField: "value",
            textField: "label",
            editable: false,
            panelHeight: 400,
            loadFilter: function (data) {
                data.unshift({value: '', label: '請選擇角色'});
                return data;
            }
        });
    },
    error: function (error) {
        alert("初始化下拉控件失败");
    }
});
//獲取廠區
$.get(ctx+'/tqhfactoryidconfig/allFactorys/',
    function (result) {
        $("#factoryid").combobox({
            data: result,
            valueField: "factoryid",
            textField: "factoryname",
            editable: false,
            panelHeight: 400,
            loadFilter: function (data) {
                data.unshift({factoryid: '', factoryname: '請選擇'});
                return data;
            }
        });
    });
