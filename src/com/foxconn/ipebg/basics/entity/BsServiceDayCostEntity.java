package com.foxconn.ipebg.basics.entity;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 警衛服務費用報表
 * S6114893
 * 2022-07-29
 */
@Entity
@Table(name = "BS_SERVICE_DAY_COST")
@DynamicUpdate
@DynamicInsert
public class BsServiceDayCostEntity extends DataEntity<BsServiceDayCostEntity> implements Serializable {
    private static final long serialVersionUID = 1L;
    // 崗位編碼
    private String recno;
    // 事業群
    private String dptQun;
    // 法人
    private String legal;
    // 部門
    private String dptBu;
    // 費用代碼
    private String costId;
    // 崗位名稱
    private String postName;
    // 保安公司
    private String securityCom;
    // 白班人數
    private Integer pernuA;
    // 夜班人數
    private Integer pernuB;
    // 總人數
    private Integer pernu;
    // 正常上班費用
    private Double normalCost;
    // 培訓費用
    private Double trainingCost;
    // 常規費用（正常上班費用+培訓費用）
    private Double regularCost;
    // 加班時數
    private Integer overtimeHours;
    // 加班費用
    private Double overtimeCost;
    // 總費用
    private Double totalCost;
    // 結算日期
    private Date workDate;

    @Column(name = "RECNO", nullable = false, length = 20)
    public String getRecno() {
        return recno;
    }

    public void setRecno(String recno) {
        this.recno = recno;
    }

    @Column(name = "DPT_QUN", nullable = false, length = 20)
    public String getDptQun() {
        return dptQun;
    }

    public void setDptQun(String dptQun) {
        this.dptQun = dptQun;
    }

    @Column(name = "LEGAL", nullable = false, length = 20)
    public String getLegal() {
        return legal;
    }

    public void setLegal(String legal) {
        this.legal = legal;
    }

    @Column(name = "DPT_BU", nullable = false, length = 40)
    public String getDptBu() {
        return dptBu;
    }

    public void setDptBu(String dptBu) {
        this.dptBu = dptBu;
    }

    @Column(name = "COST_ID", nullable = false, length = 20)
    public String getCostId() {
        return costId;
    }

    public void setCostId(String costId) {
        this.costId = costId;
    }

    @Column(name = "POST_NAM", nullable = false, length = 50)
    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }

    @Column(name = "PERNU_A", nullable = true)
    public Integer getPernuA() {
        return pernuA;
    }

    public void setPernuA(Integer pernuA) {
        this.pernuA = pernuA;
    }

    @Column(name = "PERNU_B", nullable = true)
    public Integer getPernuB() {
        return pernuB;
    }

    public void setPernuB(Integer pernuB) {
        this.pernuB = pernuB;
    }

    @Column(name = "PERNU")
    public Integer getPernu() {
        return pernu;
    }

    public void setPernu(Integer pernu) {
        this.pernu = pernu;
    }

    @Column(name = "NORMAL_COST", nullable = false)
    public Double getNormalCost() {
        return normalCost;
    }

    public void setNormalCost(Double normalCost) {
        this.normalCost = normalCost;
    }

    @Column(name = "TRAINING_COST", nullable = false)
    public Double getTrainingCost() {
        return trainingCost;
    }

    public void setTrainingCost(Double trainingCost) {
        this.trainingCost = trainingCost;
    }

    @Column(name = "REGULAR_COST", nullable = false)
    public Double getRegularCost() {
        return regularCost;
    }

    public void setRegularCost(Double regularCost) {
        this.regularCost = regularCost;
    }

    @Column(name = "OVERTIME_HOURS", nullable = false)
    public Integer getOvertimeHours() {
        return overtimeHours;
    }

    public void setOvertimeHours(Integer overtimeHours) {
        this.overtimeHours = overtimeHours;
    }

    @Column(name = "OVERTIME_COST", nullable = false)
    public Double getOvertimeCost() {
        return overtimeCost;
    }

    public void setOvertimeCost(Double overtimeCost) {
        this.overtimeCost = overtimeCost;
    }

    @Column(name = "TOTAL_COST", nullable = false)
    public Double getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(Double totalCost) {
        this.totalCost = totalCost;
    }

    @Column(name = "WORK_DATE", nullable = false)
    public Date getWorkDate() {
        return workDate;
    }

    public void setWorkDate(Date workDate) {
        this.workDate = workDate;
    }

    @Column(name = "SECURITY_COM", nullable = false, length = 15)
    public String getSecurityCom() {
        return securityCom;
    }

    public void setSecurityCom(String securityCom) {
        this.securityCom = securityCom;
    }
}
