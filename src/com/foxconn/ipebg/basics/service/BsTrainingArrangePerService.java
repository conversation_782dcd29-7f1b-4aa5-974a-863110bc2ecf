package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.entity.BsTrainingArrangePerEntity;
import com.foxconn.ipebg.basics.dao.BsTrainingArrangePerDao;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.Dict;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WorkFlowEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.system.service.DictService;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 培訓期日排班表人員表
 *
 * <AUTHOR>
 * @email <EMAIL> 
 * @date 2019-09-09 13:50:29
 */
@Service
@Transactional(readOnly=true)
public class  BsTrainingArrangePerService extends BaseBusinessService<BsTrainingArrangePerEntity, String>{
    @Autowired
    private BsTrainingArrangePerDao bsTrainingArrangePerDao;

    @Autowired
    private UserServiceUtil serviceUtil;
    @Autowired
    private DictService dictService;

    @Override
    public HibernateDao<BsTrainingArrangePerEntity, String> getEntityDao() {
        return bsTrainingArrangePerDao;
    }

    public BsTrainingArrangePerEntity findBySerialno(String serialno) {
        return this.bsTrainingArrangePerDao.findUniqueBy("serialno", serialno);
    }
    @Transactional(readOnly=false)
    public int deleteByBstaId(String bstaId) {
        String hql="delete from BsTrainingArrangePerEntity where bstaId= ?0";
        return bsTrainingArrangePerDao.batchExecute(hql,bstaId);
    }
    
}
