package com.foxconn.ipebg.system.dao;

import java.util.List;

import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.system.entity.Organization;
import org.hibernate.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.foxconn.ipebg.system.entity.UserOrg;


/**
 * 用户机构Dao
 *
 * <AUTHOR>
 * @date 2015年5月9日
 */
@Repository
public class UserOrgDao extends HibernateDao<UserOrg, Integer> {
    @Autowired
    private OrganizationDao organizationDao;
    /**
     * 删除用户机构
     *
     * @param userId
     * @param orgId
     */
    public void deleteUO(Integer userId) {
        String hql = "delete UserOrg ur where ur.userId=?0 ";
        batchExecute(hql, userId);
    }

    /**
     * 查询用户拥有的机构id集合
     *
     * @param userId
     * @return 结果集合
     */
    @SuppressWarnings("unchecked")
    public List<Integer> findOrgIds(Integer userId) {
        String hql = "select ur.orgId from UserOrg ur where ur.userId=?0";
        Query query = createQuery(hql, userId);
        return query.list();
    }

    /**
     * 方法描述: 獲取審核節點用戶id
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/11  下午 01:46
     * @Return
     **/

    public List<Integer> getUserListByDeptNO(String deptNo) {
        //查詢當前人所在單位
        Organization o= organizationDao.findUniqueBy("orgCode",deptNo);
        String hql = "select ur.orgId from UserOrg ur where ur.orgId=?0";
        Query query = createQuery(hql, o.getId());
        return query.list();
    }
}
