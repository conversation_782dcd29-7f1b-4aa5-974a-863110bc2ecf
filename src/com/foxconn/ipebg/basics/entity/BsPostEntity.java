package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 崗位明細表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-03-07 09:57:22
 */
@Entity
@Table(name = "BS_POST")
@DynamicUpdate
@DynamicInsert
public class BsPostEntity extends DataEntity<BsPostEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    //主表任務單號
    private String serialno;
    //編號
    private String recno;
    //廠區
    private String site;
    //區域
    private String area;
    //棟
    private String block;
    //層
    private String floor;
    //方位
    private String position;
    //詳細位置
    private String location;
    //崗位類別
    private String postType;
    //班制
    private String postShift;
    //崗位名稱
    private String postName;
    //人數(不含調休)
    private Integer postPerNu;
    //需求時間-開始時間
    private Date postStartDate;
    //需求時間-結束時間
    private Date postEndDate;
    //崗位級別
    private String postLevel;
    //派駐保安公司
    private String securityCom;
    //實到崗人數
    private Integer postPerNuR;
    //生效時間
    private Date postEffectDate;
    //所属部门ID
    private String dptId;

    /**
     * @return 获取记录对象，用于保存修改记录
     */
    @Transient
    public BsPostHistoryEntity getHistoryEntity() {
        BsPostHistoryEntity history = new BsPostHistoryEntity();
        history.setSerialno(getSerialno());
        history.setRecno(getRecno());
        history.setArea(getArea());
        history.setBlock(getBlock());
        history.setFloor(getFloor());
        history.setPosition(getPosition());
        history.setPostType(getPostType());
        history.setPostShift(getPostShift());
        history.setPostName(getPostName());
        history.setPostPerNu(getPostPerNu());
        history.setSecurityCom(getSecurityCom());
        history.setPostStartDate(getPostStartDate());
        history.setPostEndDate(getPostEndDate());
        history.setCreateBy(getCreateBy());
        history.setCreateDate(new Date());
        history.setNewRecord(true);
        return history;
    }

    /**
     * 设置：主表任務單號
     */
    public void setSerialno(String serialno) {
        this.serialno = serialno;
    }

    /**
     * 获取：主表任務單號
     */
    @Column(name = "SERIALNO", nullable = false, length = 20)
    public String getSerialno() {
        return serialno;
    }

    /**
     * 设置：編號
     */
    public void setRecno(String recno) {
        this.recno = recno;
    }

    /**
     * 获取：編號
     */

    @Column(name = "RECNO", nullable = false, length = 20)
    public String getRecno() {
        return recno;
    }

    /**
     * 设置：廠區
     */
    public void setSite(String site) {
        this.site = site;
    }

    /**
     * 获取：廠區
     */

    @Column(name = "SITE", nullable = false, length = 20)
    public String getSite() {
        return site;
    }

    /**
     * 设置：區域
     */
    public void setArea(String area) {
        this.area = area;
    }

    /**
     * 获取：區域
     */

    @Column(name = "AREA", nullable = false, length = 20)
    public String getArea() {
        return area;
    }

    /**
     * 设置：棟
     */
    public void setBlock(String block) {
        this.block = block;
    }

    /**
     * 获取：棟
     */

    @Column(name = "BLOCK", nullable = false, length = 20)
    public String getBlock() {
        return block;
    }

    /**
     * 设置：層
     */
    public void setFloor(String floor) {
        this.floor = floor;
    }

    /**
     * 获取：層
     */

    @Column(name = "FLOOR", nullable = false, length = 20)
    public String getFloor() {
        return floor;
    }

    /**
     * 设置：方位
     */
    public void setPosition(String position) {
        this.position = position;
    }

    /**
     * 获取：方位
     */

    @Column(name = "POSITION", nullable = false, length = 20)
    public String getPosition() {
        return position;
    }

    /**
     * 设置：詳細位置
     */
    public void setLocation(String location) {
        this.location = location;
    }

    /**
     * 获取：詳細位置
     */

    @Column(name = "LOCATION", nullable = false, length = 20)
    public String getLocation() {
        return location;
    }

    /**
     * 设置：崗位類別
     */
    public void setPostType(String postType) {
        this.postType = postType;
    }

    /**
     * 获取：崗位類別
     */

    @Column(name = "POST_TYPE", nullable = false, length = 20)
    public String getPostType() {
        return postType;
    }

    /**
     * 设置：班制
     */
    public void setPostShift(String postShift) {
        this.postShift = postShift;
    }

    /**
     * 获取：班制
     */

    @Column(name = "POST_SHIFT", nullable = false, length = 20)
    public String getPostShift() {
        return postShift;
    }

    /**
     * 设置：崗位名稱
     */
    public void setPostName(String postName) {
        this.postName = postName;
    }

    /**
     * 获取：崗位名稱
     */

    @Column(name = "POST_NAME", nullable = false, length = 20)
    public String getPostName() {
        return postName;
    }

    /**
     * 设置：人數(不含調休)
     */
    public void setPostPerNu(Integer postPerNu) {
        this.postPerNu = postPerNu;
    }

    /**
     * 获取：人數(不含調休)
     */

    @Column(name = "POST_PER_NU", nullable = false, length = 20)
    public Integer getPostPerNu() {
        return postPerNu;
    }

    /**
     * 设置：需求時間-開始時間
     */
    public void setPostStartDate(Date postStartDate) {
        this.postStartDate = postStartDate;
    }

    /**
     * 获取：需求時間-開始時間
     */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "POST_START_DATE", nullable = false, length = 20)
    public Date getPostStartDate() {
        return postStartDate;
    }

    /**
     * 设置：需求時間-結束時間
     */
    public void setPostEndDate(Date postEndDate) {
        this.postEndDate = postEndDate;
    }

    /**
     * 获取：需求時間-結束時間
     */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "POST_END_DATE", nullable = false, length = 20)
    public Date getPostEndDate() {
        return postEndDate;
    }

    /**
     * 设置：崗位級別
     */
    public void setPostLevel(String postLevel) {
        this.postLevel = postLevel;
    }

    /**
     * 获取：崗位級別
     */

    @Column(name = "POST_LEVEL", nullable = false, length = 20)
    public String getPostLevel() {
        return postLevel;
    }

    /**
     * 设置：派駐保安公司
     */
    public void setSecurityCom(String securityCom) {
        this.securityCom = securityCom;
    }

    /**
     * 获取：派駐保安公司
     */

    @Column(name = "SECURITY_COM", nullable = false, length = 20)
    public String getSecurityCom() {
        return securityCom;
    }

    /**
     * 设置：實到崗人數
     */
    public void setPostPerNuR(Integer postPerNuR) {
        this.postPerNuR = postPerNuR;
    }

    /**
     * 获取：實到崗人數
     */

    @Column(name = "POST_PER_NU_R", nullable = false, length = 20)
    public Integer getPostPerNuR() {
        return postPerNuR;
    }

    /**
     * 设置：生效時間
     */
    public void setPostEffectDate(Date postEffectDate) {
        this.postEffectDate = postEffectDate;
    }

    /**
     * 获取：生效時間
     */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "POST_EFFECT_DATE", nullable = false, length = 20)
    public Date getPostEffectDate() {
        return postEffectDate;
    }

    @Column(name = "dpt_id")
    public String getDptId() {
        return dptId;
    }

    public void setDptId(String dptId) {
        this.dptId = dptId;
    }
}
