package com.foxconn.ipebg.basics.dao;

import com.foxconn.ipebg.basics.entity.BsServiceDayCostEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import org.hibernate.jdbc.Work;
import org.springframework.stereotype.Repository;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Types;
import java.util.Date;

/**
 * 警衛服務費用結賬明細主表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-10-07 13:15:24
 */
@Repository
public class BsServiceDayCostDao extends HibernateDao<BsServiceDayCostEntity,String> {

    /**
     * 調用存儲過程生成警衛服務費用報表數據
     * @param date 生成日期
     */
	public void generateServiceCost(final Date date) throws SQLException {
        getSession().doWork(new Work() {
            @Override
            public void execute(Connection connection) throws SQLException {
                CallableStatement callable = connection.prepareCall("{CALL pk_workstatus_rpt_bs_service_day_cost_sp(?, ?)}");
                callable.setDate(1, new java.sql.Date(date.getTime()));
                callable.registerOutParameter(2, Types.OTHER);
                callable.execute();
                callable.close();
            }
        });
    }
}
