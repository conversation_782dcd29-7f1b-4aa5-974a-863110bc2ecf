package com.foxconn.ipebg.report.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;
import java.util.LinkedHashMap;

/**
 * 園區警衛服務費用報表v3（按人員統計費用）
 */
@Entity
@Table(name = "BS_SERVICE_PERSON_COST")
@DynamicUpdate
@DynamicInsert
public class ServicePersonCostEntity extends DataEntity<ServicePersonCostEntity> implements Serializable {
    private static final long serialVersionUID = 1L;
    // 人員工號
    private String empNo;
    // 人員姓名
    private String empName;
    // 班別編碼
    private String shiftNo;
    // 班別名稱
    private String shiftName;
    // 崗位編碼
    private String postNo;
    // 崗位名稱
    private String postName;
    // 法人
    private String legal;
    // 事業群
    private String dptQun;
    // 部門
    private String dptBu;
    // 費用代碼
    private String costId;
    // 保安公司
    private String company;
    // 入職日期（警衛人員合格日期）
    private Date entryDate;
    // 考试合格日期
    private Date passDate;
    // 排班週期開始日期
    private Date periodBeginDate;
    // 排班週期結束日期
    private Date periodEndDate;
    // 排班週期內稽核正常天數
    private Integer dayCount;
    // 正常上班費用
    private Double normalCost;
    // 培訓費用
    private Double traineeCost;
    // 加班費用
    private Double overtimeCost;
    // 總費用
    private Double totalCost;

    @Transient
    public static LinkedHashMap<String, String> excelFieldMap() {
        LinkedHashMap<String, String> fieldMap = new LinkedHashMap<String, String>();
        fieldMap.put("empNo", "工號");
        fieldMap.put("empName", "姓名");
        fieldMap.put("postName", "崗位名稱");
        fieldMap.put("shiftName", "班別");
        fieldMap.put("dptQun", "事業群");
        fieldMap.put("legal", "法人");
        fieldMap.put("dptBu", "部門");
        fieldMap.put("costId", "費用代碼");
        fieldMap.put("entryDate", "入職日期");
        fieldMap.put("passDate", "合格日期");
        fieldMap.put("periodBeginDate", "支付時期（起）");
        fieldMap.put("periodEndDate", "支付時期（止）");
        fieldMap.put("dayCount", "工作天數");
        fieldMap.put("normalCost", "應發薪資");
        fieldMap.put("traineeCost", "培訓費用");
        fieldMap.put("overtimeCost", "加班費");
        fieldMap.put("totalCost", "實發薪資");
        return fieldMap;
    }

    @Column(name = "EMP_NO", nullable = false, length = 20)
    public String getEmpNo() {
        return empNo;
    }

    public void setEmpNo(String empNo) {
        this.empNo = empNo;
    }

    @Column(name = "EMP_NAME", nullable = false, length = 20)
    public String getEmpName() {
        return empName;
    }

    public void setEmpName(String empName) {
        this.empName = empName;
    }

    @Column(name = "SHIFT_NO", nullable = false, length = 20)
    public String getShiftNo() {
        return shiftNo;
    }

    public void setShiftNo(String shiftNo) {
        this.shiftNo = shiftNo;
    }

    @Column(name = "SHIFT_NAME", nullable = false, length = 20)
    public String getShiftName() {
        return shiftName;
    }

    public void setShiftName(String shiftName) {
        this.shiftName = shiftName;
    }

    @Column(name = "POST_NO", nullable = false, length = 20)
    public String getPostNo() {
        return postNo;
    }

    public void setPostNo(String postNo) {
        this.postNo = postNo;
    }

    @Column(name = "POST_NAME", nullable = false, length = 20)
    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }

    @Column(name = "LEGAL_PERSON", nullable = false, length = 20)
    public String getLegal() {
        return legal;
    }

    public void setLegal(String legal) {
        this.legal = legal;
    }

    @Column(name = "DPT_QUN", nullable = false, length = 20)
    public String getDptQun() {
        return dptQun;
    }

    public void setDptQun(String dptQun) {
        this.dptQun = dptQun;
    }

    @Column(name = "DPT_BU", nullable = false, length = 20)
    public String getDptBu() {
        return dptBu;
    }

    public void setDptBu(String dptBu) {
        this.dptBu = dptBu;
    }

    @Column(name = "COST_ID", nullable = false, length = 20)
    public String getCostId() {
        return costId;
    }

    public void setCostId(String costId) {
        this.costId = costId;
    }

    @Column(name = "COMPANY", nullable = false, length = 20)
    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    @Column(name = "ENTRY_DATE", nullable = false, length = 20)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    public Date getEntryDate() {
        return entryDate;
    }

    public void setEntryDate(Date entryDate) {
        this.entryDate = entryDate;
    }

    @Column(name = "PASS_DATE", nullable = false, length = 20)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    public Date getPassDate() {
        return passDate;
    }

    public void setPassDate(Date passDate) {
        this.passDate = passDate;
    }

    @Column(name = "PERIOD_BEGIN_DATE", nullable = false, length = 20)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    public Date getPeriodBeginDate() {
        return periodBeginDate;
    }

    public void setPeriodBeginDate(Date periodBeginDate) {
        this.periodBeginDate = periodBeginDate;
    }

    @Column(name = "PERIOD_END_DATE", nullable = false, length = 20)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    public Date getPeriodEndDate() {
        return periodEndDate;
    }

    public void setPeriodEndDate(Date periodEndDate) {
        this.periodEndDate = periodEndDate;
    }

    @Column(name = "DAY_COUNT", nullable = false, length = 20)
    public Integer getDayCount() {
        return dayCount;
    }

    public void setDayCount(Integer dayCount) {
        this.dayCount = dayCount;
    }

    @Column(name = "NORMAL_COST", nullable = false, length = 20)
    public Double getNormalCost() {
        return normalCost;
    }

    public void setNormalCost(Double normalCost) {
        this.normalCost = normalCost;
    }

    @Column(name = "TRAINEE_COST", nullable = false, length = 20)
    public Double getTraineeCost() {
        return traineeCost;
    }

    public void setTraineeCost(Double traineeCost) {
        this.traineeCost = traineeCost;
    }

    @Column(name = "OVERTIME_COST", nullable = false, length = 20)
    public Double getOvertimeCost() {
        return overtimeCost;
    }

    public void setOvertimeCost(Double overtimeCost) {
        this.overtimeCost = overtimeCost;
    }

    @Column(name = "TOTAL_COST", nullable = false, length = 20)
    public Double getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(Double totalCost) {
        this.totalCost = totalCost;
    }
}
