package com.foxconn.ipebg.audit.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * 代碼基本表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-01-23 09:32:47
 */
@Entity
@Table(name = "E_BS_CODE")
@DynamicUpdate
@DynamicInsert
public class EBsCodeEntity extends DataEntity<EBsCodeEntity> implements
		Serializable {
	private static final long serialVersionUID = 1L;

	// 代碼編號
	private Integer codeId;
	// 代碼名稱
	private String codeNam;
	// 代碼類別
	private String codeTyp;
	// 代碼說明
	private String codeMemo;
	// 是否必填
	private String required;
	/**
	 * 设置：代碼編號
	 */
	public void setCodeId(Integer codeId) {
		this.codeId = codeId;
	}

	/**
	 * 获取：代碼編號
	 */

	@Column(name = "CODE_ID", nullable = false, length = 20)
	public Integer getCodeId() {
		return codeId;
	}

	/**
	 * 设置：代碼名稱
	 */
	public void setCodeNam(String codeNam) {
		this.codeNam = codeNam;
	}

	/**
	 * 获取：代碼名稱
	 */

	@Column(name = "CODE_NAM", nullable = false, length = 20)
	public String getCodeNam() {
		return codeNam;
	}

	/**
	 * 设置：代碼類別
	 */
	public void setCodeTyp(String codeTyp) {
		this.codeTyp = codeTyp;
	}

	/**
	 * 获取：代碼類別
	 */

	@Column(name = "CODE_TYP", nullable = false, length = 20)
	public String getCodeTyp() {
		return codeTyp;
	}

	/**
	 * 设置：代碼說明
	 */
	public void setCodeMemo(String codeMemo) {
		this.codeMemo = codeMemo;
	}

	/**
	 * 获取：代碼說明
	 */

	@Column(name = "CODE_MEMO", nullable = false, length = 20)
	public String getCodeMemo() {
		return codeMemo;
	}

	/**
	 * 获取：是否必填
	 */
	public String getRequired() {
		return required;
	}

	/**
	 * 设置：是否必填
	 */
	@Column(name = "required", nullable = false, length = 10)
	public void setRequired(String required) {
		this.required = required;
	}
}
