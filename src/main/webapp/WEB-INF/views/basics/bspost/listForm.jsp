<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>崗位明細表</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>

<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/bspost/${action}" method="post">
	<table class="formTable">
		<tr>
			<td>
				<input type="hidden" name="ids" value="${bsPost.id}"/>
			</td>
		</tr>
		<tr>
			<td>主表任務單號：</td>
			<td>
				<input id="serialno" name="serialno" class="easyui-validatebox" data-options="width: 150"
					   value="${bsPost.serialno }" readonly />
			</td>
		</tr>
		<tr>
			<td>編號：</td>
			<td>
				<input id="recno" name="recno" class="easyui-validatebox" data-options="width: 150"
					   value="${bsPost.recno }" readonly />
			</td>
		</tr>
		<tr>
			<td>區域：</td>
			<td>
				<input id="area" name="area" class="easyui-validatebox" data-options="width: 150"
					   value="${bsPost.area }" readonly />
			</td>
		</tr>
		<tr>
			<td>棟：</td>
			<td>
				<input id="block" name="block" class="easyui-validatebox" data-options="width: 150"
					   value="${bsPost.block }" readonly />
			</td>
		</tr>
		<tr>
			<td>層：</td>
			<td>
				<input id="floor" name="floor" class="easyui-validatebox" data-options="width: 150"
					   value="${bsPost.floor }" readonly />
			</td>
		</tr>
		<tr>
			<td>方位：</td>
			<td>
				<input id="position" name="position" class="easyui-validatebox" data-options="width: 150"
					   value="${bsPost.position }" readonly/>
			</td>
		</tr>
		<tr>
			<td>崗位類別：</td>
			<td>
				<input id="postType" name="postType" class="easyui-validatebox" data-options="width: 150"
					   value="${bsPost.postType }" readonly />
			</td>
		</tr>
		<tr>
			<td>班制：</td>
			<td>
				<input id="postShift" name="postShift" class="easyui-validatebox" data-options="width: 150"
					   value="${bsPost.postShift }" readonly />
			</td>
		</tr>
		<tr>
			<td>崗位名稱：</td>
			<td>
				<input id="postName" name="postName" class="easyui-validatebox" data-options="width: 150"
					   value="${bsPost.postName }"/>
			</td>
		</tr>
		<tr>
			<td>人數(不含調休)：</td>
			<td>
				<input id="postPerNu" name="postPerNu" class="easyui-validatebox" data-options="width: 150"
					   value="${bsPost.postPerNu }"/>
			</td>
		</tr>
		<tr>
			<td>需求時間-開始時間：</td>
			<td>
				<input id="postStartDate" name="postStartDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
					   data-options="width: 150" disabled="disabled"
					   value="<fmt:formatDate value="${bsPost.postStartDate}"/>"/>
			</td>
		</tr>
		<tr>
			<td>派駐保安公司：</td>
			<td>
				<input id="securityCom" name="securityCom" class="easyui-combobox" data-options="panelHeight:'auto',valueField:'value', textField:'label',
	   				url: '${ctx}/system/user/userSecurityCom',width: 150" value="${bsPost.securityCom }"/>
			</td>
		</tr>
		<tr>
			<td>生效時間：</td>
			<td>
				<input id="postEffectDate" name="postEffectDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
					   data-options="width: 150" disabled="disabled"
					   value="<fmt:formatDate value="${bsPost.postEffectDate}"/>"/>
			</td>
		</tr>
		<tr style="display: none">
			<td>
				<input id="createBy" name="createBy" class="easyui-validatebox" data-options="width: 150"
					   value="${bsPost.createBy }" readonly />
				<input id="createDate" name="createDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
					   data-options="width: 160" disabled="disabled"
					   value="<fmt:formatDate value="${bsPost.createDate}"/>"/>
				<input id="updateBy" name="updateBy" class="easyui-validatebox" data-options="width: 150"
					   value="${bsPost.updateBy }" readonly />
				<input id="updateDate" name="updateDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
					   data-options="width: 160" disabled="disabled"
					   value="<fmt:formatDate value="${bsPost.updateDate}"/>"/>
				<input id="delFlag" name="delFlag" type="hidden" class="easyui-validatebox" data-options="width: 150"
					   value="${bsPost.delFlag }"/>
				<input id="dptId" name="dptId" type="hidden" class="easyui-validatebox" data-options="width: 150"
					   value="${bsPost.dptId }"/>
			</td>
		</tr>
	</table>
</form>
</div>
<script src='${ctx}/static/js/basics/bspost.js?"+Math.random()"'></script>
<script type="text/javascript">
   //提交表单
$('#mainform').form({
    onSubmit: function(){
        var isValid = $(this).form('validate');
        return isValid;	// 返回false终止表单提交
    },
    success:function(data){ 
        successTip(data,dg,d);
    }
});
</script>
</body>
</html>