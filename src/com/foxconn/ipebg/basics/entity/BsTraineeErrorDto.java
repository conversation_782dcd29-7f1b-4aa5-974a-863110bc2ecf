package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import java.util.Date;

public class BsTraineeErrorDto implements Serializable {
	private static final long serialVersionUID = 1L;
	// 工號
	private String empNo;
	
	// 姓名
	private String empName;
	// 身份證
	private String psnId;
	// 硬卡號
	private String cardId;
	// 保安公司
	private String company;
	// 性別（0.男1.女）
	private String status;
	// 失敗原因
	private String errorLog;

	public String getEmpNo() {
		return empNo;
	}
	public void setEmpNo(String empNo) {
		this.empNo = empNo;
	}
	public String getEmpName() {
		return empName;
	}
	public void setEmpName(String empName) {
		this.empName = empName;
	}
	public String getPsnId() {
		return psnId;
	}
	public void setPsnId(String psnId) {
		this.psnId = psnId;
	}
	public String getCardId() {
		return cardId;
	}
	public void setCardId(String cardId) {
		this.cardId = cardId;
	}
	public String getCompany() {
		return company;
	}
	public void setCompany(String company) {
		this.company = company;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getErrorLog() {
		return errorLog;
	}
	public void setErrorLog(String errorLog) {
		this.errorLog = errorLog;
	}
}
