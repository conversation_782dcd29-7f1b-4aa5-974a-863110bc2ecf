package com.foxconn.ipebg.basics.service;

import cn.hutool.core.date.DateUtil;
import com.foxconn.ipebg.basics.entity.BsArrangePostPersonEntity;
import com.foxconn.ipebg.basics.dao.BsArrangePostPersonDao;
import com.foxconn.ipebg.basics.entity.BsPostPatchEntity;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import com.foxconn.ipebg.system.entity.Permission;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.hibernate.type.StandardBasicTypes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.Dict;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WorkFlowEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.system.service.DictService;

import java.util.*;

/**
 * 崗位人員表
 *
 * <AUTHOR>
 * @email <EMAIL> 
 * @date 2019-09-24 14:26:36
 */
@Service
@Transactional(readOnly=true)
public class  BsArrangePostPersonService extends BaseBusinessService<BsArrangePostPersonEntity, String>{
    @Autowired
    private BsArrangePostPersonDao bsArrangePostPersonDao;

    @Autowired
    private DictService dictService;

    @Autowired
    private BsPostPatchService patchService;

    @Override
    public HibernateDao<BsArrangePostPersonEntity, String> getEntityDao() {
        return bsArrangePostPersonDao;
    }

    public BsArrangePostPersonEntity findBySerialno(String serialno) {
        return this.bsArrangePostPersonDao.findUniqueBy("serialno", serialno);
    }

    /**
     * 排班列表分頁查詢
     * @param page
     * @param params
     * @return
     */
    public Map<String,Object> listPage(Page page, Map<String, String> params) {
        //數據
        StringBuffer sql= new StringBuffer("SELECT b.arrange_group_id, b.arrange_date   " +
                "          FROM (SELECT a.arrange_group_id, a.arrange_date   " +
                "                  FROM bs_arrange_post_person a   " +
                "                 where 1 = 1   ");
        if(StringUtils.isNotBlank(params.get("arrangeGroupId"))){
            sql.append("                   and a.arrange_group_id = '"+params.get("arrangeGroupId")+"'  " );
        }
        if(StringUtils.isNotBlank(params.get("startDate")) && StringUtils.isNotBlank(params.get("endDate"))){
            sql.append("                   and a.arrange_date between to_date('"+params.get("startDate")+"','yyyy-mm-dd') and  to_date('"+params.get("endDate")+"','yyyy-mm-dd')  " );
        }
        sql.append("                 group by a.arrange_group_id, a.arrange_date  " +
                "                 order by a.arrange_date desc  " +
                "                ) b  ") ;
        if(page!=null){
            sql.append("LIMIT " + (page.getEnd() - page.getFirst()) + " OFFSET " + (page.getFirst() - 1));
        }else{
            sql.append("                ) c ");
        }

        List list = this.bsArrangePostPersonDao.createSQLQuery(sql.toString()).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();

        //總數
        StringBuffer countSql=new StringBuffer("  SELECT count(*) FROM (   SELECT count(a.id)   " +
                "                  FROM bs_arrange_post_person a   " +
                "                 where 1 = 1   " );
        if(StringUtils.isNotBlank(params.get("trainingPeriodNo"))){
            sql.append("                   and a.arrange_group_id = '"+params.get("arrangeGroupId")+"'  " );
        }
        if(StringUtils.isNotBlank(params.get("startDate")) && StringUtils.isNotBlank(params.get("endDate"))){
            sql.append("                   and a.arrange_date between to_date('"+params.get("startDate")+"','yyyy-mm-dd') and  to_date('"+params.get("endDate")+"','yyyy-mm-dd')  " );
        }
        countSql.append(       "         group by a.arrange_group_id, a.arrange_date  ) AS counts ");

        Integer count = Integer.valueOf(this.bsArrangePostPersonDao.createSQLQuery(countSql.toString()).uniqueResult().toString());
        //返回值
        Map<String,Object> result=new HashMap<>();
        result.put("rows",list);
        result.put("total",count);
        return result;
    }

    public List<BsArrangePostPersonEntity> listNormalTrainee(Map<String, String> params) {
        StringBuffer hql=new StringBuffer("select a.id, a.create_by, a.create_date, a.update_by, " +
            "a.update_date, a.recno, a.post_name, a.emp_no, a.emp_name, a.shift_no, " +
            "a.arrange_date, a.arrange_group_id, a.arrange_group_name, " +
            "  case when c.emp_no is not null then c.del_flag else '1' end as del_flag " +
            "from bs_arrange_post_person a " +
            " inner join bs_abnormal_personnel b on" +
            " a.recno = b.post_recno " +
            " and a.arrange_date = b.shift_date " +
            " and a.emp_no = b.emp_no " +
            " and a.shift_no = b.shift_no " +
            " and b.abnormal_state = '1' " +
            " left outer join per_info c on a.emp_no = c.emp_no " +
            " where 1 = 1 "
        );
        if(StringUtils.isNotBlank(params.get("recno"))){
            hql.append("    and a.recno= '" + params.get("recno")+"'  ");
        }
        if(StringUtils.isNotBlank(params.get("shiftNo"))){
            hql.append("    and a.shift_no=  '" + params.get("shiftNo")+"'    ");
        }
        if(StringUtils.isNotBlank(params.get("arrangeDate"))){
            if (params.get("arrangeDate").length() > 8) {
                hql.append("    and a.arrange_date= to_date('"+ params.get("arrangeDate")+ "','yyyy-mm-dd')" );
            } else {
                hql.append("    and a.arrange_date= to_date('"+ params.get("arrangeDate")+ "','yyyymmdd')" );
            }
        }
        List<BsArrangePostPersonEntity> result = this.bsArrangePostPersonDao.createSQLQuery(hql.toString()).addEntity(BsArrangePostPersonEntity.class).list();
        return result;
    }

    public List<Map<String, String>> listAllGroup() {
        String sql = "SELECT * FROM bs_arrange_group order by create_by desc";
        SQLQuery query = bsArrangePostPersonDao.createSQLQuery(sql);
        query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        return query.list();
    }

    // 警衛排班中的崗位
    public List<Map<String, Object>> listGroupPost(Map<String, String> params) {
        String arrangeGroupId = params.get("arrangeGroupId");
        String arrangeDate = params.get("arrangeDate");
        if (StringUtils.isBlank(arrangeGroupId) || StringUtils.isBlank(arrangeDate)) {
            return null;
        }
        Date date = DateUtil.parse(arrangeDate, "yyyy-MM-dd");
        String sql = "SELECT b.* " +
                "  FROM bs_arrange_group_post a, bs_post b " +
                " where a.recno = b.recno " +
                " and a.arrange_group_id = '"+arrangeGroupId+"' " +
                " order by b.post_name ";
        SQLQuery query = bsArrangePostPersonDao.createSQLQuery(sql);
        query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Map<String, Object>> list = query.list();
        for (Map<String, Object> map : list) {
            String recno = (String) map.get("recno");
            BsPostPatchEntity patch = patchService.patchEntityByDate(recno, date);
            if (patch != null) {
                // 更新臨時增撤崗的崗位人數和班制
                map.put("post_shift", patch.getPostShift());
                map.put("post_per_nu", patch.getPostPerNu());
            }
            logger.debug(String.valueOf(map));
        }
        return list;
    }

    /**
     * 違反六休一人員
     *
     * @return
     */
    public List<Map<String, String>> overSixOnePer(Map<String, String> params) {
        if (StringUtils.isBlank(params.get("arrangeDate")) || StringUtils.isBlank(params.get("arrangeGroupId"))) {
            return null;
        }
        int maxContinueWorkDays = Integer.parseInt(dictService.getDictByLabelAndType("max_continue_work_days", "arrange_setting").getValue());
        if (maxContinueWorkDays > 0) {
            StringBuffer sql = new StringBuffer();

            //sql: work six days have a rest
            for (int i = 0; i <= maxContinueWorkDays; i++) {
                if (i != 0) {
                    sql.append("   union all  ");
                }
                sql.append("   SELECT a.emp_no ,max(a.emp_name) emp_Name  " +
                        "   FROM bs_arrange_post_person a  " +
                        "   WHERE   " +
                        //"   a.arrange_group_id =  '" + params.get("arrangeGroupId") + "'  " +
                        "    a.arrange_date between  to_date('" + params.get("arrangeDate") + "', 'yyyy-mm-dd')-" + (maxContinueWorkDays - i) + " and to_date('" + params.get("arrangeDate") + "', 'yyyy-mm-dd')+ " + i +
                        "   group by a.emp_no " +
                        "   having count(a.id)>= "+maxContinueWorkDays+"  ");
            }

            sql.append(" order by emp_no");

            SQLQuery query = bsArrangePostPersonDao.createSQLQuery(sql.toString());
            query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
            return query.list();
        } else {
            return new ArrayList<Map<String, String>>();
        }
    }


    /**
     * 違反白班連排超時
     *
     * @return
     */
    public List<Map<String, String>> dayOverTimePer(Map<String, String> params) {
        if (StringUtils.isBlank(params.get("arrangeDate")) || StringUtils.isBlank(params.get("arrangeGroupId")) ) {
            return null;
        }
        String allowContinueWork = dictService.getDictByLabelAndType("allow_continue_work", "arrange_setting").getValue();
        if (allowContinueWork.equalsIgnoreCase("Y")) {
            return new ArrayList<Map<String, String>>();
        } else {
            String sql="SELECT a.emp_no ,a.emp_name " +
                    "  FROM bs_arrange_post_person a " +
                    " where a.arrange_group_id = '"+params.get("arrangeGroupId")+"' " +
                    "   and a.arrange_date = to_date('"+params.get("arrangeDate")+"', 'yyyy-mm-dd')-1 " +
                    "   and a.shift_no='B' " +
                    " order by a.emp_no ";

            SQLQuery query = bsArrangePostPersonDao.createSQLQuery(sql.toString());
            query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
            return query.list();
        }
    }

    /**
     * 違反夜班連排超時
     *
     * @return
     */
    public List<Map<String, String>> nightOverTimePer(Map<String, String> params) {
        if (StringUtils.isBlank(params.get("arrangeDate")) || StringUtils.isBlank(params.get("arrangeGroupId")) ) {
            return null;
        }
        String allowContinueWork = dictService.getDictByLabelAndType("allow_continue_work", "arrange_setting").getValue();
        if (allowContinueWork.equalsIgnoreCase("Y")) {
            return new ArrayList<Map<String, String>>();
        } else {
            String sql="SELECT a.emp_no,a.emp_name " +
                    "  FROM bs_arrange_post_person a " +
                    " where a.arrange_group_id = '"+params.get("arrangeGroupId")+"' " +
                    "   and a.arrange_date = to_date('"+params.get("arrangeDate")+"', 'yyyy-mm-dd')+1 " +
                    "   and a.shift_no='A' " +
                    " order by a.emp_no ";

            SQLQuery query = bsArrangePostPersonDao.createSQLQuery(sql.toString());
            query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
            return query.list();
        }
    }

    /**
     * 群組所有人員
     * @param params
     * @return
     */
    public List<Map<String, String>> listAllGroupPer(Map<String, String> params) {
        if ( StringUtils.isBlank(params.get("arrangeGroupId")) ) {
            return null;
        }

        String sql="SELECT b.emp_no ,b.emp_name  FROM  bs_arrange_group_person a,per_info b where a.emp_no=b.emp_no and  a.arrange_group_id='"+params.get("arrangeGroupId")+"' and b.del_flag = '0' order by b.emp_no ";

        SQLQuery query = bsArrangePostPersonDao.createSQLQuery(sql);
        query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        return query.list();
    }

    /**
     * 日排班人員
     * @param params
     * @return
     */
    public List<Map<String, String>> listArrangePer(Map<String, String> params) {
        if (StringUtils.isBlank(params.get("arrangeDate")) || StringUtils.isBlank(params.get("arrangeGroupId")) ) {
            return null;
        }
        String sql="SELECT * " +
                "  FROM bs_arrange_post_person a " +
                " where a.arrange_group_id = '"+params.get("arrangeGroupId")+"' " +
                "   and a.arrange_date = to_date('"+params.get("arrangeDate")+"', 'yyyy-mm-dd')" +
                " order by a.emp_no";
        SQLQuery query = bsArrangePostPersonDao.createSQLQuery(sql);
        query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        return query.list();
    }

    @Transactional(readOnly=false)
    public int deleteBy(String arrangeGroupId,Date arrangeDate) {
        String hql="delete from BsArrangePostPersonEntity where arrangeGroupId= ?0 and arrangeDate= ?1";
        return bsArrangePostPersonDao.batchExecute(hql,arrangeGroupId,arrangeDate);
    }
}
