package com.foxconn.ipebg.basics.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Date;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.basics.entity.BsArrangeGroupPersonEntity;
import com.foxconn.ipebg.basics.entity.BsArrangeGroupPostEntity;
import com.foxconn.ipebg.basics.service.BsArrangeGroupPersonService;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;
import com.foxconn.ipebg.system.entity.User;

/**
 * 排班群組匹配合格人員
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-09-11 10:28:22
 */
@Controller
@RequestMapping("bsarrangegroupperson")
public class BsArrangeGroupPersonController extends BaseController {

    @Autowired
    private BsArrangeGroupPersonService bsArrangeGroupPersonService;
   
    /**
      * 方法描述: 列表信息
      * @Author: S6113712
      * @CreateDate:   2019-09-11 10:28:22
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:bsarrangegroupperson:list")
    public String list() {
        //查询列表数据
        return "basics/bsarrangegroupperson/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6113712
      * @CreateDate:   2019-09-11 10:28:22
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:bsarrangegroupperson:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<BsArrangeGroupPersonEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
//        page.setOrderBy("createDate");
//  		page.setOrder("desc");
        User user = UserUtil.getCurrentUser();
        if (user != null) {
            // 按保安公司查询
            PropertyFilter filter = new PropertyFilter("INS_company", user.getSecurityComs());
            filters.add(filter);
        }
        page = bsArrangeGroupPersonService.search(page, filters);
        return getEasyUIData(page);
    }
    /**
     * 方法描述: 保存
     * @Author: S6113712
     * @CreateDate:   2019-09-11 10:28:22
     * @Return
     **/
   
    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("basics:bsarrangegroupperson:add")
	@ResponseBody
	public String create(HttpServletRequest request, Model model) {

		String arrangeGroupId = request.getParameter("arrangeGroupId");
		String empNos = request.getParameter("empNos");

		String[] empNoArray = empNos.split(",");

		for (int i = 0; i < empNoArray.length; i++) {
			BsArrangeGroupPersonEntity bsArrangeGroupPerson = new BsArrangeGroupPersonEntity();

			bsArrangeGroupPerson.setEmpNo(empNoArray[i]);
			bsArrangeGroupPerson.setArrangeGroupId(arrangeGroupId);
			bsArrangeGroupPerson.setNewRecord(true);
			bsArrangeGroupPerson.setCreateDate(new Date());
			bsArrangeGroupPersonService.save(bsArrangeGroupPerson);
		}
		return "success";

	}
    
    /**
      * 方法描述: 添加跳轉
      * @Author: S6113712
      * @CreateDate:   2019-09-11 10:28:22
      * @Return
      **/

    @RequiresPermissions("basics:bsarrangegroupperson:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
//        model.addAttribute("bsArrangeGroupPerson", new BsArrangeGroupPersonEntity());
//        model.addAttribute("action", "create");

        return "basics/bsarrangegroupperson/addGroupPersonList";
    }
    
     /**
     * 方法描述: 修改跳轉
     * @Author: S6113712
     * @CreateDate:   2018-10-06 11:04:39
     * @Return
     **/

   @RequiresPermissions("basics:bsarrangegroupperson:update")
   @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
   public String updateForm(@PathVariable("id") String id, Model model) {
       model.addAttribute("bsArrangeGroupPerson", bsArrangeGroupPersonService.get(id));
       model.addAttribute("action", "update");
      return "basics/bsarrangegroupperson/listForm";
   }
   
	/**
	 * 方法描述: 修改
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-11 10:55:00
	 * @Return
	 **/

   @RequestMapping(value = "update", method = RequestMethod.POST)
   @RequiresPermissions("basics:bsarrangegroupperson:update")
   @ResponseBody
   public String update(@Valid BsArrangeGroupPersonEntity bsArrangeGroupPerson,@RequestParam(value = "ids") String ids) {
	   bsArrangeGroupPerson.setId(ids);
	   bsArrangeGroupPerson.setUpdateDate(new Date());
	   bsArrangeGroupPersonService.update(bsArrangeGroupPerson);
	   
       return "success";
   }
    /**
      * 方法描述: 根據主鍵刪除
      * @Author: S6113712
      * @CreateDate:   2019-09-11 10:28:22
      * @Return
      **/

    @RequestMapping("delete/{ids}")
    @RequiresPermissions("basics:bsarrangegroupperson:delete")
    @ResponseBody
    public String delete(@PathVariable("ids") String ids) {
    	
    	String[] idArray =ids.split(",");
    	for (int i=0;i<idArray.length;i++)
    	{
    		bsArrangeGroupPersonService.delete(idArray[i]);
    	}    
        return "success";

    }
    /**
      * 导出excel
      * @Author: S6113712
      * @CreateDate:   2019-09-11 10:28:22
      * @Return
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {}
}
