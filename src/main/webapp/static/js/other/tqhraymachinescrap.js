var dg;
var d;
$(function () {
    dg = $('#dg').datagrid({
        method: "get",
        url: ctx+'/tqhraymachinescrap/list',
        fit: true,
        fitColumns: true,
        border: false,
        idField: 'id',
        striped: true,
        pagination: true,
        rownumbers: true,
        pageNumber: 1,
        pageSize: 20,
        pageList: [10, 20, 30, 40, 50],
        singleSelect: true,
        columns: [[
            {field: 'id', title: '主鍵', hidden: true},
            {field: 'serialno', title: '任務編碼', sortable: true, width: 150, formatter: operation},
            {field: 'dealno', title: '承辦人工號', sortable: true, width: 100},
            {field: 'dealname', title: '承辦人名稱', sortable: true, width: 100},
            {field: 'dealdeptno', title: '單位代碼', sortable: true, width: 100},
            {field: 'dealdeptname', title: '單位名稱', sortable: true, width: 100},
            {field: 'createtime', title: '提報日期', sortable: true, width: 100},
            {field: 'workstatus', title: '表單狀態', sortable: true, width: 100},
            {field: 'nodeName', title: '當前簽核節點', sortable: true, width: 100},
            {field: 'auditUser', title: '當前簽核人', sortable: true, width: 100},
            {field: 'complettime', title: '簽核完成時間', sortable: true, width: 100}
        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        toolbar: '#tb'
    });
    $(":input").removeClass("validatebox-invalid");
    //承諾書模板下載
    // $("#btnCommitmentTpl").click(commitmentTpl);
    //批量上傳模板下載
    // $("#btnBatchImportTpl").click(batchImportTpl);

    //默認顯示當前日期
    //$("#environment_endtime").val(new Date().format("yyyy-mm-dd"));
//添加一筆
    $("#addScrapItem").click(function(){

        var len=parseInt($("#rayMachineScrapItemTableIndex").val());
        var b = $("#rayMachineScrapItemTable tr").length;
        $("#rayMachineScrapItemTable").append("<tr align='center' id='rayMachineScrapItemTr"+len+"'>"
            +"<td>"+b+"</td>"
            +"<td><input id='machineName"+len+"' name='tQhRayMachineScrapItems["+(len-1)+"].machineName' class='easyui-validatebox' data-options='required:true' style='width:80px;'/></td>"
            +"<td><input id='manufacturer"+len+"' name='tQhRayMachineScrapItems["+(len-1)+"].manufacturer' class='easyui-validatebox' data-options='required:true' style='width:80px;'/></td>"
            +"<td><input id='machineModel"+len+"' name='tQhRayMachineScrapItems["+(len-1)+"].machineModel' class='easyui-validatebox' data-options='required:true' style='width:80px;'/></td>"
            +"<td><input id='dateOfProduction"+len+"' name='tQhRayMachineScrapItems["+(len-1)+"].dateOfProduction' class='easyui-my97' datefmt='yyyy-MM-dd' data-options='width: 100,required:true'/></td>"
            +"<td><input id='usesFor"+len+"' name='tQhRayMachineScrapItems["+(len-1)+"].usesFor' class='easyui-validatebox' data-options='required:true' style='width:80px;'/></td>"
            +"<td><input id='maxVoltage"+len+"' name='tQhRayMachineScrapItems["+(len-1)+"].maxVoltage' class='easyui-validatebox' data-options='required:true' style='width:80px;'/></td>"
            +"<td><input id='minCurrent"+len+"' name='tQhRayMachineScrapItems["+(len-1)+"].minCurrent' class='easyui-validatebox' data-options='required:true' style='width:80px;'/></td>"
            +"<td><input id='rayType"+len+"' name='tQhRayMachineScrapItems["+(len-1)+"].rayType' class='easyui-validatebox' data-options='required:true' style='width:80px;'/></td>"
            +"<td><input id='rayOption"+len+"' name='tQhRayMachineScrapItems["+(len-1)+"].rayOption' class=\"easyui-combobox rayOptionitemclass\" data-options=\"panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadRayOptionType("+len+");}\" style='width:80px;'/></td>"
            +"<td><input id='scrapDate"+len+"' name='tQhRayMachineScrapItems["+(len-1)+"].scrapDate' class='easyui-my97' datefmt='yyyy-MM-dd' minDate='%y-%M-%d' data-options='width: 100,required:true'/></td>"
            +"<td><input id='reason"+len+"' name='tQhRayMachineScrapItems["+(len-1)+"].reason' class='easyui-validatebox' data-options='required:true' style='width:100px;'/></td>"
            +"<td><input id='rayWhere"+len+"' name='tQhRayMachineScrapItems["+(len-1)+"].rayWhere' class='easyui-validatebox' data-options='required:true' style='width:100px;'/></td>"
            +"<td><input type='image' src='"+ctx+"/static/images/deleteRow.png' onclick='deltr("+len+");return false;'/></td>"+"</tr>");
        $.parser.parse($("#rayMachineScrapItemTr"+len));
        $("#rayMachineScrapItemTableIndex").val(len+1);
        $("#machineName"+len).val('');
        $("#manufacturer"+len).val('');
        $("#machineModel"+len).val('');
        $("#dateOfProduction"+len).val('');
        $("#usesFor"+len).val('');
        $("#maxVoltage"+len).val('');
        $("#minCurrent"+len).val('');
        $("#rayType"+len).val('');
        //$("#rayOption" + len).combobox('setValue','請選擇');
        //$("#scrapDate"+len).val('');
        $("#reason"+len).val('');
        $("#rayWhere"+len).val('');
        $("#batchImport").linkbutton('disable');
        // loadRayOptionType();
    });
});
//點擊添加一筆射線裝置加載設備類別
function loadRayOptionType(flag) {
    $.ajax({
        url: ctx+"/system/dict/getDictByType/ray_option",
        type: "GET",
        success: function (data) {
            var themecombo2 = [{'label': '請選擇', 'value': ''}];
            for (var i = 0; i < data.length; i++) {
                themecombo2.push({"label": data[i].label, "value": data[i].value});
            }
            // $(".rayOptionitemclass").combobox("loadData", themecombo2);
            if (null != flag) {
                //重新載入所有明細
                $("#rayOption" + flag).combobox("loadData", themecombo2);
                // $(".rayOptionitemclass").combobox("loadData", themecombo2);
            } else {

                var len = $("#rayMachineScrapItemTable tr").length - 1;
                // for (var i = 1; i <= len; i++) {
                $("#rayOption" + len).combobox("loadData", themecombo2);
                // }
            }
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });

}
//刪除一筆
function deltr(index) {
    // debugger;

    $("#rayMachineScrapItemTr" + index).remove();
    var b = $("#rayMachineScrapItemTable tr").length;
    for (var i = 1 ; i <= b; i++) {
        $("#rayMachineScrapItemTable").find("tr").eq(i).find("td").eq(0).html(i);
        $("#rayMachineScrapItemTable").find("tr").eq(i).find("td").eq(1).find("input[name^='tQhRayMachineScrapItems']").attr("name","tQhRayMachineScrapItems["+(i-1) +"].machineName");
        $("#rayMachineScrapItemTable").find("tr").eq(i).find("td").eq(2).find("input[name^='tQhRayMachineScrapItems']").attr("name","tQhRayMachineScrapItems["+(i-1) +"].manufacturer");
        $("#rayMachineScrapItemTable").find("tr").eq(i).find("td").eq(3).find("input[name^='tQhRayMachineScrapItems']").attr("name","tQhRayMachineScrapItems["+(i-1)+"].machineModel");
        $("#rayMachineScrapItemTable").find("tr").eq(i).find("td").eq(4).find("input[name^='tQhRayMachineScrapItems']").attr("name","tQhRayMachineScrapItems["+(i-1) +"].dateOfProduction");
        $("#rayMachineScrapItemTable").find("tr").eq(i).find("td").eq(5).find("input[name^='tQhRayMachineScrapItems']").attr("name","tQhRayMachineScrapItems["+(i-1) +"].usesFor");
        $("#rayMachineScrapItemTable").find("tr").eq(i).find("td").eq(6).find("input[name^='tQhRayMachineScrapItems']").attr("name","tQhRayMachineScrapItems["+(i-1) +"].maxVoltage");
        $("#rayMachineScrapItemTable").find("tr").eq(i).find("td").eq(7).find("input[name^='tQhRayMachineScrapItems']").attr("name","tQhRayMachineScrapItems["+(i-1) +"].minCurrent");
        $("#rayMachineScrapItemTable").find("tr").eq(i).find("td").eq(8).find("input[name^='tQhRayMachineScrapItems']").attr("name","tQhRayMachineScrapItems["+(i-1) +"].rayType");
        $("#rayMachineScrapItemTable").find("tr").eq(i).find("td").eq(9).find("input[name^='tQhRayMachineScrapItems']").attr("name","tQhRayMachineScrapItems["+(i-1) +"].rayOption");
        $("#rayMachineScrapItemTable").find("tr").eq(i).find("td").eq(10).find("input[name^='tQhRayMachineScrapItems']").attr("name","tQhRayMachineScrapItems["+(i-1) +"].scrapDate");
        $("#rayMachineScrapItemTable").find("tr").eq(i).find("td").eq(11).find("input[name^='tQhRayMachineScrapItems']").attr("name","tQhRayMachineScrapItems["+(i-1) +"].reason");
        $("#rayMachineScrapItemTable").find("tr").eq(i).find("td").eq(12).find("input[name^='tQhRayMachineScrapItems']").attr("name","tQhRayMachineScrapItems["+(i-1) +"].rayWhere");

    }
    if (b == 1) {
        $("#rayMachineScrapItemTableIndex").val(1);
        $("#batchImport").linkbutton('enable');
    }
}
function operation(value, row, index) {
    return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('詳情',ctx+'/tqhraymachinescrap/view/" + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
};
//弹窗增加
function add() {
    d = $("#dlg").dialog({
        title: '報停/報廢信息批量導入',
        width: 380,
        height: 380,
        href: ctx+'tqhraymachinescrap/create',
        maximizable: true,
        modal: true,
        buttons: [{
            text: '确认',
            handler: function () {
                $("#mainform").submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

function openBatchImportWin(){
    var optionWin = $("#optionWin").window();

    optionWin.window({
        center : true
    });
    //顯示於畫面中
    optionWin.slideDown();
    //清空optionWin頁面中所有元件的值
    /*     $("#pointForm .easyui-combobox").each(function(){
     $(this).combobox("clear");
     }); */
    $("#batchImportForm").form("clear");
    $("#labelListAddResult").html("");
    $("#downloadError").hide();
    //optionWin.find("input").val("");
    optionWin.window("open");
}
//删除
function del() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function (data) {
        if (data) {
            $.ajax({
                type: 'get',
                url: ctx+"tqhraymachinescrap/delete/" + row.id,
                success: function (data) {
                    successTip(data, dg);
                }
            });
        }
    });
}

//弹窗修改
function upd() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    d = $("#dlg").dialog({
        title: '修改射線裝置報停、報廢表',
        width: 380,
        height: 340,
        href: ctx+'tqhraymachinescrap/update/' + row.id,
        maximizable: true,
        modal: true,
        buttons: [{
            text: '修改',
            handler: function () {
                $('#mainform').submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//创建查询对象并查询
function cx() {
    var obj = $("#searchFrom").serializeObject();
    dg.datagrid('load', obj);
}

//表單進度查詢頁面Excel導出
function exportListExcel() {
    document.location.href = ctx+'/tqhraymachinescrap/downLoad/commitmentTpl';
}
//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("clear");
}

//根據工號查出當前申請人的信息
function queryUserInfo() {
    var empno = $.trim($("#dealno").val().toUpperCase());
    if (empno != null && empno != "") {
        $.ajax({
            url: ctx+'/system/user/getUserInfo/',
            type: 'POST',
            beforeSend: ajaxLoading,
            //dataType: 'json',
            data: {empno: empno},
            success: function (data) {
                ajaxLoadEnd();
                if (!data) {
                    $.messager.alert("溫馨提示", "工號不存在", "error");
                    $('#dealno').val('');
                    $('#dealname').val('');
                    $('#dealdeptno').val('');
                    $('#dealdeptname').val('');
                    $('#dealemail').val('');
                } else {
                    $('#dealname').val(data.empname);
                    $('#dealdeptno').val(data.deptno);
                    $('#dealdeptname').val(data.deptname);
                    $('#dealfactoryid').combobox('setValue', data.factoryid);
                    $('#dealemail').val(data.email);
                }
            }
        });
    }
}
//獲取廠區
$.get(ctx+'/tqhfactoryidconfig/allFactorys/',
    function(result) {
        $("#dealfactoryid").combobox({
            data: result,
            valueField: "factoryid",
            textField: "factoryname",
            editable: false,
            panelHeight: 400,
            loadFilter:function(data){
                data.unshift({factoryid:'',factoryname:'請選擇'});
                return data;
            }
        });
    });
//創建下拉查詢條件
$.ajax({
    url: ctx+"/system/dict/getDictByType/audit_status",
    //dataType:"json",
    type: "GET",
    success: function (data) {
        //绑定第一个下拉框
        $("#qysjzt").combobox({
            data: data,
            valueField: "value",
            textField: "label",
            editable: false,
            panelHeight: 400,
            loadFilter: function (data) {
                data.unshift({value: '', label: '請選擇'});
                return data;
            }
        });
    },
    error: function (error) {
        alert("初始化下拉控件失败");
    }
});
//驗證申請人聯繫電話
function valdApplyTel(obj){
    var phoneL=$.trim($("#dealtel").val()).split("").length;
    var tel = obj.value;
    if(tel!=""){
        var temp = tel;
        var regex  = /^[0-9]{3}\+[0-9]{5}$/;
        var patt1=new RegExp(regex);
        var result = patt1.test(tel);
        if(!result){
            alert("分機格式有誤!");
            obj.value="";
            obj.focus();
            return;
        }
    }
}
//validatebox驗證擴展
// $.extend($.fn.validatebox.defaults.rules, {
//     email: {
//         validator: function (value, param) {
//             var dealemail = $('#dealemail');
//             return valdEmail(dealemail)
//         },
//         message: '郵箱的格式不正確！'
//     }
// });
//驗證郵箱
function valdEmail(obj){
    var email = obj.value;
    var temp = email;
    if(email=="")return false;
    var regex = /\w+([-/+.]\w+)*@(mail.foxconn.com|foxconn.com|fii-foxconn.com)$/;
    var patt1=new RegExp(regex);
    var result = patt1.test(email);
    if(!result){
        alert("郵箱的格式不正確!");
        obj.value="";
        // obj.focus();
        obj.value =temp;
        return false;
    };
}
function valdChkbox() {
    var obj = $("#dealemail")[0];
    return valdEmail(obj);

}

function saveInfo(flag) {
    var isValid = $("#mainform").form('validate');
    if (!isValid) {
        return;
    }
    if (!valdChkbox()) {
        return;
    }
    $.ajax({
        url: ctx+'/tqhraymachinescrap/create/' + flag,
        type: 'POST',
        beforeSend: ajaxLoading,
        //dataType: 'json',
        data: $('#mainform').serialize(),
        success: function (data) {
            ajaxLoadEnd();
            successTip(data, dg, d);
            window.parent.mainpage.mainTabs.closeCurrentTab();
        }
    });
}
//附件上傳
function uploadFile() {
    var attachIds = $.trim($("#attachids").val());
    var attachIdsL = attachIds.split(",").length;
    if (attachIdsL >= 6) {
        alert("上傳附件個數不能超過五個！");
        return;
    }
    // 创建
    var form_data = new FormData();
    // 获取文件
//        var file = document.getElementById('fileToUpload').files[0];
    var file_data = $("#attachidsUpload").prop("files")[0];
    form_data.append("file", file_data);
    form_data.append("path", "test");
    // 把所以表单信息
    $.ajax({
        type: "POST",
        url: ctx+"/admin/upload",
        beforeSend: ajaxLoading,
//            dataType : "json",
        processData: false,  // 注意：让jQuery不要处理数据
        contentType: false,  // 注意：让jQuery不要设置contentType
        data: form_data
    }).success(function (msg) {
        ajaxLoadEnd();
        $("#dowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href='+ctx+'"/admin/download/' + msg.id + '">' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delAtt(\'' + msg.id + '\')"></div></div>');
        $("#attachids").val($("#attachids").val() + msg.id + ",");
        // $("#attachids").val(msg.id);
    }).fail(function (msg) {
    });
}
//環保科技處對應窗口附件上傳
function reUploadFile() {
    var attachIds = $.trim($("#reattachids").val());
    var attachIdsL = attachIds.split(",").length;
    if (attachIdsL >= 6) {
        alert("上傳附件個數不能超過五個！");
        return;
    }
    // 创建
    var form_data = new FormData();
    // 获取文件
//        var file = document.getElementById('fileToUpload').files[0];
    var file_data = $("#attachidsUpload").prop("files")[0];
    form_data.append("file", file_data);
    form_data.append("path", "test");
    // 把所以表单信息
    $.ajax({
        type: "POST",
        url: ctx+"/admin/upload",
//            dataType : "json",
        processData: false,  // 注意：让jQuery不要处理数据
        contentType: false,  // 注意：让jQuery不要设置contentType
        data: form_data
    }).success(function (msg) {
        $("#reDowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href='+ctx+'"/admin/download/' + msg.id + '">' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delReAtt(\'' + msg.id + '\')"></div></div>');
        $("#reattachids").val($("#reattachids").val() + msg.id + ",");
        // $("#attachids").val(msg.id);
    }).fail(function (msg) {
    });
}
function delAtt(fileid) {
    $("#" + fileid).remove();
    var kk = $("#attachids").val();
    kk = kk.replace(fileid + ",", "");
    $("#attachids").val(kk);
    $.post(ctx+'/admin/delete/' + fileid, {}, function (data) {
        console.log(data);
        successTip(data);
    });
}
//導入
function btnUploadExcel() {
    var file_data = $("#batchFile").prop("files")[0];
    if (file_data.name == "") {
        $.messager.alert('系統提示', '請選擇文件，再進行導入！', 'info');
        return;
    }
    var suffix = /\.[^\.]+$/.exec(file_data.name);
    var isExcel2003 = "";
    if (suffix == ".xls") {
        isExcel2003 = "true";
    } else if (suffix == ".xlsx") {
        isExcel2003 = "false";
    } else {
        $.messager.alert('系統提示', '導入文件格式不正確！', 'info');
        return;
    }
    // 创建
    var form_data = new FormData();
    // 获取文件
//        var file = document.getElementById('fileToUpload').files[0];
    form_data.append("file", file_data);
    form_data.append("isExcel2003", isExcel2003);
    //     $.messager.progress({
//         interval : 100,
//         text : "正在導入"
//     });
    // 把所以表单信息
    $.ajax({
        type: "POST",
        url: ctx+"/tqhraymachinescrap/upload",
//            dataType : "json",
        processData: false,  // 注意：让jQuery不要处理数据
        contentType: false,  // 注意：让jQuery不要设置contentType
        data: form_data
    }).success(function (msg) {
        // console.log(msg)
        if (msg == 'success') {
            $("#labelListAddResult").html("導入成功");
            $("#downloadError").hide();
        }else{
            if(msg =='2') {
                $("#labelListAddResult").html("導入失敗, ");
                $("#downloadError").show();
            }else{
                $("#labelListAddResult").html(msg);
            }
        }
    }).fail(function (msg) {
        $("#labelListAddResult").html("導入失敗");
    });
}
// function btnUploadExcel() {
//     var empNoFileName = $("#OvertimeAddFile").filebox("getValue");
//     if (empNoFileName == "") {
//         $.messager.alert('系統提示', '請選擇文件，再進行導入！', 'info');
//         return;
//     }
//     var url = ctx + "/tqhraymachinescrap/upload?isExcel2003=";
//     var suffix = /\.[^\.]+$/.exec(empNoFileName);
//     if (suffix == ".xls") {
//         url += "true";
//     } else if (suffix == ".xlsx") {
//         url += "false";
//     } else {
//         $.messager.alert('系統提示', '導入文件格式不正確！', 'info');
//         return;
//     }
//     $.messager.progress({
//         interval : 100,
//         text : "正在導入"
//     });
//     $("#batchImportForm").form("submit", {
//         method : "post",
//         url : url,
//         success : function(data) {
//             $.messager.progress('close');
//             data = IPEBG.str2json(data);
//             if (data.state == "SUCCESS") {
// //						console.log(data);
//                 //先顯示DIV，再更新表格內容，div中數據會全部顯示
//                 $("#importResultTableDiv").show();
//                 var importResultTableShowData = [];
//                 var dataLength = data.data.data.length;
//                 for(var i = 0; i < 15 && i < dataLength; i++){
//                     importResultTableShowData.push(data.data.data[i]);
//                 }
//                 $("#importResultTable").datagrid({data:importResultTableShowData});
//                 $("#importResultTable").datagrid('hideColumn',"tmp2");
//                 $("#importResultTable").datagrid('hideColumn',"tmp3");
//                 $("#cumNoSalaryInfoAddExcelDiv").hide();
//                 $("#labelListAddResult").html("導入系統 " + dataLength + "筆");
//             } else if (data.msg != undefined) {
//                 $.messager.alert("系統提示", data.msg, "info");
//             } else {
//                 $.messager.alert("系統提示", "保存失敗！", "info");
//             }
//         }
//     });
// }
//查看錯誤信息
function downloadError(){
    // document.location.href = '/tqhraymachinescrap/downLoad/errorExcel';
}
//承諾書模板下載
function commitmentTpl(){
    document.location.href = ctx+'/tqhraymachinescrap/downLoad/commitmentTpl';
}
//批量導入模板下載
function batchImportTpl(){
    document.location.href = ctx+'/tqhraymachinescrap/downLoad/batchImportTpl';
}
//导出excel
function exportExcel(){
    var form = document.getElementById("searchFrom");
    searchFrom.action=ctx+'/tqhraymachinescrap/exportExcel';
    form.submit();
}