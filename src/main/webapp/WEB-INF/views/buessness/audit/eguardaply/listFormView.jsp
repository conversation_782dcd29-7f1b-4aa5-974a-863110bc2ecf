<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>警衛服務申請主表</title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
	<form id="mainform" action="${ctx}/eguardaply/${action}" method="post">
		<input id="ids" name="ids" type="hidden" value="${eGuardAply.id }" />
		<input id="serialno" name="eGuardAplyEntity.serialno" type="hidden"
			value="${eGuardAply.serialno }" /> <input id="createtime"
			name="eGuardAplyEntity.createtime" type="hidden"
			value="${eGuardAply.createtime }" />
		<div class="commonW">
			<div class="headTitle">警衛服務申請單</div>
			<div class="position_L">
				任務編碼：<span id="" style="color:#999;"> <c:choose>
						<c:when test="${eGuardAply.serialno==null}">
                     提交成功后自動編碼
                 </c:when>
						<c:otherwise>
                     ${eGuardAply.serialno}
                 </c:otherwise>
					</c:choose>
				</span>
			</div>
				<div class="position_L1 margin_L">
					填單時間：<span style="color:#999;"> <c:choose>
							<c:when test="${eGuardAply.createtime==null}">
						YYYY/MM/DD
					</c:when>
							<c:otherwise>
								<fmt:formatDate value="${eGuardAply.createtime }" pattern="yyyy-MM-dd HH:mm:ss"/>
					</c:otherwise>
						</c:choose>
					</span>
				</div>
			<div class="position_R margin_R">
				填單人：${eGuardAply.makerno}/${eGuardAply.makername}</div>
			<div class="clear"></div>
			<table class="formList" id="buildprojecttable">
				<tr>
					<td>
						<table class="formList">

							<tr align="center">

								<td width="6%">事業群&nbsp;<font color="red">*</font></td>
								<td width="14%" class="td_style1"><input readonly
									id="dptQun" name="eGuardAplyEntity.dptQun"
									class="easyui-validatebox" readonly
									data-options="required:true" value="${eGuardAply.dptQun }" /></td>

								<td width="6%">事業處&nbsp;<font color="red">*</font></td>
								<td width="14%" class="td_style1"><input readonly
									id="dptChu" name="eGuardAplyEntity.dptChu"
									class="easyui-validatebox"
									data-options="width: 80,required:true"
									value="${eGuardAply.dptChu }" /></td>
								<td width="6%">事業處代碼&nbsp;<font color="red">*</font></td>
								<td width="14%" class="td_style1"><input readonly
									id="dptChuCode" name="eGuardAplyEntity.dptChuCode"
									class="easyui-validatebox" data-options="width:80"
									value="${eGuardAply.dptChuCode }" /></td>
								<td>需求類型&nbsp;<font color="red">*</font></td>
								<td colspan="8" class="td_style1"><input disabled
									type="radio"
									name="eGuardAplyEntity.requirememtType" value="0"
									<c:if test="${eGuardAply.requirememtType=='0'}">							
								      checked="checked"
						            </c:if> />增崗
									<input type="radio" disabled
									name="eGuardAplyEntity.requirememtType" value="1"
									<c:if test="${eGuardAply.requirememtType=='1'}">							
								      checked="checked"
						            </c:if> />崗位異動
									<input type="radio" disabled
									name="eGuardAplyEntity.requirememtType" value="2"
									<c:if test="${eGuardAply.requirememtType=='2'}">
									  checked="checked"
									</c:if> />臨時撤崗</td>

							</tr>
							<tr align="center">

								<td width="6%">部門&nbsp;<font color="red">*</font></td>
								<td width="14%" class="td_style1"><input readonly
									id="dptBu" name="eGuardAplyEntity.dptBu"
									class="easyui-validatebox " data-options="required:true"
									value="${eGuardAply.dptBu }" /></td>
								<td>費用代碼&nbsp;<font color="red">*</font></td>
								<td colspan="1" class="td_style1"><input readonly
									id="dptCode" name="eGuardAplyEntity.dptCode"
									class="easyui-validatebox" data-options="required:true"
									value="${eGuardAply.dptCode }" /></td>

								<td>聯繫人&nbsp;<font color="red">*</font></td>
								<td colspan="2" class="td_style1"><input readonly
									id="linkman" name="eGuardAplyEntity.linkman"
									class="easyui-validatebox" data-options="required:true"
									value="${eGuardAply.linkman }" /></td>
								<td>聯繫電話&nbsp;<font color="red">*</font></td>
								<td class="td_style1"><input readonly id="dealtel"
									name="eGuardAplyEntity.dealtel" class="easyui-validatebox"
									style="width:90px;" value="${eGuardAply.dealtel }"
									data-options="required:true,prompt:'579+66666'"
									onblur="valdApplyTel(this)" /></td>

							</tr>
							<tr align="center">
								<td>所屬法人</td>
								<td colspan="1" class="td_style1"><input readonly
									id="legalPerson" name="eGuardAplyEntity.legalPerson"
									class="easyui-combobox"
									data-options="panelHeight:'auto',valueField:'value', textField:'label',url: '${ctx}/system/dict/getDictByType/guard_legalperson',width: 150"
									value="${eGuardAply.legalPerson }" /></td>
								<td>聯繫郵箱&nbsp;<font color="red">*</font></td>
								<td colspan="7" class="td_style1"><input readonly
									id="dealemail" name="eGuardAplyEntity.dealemail"
									class="easyui-validatebox" value="${eGuardAply.dealemail }"
									style="width:300px;" data-options="required:true"
									onblur="valdEmail(this)" /></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td>
						<table class="formList">


							<tr align="center">
								<td colspan="10" class="td_style1">崗位信息</td>
							</tr>
							<tr align="center">



							</tr>

							<tr align="center">
								<td colspan="10" width="100%">
									<div style="overflow-x:auto;width: 1200px">
										<input id="buildprojectApplyItemTableIndex" type="hidden"
											value="<c:if test="${itemEntity!=null&&itemEntity.size()>0}">${itemEntity.size() + 1}</c:if>
                            <c:if test="${itemEntity==null}">2</c:if>">
										</input>
										<table id="buildprojectApplyItemTable" width="100%">
											<thead>
												<tr>
													<th>序號<font color="red">*</font></th>
													<th>區域<font color="red">*</font></th>
													<th>棟<font color="red">*</font></th>
													<th>層<font color="red">*</font></th>
													<th>方位<font color="red">*</font></th>
													<th>崗位類別<font color="red">*</font></th>
													<th>班制<font color="red">*</font></th>
													<th>崗位名稱<font color="red">*</font></th>
													<th>人數(不含調休)<font color="red">*</font></th>
													<th>需求時間<font color="red">*</font></th>
												</tr>
											</thead>

											<tbody>
												<c:if test="${itemEntity!=null&&itemEntity.size()>0}">
													<c:forEach items="${itemEntity}" var="item"
														varStatus="status">
														<tr align="center" id="Item${status.index}">
															<td>${status.index+1}</td>
															<td><input readonly id="area${status.index}"
																name="eGuardItemAplyEntity[${status.index}].area"
																class="easyui-validatebox" data-options="required:true"
																style="width:50px;" value="${item.area}" /></td>

															<td><input readonly id="block${status.index}"
																name="eGuardItemAplyEntity[${status.index}].block"
																class="easyui-validatebox" data-options="required:true"
																style="width:50px;" value="${item.block}" /></td>

															<td><input readonly id="floor${status.index}"
																name="eGuardItemAplyEntity[${status.index}].floor"
																class="easyui-validatebox" data-options="required:true"
																style="width:50px;" value="${item.floor}" /></td>

															<td><input readonly id="position${status.index}"
																name="eGuardItemAplyEntity[${status.index}].position"
																class="easyui-validatebox" data-options="required:true"
																style="width:50px;" value="${item.position}" /></td>

															<td><input readonly id="postType${status.index}"
																name="eGuardItemAplyEntity[${status.index}].postType"
																class="easyui-validatebox" data-options="required:true"
																style="width:50px;" value="${item.postType}" /></td>

															<td><input readonly id="postShift${status.index}"
																name="eGuardItemAplyEntity[${status.index}].postShift"
																class="easyui-validatebox" data-options="required:true"
																style="width:50px;" value="${item.postShift}" /></td>

															<td><input readonly id="postName${status.index}"
																name="eGuardItemAplyEntity[${status.index}].postName"
																class="easyui-validatebox" data-options="required:true"
																style="width:100px;" value="${item.postName}" /></td>

															<td><input readonly id="postPerNu${status.index}"
																name="eGuardItemAplyEntity[${status.index}].postPerNu"
																class="easyui-validatebox" data-options="required:true"
																onkeyup="this.value=this.value.replace(/[^\d]/g,'') "
																onafterpaste="this.value=this.value.replace(/[^\d]/g,'') "
																style="width:50px;" value="${item.postPerNu}" /></td>

															<td><input readonly
																id="postStartDate${status.index}"
																name="eGuardItemAplyEntity[${status.index}].postStartDate"
																value="<fmt:formatDate value="${item.postStartDate}"/>"
																class="easyui-my97" datefmt="yyyy-MM-dd"
																minDate="%y-%M-%d"
																data-options="width: 100,required:true" />
																<c:if test="${eGuardAply.requirememtType=='2'}">
																	<input
																	readonly id="postEndDate${status.index}"
																	name="eGuardItemAplyEntity[${status.index}].postEndDate"
																	value="<fmt:formatDate value="${item.postEndDate}"/>"
																	class="easyui-my97" datefmt="yyyy-MM-dd"
																	minDate="%y-%M-%d"
																	data-options="width: 100,required:true" />
																</c:if>
															</td>
														</tr>
													</c:forEach>
												</c:if>
												<c:if test="${itemEntity==null}">
													<tr align="center" id="Item1">
														<td>1</td>
														<td><input id="area1"
															name="eGuardItemAplyEntity[0].area"
															class="easyui-combobox"
															data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadArea('1');},onSelect:function(){loadBlock('1');}"
															style="width:150px;" value="" /></td>
														<td><input id="block1"
															name="eGuardItemAplyEntity[0].block"
															class="easyui-combobox"
															data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadBlock('1');},onSelect:function(){loadFloor('1');}"
															style="width:150px;" value="" /></td>
														<td><input id="floor1"
															name="eGuardItemAplyEntity[0].floor"
															class="easyui-combobox"
															data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false "
															style="width:150px;" value="" /></td>
														<td><input id="position1"
															name="eGuardItemAplyEntity[0].position"
															class="easyui-combobox"
															data-options="panelHeight:'auto',valueField:'value', textField:'label',url: '${ctx}/system/dict/getDictByType/guard_position',width: 150"
															style="width:150px;" value="" /></td>
														<td><input id="postType1"
															name="eGuardItemAplyEntity[0].postType"
															class="easyui-combobox"
															data-options="panelHeight:'auto',valueField:'value', textField:'label',url: '${ctx}/system/dict/getDictByType/guard_postType',width: 150"
															style="width:150px;" value="" /></td>
														<td><input id="postShift1"
															name="eGuardItemAplyEntity[0].postShift"
															class="easyui-combobox"
															data-options="panelHeight:'auto',valueField:'value', textField:'label',url: '${ctx}/system/dict/getDictByType/guard_position',width: 150"
															style="width:150px;" value="" /></td>
														<td><input id="postName1"
															name="eGuardItemAplyEntity[0].postName"
															class="easyui-validatebox" data-options="required:true"
															style="width:150px;" value="" /></td>
														<td><input id="postPerNu1"
															name="eGuardItemAplyEntity[0].postPerNu"
															onkeyup="this.value=this.value.replace(/[^\d]/g,'') "
															onafterpaste="this.value=this.value.replace(/[^\d]/g,'') "
															class="easyui-validatebox" data-options="required:true"
															style="width:150px;" value="" /></td>
														<td><input id="postStartDate1"
															name="eGuardItemAplyEntity[0].postStartDate"
															value="<fmt:formatDate value="${item.postStartDate}"/>"
															class="easyui-my97" datefmt="yyyy-MM-dd"
															minDate="%y-%M-%d"
															data-options="width: 100,required:true" /><input
															id="postEndDate1"
															name="eGuardItemAplyEntity[0].postEndDate"
															value="<fmt:formatDate value="${item.postEndDate}"/>"
															class="easyui-my97" datefmt="yyyy-MM-dd"
															minDate="%y-%M-%d"
															data-options="width: 100,required:true" /></td>

														<td><input type="image"
															src="${ctx}/static/images/deleteRow.png"
															onclick="deltr(1);return false;" /></td>

													</tr>
												</c:if>
											</tbody>
										</table>
									</div>
								</td>
							</tr>
								<tr align="center">
								<td colspan="2">需求原因說明</td>
								<td colspan="8"><textarea id="applyExplain" readonly
										name="eGuardAplyEntity.applyExplain"
										class="easyui-validatebox" style="width:1000px;height:60px;"
										rows="4" cols="4" value="${eGuardAply.applyExplain}" data-options="required:true">${eGuardAply.applyExplain}</textarea></td>
							</tr>
							<tr align="center">
								<td>附件</td>
								<td colspan="9" class="td_style1">
									<div id="dowloadUrl">
										<c:forEach items="${file}" varStatus="i" var="item">
											<div id="${item.id}"
												style="line-height:30px;margin-left:5px;" class="float_L">
												<div class="float_L">
													<a href="${item.url}" target="_blank">${item.name}</a>
												</div>
											</div>
										</c:forEach>
									</div>
								</td>
							</tr>
							<tr>
								<td>現場勘查說明</td>
								<td align="center">安全管控必要性：</td>
								<td colspan="2" class="td_style1"><input type="radio" disabled 
									name="eGuardAplyEntity.safeManagement"
									value="0" onclick="kanchaCheck()"
									<c:if test="${eGuardAply.safeManagement=='0'}">							
								      checked="checked"
						            </c:if> />符合
									<input type="radio" disabled
									name="eGuardAplyEntity.safeManagement" value="1"
									onclick="kanchaCheck()"
									<c:if test="${eGuardAply.safeManagement=='1'}">							
								      checked="checked"
						            </c:if> />不符合
								</td>
								<td align="center">人力需求合理性：</td>
								<td colspan="2" class="td_style1"><input type="radio" disabled
									name="eGuardAplyEntity.manpowerDemand"
									value="0" onclick="kanchaCheck()"
									<c:if test="${eGuardAply.manpowerDemand=='0'}">							
								      checked="checked"
						            </c:if> />合理
									<input type="radio" disabled
									name="eGuardAplyEntity.manpowerDemand" value="1"
									onclick="kanchaCheck()"
									<c:if test="${eGuardAply.manpowerDemand=='1'}">							
								      checked="checked"
						            </c:if> />不合理</td>
								<td align="center">安全硬件設施：</td>
								<td colspan="2" class="td_style1"><input type="radio" disabled
									name="eGuardAplyEntity.safetyFacilities"
									value="0"
									<c:if test="${eGuardAply.safetyFacilities=='0'}">							
								      checked="checked"
						            </c:if> />安全硬件設施
									<input type="radio" disabled
									name="eGuardAplyEntity.safetyFacilities" value="1"
									<c:if test="${eGuardAply.safetyFacilities=='1'}">							
								      checked="checked"
						            </c:if> />加強項目</td>
							</tr>
							<c:if test="${eGuardAply.requirememtType=='0'}">
								<tr>
									<td>派駐保安公司</td>
									<td colspan="9" class="td_style1"><input id="securityCom1" disabled
																			 name="eGuardItemAplyEntity[0].securityCom"
																			 class="easyui-combobox"
																			 data-options="panelHeight:'auto',valueField:'value', textField:'label',url: '${ctx}/system/dict/getDictByType/guard_securityCom',width: 150"
																			 style="width:150px;" value="${itemEntity[0].securityCom}" />
									</td>
								</tr>
							</c:if>
							<tr>
								<th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄

								</th>
							</tr>
							<tr>
								<td colspan="10" style="text-align:left;">
									${chargeNodeInfo}</td>
							</tr>

							<tr>
								<td colspan="10" style="text-align:left;"><iframe
										id="qianheLogFrame" name="qianheLogFrame"
										src="${ctx}/eformsign/goChargeLog?serialNo=${eGuardAply.serialno}"
										width="100%"></iframe></td>
							</tr>
							<tr>
								<td colspan="10" style="text-align:center;padding-left:10px;">
									<a href="javascript:;" id="btnClose" class="easyui-linkbutton"
									data-options="iconCls:'icon-cancel'" style="width: 100px;"
									onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp; <a
									href="#" id="btnPrint" class="easyui-linkbutton"
									data-options="iconCls:'icon-print'" style="width: 100px;"
									onclick="printWindow('btnClose,btnPrint');">列印</a>
									<div id="win"></div> <input type="hidden" id="itemNoDTO"
									name="itemNoDTO" value="" />

								</td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
		</div>
	</form>

	<script
		src='${ctx}/static/js/audit/eguardaply.js?random=<%= Math.random()%>'></script>
</body>
</html>
