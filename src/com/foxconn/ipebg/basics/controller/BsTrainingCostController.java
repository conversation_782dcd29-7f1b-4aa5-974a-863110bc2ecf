package com.foxconn.ipebg.basics.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Date;

import com.alibaba.fastjson.JSON;
import com.foxconn.ipebg.basics.service.BsTrainingArrangeService;
import com.foxconn.ipebg.system.entity.User;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.basics.entity.BsTrainingCostEntity;
import com.foxconn.ipebg.basics.service.BsTrainingCostService;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;


/**
 * 培訓期費用結帳明細表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-10-07 08:29:34
 */
@Controller
@RequestMapping("bstrainingcost")
public class BsTrainingCostController extends BaseController {

    @Autowired
    private BsTrainingCostService bsTrainingCostService;
    @Autowired
    private BsTrainingArrangeService bsTrainingArrangeService;
   
    /**
      * 方法描述: 列表信息
      * @Author: ********
      * @CreateDate:   2019-10-07 08:29:34
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:bstrainingcost:list")
    public String list( Model model) {
        model.addAttribute("periodList", JSON.toJSONString(bsTrainingArrangeService.listAllTrainingPeriod()));
        //查询列表数据
        return "basics/bstrainingcost/list";
    }


    /**
     * 正式崗報表的培訓費用明細
     * @Author: ********
     * @CreateDate:   2019-10-07 08:29:34
     * @Return
     **/
    @RequestMapping(value="costDetail",method = RequestMethod.GET)
    //@RequiresPermissions("basics:bstrainingcost:list")
    public String costDetail(String startDate, String endDate,String postRecno, Model model) {
        model.addAttribute("periodList", JSON.toJSONString(bsTrainingArrangeService.listAllTrainingPeriod()));
        model.addAttribute("startDate", startDate);
        model.addAttribute("endDate", endDate);
        model.addAttribute("postRecno", postRecno);
        //查询列表数据
        return "basics/bstrainingcost/list";
    }


    /**
      * 方法描述:  分頁查詢信息
      * @Author: ********
      * @CreateDate:   2019-10-07 08:29:34
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:bstrainingcost:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<BsTrainingCostEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        User user = UserUtil.getCurrentUser();
        if (user != null) {
            // 按保安公司查询
            PropertyFilter filter = new PropertyFilter("INS_securityCom", user.getSecurityComs());
            filters.add(filter);
        }
        page = bsTrainingCostService.search(page, filters);
        return getEasyUIData(page);
    }
    /**
     * 方法描述: 保存
     * @Author: ********
     * @CreateDate:   2019-10-07 08:29:34
     * @Return
     **/
   
    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("basics:bstrainingcost:add")
    @ResponseBody
    public String create(@Valid BsTrainingCostEntity bsTrainingCost, Model model) {
        bsTrainingCost.setNewRecord(true);
    	bsTrainingCost.setCreateDate(new Date());
        bsTrainingCostService.save(bsTrainingCost);
        return "success";
    }
    
    /**
      * 方法描述: 添加跳轉
      * @Author: ********
      * @CreateDate:   2019-10-07 08:29:34
      * @Return
      **/

    @RequiresPermissions("basics:bstrainingcost:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("bsTrainingCost", new BsTrainingCostEntity());
        model.addAttribute("action", "create");
        return "basics/bstrainingcost/listForm";
    }
    
     /**
     * 方法描述: 修改跳轉
     * @Author: S6113712
     * @CreateDate:   2018-10-06 11:04:39
     * @Return
     **/

   @RequiresPermissions("basics:bstrainingcost:update")
   @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
   public String updateForm(@PathVariable("id") String id, Model model) {
       model.addAttribute("bsTrainingCost", bsTrainingCostService.get(id));
       model.addAttribute("action", "update");
      return "basics/bstrainingcost/listForm";
   }
   
	/**
	 * 方法描述: 修改
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-11 10:55:00
	 * @Return
	 **/

   @RequestMapping(value = "update", method = RequestMethod.POST)
   @RequiresPermissions("basics:bstrainingcost:update")
   @ResponseBody
   public String update(@Valid BsTrainingCostEntity bsTrainingCost,@RequestParam(value = "ids") String ids) {
	   bsTrainingCost.setId(ids);
	   bsTrainingCost.setUpdateDate(new Date());
	   bsTrainingCostService.update(bsTrainingCost);
	   
       return "success";
   }
    /**
      * 方法描述: 根據主鍵刪除
      * @Author: ********
      * @CreateDate:   2019-10-07 08:29:34
      * @Return
      **/

    @RequestMapping("delete/{id}")
    @RequiresPermissions("basics:bstrainingcost:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        bsTrainingCostService.delete(id);
        return "success";
    }
    /**
      * 导出excel
      * @Author: ********
      * @CreateDate:   2019-10-07 08:29:34
      * @Return
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {}
}
