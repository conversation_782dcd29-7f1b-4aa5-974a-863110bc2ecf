var dg;
var d;
$(function() {
	dg = $('#dg').datagrid({
		method : "get",
		url : ctx + '/artificialaudit/list',
		fit : true,
		fitColumns : true,
		border : false,
		idField : 'id',
		striped : true,
		pagination : true,
		rownumbers : true,
		loadFilter : function(data) {
			// 过滤数据
			var value = {
				total : data.total,
				rows : []
			};
			var x = 0;
			for ( var i = 0; i < data.rows.length; i++) {
				// if (data.rows[i].tranModel.eserStat == "W")
				if (data.rows[i].abnormalResult1 == "N") {
					data.rows[i].abnormalResult1 = "異常";
				} else if (data.rows[i].abnormalResult1 == "Y") {
					data.rows[i].abnormalResult1 = "正常";
				}

				if (data.rows[i].abnormalResult2 == "N") {
					data.rows[i].abnormalResult2 = "異常";
				} else if (data.rows[i].abnormalResult2 == "Y") {
					data.rows[i].abnormalResult2 = "正常";
				}

				value.rows[x++] = data.rows[i];
			}
			return value;

		},
		pageNumber : 1,
		pageSize : 20,
		pageList : [ 10, 20, 30, 40, 50 ],
		singleSelect : true,
		sortName: 'createDate',
		sortOrder: 'desc',
		columns : [ [ {
			field : 'id',
			title : '主鍵',
			hidden : true
		}, {
			field : 'empNo',
			title : '工號',
			sortable : true,
			width : 100
		}, {
			field : 'empName',
			title : '姓名',
			sortable : true,
			width : 100
		}, {
			field : 'postName',
			title : '崗位名稱/培訓期',
			sortable : true,
			width : 100
		}, {
			field : 'shiftDate',
			title : '排班日期',
			sortable : true,
			width : 100,
			formatter : formatDate
		},{
			field : 'shiftName',
			title : '班別',
			sortable : true,
			width : 100
		}, {
			field : 'abnormalState',
			title : '狀態',
			sortable : true,
			width : 100
		}, {
			field : 'abnormalResult1',
			title : '第一次稽核結果',
			sortable : true,
			width : 100
		}, {
			field : 'abnormalCauses1',
			title : '第一次異常原因',
			sortable : true,
			width : 100
		}, {
			field : 'abnormalResult2',
			title : '第二次稽核結果',
			sortable : true,
			width : 100
		}, {
			field : 'abnormalCauses2',
			title : '第二次異常原因',
			sortable : true,
			width : 100
		}, {
			field : 'createBy',
			title : '創建人',
			sortable : true,
			width : 100
		}, {
			field : 'createDate',
			title : '創建時間',
			sortable : true,
			width : 100
		}

		] ],
		enableHeaderClickMenu : true,
		enableHeaderContextMenu : true,
		enableRowContextMenu : false,
		rowTooltip : true,
		toolbar : '#tb'
	});
	// 批量上傳模板下載
	$("#btnBatchImportTpl").click(batchImportTpl);

});

// 弹窗增加
function add() {
	d = $("#dlg").dialog({
		title : '添加人工稽核錄入基本資料',
		width : 300,
		height : 380,
		href : ctx + '/artificialaudit/create',
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '确认',
			handler : function() {
				var empName = $.trim($("#empName").val());
				if (empName == "") {
					$.messager.alert("溫馨提示", "警衛人員信息不能為空！", "info");
					return;
				}
				var abnormalCauses1=$.trim($("#abnormalCauses1").val());//
				
				if(abnormalCauses1.length>50){
					$.messager.alert("溫馨提示", "第一次異常原因必須在50字以內！", "info");
					return;
				}
	            var abnormalCauses2=$.trim($("#abnormalCauses2").val());//
				
				if(abnormalCauses2.length>50){
					$.messager.alert("溫馨提示", "第二次異常原因必須在50字以內！", "info");
					return;
				}
				$("#mainform").submit();
			}
		}, {
			text : '取消',
			handler : function() {
				d.panel('close');
			}
		} ]
	});
}

// 删除
function del() {
	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data) {
		if (data) {
			$.ajax({
				type : 'get',
				url : ctx + "/artificialaudit/delete/" + row.id,
				success : function(data) {
					successTip(data, dg);
				}
			});
		}
	});
}

// 弹窗修改
function upd() {
	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	d = $("#dlg").dialog({
		title : '修改人工稽核錄入基本資料',
		width : 380,
		height : 340,
		href : ctx + '/artificialaudit/update/' + row.id,
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '修改',
			handler : function() {
				$('#mainform').submit();
			}
		}, {
			text : '取消',
			handler : function() {
				d.panel('close');
			}
		} ]
	});
}
// 创建查询对象并查询
function cx() {
	var obj = $("#searchFrom").serializeObject();
	dg.datagrid('load', obj);
}
// 表單進度查詢頁面查詢條件重置
function listSearchReset() {
	$("#searchFrom").form("reset");
}
// 重新比对该日期的出勤状况
function reCheck() {
	var date = $('#gen_date').datebox('getValue');
	if (date === '') {
		$.messager.alert("溫馨提示", "請輸入要重新比對的日期", "info");
		return;
	}
	$.ajax({
		url: ctx+'/artificialaudit/checkAbnormalPerson',
		type: 'POST',
		beforeSend: ajaxLoading,
		data: { date: date },
		success: function (response) {
			ajaxLoadEnd();
			if (response.code === 1) {
				// 生成成功
				$.messager.alert("溫馨提示", response.msg, "info");
			} else {
				// 生成失敗
				$.messager.alert("溫馨提示", response.msg, "info");
			}
		}
	});
}
// 导出excel
function exportExcel() {
	var form = document.getElementById("searchFrom");
	searchFrom.action = ctx + '/artificialaudit/exportExcel';
	form.submit();
}
// 根據工號查出警衛人員排班信息
function queryEmpInfo() {
	$("#empNo").val($("#empNo").val().toUpperCase());
	var empNo = $.trim($("#empNo").val().toUpperCase());
	var shiftDate = $('#shiftDate').combobox('getValue');
	var urlStr = "";
	if (shiftDate == "" || shiftDate == null) {
		$.messager.alert("溫馨提示", "日期不能為空！", "info");
		return;
	} else {
		urlStr = "/bsarrange/getEmpInfo";
	}

	if (empNo != null && empNo != "") {
		$.ajax({
			url : ctx + urlStr,
			type : 'GET',
			beforeSend : ajaxLoading,
			// dataType: 'json',
			data : {
				empNo : empNo,
				shiftDate : shiftDate
			},
			success : function(data) {
				ajaxLoadEnd();
				if (!data) {
					$.messager.alert("溫馨提示", "該員工當天未排班", "error");
					$('#empName').val('');
					$('#postName').val('');
					$('#shiftName').val('');
					$('#company').val('');
					$('#postRecno').val('');
					$('#shiftNo').val('');
				} else {
					$('#empName').val(data.empName);
					$('#postName').val(data.postName);
					$('#shiftName').val(data.shiftName);
					$('#company').val(data.company);
					$('#postRecno').val(data.postRecno);
					$('#shiftNo').val(data.shiftNo);
				}
			}
		});
	}
}
// 導入
function btnUploadExcel() {

	// var table = document.getElementById("buildprojectApplyItemTable");
	// var len = table.rows.length;
	// if (len>1)
	// {
	// for (var i = 0; i < len-1 ; i++) {
	// table.deleteRow(len-i-1);
	// }
	// }

	var file_data = $("#batchFile").prop("files")[0];
	if (file_data.name == "") {
		$.messager.alert('系統提示', '請選擇文件，再進行導入！', 'info');
		return;
	}
	var suffix = /\.[^\.]+$/.exec(file_data.name);
	var isExcel2003 = "";
	if (suffix == ".xls") {
		isExcel2003 = "true";
	} else if (suffix == ".xlsx") {
		isExcel2003 = "false";
	} else {
		$.messager.alert('系統提示', '導入文件格式不正確！', 'info');
		return;
	}
	// 创建
	var form_data = new FormData();
	// 获取文件
	form_data.append("file", file_data);
	form_data.append("isExcel2003", isExcel2003);

	// 把所以表单信息
	$.ajax({
		type : "POST",
		url : ctx + "/artificialaudit/upload",
		// dataType : "json",
		processData : false, // 注意：让jQuery不要处理数据
		contentType : false, // 注意：让jQuery不要设置contentType
		data : form_data
	}).success(function(msg) {
		// console.log(msg)
		if (msg == 'success') {
			$("#labelListAddResult").html("導入成功");
			$("#downloadError").hide();
		} else {
			if (msg == '2') {
				$("#labelListAddResult").html("導入失敗, ");
				$("#downloadError").show();
			} else if (msg == 'ip地址未綁定，無權限操作！')

			{
				$("#labelListAddResult").html(msg);
			} else {
				$("#labelListAddResult").html(msg);
				$("#downloadError").show();
			}
		}
	}).fail(function(msg) {
		$("#labelListAddResult").html("導入失敗");
	});
}

// 批量導入模板下載
function batchImportTpl() {
	document.location.href = ctx + '/artificialaudit/downLoad/batchImportTpl';
}

function openBatchImportWin() {
	var optionWin = $("#optionWin").window();

	optionWin.window({
		center : true
	});
	// 顯示於畫面中
	optionWin.slideDown();
	// 清空optionWin頁面中所有元件的值
	/*
	 * $("#pointForm .easyui-combobox").each(function(){
	 * $(this).combobox("clear"); });
	 */
	$("#batchImportForm").form("clear");
	$("#labelListAddResult").html("");
	$("#downloadError").hide();
	// optionWin.find("input").val("");
	optionWin.window("open");
}