<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>警衛服務費用匯總</title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
</script>

<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
	<table id="summaryDG"></table>
	<script type="text/javascript">
		debugger
		$('#summaryDG').datagrid({
			method: "get",
			url: ctx + '/servicepersoncost/getSummary',
			fit: true,
			fitColumns: false,
			border: false,
			queryParams:$('#searchFrom').serializeObject(),
			striped: true,
			pagination: false,
			rownumbers: false,
			columns: [[
				{
					field: 'label',
					title: '條目',
					sortable: false,
					halign: 'center',
					width: 150
				},
				{
					field: 'value',
					title: '內容',
					sortable: false,
					halign: 'center',
					width: 100
				}
			]],
			loadFilter: function (data) {
				debugger
				return data;
			}
		});
	</script>
</body>
</html>