<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>申請表單審核記錄表</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>

<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/eformsign/${action}" method="post">
		<table class="formTable">
		    			<tr>
				<td>申請單號：</td>
				<td>
				    					<input type="hidden" name="applyId" value="${eFormSign.applyId}"/>
															<input id="applyId" name="applyId" class="easyui-validatebox" data-options="width: 150" value="${eFormSign.applyId }" />
									</td>
			</tr>
						<tr>
				<td>簽單順序：</td>
				<td>
				    										<input id="signOrd" name="signOrd" class="easyui-validatebox" data-options="width: 150" value="${eFormSign.signOrd }" />
									</td>
			</tr>
						<tr>
				<td>簽核流程節點名稱：</td>
				<td>
				    										<input id="signNam" name="signNam" class="easyui-validatebox" data-options="width: 150" value="${eFormSign.signNam }" />
									</td>
			</tr>
						<tr>
				<td>簽核人工號：</td>
				<td>
				    										<input id="eserId" name="eserId" class="easyui-validatebox" data-options="width: 150" value="${eFormSign.eserId }" />
									</td>
			</tr>
						<tr>
				<td>簽核人姓名：</td>
				<td>
				    										<input id="eserNam" name="eserNam" class="easyui-validatebox" data-options="width: 150" value="${eFormSign.eserNam }" />
									</td>
			</tr>
						<tr>
				<td>簽核人郵件：</td>
				<td>
				    										<input id="eserMail" name="eserMail" class="easyui-validatebox" data-options="width: 150" value="${eFormSign.eserMail }" />
									</td>
			</tr>
						<tr>
				<td>審核狀態(W待審核Y審核通過N駁回E結案)：</td>
				<td>
				    										<input id="eserStat" name="eserStat" class="easyui-validatebox" data-options="width: 150" value="${eFormSign.eserStat }" />
									</td>
			</tr>
						<tr>
				<td>簽核批註：</td>
				<td>
				    										<input id="eserMemo" name="eserMemo" class="easyui-validatebox" data-options="width: 150" value="${eFormSign.eserMemo }" />
									</td>
			</tr>
						<tr>
				<td>簽核日期：</td>
				<td>
				    										<input id="eserDate" name="eserDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${eFormSign.eserDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>簽核電腦IP：</td>
				<td>
				    										<input id="eserIp" name="eserIp" class="easyui-validatebox" data-options="width: 150" value="${eFormSign.eserIp }" />
									</td>
			</tr>
						<tr>
				<td>建立人員：</td>
				<td>
				    										<input id="crter" name="crter" class="easyui-validatebox" data-options="width: 150" value="${eFormSign.crter }" />
									</td>
			</tr>
						<tr>
				<td>建立日期：</td>
				<td>
				    										<input id="crtdate" name="crtdate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${eFormSign.crtdate}"/>" />
									</td>
			</tr>
						<tr>
				<td>修改人員：</td>
				<td>
				    										<input id="mdier" name="mdier" class="easyui-validatebox" data-options="width: 150" value="${eFormSign.mdier }" />
									</td>
			</tr>
						<tr>
				<td>修改日期：</td>
				<td>
				    										<input id="mdidate" name="mdidate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${eFormSign.mdidate}"/>" />
									</td>
			</tr>
					</table>
	</form>
  </div>
<script src='${ctx}/static/js/audit/eformsign.js?"+Math.random()"'></script>
<script type="text/javascript">
   //提交表单
$('#mainform').form({
    onSubmit: function(){
        var isValid = $(this).form('validate');
        return isValid;	// 返回false终止表单提交
    },
    success:function(data){
        successTip(data,dg,d);
    }
});
</script>
</body>
</html>