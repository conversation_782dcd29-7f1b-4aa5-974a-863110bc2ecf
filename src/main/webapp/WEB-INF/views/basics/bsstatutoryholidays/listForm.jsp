<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>法定節假日表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>

    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/bsstatutoryholidays/${action}" method="post">
    <table class="formTable">
        <input type="hidden" name="ids" value="${bsStatutoryHolidays.id }" />
        <input type="hidden" name="delFlag"  value="${bsStatutoryHolidays.delFlag }"/>

        <%--<tr>
            <td>法定節假日ID Primary：</td>
            <td>
                <input type="hidden" name="id" value="${bsStatutoryHolidays.id}"/>
                <input id="id" name="id" class="easyui-validatebox" data-options="width: 150"
                       value="${bsStatutoryHolidays.id }"/>
            </td>
        </tr>--%>
        <tr>
            <td>日期：</td>
            <td>
                <input id="holidayDate" name="holidayDate" class="easyui-my97" datefmt="yyyy-MM-dd"
                       data-options="width: 160,required:true"
                       value="<fmt:formatDate value="${bsStatutoryHolidays.holidayDate}"/>"/>
            </td>
        </tr>
        <tr>
            <td>描述：</td>
            <td>
                <input id="holidayDesc" name="holidayDesc" class="easyui-validatebox" data-options="width: 160"
                       value="${bsStatutoryHolidays.holidayDesc }"/>
            </td>
        </tr>
        <tr style="display: none">
            <td>
                <input id="createBy" name="createBy" class="easyui-validatebox" data-options="width: 150" readonly
                       value="${bsStatutoryHolidays.createBy }"/>
                <input id="createDate" name="createDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
                       data-options="width: 160" disabled="disabled"
                       value="<fmt:formatDate value="${bsStatutoryHolidays.createDate}"/>"/>
                <input id="updateBy" name="updateBy" class="easyui-validatebox" data-options="width: 150" readonly
                       value="${bsStatutoryHolidays.updateBy }"/>
                <input id="updateDate" name="updateDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
                       data-options="width: 160" disabled="disabled"
                       value="<fmt:formatDate value="${bsStatutoryHolidays.updateDate}"/>"/>
            </td>
        </tr>
    </table>
</form>
</div>
<script src='${ctx}/static/js/basics/bsstatutoryholidays.js?"+Math.random()"'></script>
<script type="text/javascript">
    //提交表单
    $('#mainform').form({
        onSubmit: function () {
            var isValid = $(this).form('validate');
            return isValid;	// 返回false终止表单提交
        },
        success: function (data) {
            successTip(data, dg, d);
        }
    });
</script>
</body>
</html>