<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>警衛服務費用結賬明細主表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
        var _bsDptList=${bsDptList};
        var _legalPersonDicts=${legalPersonDicts};
        var _qun, _legal, _securityCom, _beginDate, _endDate;
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .grid-colunm {
            width: 100%;
            border: 1px solid #FFFFFF;
            text-align: center;
        }
        .grid-colunm-border {
            width: 100%;
            border-bottom: 1px solid #cccccc;
        }
        .grid-colunm:hover {
            width: 100%;
            text-align: center;
            border: 1px solid #999dff;
            background-color: #e0ecff;
            cursor:pointer;
        }
        .trainee{
            float: left;
            text-align: center;
            border: 1px solid #e0ecff;
            width: 140px;
            padding: 10px 5px;
            border-radius: 5px;
            margin: 5px 2px;
        }
        .resign-trainee{
            float: left;
            text-align: center;
            border: 1px solid #e0ecff;
            background-color: #9d9d9d;
            width: 140px;
            padding: 10px 5px;
            border-radius: 5px;
            margin: 5px 2px;
        }
        .trainee-title{
            text-align: center;
            background-color: #e0ecff;
            padding: 10px 0px;
            color: #783bc7;
            cursor:pointer;
        }
    </style>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
    <form id="searchFrom" action="">
        <input id="filter_buNam" name="filter_EQS_dptQun" class="easyui-combobox" data-options="width:150,prompt: '事業群'"/>
        <input id="filter_legalId" name="filter_EQS_legal" class="easyui-combobox" data-options="width:150,prompt: '法人'"/>
        <input id="filter_securityCom" name="filter_EQS_securityCom" class="easyui-combobox"
               data-options="url: '${ctx}/system/user/userSecurityCom',valueField:'value', textField:'label',width: 150,prompt: '保安公司'"/>
        <input id="filter_startDate" name="filter_GED_workDate" class="easyui-datebox" data-options="width:150,prompt: '起始日期'"/>
        <input id="filter_endDate" name="filter_LED_workDate" class="easyui-datebox" data-options="width:150,prompt: '截止日期'"/>

        <span class="toolbar-item dialog-tool-separator"></span>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
           onclick="listSearchReset()">重置</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel"
           onclick="exportExcel()">導出Excel</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-sum"
           onclick="showSummary()">查看匯總</a>
    </form>
    <shiro:hasPermission name="basics:bsarrangegroup:add">
        <form id="generateForm" action="">
            <input type="text" id="gen_date" name="date" class="easyui-my97" datefmt="yyyy-MM-dd" data-options="width:150,prompt: '生成日期'"/>
            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-hamburg-up" plain="true" onclick="generate();">生成</a>
        </form>
    </shiro:hasPermission>
</div>
<table id="dg"></table>
<div id="modal" class="easyui-window" title="人員明細" data-options="modal:true,closed:true,iconCls:'icon-blank'" style="width:600px;height:500px;padding:10px;">
    <div id="title" class="trainee-title" onclick="closeModel()"></div>
    <div id="personArr">
    </div>
</div>
<div id="dlg"></div>
<script src="${ctx}/static/js/basics/bsservicedaycost.js?random=<%= Math.random()%>"></script>
</body>
</html>
