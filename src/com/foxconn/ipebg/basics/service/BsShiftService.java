package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.entity.BsShiftEntity;
import com.foxconn.ipebg.basics.dao.BsShiftDao;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.Dict;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WorkFlowEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.system.service.DictService;

import java.util.Date;
import java.util.List;

/**
 * 班別資料
 *
 * <AUTHOR>
 * @email <EMAIL> 
 * @date 2019-03-07 09:57:22
 */
@Service
@Transactional(readOnly=true)
public class  BsShiftService extends BaseBusinessService<BsShiftEntity, String>{
    @Autowired
    private BsShiftDao bsShiftDao;

    @Autowired
    private UserServiceUtil serviceUtil;
    @Autowired
    private DictService dictService;

    @Override
    public HibernateDao<BsShiftEntity, String> getEntityDao() {
        return bsShiftDao;
    }

    public BsShiftEntity findByShiftNo(String shiftNo) {
        return this.bsShiftDao.findUniqueBy("shiftNo", shiftNo);
    }
    
    public List<BsShiftEntity> findShiftEntityes() {
		return bsShiftDao.findAll();
	}
}
