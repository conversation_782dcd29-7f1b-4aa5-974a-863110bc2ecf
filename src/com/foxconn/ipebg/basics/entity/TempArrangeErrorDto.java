package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import java.util.Date;

public class TempArrangeErrorDto implements Serializable {
	private static final long serialVersionUID = 1L;

	// 崗位編號
	private String postRecno;
	// 崗位名稱
	private String postName;
	// 班制
	private String postShift;
	// 詳細位置（小崗位名稱）
	private String location;
	// 排班日期
	private Date shiftDate;
	// 班別代碼
	private String shiftNo;
	// 班別
	private String shiftName;
	// 開始時間
	private String startTime;
	// 結束時間
	private String endTime;
	// 工號
	private String empNo;
	// 姓名
	private String empName;
	// 比對-狀態：正常為0
	private String checkState;
	// 比對-異常原因
	private String checkCauses;
	// 創建人IP
	private String createIp;
	// 失敗原因
	private String errorLog;

	public String getPostRecno() {
		return postRecno;
	}

	public void setPostRecno(String postRecno) {
		this.postRecno = postRecno;
	}

	public String getPostName() {
		return postName;
	}

	public void setPostName(String postName) {
		this.postName = postName;
	}

	public String getPostShift() {
		return postShift;
	}

	public void setPostShift(String postShift) {
		this.postShift = postShift;
	}

	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}

	public Date getShiftDate() {
		return shiftDate;
	}

	public void setShiftDate(Date shiftDate) {
		this.shiftDate = shiftDate;
	}

	public String getShiftNo() {
		return shiftNo;
	}

	public void setShiftNo(String shiftNo) {
		this.shiftNo = shiftNo;
	}

	public String getShiftName() {
		return shiftName;
	}

	public void setShiftName(String shiftName) {
		this.shiftName = shiftName;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getEmpNo() {
		return empNo;
	}

	public void setEmpNo(String empNo) {
		this.empNo = empNo;
	}

	public String getEmpName() {
		return empName;
	}

	public void setEmpName(String empName) {
		this.empName = empName;
	}

	public String getCheckState() {
		return checkState;
	}

	public void setCheckState(String checkState) {
		this.checkState = checkState;
	}

	public String getCheckCauses() {
		return checkCauses;
	}

	public void setCheckCauses(String checkCauses) {
		this.checkCauses = checkCauses;
	}

	public String getCreateIp() {
		return createIp;
	}

	public void setCreateIp(String createIp) {
		this.createIp = createIp;
	}

	public String getErrorLog() {
		return errorLog;
	}

	public void setErrorLog(String errorLog) {
		this.errorLog = errorLog;
	}

}
