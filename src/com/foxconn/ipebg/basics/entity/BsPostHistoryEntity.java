package com.foxconn.ipebg.basics.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;
import java.util.LinkedHashMap;

/**
 * 崗位变更历史表
 *
 * <AUTHOR>
 * @date 2022-07-20
 */
@Entity
@Table(name = "BS_POST_HISTORY")
@DynamicUpdate
@DynamicInsert
public class BsPostHistoryEntity extends DataEntity<BsPostHistoryEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    //主表任務單號
    private String serialno;
    //編號
    private String recno;
    //區域
    private String area;
    //棟
    private String block;
    //層
    private String floor;
    //方位
    private String position;
    // 申請人工號
    private String applyEmpNo;
    // 申請人姓名
    private String applyEmpName;
    // 申請人部門
    private String applyEmpBu;
    // 申請單需求類型
    private String applyType;
    //崗位類別
    private String postType;
    //班制
    private String postShift;
    //崗位名稱
    private String postName;
    //人數(不含調休)
    private Integer postPerNu;
    //派駐保安公司
    private String securityCom;
    // 需求開始時間
    private Date postStartDate;
    // 需求結束時間
    private Date postEndDate;

    public static LinkedHashMap<String, String> excelFieldMap() {
        LinkedHashMap<String, String> fieldMap = new LinkedHashMap<String, String>();
        fieldMap.put("recno", "崗位編號");
        fieldMap.put("postName", "崗位名稱");
        fieldMap.put("applyType", "需求類別");
        fieldMap.put("applyEmpName", "申請人姓名");
        fieldMap.put("applyEmpBu", "申請人部門");
        fieldMap.put("createDate", "實際安排時間");
        fieldMap.put("area", "區域");
        fieldMap.put("block", "棟");
        fieldMap.put("floor", "層");
        fieldMap.put("position", "方位");
        fieldMap.put("postType", "崗位類別");
        fieldMap.put("postShift", "班制");
        fieldMap.put("postPerNu", "人數(不含調休)");
        fieldMap.put("postStartDate", "需求時間1");
        fieldMap.put("postEndDate", "需求時間2");
        fieldMap.put("securityCom", "派駐保安公司");
        fieldMap.put("serialno", "申請單號");
        return fieldMap;
    }

    /**
     * 设置：主表任務單號
     */
    public void setSerialno(String serialno) {
        this.serialno = serialno;
    }

    /**
     * 获取：主表任務單號
     */
    @Column(name = "SERIALNO", nullable = false, length = 20)
    public String getSerialno() {
        return serialno;
    }

    /**
     * 设置：編號
     */
    public void setRecno(String recno) {
        this.recno = recno;
    }

    /**
     * 获取：編號
     */

    @Column(name = "RECNO", nullable = false, length = 20)
    public String getRecno() {
        return recno;
    }

    /**
     * 设置：區域
     */
    public void setArea(String area) {
        this.area = area;
    }

    /**
     * 获取：區域
     */

    @Column(name = "AREA", nullable = false, length = 20)
    public String getArea() {
        return area;
    }

    /**
     * 设置：棟
     */
    public void setBlock(String block) {
        this.block = block;
    }

    /**
     * 获取：棟
     */

    @Column(name = "BLOCK", nullable = false, length = 20)
    public String getBlock() {
        return block;
    }

    /**
     * 设置：層
     */
    public void setFloor(String floor) {
        this.floor = floor;
    }

    /**
     * 获取：層
     */

    @Column(name = "FLOOR", nullable = false, length = 20)
    public String getFloor() {
        return floor;
    }

    /**
     * 设置：方位
     */
    public void setPosition(String position) {
        this.position = position;
    }

    /**
     * 获取：方位
     */

    @Column(name = "POSITION", nullable = false, length = 20)
    public String getPosition() {
        return position;
    }

    /**
     * 设置：崗位類別
     */
    public void setPostType(String postType) {
        this.postType = postType;
    }

    /**
     * 获取：崗位類別
     */

    @Column(name = "POST_TYPE", nullable = false, length = 20)
    public String getPostType() {
        return postType;
    }

    /**
     * 设置：班制
     */
    public void setPostShift(String postShift) {
        this.postShift = postShift;
    }

    /**
     * 获取：班制
     */

    @Column(name = "POST_SHIFT", nullable = false, length = 20)
    public String getPostShift() {
        return postShift;
    }

    /**
     * 设置：崗位名稱
     */
    public void setPostName(String postName) {
        this.postName = postName;
    }

    /**
     * 获取：崗位名稱
     */

    @Column(name = "POST_NAME", nullable = false, length = 20)
    public String getPostName() {
        return postName;
    }

    /**
     * 设置：人數(不含調休)
     */
    public void setPostPerNu(Integer postPerNu) {
        this.postPerNu = postPerNu;
    }

    /**
     * 获取：人數(不含調休)
     */

    @Column(name = "POST_PER_NU", nullable = false, length = 20)
    public Integer getPostPerNu() {
        return postPerNu;
    }

    /**
     * 设置：派駐保安公司
     */
    public void setSecurityCom(String securityCom) {
        this.securityCom = securityCom;
    }

    /**
     * 获取：派駐保安公司
     */
    @Column(name = "SECURITY_COM", nullable = true, length = 20)
    public String getSecurityCom() {
        return securityCom;
    }

    @Column(name = "APPLY_EMP_NO", nullable = false, length = 50)
    public String getApplyEmpNo() {
        return applyEmpNo;
    }

    public void setApplyEmpNo(String applyEmpNo) {
        this.applyEmpNo = applyEmpNo;
    }

    @Column(name = "APPLY_EMP_NAME", nullable = false, length = 50)
    public String getApplyEmpName() {
        return applyEmpName;
    }

    public void setApplyEmpName(String applyEmpName) {
        this.applyEmpName = applyEmpName;
    }

    @Column(name = "APPLY_EMP_BU", nullable = false, length = 80)
    public String getApplyEmpBu() {
        return applyEmpBu;
    }

    public void setApplyEmpBu(String applyEmpBu) {
        this.applyEmpBu = applyEmpBu;
    }

    @Column(name = "APPLY_TYPE", nullable = false, length = 10)
    public String getApplyType() {
        return applyType;
    }

    public void setApplyType(String applyType) {
        this.applyType = applyType;
    }

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "POST_START_DATE", nullable = false, length = 20)
    public Date getPostStartDate() {
        return postStartDate;
    }

    public void setPostStartDate(Date postStartDate) {
        this.postStartDate = postStartDate;
    }

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "POST_END_DATE", nullable = false, length = 20)
    public Date getPostEndDate() {
        return postEndDate;
    }

    public void setPostEndDate(Date postEndDate) {
        this.postEndDate = postEndDate;
    }
}
