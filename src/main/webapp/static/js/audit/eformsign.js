var dg;
var d;
$(function(){
    dg=$('#dg').datagrid({
        method: "get",
        url:ctx+'/eformsign/list',
        fit : true,
        fitColumns : true,
        border : false,
        idField : 'applyId',
        striped:true,
        pagination:true,
        rownumbers:true,
        pageNumber:1,
        pageSize : 20,
        pageList : [ 10, 20, 30, 40, 50 ],
        singleSelect:true,
        columns:[[
							                    { field: 'applyId', title: '申請單號',hidden:true},
											                    { field: 'signOrd', title: '簽單順序',sortable:true,width:100}
					,
																                    { field: 'signNam', title: '簽核流程節點名稱',sortable:true,width:100}
					,
																                    { field: 'eserId', title: '簽核人工號',sortable:true,width:100}
					,
																                    { field: 'eserNam', title: '簽核人姓名',sortable:true,width:100}
					,
																                    { field: 'eserMail', title: '簽核人郵件',sortable:true,width:100}
					,
																                    { field: 'eserStat', title: '審核狀態(W待審核Y審核通過N駁回E結案)',sortable:true,width:100}
					,
																                    { field: 'eserMemo', title: '簽核批註',sortable:true,width:100}
					,
																                    { field: 'eserDate', title: '簽核日期',sortable:true,width:100}
					,
																                    { field: 'eserIp', title: '簽核電腦IP',sortable:true,width:100}
					,
																                    { field: 'crter', title: '建立人員',sortable:true,width:100}
					,
																                    { field: 'crtdate', title: '建立日期',sortable:true,width:100}
					,
																                    { field: 'mdier', title: '修改人員',sortable:true,width:100}
					,
																                    { field: 'mdidate', title: '修改日期',sortable:true,width:100}
												        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        rowTooltip: true,
        toolbar:'#tb'
    });
});

//弹窗增加
function add() {
    d=$("#dlg").dialog({
        title: '添加申請表單審核記錄表',
        width: 380,
        height: 380,
        href:ctx+'/eformsign/create',
        maximizable:true,
        modal:true,
        buttons:[{
            text:'确认',
            handler:function(){
                $("#mainform").submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}

//删除
function del(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data){
        if (data){
	$.ajax({
                type:'get',
                url:ctx+"/eformsign/delete/"+row.applyId,
                success: function(data){
                    successTip(data,dg);
                }
            });
        }
    });
}

//弹窗修改
function upd(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    d=$("#dlg").dialog({
        title: '修改申請表單審核記錄表',
        width: 380,
        height: 340,
        href:ctx+'/eformsign/update/'+row.applyId,
        maximizable:true,
        modal:true,
        buttons:[{
            text:'修改',
            handler:function(){
                $('#mainform').submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}
//创建查询对象并查询
function cx(){
    var obj=$("#searchFrom").serializeObject();
    dg.datagrid('load',obj);
}
//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("reset");
}
//导出excel
function exportExcel(){
    var form = document.getElementById("searchFrom");
    searchFrom.action=ctx+'/eformsign/exportExcel';
    form.submit();
}