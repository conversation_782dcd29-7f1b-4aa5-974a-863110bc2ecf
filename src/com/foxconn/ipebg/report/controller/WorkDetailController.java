package com.foxconn.ipebg.report.controller;

import java.sql.SQLException;
import java.sql.Types;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Date;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.report.entity.WorkDetailEntity;
import com.foxconn.ipebg.report.service.WorkDetailService;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;


/**
 * 班別資料
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-03-07 09:57:22
 */
@Controller
@RequestMapping("workdetail")
public class WorkDetailController extends BaseController {

    @Autowired
    private WorkDetailService workDetailService;
   
    /**
      * 方法描述: 列表信息
      * @Author: S6113712
      * @CreateDate:   2019-03-07 09:57:22
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:bsshift:list")
    public String list() {
        //查询列表数据
        return "report/workdetail";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6113712
      * @CreateDate:   2019-03-07 09:57:22
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:bsshift:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
    	
    	String empNo=request.getParameter("empNo");
    	String empName=request.getParameter("empName");
    	String postType=request.getParameter("postType");
    	String qryDate=request.getParameter("qryDate");
    	String securityCom = request.getParameter("securityCom");

    	//int result = workStatusService.callProc("{CALL search_clean_area_sum(?0,?1,?2)}", "JC", "2018-07-01", "2018-07-11");
    	//int result = workStatusService.callProc("{CALL pk_workstatus_rpt.select_workstatus_detail(?0,?1)}", "JC", "");
//    	int result = workDetailService.callProc("{CALL pk_rptws_cal_select_workdetail_detail(?0,?1,?2,?3,?4)}", empNo, empName, postType, qryDate, "");
//        workDetailService.callProc1("{CALL pk_rptws_cal_select_workdetail_detail(?0,?1,?2,?3)}", empNo, empName, postType, qryDate);
//    	workDetailService.callProc1("{CALL pk_rptws_cal_select_workdetail_detail("+ empNo +","+ empName +","+ postType +","+ qryDate +")}");
        try {
            workDetailService.executeProcedure("{CALL pk_rptws_cal_select_workdetail_detail(?,?,?,?,?,?)}", new String[]{empNo, empName, postType, qryDate, securityCom}, new int[]{Types.VARCHAR}, null);
        } catch (SQLException e) {
            e.printStackTrace();
//            throw e;
        }
    	
        Page<WorkDetailEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        page = workDetailService.search(page, filters);
        return getEasyUIData(page);
    }

    /**
      * 导出excel
      * @Author: S6113712
      * @CreateDate:   2019-03-07 09:57:22
      * @Return
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {}
}
