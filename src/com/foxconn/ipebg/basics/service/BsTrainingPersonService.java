package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.entity.BsTraineeEntity;
import com.foxconn.ipebg.basics.entity.BsTrainingPersonEntity;
import com.foxconn.ipebg.basics.dao.BsTrainingPersonDao;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.hibernate.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.Dict;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WorkFlowEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.system.service.DictService;

import java.util.Date;
import java.util.List;

/**
 * 培訓期培訓人員明細基本資料
 *
 * <AUTHOR>
 * @email <EMAIL> 
 * @date 2019-08-29 10:42:48
 */
@Service
@Transactional(readOnly=true)
public class  BsTrainingPersonService extends BaseBusinessService<BsTrainingPersonEntity, String>{
    @Autowired
    private BsTrainingPersonDao bsTrainingPersonDao;

    @Autowired
    private UserServiceUtil serviceUtil;
    @Autowired
    private DictService dictService;

    @Override
    public HibernateDao<BsTrainingPersonEntity, String> getEntityDao() {
        return bsTrainingPersonDao;
    }

    public BsTrainingPersonEntity findBySerialno(String serialno) {
        return this.bsTrainingPersonDao.findUniqueBy("serialno", serialno);
    }
    
    public BsTrainingPersonEntity findByEmpNo(String empNo) {
        return this.bsTrainingPersonDao.findUniqueBy("empNo", empNo);
    }
    
 // 人員刪除驗證
 	public boolean isExistEmpNo(String empNo) {
 		boolean result = false;
 		List<BsTrainingPersonEntity> list = bsTrainingPersonDao
 				.find("from BsTrainingPersonEntity t where t.empNo=?0  ",//and t.empNo=?1
 						
 						empNo);
 		if (list.size() > 0) {
 			result = true;
 		} else {
 			result = false;
 		}
 		return result;
 	}
 	//培訓期刪除驗證
  	public boolean isExistTrainingPeriodNo(String trainingPeriodNo) {
  		boolean result = false;
  		List<BsTrainingPersonEntity> list = bsTrainingPersonDao
  				.find("from BsTrainingPersonEntity t where t.trainingPeriodNo=?0  ",//and t.empNo=?1
  						
  						trainingPeriodNo);
  		if (list.size() > 0) {
  			result = true;
  		} else {
  			result = false;
  		}
  		return result;
  	}

	public Page<BsTrainingPersonEntity> getTraineeReportDetailList(final Page<BsTrainingPersonEntity> page, BsTrainingPersonEntity bsTrainingPerson) {

		StringBuilder sb = new StringBuilder();
		StringBuilder countSB = new StringBuilder();
		sb.append(" select * from v_report_detail t ").append(" WHERE 1=1  ");
	    countSB.append("select count(1) from v_report_detail t where 1=1 ");
	    if (!bsTrainingPerson.getTrainingPeriodNo().equals(""))//||!serialnoQry.equals(null)
        {
        	sb.append("  and t.training_period_no like '%"+bsTrainingPerson.getTrainingPeriodNo()+"%' ");
        	countSB.append("  and t.training_period_no like '%"+bsTrainingPerson.getTrainingPeriodNo()+"%' ");
        }
        if (!bsTrainingPerson.getEmpNo().equals(""))//||!serialnoQry.equals(null)
        {
        	sb.append("  and t.emp_no like '%"+bsTrainingPerson.getEmpNo()+"%' ");
        	countSB.append("  and t.emp_no like '%"+bsTrainingPerson.getEmpNo()+"%' ");
		}
        if (!bsTrainingPerson.getEmpName().equals(""))//||!serialnoQry.equals(null)
        {
        	sb.append("  and t.emp_name like '%"+bsTrainingPerson.getEmpName()+"%' ");
        	countSB.append("  and t.emp_name like '%"+bsTrainingPerson.getEmpName()+"%' ");
		}
        
        if (!bsTrainingPerson.getCompany().equals(""))//||!serialnoQry.equals(null)
        {
        	sb.append("  and t.company = '"+bsTrainingPerson.getCompany()+"' ");
        	countSB.append("  and t.company = '"+bsTrainingPerson.getCompany()+"' ");
        }

		sb.append(" ORDER BY emp_no limit " + page.getPageSize() + " offset " + ((page.getPageNo() - 1) * page.getPageSize()));
		@SuppressWarnings("unchecked")
		List<BsTrainingPersonEntity> personList = this.bsTrainingPersonDao
				.createSQLQuery(sb.toString()).addEntity(BsTrainingPersonEntity.class)
				.list();
		// 計算 count
		Query cntQuery = this.bsTrainingPersonDao.createSQLQuery(countSB.toString());
		page.setTotalCount(((Number)cntQuery.uniqueResult()).longValue());
		page.setResult(personList);
		return page;
	}
}
