package com.foxconn.ipebg.buessness.common.service;

import com.foxconn.ipebg.buessness.common.entity.TQhChargelogEntity;
import com.foxconn.ipebg.buessness.common.dao.TQhChargelogDao;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * 主管簽核日誌表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-08 09:28:35
 */
@Service
@Transactional(readOnly=true)
public class  TQhChargelogService extends BaseBusinessService<TQhChargelogEntity, String>{
    @Autowired
    private TQhChargelogDao tQhChargelogDao;
    @Override
    public HibernateDao<TQhChargelogEntity, String> getEntityDao() {
        return tQhChargelogDao;
    }

    public List<TQhChargelogEntity> queryChargeLog(String serialNo){
        return this.tQhChargelogDao.findBy("serialno",serialNo);
    }
}
