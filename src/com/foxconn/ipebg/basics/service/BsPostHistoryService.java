package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.dao.BsPostHistoryDao;
import com.foxconn.ipebg.basics.entity.BsPostHistoryEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 崗位修改记录明細表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-03-07 09:57:22
 */
@Service
@Transactional(readOnly = true)
public class BsPostHistoryService extends BaseBusinessService<BsPostHistoryEntity, String> {
	@Autowired
	private BsPostHistoryDao bsPostDao;

	@Override
	public HibernateDao<BsPostHistoryEntity, String> getEntityDao() {
		return bsPostDao;
	}
}
