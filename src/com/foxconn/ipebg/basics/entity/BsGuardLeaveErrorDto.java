package com.foxconn.ipebg.basics.entity;

import java.util.Date;
import java.util.LinkedHashMap;

public class BsGuardLeaveErrorDto {
    private String empNo;

    private String empName;

    private String startDate;

    private String endDate;

    private String leaveType;

    private String shiftNo;

    private String dayCount;

    private String reason;

    private String errorLog;

    public static LinkedHashMap<String, String> getFieldMap() {
        LinkedHashMap<String, String> fieldMap = new LinkedHashMap<String, String>();
        fieldMap.put("empNo", "工號");
        fieldMap.put("empName", "姓名");
        fieldMap.put("leaveType", "假別");
        fieldMap.put("shiftNo", "請假日班別");
        fieldMap.put("startDate", "請假開始日期");
        fieldMap.put("endDate", "請假結束日期");
        fieldMap.put("dayCount", "請假天數");
        fieldMap.put("reason", "請假事由");
        fieldMap.put("errorLog", "導入失敗原因");
        return fieldMap;
    }

    public String getEmpNo() {
        return empNo;
    }

    public void setEmpNo(String empNo) {
        this.empNo = empNo;
    }

    public String getEmpName() {
        return empName;
    }

    public void setEmpName(String empName) {
        this.empName = empName;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getErrorLog() {
        return errorLog;
    }

    public void setErrorLog(String errorLog) {
        this.errorLog = errorLog;
    }

    public String getLeaveType() {
        return leaveType;
    }

    public void setLeaveType(String leaveType) {
        this.leaveType = leaveType;
    }

    public String getShiftNo() {
        return shiftNo;
    }

    public void setShiftNo(String shiftNo) {
        this.shiftNo = shiftNo;
    }

    public String getDayCount() {
        return dayCount;
    }

    public void setDayCount(String dayCount) {
        this.dayCount = dayCount;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}
