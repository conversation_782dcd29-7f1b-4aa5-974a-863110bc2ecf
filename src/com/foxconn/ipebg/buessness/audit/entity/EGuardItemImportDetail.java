package com.foxconn.ipebg.buessness.audit.entity;

import com.foxconn.ipebg.basics.entity.BsPostEntity;

import java.util.List;

public class EGuardItemImportDetail {
    List<EGuardItemErrorDto> errorDtoList;
    List<BsPostEntity> itemList;

    public List<EGuardItemErrorDto> getErrorDtoList() {
        return errorDtoList;
    }

    public void setErrorDtoList(List<EGuardItemErrorDto> errorDtoList) {
        this.errorDtoList = errorDtoList;
    }

    public List<BsPostEntity> getItemList() {
        return itemList;
    }

    public void setItemList(List<BsPostEntity> itemList) {
        this.itemList = itemList;
    }
}
