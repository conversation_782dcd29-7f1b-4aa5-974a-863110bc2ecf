package com.foxconn.ipebg.system.service;

import com.foxconn.ipebg.system.entity.TPubFileobjectEntity;
import com.foxconn.ipebg.system.utils.FTPUtil;
import com.foxconn.ipebg.system.dao.TPubFileobjectDao;
import com.foxconn.ipebg.audit.entity.EFormSignEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;


/**
 * 上傳文件附件信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-10 15:38:57
 */
@Service
@Transactional(readOnly=true)
public class  TPubFileobjectService extends BaseBusinessService<TPubFileobjectEntity, String>{
    @Autowired
    private TPubFileobjectDao tPubFileobjectDao;
    @Autowired
    private DictService dictService;
    @Override
    public HibernateDao<TPubFileobjectEntity, String> getEntityDao() {
        return tPubFileobjectDao;
    }

    public TPubFileobjectEntity findById(String id){
        return tPubFileobjectDao.findUniqueBy("id",id);
    }

    public List<TPubFileobjectEntity> findByIds(String ids, String ctx) {
    	List<TPubFileobjectEntity> list =null;
        if(ids==null){
            return null;
        }
        //String filePath = entity.getUrl();
        String ftpIp = dictService.get(2017102503).getValue();
        String ftpUser = dictService.get(2017102501).getValue();
        String ftpPass = dictService.get(2017102502).getValue();
        String rootPath = dictService.get(101002).getValue();
        FTPUtil ftpUtil = new FTPUtil(ftpIp,21,ftpUser,ftpPass,rootPath);
        
         list =tPubFileobjectDao.findIn("id", Arrays.asList(ids.split(",")));
         TPubFileobjectEntity tPubFileobjectEntity = new TPubFileobjectEntity();
         for (int i=0;i<list.size();i++)
         {
             list.get(i).setUrl(ctx + "/admin/download/"+list.get(i).getId());
         }
         return list ;
    }
   
}
