var dg;
var d;
$(function () {
    //绑定培訓期下拉框
    $('#trainingPeriodNo').combogrid({
        data: _periodList,
        panelWidth: 200,
        multiple: false,
        idField: 'training_period_no',
        textField: 'training_period_name',
        fitColumns: true,
        columns: [[
            {field: 'training_period_name', title: '培訓期名稱', width: 80}
        ]]
    });
    if($("#hidePostRecno").val()){
        $("#searchFrom").hide();
    }
    dg = $('#dg').datagrid({
        method: "get",
        url: ctx + '/bstrainingdaycost/list',
        fit: true,
        fitColumns: false,
        border: false,
        idField: 'id',
        striped: true,
        queryParams:{
            sort:'trainingPeriodNo',
            order:'desc',
            // filter_EQS_workMonth:$("#hideWorkMonth").val(),
            filter_EQS_postRecno:$("#hidePostRecno").val(),
            filter_GED_costDate:$("#hideCostStartDate").val(),
            filter_LED_costDate:$("#hideCostEndDate").val()
        },
        pagination: true,
        rownumbers: true,
        pageNumber: 1,
        pageSize: 20,
        pageList: [10, 20, 30, 40, 50],
        singleSelect: true,
        frozenColumns:[[
            {field: 'id', title: '資料識別ID  Primary', hidden: true, width: 100},
            {field: 'trainingPeriodName', title: '培訓期', sortable: true, width: 120}
            ,
            {field: 'empNo', title: '工號', sortable: true, width: 80}
            ,
            {field: 'empName', title: '姓名', sortable: true, width: 80}
        ]],
        columns: [[
            {field: 'dates', title: '培訓日期', sortable: true, width: 500}
            ,
            {field: 'days', title: '培訓天數', sortable: true,align: 'right', width: 60}
            ,
            {field: 'trainingCost', title: '培訓費用', sortable: true,align: 'right', width: 60}
            ,
            {field: 'trainingPeriodNo', title: '培訓期編碼',  hidden: true, width: 100}
            ,
            {field: 'costDate', title: '結算日期', sortable: false, width: 80}
            ,
            {field: 'startDate', title: '培訓開始時間', sortable: true, width: 100,
                formatter:function(value,row,index){
                    return value.substr(0,10);
                }
            }
            ,
            {field: 'endDate', title: '培訓結束時間', sortable: true, width: 100,
                formatter:function(value,row,index){
                    return value.substr(0,10);
                }
            }

        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        rowTooltip: true,
        toolbar: '#tb'
    });
});

//创建查询对象并查询
function cx() {
    var obj = $("#searchFrom").serializeObject();
    dg.datagrid('load', obj);
}

//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("reset");
}

//导出excel
function exportExcel() {
    var form = document.getElementById("searchFrom");
    searchFrom.action = ctx + '/bstrainingcost/exportExcel';
    form.submit();
}