package com.foxconn.ipebg.system.utils;

import com.foxconn.ipebg.common.utils.Global;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.PrintWriter;
public class AjaxFilter implements Filter {
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    public void doFilter(ServletRequest servletRequestt, ServletResponse servletResponse,
                         FilterChain chain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequestt;
        HttpServletResponse response = (HttpServletResponse) servletResponse;

        String currentURL = request.getRequestURI();//取得根目录所对应的绝对路径:
        //String targetURL = currentURL.substring(currentURL.indexOf("/", 1), currentURL.length());  //截取到当前文件名用于比较
        /*String strURL = request.getRequestURL().toString();
        if (strURL.indexOf("/newEsign/login") == -1 && strURL.indexOf("static/images/kaptcha.jpg") == -1) {
            //获得session判断是否存在
            User user = UserUtil.getCurrentUser();
            if (user == null) {
                String errors = "您还没有登录，或者session已过期。请先登陆!";
                request.setAttribute("Message", errors);
                //跳转至登录页面
                request.getRequestDispatcher("{adminPath}/login").forward(request, response);
            } else {
                //判斷是否ajax請求超時
                String ajaxSubmit = request.getHeader("X-Requested-With");
                if (ajaxSubmit != null && ajaxSubmit.equals("XMLHttpRequest")) {
                    response.setHeader("sessionstatus", "timeout");
                    response.getWriter().print("sessionstatus");
                    return;
                }
            }
        }

        chain.doFilter(request, response);*/

        // 超时处理，ajax请求超时设置超时状态，页面请求超时则返回提示并重定向
        HttpSession session = request.getSession();
        String loginUrl= Global.getAdminPath()+"/login";
        if (currentURL.indexOf(Global.getAdminPath()+"/login") == -1
                && currentURL.indexOf("/static/images/kaptcha.jpg") == -1
                && currentURL.indexOf("/audit/login") == -1
                && currentURL.indexOf("/eguardaply/auditMail/") == -1
                && currentURL.indexOf("/eguardaply/viewMail/") == -1
                && currentURL.indexOf("/tempSecurityAply/auditMail/") == -1
                && currentURL.indexOf("/tempSecurityAply/viewMail/") == -1
                && currentURL.indexOf("/tempSecurityCostAply/auditMail/") == -1
                && currentURL.indexOf("/tempSecurityCostAply/viewMail/") == -1
                && currentURL.indexOf("/eformsign/") == -1		
                && currentURL.indexOf("/wfcontroller/listForOld") == -1
                && currentURL.indexOf("/static/") == -1
                && currentURL.indexOf("/system/status/") == -1
                &&session.getAttribute("user") == null) {
            // 判断是否为ajax请求
            if (request.getHeader("x-requested-with") != null
                    && request.getHeader("x-requested-with")
                    .equalsIgnoreCase("XMLHttpRequest")) {
                response.addHeader("sessionstatus", "timeOut");
                response.addHeader("loginPath", loginUrl);
                chain.doFilter(request, response);// 不可少，否则请求会出错
            } else {
                response.sendRedirect(currentURL.substring(0,currentURL.lastIndexOf("/"))+Global.getAdminPath()+"/login");
            }
        } else {
            chain.doFilter(request, response);
        }


        /*String ajaxSubmit = request.getHeader("X-Requested-With");
        if (ajaxSubmit != null && ajaxSubmit.equals("XMLHttpRequest")) {
            if (request.getSession(false) == null) {
                response.setHeader("sessionstatus", "timeout");
                response.getWriter().print("sessionstatus");
                return;
            }
        }
        chain.doFilter(servletRequestt, servletResponse);*/
    }

    public void destroy() {

    }
}
