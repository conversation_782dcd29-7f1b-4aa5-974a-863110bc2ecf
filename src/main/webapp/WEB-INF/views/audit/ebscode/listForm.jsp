<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>簽核節點信息</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>

    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/ebscode/${action}" method="post">
    <table class="formTable">
        <input type="hidden" name="ids" value="${eBsCode.id}"/>
        <tr>
            <td>流程類型：</td>
            <td>
                <input id="codeTyp" name="codeTyp" class="easyui-validatebox" data-options="width: 180,required:true"
                       value="${eBsCode.codeTyp }"/>
            </td>
        </tr>
        <tr>
            <td>排序：</td>
            <td>
                <input id="codeId" name="codeId" class="easyui-validatebox" data-options="width: 180,required:true"
                       value="${eBsCode.codeId }"/>
            </td>
        </tr>
        <tr>
            <td>簽核節點名稱：</td>
            <td>
                <input id="codeNam" name="codeNam" class="easyui-validatebox" data-options="width: 180,required:true"
                       value="${eBsCode.codeNam }"/>
            </td>
        </tr>
        <tr>
            <td>是否必選節點：</td>
            <td>
                <input id="required" name="required" class="easyui-combobox"
                       data-options="width: 180,
                                    data:[{id:'0',text:'可選'},{id:'1',text:'必選'}],
                    valueField:'id',
                     textField:'text',
                   panelHeight:'auto',
                                required:true"
                       value="${eBsCode.required}" />
            </td>
        </tr>
        <tr>
            <td>備註：</td>
            <td>
                <input id="codeMemo" name="codeMemo" class="easyui-validatebox" data-options="width: 180"
                       value="${eBsCode.codeMemo }"/>
            </td>
        </tr>
        <tr style="display: none">
            <td>
                <input id="createBy" name="createBy" class="easyui-validatebox" data-options="width: 180" readonly
                       value="${eBsCode.createBy }"/>
                <input id="createDate" name="createDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
                       data-options="width: 180" disabled="disabled"
                       value="<fmt:formatDate value="${eBsCode.createDate}"/>"/>
                <input id="updateBy" name="updateBy" class="easyui-validatebox" data-options="width: 180" readonly
                       value="${eBsCode.updateBy }"/>
                <input id="updateDate" name="updateDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
                       data-options="width: 180" disabled="disabled"
                       value="<fmt:formatDate value="${eBsCode.updateDate}"/>"/>
            </td>
        </tr>
    </table>
</form>
</div>
<script src='${ctx}/static/js/audit/ebscode.js?"+Math.random()"'></script>
<script type="text/javascript">
    //提交表单
    $('#mainform').form({
        onSubmit: function () {
            var isValid = $(this).form('validate');
            return isValid;	// 返回false终止表单提交
        },
        success: function (data) {
            successTip(data, dg, d);
        }
    });
</script>
</body>
</html>
