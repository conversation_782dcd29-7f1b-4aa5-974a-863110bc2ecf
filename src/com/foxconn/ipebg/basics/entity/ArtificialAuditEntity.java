package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * 人工稽核錄入基本資料
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-18 09:09:19
 */
@Entity
@Table(name = "ARTIFICIAL_AUDIT")
@DynamicUpdate
@DynamicInsert
public class ArtificialAuditEntity extends DataEntity<ArtificialAuditEntity>
		implements Serializable {
	private static final long serialVersionUID = 1L;

	// 工號
	private String empNo;
	// 姓名
	private String empName;
	// 公司名稱
	private String company;
	// 崗位編號
	private String postRecno;
	// 崗位名稱
	private String postName;
	// 排班日期
	private Date shiftDate;
	// 班別代碼
	private String shiftNo;
	// 班別
	private String shiftName;
	// 上班時間
	private Date cardTime;
	// 異常狀態
	private String abnormalState;
	// 第一次異常結果
	private String abnormalResult1;
	// 第一次異常原因
	private String abnormalCauses1;
	// 第二次異常結果
	private String abnormalResult2;
	// 第二次異常原因
	private String abnormalCauses2;

	/**
	 * 设置：工號
	 */
	public void setEmpNo(String empNo) {
		this.empNo = empNo;
	}

	/**
	 * 获取：工號
	 */

	@Column(name = "EMP_NO", nullable = false, length = 20)
	public String getEmpNo() {
		return empNo;
	}

	/**
	 * 设置：姓名
	 */
	public void setEmpName(String empName) {
		this.empName = empName;
	}

	/**
	 * 获取：姓名
	 */

	@Column(name = "EMP_NAME", nullable = false, length = 20)
	public String getEmpName() {
		return empName;
	}

	/**
	 * 设置：公司名稱
	 */
	public void setCompany(String company) {
		this.company = company;
	}

	/**
	 * 获取：公司名稱
	 */

	@Column(name = "COMPANY", nullable = false, length = 20)
	public String getCompany() {
		return company;
	}

	/**
	 * 设置：崗位編號
	 */
	public void setPostRecno(String postRecno) {
		this.postRecno = postRecno;
	}

	/**
	 * 获取：崗位編號
	 */

	@Column(name = "POST_RECNO", nullable = false, length = 20)
	public String getPostRecno() {
		return postRecno;
	}

	/**
	 * 设置：崗位名稱
	 */
	public void setPostName(String postName) {
		this.postName = postName;
	}

	/**
	 * 获取：崗位名稱
	 */

	@Column(name = "POST_NAME", nullable = false, length = 20)
	public String getPostName() {
		return postName;
	}

	/**
	 * 设置：排班日期
	 */
	public void setShiftDate(Date shiftDate) {
		this.shiftDate = shiftDate;
	}

	/**
	 * 获取：排班日期
	 */

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Column(name = "SHIFT_DATE", nullable = false, length = 20)
	public Date getShiftDate() {
		return shiftDate;
	}

	/**
	 * 设置：班別代碼
	 */
	public void setShiftNo(String shiftNo) {
		this.shiftNo = shiftNo;
	}

	/**
	 * 获取：班別代碼
	 */

	@Column(name = "SHIFT_NO", nullable = false, length = 20)
	public String getShiftNo() {
		return shiftNo;
	}

	/**
	 * 设置：班別
	 */
	public void setShiftName(String shiftName) {
		this.shiftName = shiftName;
	}

	/**
	 * 获取：班別
	 */

	@Column(name = "SHIFT_NAME", nullable = false, length = 20)
	public String getShiftName() {
		return shiftName;
	}

	/**
	 * 设置：上班時間
	 */
	public void setCardTime(Date cardTime) {
		this.cardTime = cardTime;
	}

	/**
	 * 获取：上班時間
	 */

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Column(name = "CARD_TIME", nullable = false, length = 20)
	public Date getCardTime() {
		return cardTime;
	}

	/**
	 * 设置：異常狀態
	 */
	public void setAbnormalState(String abnormalState) {
		this.abnormalState = abnormalState;
	}

	/**
	 * 获取：異常狀態
	 */

	@Column(name = "ABNORMAL_STATE", nullable = false, length = 20)
	public String getAbnormalState() {
		return abnormalState;
	}

	/**
	 * 设置：第一次異常結果
	 */
	public void setAbnormalResult1(String abnormalResult1) {
		this.abnormalResult1 = abnormalResult1;
	}

	/**
	 * 获取：第一次異常結果
	 */

	@Column(name = "ABNORMAL_RESULT1", nullable = false, length = 20)
	public String getAbnormalResult1() {
		return abnormalResult1;
	}

	/**
	 * 设置：第一次異常原因
	 */
	public void setAbnormalCauses1(String abnormalCauses1) {
		this.abnormalCauses1 = abnormalCauses1;
	}

	/**
	 * 获取：第一次異常原因
	 */

	@Column(name = "ABNORMAL_CAUSES1", nullable = false, length = 20)
	public String getAbnormalCauses1() {
		return abnormalCauses1;
	}

	/**
	 * 设置：第二次異常結果
	 */
	public void setAbnormalResult2(String abnormalResult2) {
		this.abnormalResult2 = abnormalResult2;
	}

	/**
	 * 获取：第二次異常結果
	 */

	@Column(name = "ABNORMAL_RESULT2", nullable = false, length = 20)
	public String getAbnormalResult2() {
		return abnormalResult2;
	}

	/**
	 * 设置：第二次異常原因
	 */
	public void setAbnormalCauses2(String abnormalCauses2) {
		this.abnormalCauses2 = abnormalCauses2;
	}

	/**
	 * 获取：第二次異常原因
	 */

	@Column(name = "ABNORMAL_CAUSES2", nullable = false, length = 20)
	public String getAbnormalCauses2() {
		return abnormalCauses2;
	}

}
