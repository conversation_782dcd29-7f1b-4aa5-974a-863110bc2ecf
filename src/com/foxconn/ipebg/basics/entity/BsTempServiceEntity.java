package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 臨時勤務信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-04 09:10:27
 */
@Entity
@Table(name = "BS_TEMP_SERVICE")
@DynamicUpdate
@DynamicInsert
public class BsTempServiceEntity extends DataEntity<BsTempServiceEntity> implements Serializable {
    private static final long serialVersionUID = 1L;


    //事由
    private String cause;
    //地點
    private String location;
    //人數
    private Integer peopleNumber;
    //開始時間
    private Date startTime;
    //結束時間
    private Date endTime;
    //法人
    private String legalPerson;
    //用人單位
    private String employerUnit;
    //費用代碼
    private String costCode;
    //臨時勤務費用(分)
    private Integer expenses;


    /**
     * 设置：事由
     */
    public void setCause(String cause) {
        this.cause = cause;
    }

    /**
     * 获取：事由
     */

    @Column(name = "CAUSE", nullable = false, length = 20)
    public String getCause() {
        return cause;
    }

    /**
     * 设置：地點
     */
    public void setLocation(String location) {
        this.location = location;
    }

    /**
     * 获取：地點
     */

    @Column(name = "LOCATION", nullable = false, length = 20)
    public String getLocation() {
        return location;
    }

    /**
     * 设置：人數
     */
    public void setPeopleNumber(Integer peopleNumber) {
        this.peopleNumber = peopleNumber;
    }

    /**
     * 获取：人數
     */

    @Column(name = "PEOPLE_NUMBER", nullable = false, length = 20)
    public Integer getPeopleNumber() {
        return peopleNumber;
    }

    /**
     * 设置：開始時間
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    /**
     * 获取：開始時間
     */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "START_TIME", nullable = false, length = 20)
    public Date getStartTime() {
        return startTime;
    }

    /**
     * 设置：結束時間
     */
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    /**
     * 获取：結束時間
     */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "END_TIME", nullable = false, length = 20)
    public Date getEndTime() {
        return endTime;
    }

    /**
     * 设置：法人
     */
    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    /**
     * 获取：法人
     */

    @Column(name = "LEGAL_PERSON", nullable = false, length = 20)
    public String getLegalPerson() {
        return legalPerson;
    }

    /**
     * 设置：用人單位
     */
    public void setEmployerUnit(String employerUnit) {
        this.employerUnit = employerUnit;
    }

    /**
     * 获取：用人單位
     */

    @Column(name = "EMPLOYER_UNIT", nullable = false, length = 20)
    public String getEmployerUnit() {
        return employerUnit;
    }

    /**
     * 设置：費用代碼
     */
    public void setCostCode(String costCode) {
        this.costCode = costCode;
    }

    /**
     * 获取：費用代碼
     */

    @Column(name = "COST_CODE", nullable = false, length = 20)
    public String getCostCode() {
        return costCode;
    }

    /**
     * 设置：臨時勤務費用(分)
     */
    public void setExpenses(Integer expenses) {
        this.expenses = expenses;
    }

    /**
     * 获取：臨時勤務費用(分)
     */

    @Column(name = "EXPENSES", nullable = false, length = 20)
    public Integer getExpenses() {
        return expenses;
    }


}
