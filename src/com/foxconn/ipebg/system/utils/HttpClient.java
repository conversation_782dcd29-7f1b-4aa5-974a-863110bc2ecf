package com.foxconn.ipebg.system.utils;

/**
 * Company foxconn
 * Copyright (C) 2004-2018 All Rights Reserved.
 */

import cn.hutool.core.util.XmlUtil;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;

/**
 * <p>Title: HttpClient.java</p>
 * <p>Description:HttpURLConnection调用方式</p>
 */
public class HttpClient {

    public static void main(String[] args) throws IOException {
        try {
            //第一步：创建服务地址，不是WSDL地址
            URL url = new URL("http://localhost:8083/RemoteServicePort");
            //第二步：打开一个通向服务地址的连接
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            //第三步：设置参数
            //3.1发送方式设置：POST必须大写
            connection.setRequestMethod("POST");
            //3.2设置数据格式：content-type
            connection.setRequestProperty("content-type", "text/xml;charset=utf-8");
            //3.3设置输入输出，因为默认新创建的connection没有读写权限，
            connection.setDoInput(true);
            connection.setDoOutput(true);

            //第四步：组织SOAP数据，发送请求
            String soapXML = getXML("AD");
            OutputStream os = connection.getOutputStream();
            os.write(soapXML.getBytes());
            //第五步：接收服务端响应，打印
            int responseCode = connection.getResponseCode();
            if (200 == responseCode) {//表示服务端响应成功
                InputStream is = connection.getInputStream();
                InputStreamReader isr = new InputStreamReader(is);
                BufferedReader br = new BufferedReader(isr);

                StringBuilder sb = new StringBuilder();
                String temp = null;
                while (null != (temp = br.readLine())) {
                    sb.append(temp);
                }
                System.out.println(sb.toString());
                System.out.println(XmlUtil.xmlToMap(sb.toString()));
                is.close();
                isr.close();
                br.close();
            }

            os.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * <?xml version="1.0" encoding="utf-8"?>
     * <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
     * <soap:Body>
     * <getMobileCodeInfo xmlns="http://WebXml.com.cn/">
     * <mobileCode>string</mobileCode>
     * <userID>string</userID>
     * </getMobileCodeInfo>
     * </soap:Body>
     * </soap:Envelope>
     *
     * @param empno
     * @return
     */
    public static String getXML(String empno) {
        String soapXML = "<?xml version=\"1.0\" encoding=\"utf-8\"?>"
                + "<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">"
                + "<soap:Body>"
                + "<getAllMyTasks xmlns=\"http://ws.chn.foxconn.com/\">"
                + "<empno>"+empno+"</empno>"
//                + "<arg0>"+empno+"</arg0>"
                + "</getAllMyTasks>"
                + "</soap:Body>"
                + "</soap:Envelope>";
//                + "</xml>";
        return soapXML;
    }
}

