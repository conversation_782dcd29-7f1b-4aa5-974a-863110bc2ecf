package com.foxconn.ipebg.common.utils;

import org.apache.log4j.Logger;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;


/**
 * 
 *日期轉換
 */
public class DateUtil {
    private static Logger logger = Logger.getLogger(DateUtil.class);
	private static long dayMillis = 1000L*60*60*24;
	private static long timeOffSet=0L;
	/**
	 * 
	 * @param date
	 * @param formatPattern
	 * @return
	 */
	public static String formatedSqlDate(Date date,String formatPattern){
		if( date==null){
			return "";
		}
		Date d = new Date(date.getTime());
		formatPattern=formatChange(formatPattern);
		SimpleDateFormat df = new SimpleDateFormat(formatPattern);
		return df.format(d);
	}
	
	/**
	 * 判斷當前時間與傳入時間的時間差（以天為單位）
	 * <li>如果傳入時間比當前時間早，則返回正數
	 * <li>如果傳入時間比當前時間晚，則返回負數
	 * <li>如果傳入時間與當前的時間一致，則返回0
	 * <li>如果傳入參數為空，則返回 1
	 * @param date1
	 * @return
	 */
	public static int dateInterval(Date date1){
		return dateInterval(date1,null);
	}

	/**
	 * 判斷date2與date1的時間差（以天為單位）
	 * <li>如果date1比date2早，則返回正數
	 * <li>如果date1比date2晚，則返回負數
	 * <li>如果date1與date2一致，則返回0
	 * <li>如果傳入參數date1為空，則返回 0
	 * <li>如果傳入參數date2為空，則系統以當前時間做為此參數進行計算
	 * @param date1
	 * @param date2
	 * @return
	 */
	public static int dateInterval(Date date1,Date date2){
		
		if(date1 == null){
			return 0;
		}
		if(date2 == null){
			date2 = new Date();
		}
		
		Date before = truncDate(date1);
		Date after = truncDate(date2);
		
		return (int)((after.getTime() - before.getTime())/dayMillis);		
	}
	
	   /**
     * 判斷date2與date1的時間差（以秒為單位）
     * <li>如果date1比date2早，則返回正數
     * <li>如果date1比date2晚，則返回負數
     * <li>如果date1與date2一致，則返回0
     * <li>如果傳入參數date1為空，則返回 0
     * <li>如果傳入參數date2為空，則系統以當前時間做為此參數進行計算
     * @param date1
     * @param date2
     * @return
     */
    public static int dateIntervalOfSecond(Date date1,Date date2){
        
        if(date1 == null){
            return 0;
        }
        if(date2 == null){
            date2 = new Date();
        }
        
        Date before =date1; //truncDate(date1);
        Date after = date2;//truncDate(date2);
        
        return (int)((after.getTime() - before.getTime())/1000L);       
    }
	
	
	
	/**
	 * 計算傳入時間加上傳入天數后得到的時間
	 * <li>如果傳入時間比當前時間早，則返回正數
	 * <li>如果傳入時間比當前時間晚，則返回負數
	 * <li>如果傳入時間與當前的時間一致，則返回0
	 * <li>如果傳入參數為空，則返回 1
	 * @param date1
	 * @return
	 */
	public static Date dateAddDay(Date date,int day){
		Date inDate = truncDate(date);
		Date addAfterDate = new Date(inDate.getTime()+dayMillis*day);
		return addAfterDate;
	}
	
	public static Date dateAddDay(Date date,double day){
		//Date inDate = truncDate(date);
		Date addAfterDate = new Date((long) (date.getTime()+dayMillis*day));
		return addAfterDate;
	}
	/**
	 * 對時間做截取操作
	 * 獲取傳入時間的年月日，忽略時分秒毫秒信息，并返回截取后的日期信息
	 * @param date
	 * @return
	 */
	public static Date truncDateYM(Date date){
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		c.set(Calendar.DATE, 0);
		c.set(Calendar.HOUR_OF_DAY, 0);
		c.set(Calendar.MINUTE, 0);
		c.set(Calendar.SECOND, 0);		
		c.set(Calendar.MILLISECOND, 0);		
		return c.getTime();
	}
	/**
	 * 對時間做截取操作
	 * 獲取傳入時間的年月，忽略日時分秒信息，并返回截取后的日期信息
	 * @param date
	 * @return
	 */
	private static Date truncDate(Date date){
		Calendar c = Calendar.getInstance();
		c.setTime(date);		
		c.set(Calendar.HOUR_OF_DAY, 0);
		c.set(Calendar.MINUTE, 0);
		c.set(Calendar.SECOND, 0);		
		return c.getTime();
	}
	private static String formatChange(String format){
		String timeStr=format;
		int len1=timeStr.length();
		int len2=timeStr.replaceAll("'", "").length();
		int count=len1-len2;
		//format串中有''字符串標示符		
		if(count>0){
			//''字符未成對出現，直接返回
			if(count%2!=0){
				logger.info("設置的["+format+"]格式有錯");
				return "";
			}
			//對format做大小寫轉換
			String[] ss=timeStr.split("'");
			StringBuffer sb=new StringBuffer();
			int i=1;
			for(String s:ss){
				if(i%2==0){
				  sb.append("'").append(s).append("'");
				}else{
					String tmp=s.replaceAll("Y", "y");
					tmp=tmp.replaceAll("D", "d");
					sb.append(tmp);
				}
				i++;
			}
			format=sb.toString();
		}else{
			String tmp=timeStr.replaceAll("Y", "y");
			tmp=tmp.replaceAll("D", "d");
			format=tmp;
		}
		return format;
	}
	/**
	 * 获得当前时间的毫秒串
	 * @return
	 */
	public static String getNowInMillis(){
		Calendar c = Calendar.getInstance();
		return c.getTimeInMillis()+"";
	}
	/**
	 * 获得当前时间的毫秒串
	 * @return
	 */
	public static Long getNowLongMillis(){
		Calendar c = Calendar.getInstance();
		return c.getTimeInMillis();
	}
	/**
	 * 获得当前时间字符串表示
	 * 默認yyyyMMdd 返回
	 * @return
	 */
	public static String getNowChnYMD(){
		return getNowTime("yyyyMMdd");
	}
	/**
	 * 获得当前时间字符串表示
	 * 默認yyyyMMddHHmmss 返回
	 * @return
	 */
	public static String getNowChnYMDhms(){
		return getNowTime("yyyyMMddHHmmss");
	}
	/**
	 * 获得当前时间字符串表示
	 * 默認yyyy-MM-dd hh:mm:ss 返回
	 * @return
	 */
	public static String getNowYMD(){
		return getNowTime("yyyy-MM-dd");
	}
	/**
	 * 获得当前时间字符串表示
	 * 默認yyyy-MM-dd HH:mm:ss 返回
	 * @return
	 */
	public static String getNowTime(){
		return getNowTime("yyyy-MM-dd HH:mm:ss");
	}
	/**
	 * 按格式，获得当前时间字符串表示
	 * @param format 
	 * 格式：YYYY-MM-DD hh:mm:ss及YYyy-MM-dD hh:mm:ss 
	 * 會統一轉化為SimpleDateFormat要求的標準格式yyyy-MM-dd hh:mm:ss
	 * @return
	 */
	public static String getNowTime(String format){
		Calendar c = Calendar.getInstance();		
//		long milis=c.getTimeInMillis()-timeOffSet;
//		c.setTimeInMillis(milis);
        format=formatChange(format);
		return new SimpleDateFormat(format).format(c.getTime());
	}
	/**
	 * 查詢距離傳入時間前x分的時間
	 * @param format 格式
	 * @param date 傳入時間
	 * @param betMinute 相隔分鐘數
	 * @return
	 */
	public static String getBeforeMinite(String format,Date date,int betMinute){
		long milis=date.getTime()-timeOffSet;
		date.setTime(milis-(long)60000*betMinute);
		format=formatChange(format);
		return new SimpleDateFormat(format).format(date.getTime());
	}
	/**
	 * 查詢距離傳入時間后x分的時間
	 * @param format 格式
	 * @param date 傳入時間
	 * @param betMinute 相隔分鐘數
	 * @return
	 */
	public static String getAfterMinite(String format,Date date,int betMinute){
		long milis=date.getTime()+timeOffSet;
		date.setTime(milis+(long)60000*betMinute);
		format=formatChange(format);
		return new SimpleDateFormat(format).format(date.getTime());
	}
	/**
	 * 按默認格式，返回當前月第一天
	 * @param format yyyy-MM-dd
	 * @return
	 */
	public static String getFirstDay(){
		Calendar c = Calendar.getInstance();
		String format="yyyy-MM-dd";
		return getFirstDay(c.get(Calendar.YEAR),c.get(Calendar.MONTH)+1,format);
	}
	/**
	 * 按默認格式，返回當前月第一天
	 * @param format yyyyMMdd
	 * @return
	 */
	public static String getFirstDayYMD(){
		Calendar c = Calendar.getInstance();
		String format="yyyyMMdd";
		return getFirstDay(c.get(Calendar.YEAR),c.get(Calendar.MONTH)+1,format);
	}	
	/**
	 * 按格式，返回當前月第一天
	 * @param format
	 * @return
	 */
	public static String getFirstDay(String format){
		Calendar c = Calendar.getInstance();
		return getFirstDay(c.get(Calendar.YEAR),c.get(Calendar.MONTH)+1,format);
	}
	/**
	 * 按格式，获取某年，某月的第一天時間
	 * @param format
	 * @return
	 */
	
	public static String getFirstDay(int year,int month,String format){
		Calendar c = Calendar.getInstance();
//		long milis=c.getTimeInMillis()-timeOffSet;
//		c.setTimeInMillis(milis);
		
		int y=c.get(Calendar.YEAR);
		int m=c.get(Calendar.MONTH)+1;
		int yd=year-y;
		int md=month-m;
		c.add(Calendar.YEAR,yd);
		c.add(Calendar.MONTH,md);
		c.set(Calendar.DATE, 1);
		format=DateUtil.formatChange(format);
		return new SimpleDateFormat(format).format(c.getTime());
	}
	
	/**
	 * 按格式，获取某年，某月的最后一天時間
	 * @param format
	 * @return
	 */
	public static String getLastDay(int year,int month,String format){
		Calendar c = Calendar.getInstance();	
		int y=c.get(Calendar.YEAR);
		int m=c.get(Calendar.MONTH);
		int yd=year-y;
		int md=month-m;
		c.add(Calendar.YEAR,yd);
		c.add(Calendar.MONTH,md);
		c.set(Calendar.DATE, 1);
		c.add(Calendar.DATE, -1);		
		format=formatChange(format);
		return new SimpleDateFormat(format).format(c.getTime());		
	}
	/**
	 * 按格式，获取發前月的最后一天時間
	 * @param format
	 * @return
	 */
	public static String getLastDay(String format){
		Calendar c = Calendar.getInstance();
		return getLastDay(c.get(Calendar.YEAR),c.get(Calendar.MONTH)+1,format);
	}
	/**
	 * 默認获取發前月的最后一天時間
	 * @param format yyyy-MM-dd
	 * @return
	 */
	public static String getLastDay(){
		Calendar c = Calendar.getInstance();
		return getLastDay(c.get(Calendar.YEAR),c.get(Calendar.MONTH)+1,"yyyy-MM-dd");
	}
	/**
	 * 默認获取發前月的最后一天時間
	 * @param format yyyyMMdd
	 * @return
	 */
	public static String getLastDayYMD(){
		Calendar c = Calendar.getInstance();
		return getLastDay(c.get(Calendar.YEAR),c.get(Calendar.MONTH)+1,"yyyyMMdd");
	}
	/**
	 * 按默认获取昨天的日期
	 * @param format yyyy-MM-dd
	 * @return
	 */
	public static String getPreDay(){		
		return getPreDay(-1,"yyyy-MM-dd");		
	}
	/**
	 * 按默认获取昨天的日期
	 * @param format yyyyMMdd
	 * @return
	 */
	public static String getPreDayYMD(){		
		return getPreDay(-1,"yyyyMMdd");		
	}
	/**
	 * 按格式获取昨天的日期
	 * @param format
	 * @return
	 */
	public static String getPreDay(String format){	
		return getPreDay(-1,format);		
	}
	/**
	 * 按默認格式，获取明天的日期
	 * @param format yyyy-MM-dd
	 * @return
	 */
	public static String getNextDay(){
		return getPreDay(1,"yyyy-MM-dd");
	}
	/**
	 * 按默認格式，获取明天的日期
	 * @param format yyyyMMdd
	 * @return
	 */
	public static String getNextDayYMD(){
		return getPreDay(1,"yyyyMMdd");
	}
	/**
	 * 按格式获取明天的日期
	 * @param format
	 * @return
	 */
	public static String getNextDay(String format){
		return getPreDay(1,format);
	}
	/**
	 * 按格式获取 今天之前或之后的几天的日期
	 * @param hex 负数往后算，正数往前算
	 * @param format
	 * @return
	 */
	public static String getPreDay(int hex,String format){
		Calendar c = Calendar.getInstance();
//		long milis=c.getTimeInMillis()+timeOffSet;
//		c.setTimeInMillis(milis);
		int d=c.get(Calendar.DATE);
		c.set(Calendar.DATE, d+hex);
		format=formatChange(format);
		return new SimpleDateFormat(format).format(c.getTime());		
	}
	public static String getMonth(){
		return getNowTime("MM");
	}
	public static int getMonthInt(){
		String mm=getNowTime("MM");
		return Integer.parseInt(mm);
	}	
	public static String getYear(){
		return getNowTime("YYYY");
	}
	public static int getYearInt(){
		String mm=getNowTime("YYYY");
		return Integer.parseInt(mm);
	}
	public static String getPreMonth(){
		return getPreMonthFormat("MM");
	}
	   public static String getDay(){
	       return getNowTime("DD");
	    }
	
       public static String getHour(){
           return getNowTime("HH");
        }
       
       public static String getMinute(){
           return getNowTime("mm");
        }
	   
	public static int getPreMonthInt(){
		String mm=getPreMonthFormat("MM");
		return Integer.parseInt(mm);
	}
	public static String getPreMonthFormat(String format){
		Calendar c = Calendar.getInstance();
		return getLastDay(c.get(Calendar.YEAR),c.get(Calendar.MONTH),format);
	}
	
	
	/**
	 * 系統時間往前推七個月---初育
	 * @param format
	 * @return
	 */
	public static String getBornTimeInterval(String format){
		Calendar c = Calendar.getInstance();
		return getLastSevenTime(c.get(Calendar.YEAR),c.get(Calendar.MONTH),c.get(Calendar.DATE),format);
	}
	/**
	 * 按格式，获取某年，某月的上七個月時間
	 * @param format
	 * @return
	 */
	
	public static String getLastSevenTime(int year,int month,int date,String format){
		Calendar c = Calendar.getInstance();
		int y=c.get(Calendar.YEAR);
		int m=c.get(Calendar.MONTH)+7;
		int d=c.get(Calendar.DATE)-1;
		int yd=year-y;
		int md=month-m;
		int dd=date-d;
		c.add(Calendar.YEAR,yd);
		c.add(Calendar.MONTH,md);
		c.add(Calendar.DATE,dd);
		format=DateUtil.formatChange(format);
		return new SimpleDateFormat(format).format(c.getTime());
	}
	/**
	 * 系統時間往前推兩個月--初婚
	 * @param format
	 * @return
	 */
	public static String getWedTimeInterval(String format){
		Calendar c = Calendar.getInstance();
		return getLastTwoTime(c.get(Calendar.YEAR),c.get(Calendar.MONTH),c.get(Calendar.DATE),format);
	}
	/**
	 * 按格式，获取某年，某月的上兩個月時間
	 * @param format
	 * @return
	 */
	
	public static String getLastTwoTime(int year,int month,int date,String format){
		Calendar c = Calendar.getInstance();
		int y=c.get(Calendar.YEAR);
		int m=c.get(Calendar.MONTH)+2;
		int d=c.get(Calendar.DATE)-1;
		int yd=year-y;
		int md=month-m;
		int dd=date-d;
		c.add(Calendar.YEAR,yd);
		c.add(Calendar.MONTH,md);
		c.add(Calendar.DATE,dd);
		format=DateUtil.formatChange(format);
		return new SimpleDateFormat(format).format(c.getTime());
	}
	
	public static void main(String[] args){
	    Date d1=new Date();
	    try {
            Thread.sleep(10000L);
        } catch (InterruptedException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
	    Date d2=new Date();
	    int i=Math.abs(dateIntervalOfSecond(d2,d1));
	    logger.info(i);
	    
/*//logger.info(getNowInMillis());
//logger.info(getNowChnYMD());
//logger.info(getNowChnYMDhms());
logger.info(getNowTime());
logger.info(getNowYMD());		
logger.info(getFirstDay("YYYY-MM-DD"));		
//logger.info(getFirstDay(2015,9,"YYYYMMDD"));
//logger.info(getFirstDay());
//logger.info(getFirstDayYMD());
//logger.info(getLastDay(2014,8,"YYYYMMDD"));
logger.info(getLastDay("YYYYMMDD"));
logger.info(getLastDay());
//logger.info(getLastDayYMD());
//logger.info(getPreDay(-20,"yyyyMMdd"));
//logger.info(getPreDay("yyyyMMdd"));
logger.info(getPreDay());
//logger.info(getPreDayYMD());
//logger.info(getNextDay());
//logger.info(getNextDayYMD());
//logger.info(getNextDay("YYYY-MM-DD"));
//logger.info(formatedSqlDate(new Date(),"YYYYMMDD"));
		logger.info(getPreMonthInt());
		logger.info(getPreMonthFormat("YYYYMMDD"));
		logger.info(getWedTimeInterval("YYYY/MM/DD"));
		logger.info(getBornTimeInterval("YYYY/MM/DD"));
		logger.info(getLastSevenTime(2015,2,17,"YYYY/MM/DD"));*/
		//logger.info(getBeforeMinite("yyyyMMddHHmm",new Date(),1));
//	    logger.info(getHour());
//	    logger.info(getMinute());
		System.out.println(getNowTime("yyMMdd"));
		System.out.println(StringUtils.leftPad("L6T205",8,"X"));

	}
	
}
