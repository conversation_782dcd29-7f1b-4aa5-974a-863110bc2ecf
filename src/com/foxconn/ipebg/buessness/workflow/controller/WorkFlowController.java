package com.foxconn.ipebg.buessness.workflow.controller;

import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.buessness.common.entity.TQhChargelogEntity;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.buessness.workflow.entity.*;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.ConvertUtils;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.system.entity.Dict;
import com.foxconn.ipebg.system.utils.IPUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.converter.json.MappingJacksonValue;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Company foxconn
 * Copyright (C) 2004-2018 All Rights Reserved.
 */
@Controller
@RequestMapping("wfcontroller")
public class WorkFlowController extends BaseController {
    @Autowired
    private WorkFlowService workFlowService;

    /**
     * 方法描述: 待辦數據匯總
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/9  下午 01:09
     * @Return
     **/

//    @RequestMapping(value = "list", method = RequestMethod.GET)
//    @ResponseBody
//    public List<MyTaskCount> infoList(HttpServletRequest request) {
////        workFlowService.processStart("test");
//        return workFlowService.myTask(null);
//    }
    /**
     * 方法描述: 待辦數據匯總
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/9  下午 01:09
     * @Return
     **/

//    @RequestMapping(value = "listForOld", method = RequestMethod.GET)
//    @ResponseBody
//    public MappingJacksonValue listForOld(HttpServletRequest request,@RequestParam(value = "empno", required = false) String empno,String callback) {
////        workFlowService.processStart("test");
//        MappingJacksonValue mappingJacksonValue=new MappingJacksonValue(workFlowService.myTask(empno));
//        mappingJacksonValue.setJsonpFunction(callback);
//        return mappingJacksonValue;
//    }
    /**
     * 方法描述: 已辦頁面
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/9  下午 01:08
     * @Return
     **/

    @RequestMapping(value = "indexMyDown", method = RequestMethod.GET)
    public String indexMytask(Model model) {
        return "buessness/workflow/indexMyDown";
    }
    /**
      * 方法描述: 待辦頁面
      * @Author: S6114648
      * @CreateDate:   2018/12/12  上午 09:48
      * @Return
      **/

    @RequestMapping(value = "index", method = RequestMethod.GET)
    public String indexMyDown(Model model, @RequestParam(value = "workFlowId", required = false) String workFlowId,
                              @RequestParam(value = "status", required = false) String status) {
        model.addAttribute("workFlowId", workFlowId);
        model.addAttribute("status", status);
        return "buessness/workflow/index";
    }

    /**
     * 方法描述: 完成任務
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/9  上午 11:17
     * @Return
     **/

//    @RequestMapping(value = "completeTask", method = RequestMethod.POST)
//    @ResponseBody
//    public String completeTask(HttpServletRequest request,
//                               @RequestParam(value = "serialno") String serialno,
//                               @RequestParam(value = "attachidsremark") String attachidsremark,
//                               @RequestParam(value = "reattachids", required = false) String reattachids,
//                               @RequestParam(value = "status") String status) {
//        String result = workFlowService.completeTask(serialno, IPUtil.getIpAddress(request), status,attachidsremark,reattachids);
//        if (result.equals(Constant.RESULT.CODE_YES.getValue())) {
//            return Constant.SUCCESS;
//        }
//        return Constant.FAIL_AUDIT_MESSAGE;
//    }

    /**
     * 方法描述: 取消任務
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/12  下午 04:39
     * @Return
     **/

//    @RequestMapping(value = "cancelTask", method = RequestMethod.POST)
//    @ResponseBody
//    public String cancelTask(HttpServletRequest request, @RequestParam(value = "serialno") String serialno) {
//        String ip = IPUtil.getIpAddress(request);
//        String result = workFlowService.cancelTask(serialno,ip);
//
//        if (result.equals(Constant.RESULT.CODE_YES.getValue())) {
//            return Constant.SUCCESS;
//        } else {
//            return Constant.FAIL;
//        }
//    }


    /**
     * 方法描述: 待辦數據
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/9  下午 01:09
     * @Return
     **/

//    @RequestMapping(value = "listMyTask", method = RequestMethod.GET)
//    @ResponseBody
//    public Map<String, Object> myInfoList(HttpServletRequest request, @RequestParam(value = "workFlowId") String workFlowId,
//                                   @RequestParam(value = "status") String status) {
//        Page<Gtasks> page = getPage(request);
//        page.setOrderBy("createDate");
//        page.setOrder("desc");
//        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
//        page = workFlowService.getAllMyTask(workFlowId, status,page,filters);
//        ConvertUtils.convertPropertyToDictLabel(page,"workstatus","audit_status");
//        return getEasyUIData(page);
//    }
    /**
      * 方法描述: 獲取我的已辦任務
      * @Author: S6114648
      * @CreateDate:   2018/12/12  上午 09:45
      * @Return
      **/

//    @RequestMapping(value = "listMyDownTask", method = RequestMethod.GET)
//    @ResponseBody
//    public Map<String, Object> listMyDownTask(HttpServletRequest request) {
//        Page<Gtasks> page = getPage(request);
//        page.setOrderBy("createDate");
//        page.setOrder("desc");
//        List<Gtasks> gtasks = new ArrayList<Gtasks>();
//        page = workFlowService.getAllMyDownTask(page);
//        ConvertUtils.convertPropertyToDictLabel(page,"workstatus","audit_status");
//        return getEasyUIData(page);
//    }
    /**
     * 方法描述: 獲取流程圖片
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/13  下午 02:11
     * @Return
     **/

    @RequestMapping(value = "getImg", method = RequestMethod.GET)
    public String getImgUrl(HttpServletRequest request, @RequestParam(value = "processId") String processId, Model model) {
        String imgUrl = workFlowService.getImgUrl(processId);
        model.addAttribute("imgUrl", imgUrl);
        return "buessness/workflow/flowImg";
    }

    /**
     * 方法描述: 通過nodeId獲取流程參數信息
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/13  下午 04:56
     * @Return
     **/

    @RequestMapping(value = "queryNodeparamByNodeId", method = RequestMethod.GET)
    public List<WfNodeparamEntity> queryNodeparamByNodeId(HttpServletRequest request,
                                                          @RequestParam(value = "serialNo") String serialNo) {
        return workFlowService.queryNodeparam(serialNo);
    }
    /**
      * 方法描述: 獲取簽核記錄
      * @Author: S6114648
      * @CreateDate:   2018/10/16  上午 08:21
      * @Return
      **/
    @RequestMapping(value = "queryChargeLog", method = RequestMethod.GET)
    @ResponseBody
    public Map<String, Object> queryChargeLog(HttpServletRequest request) {
        Page<TQhChargelogEntity> page = getPage(request);
        page.setOrderBy("createtime");
        page.setOrder(Page.DESC);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        page = workFlowService.queryChargeLog(page, filters);
        return getEasyUIData(page);
    }
    /**
      * 方法描述: 跳轉到簽核記錄頁面
      * @Author: S6114648
      * @CreateDate:   2018/10/16  上午 10:39
      * @Return
      **/

    @RequestMapping(value = "goChargeLog", method = RequestMethod.GET)
    public String goChargeLog(HttpServletRequest request,Model model,
                                                   @RequestParam(value = "serialNo") String serialNo) {
        model.addAttribute("serialno",serialNo);
        return "system/qianheLog";
    }
    /**
     * 方法描述: 獲取當前記錄的簽核節點和簽核人
     *
     * @Author: S6114648
     * @CreateDate: 2018/10/19  下午 04:58
     * @Return
     **/
    @ResponseBody
    @RequestMapping(value = "getNodeInfo/{serialNo}", method = RequestMethod.GET)
    public TaskNode getNodeInfo(@PathVariable(value = "serialNo") String serialNo) {
        return workFlowService.getNodeInfo(serialNo);
    }
    /**
      * 方法描述:
      * @Author: S6114648
      * @CreateDate:   2018/10/22  下午 04:42
      * @Return
      **/

    @ResponseBody
    @RequestMapping(value = "getActionInfo/{workflowid}", method = RequestMethod.GET)
    public WfConifgEntity findByWorkFlowId(@PathVariable(value = "workflowid") String workflowid){
        return workFlowService.findByWorkFlowId(workflowid);
    }
}
