package com.foxconn.ipebg.report.service;

import com.foxconn.ipebg.basics.entity.BsDptEntity;
import com.foxconn.ipebg.basics.entity.BsSecPerEntity;
import com.foxconn.ipebg.report.entity.AbsentReportEntity;
import com.foxconn.ipebg.basics.dao.BsDptDao;
import com.foxconn.ipebg.report.dao.AbsentReportDao;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;

import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foxconn.ipebg.system.service.DictService;
import com.foxconn.ipebg.system.utils.UserUtil;

import java.util.Date;
import java.util.List;

/**
 * 異常人員比對
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-12-21 14:54:46
 */
@Service
@Transactional(readOnly=true)
public class  AbsentReportService extends BaseBusinessService<AbsentReportEntity, String>{
    @Autowired
    private AbsentReportDao absentReportDao;

    @Autowired
    private UserServiceUtil serviceUtil;
    @Autowired
    private DictService dictService;

    @Override
    public HibernateDao<AbsentReportEntity, String> getEntityDao() {
        return absentReportDao;
    }

    public AbsentReportEntity findBySerialno(String serialno) {
        return this.absentReportDao.findUniqueBy("serialno", serialno);      
    }
    
    /**
	 * 方法描述: 條用存儲過程，有返回值
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018/12/29 下午 03:33
	 * @param TableName  相關表名
	 * @param FieldName 相關欄位名
	 * @param FirstStr 編號頭字符串           
	 * @param RecLen 序號位數
	 * @Return
	 **/
	public String CreateFormNo(String TableName, String FieldName,
			String FirstStr, int RecLen) {
		 return this.absentReportDao.CreateFormNo(TableName, FieldName, FirstStr, RecLen);
	}

	public List getDptInfoId() {
		String sql = "select t.id,  t.dpt_qun || '---' || t.dpt_chu || '---' || t.dpt_bu || '---' || t.dpt_ke dpt_name  from BS_DPT t";
	
		List list = this.absentReportDao.createSQLQuery(sql).list();
		return list;
	}

	public AbsentReportEntity findById(String id) {
		return this.absentReportDao.findUniqueBy("id", id);  
	}
	// 更新異常狀態  0異常  1異常
//	@Transactional(readOnly = false)
//	public int updateAbnormalStateByEmpNo(String empNo, Date issueDate,String abnormalState) {
//
//		String hql = "update AbsentReportEntity t  set " + "t.abnormalState = '"
//				+ abnormalState + "', t.updateDate=?1 , t.updateBy=?2  " + "where  t.empNo='" + empNo + "' and  t.issueDate=?0 ";
//
//		return this.absentReportDao.batchExecute(hql,issueDate,new Date(),UserUtil.getCurrentUser().getLoginName());
//	}

	public AbsentReportEntity findByEmpNoAndShiftDate(String empNo,Date shiftDate) {
		String hql ="from AbsentReportEntity a where a.empNo=?0 and a.issueDate=?1";
		List<AbsentReportEntity> list = this.absentReportDao.createQuery(hql, empNo, shiftDate).list();
		if(list!=null && list.size()>0){
			return list.get(0);
		}
		return null;
	}
    
}
