package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * 異常人員信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-03-11 14:46:49
 */
@Entity
@Table(name = "TEMP_ARRANGE")
@DynamicUpdate
@DynamicInsert
public class TempArrangeEntity extends DataEntity<TempArrangeEntity> implements
		Serializable {
	private static final long serialVersionUID = 1L;

	// 崗位編號
	private String postRecno;
	// 崗位名稱
	private String postName;
	// 班制
	private String postShift;
	// 詳細位置（小崗位名稱）
	private String location;
	// 排班日期
	private Date shiftDate;
	// 班別代碼
	private String shiftNo;
	// 班別
	private String shiftName;
	// 開始時間
	private String startTime;
	// 結束時間
	private String endTime;
	// 工號
	private String empNo;
	// 姓名
	private String empName;
	// 比對-狀態：正常為0
	private String checkState;
	// 比對-異常原因
	private String checkCauses;
	// 創建人IP
	private String createIp;

	/**
	 * 设置：崗位編號
	 */
	public void setPostRecno(String postRecno) {
		this.postRecno = postRecno;
	}

	/**
	 * 获取：崗位編號
	 */

	@Column(name = "POST_RECNO", nullable = false, length = 20)
	public String getPostRecno() {
		return postRecno;
	}

	/**
	 * 设置：崗位名稱
	 */
	public void setPostName(String postName) {
		this.postName = postName;
	}

	/**
	 * 获取：崗位名稱
	 */

	@Column(name = "POST_NAME", nullable = false, length = 20)
	public String getPostName() {
		return postName;
	}

	/**
	 * 设置：班制
	 */
	public void setPostShift(String postShift) {
		this.postShift = postShift;
	}

	/**
	 * 获取：班制
	 */

	@Column(name = "POST_SHIFT", nullable = false, length = 20)
	public String getPostShift() {
		return postShift;
	}

	/**
	 * 设置：詳細位置（小崗位名稱）
	 */
	public void setLocation(String location) {
		this.location = location;
	}

	/**
	 * 获取：詳細位置（小崗位名稱）
	 */

	@Column(name = "LOCATION", nullable = false, length = 20)
	public String getLocation() {
		return location;
	}

	/**
	 * 设置：排班日期
	 */
	public void setShiftDate(Date shiftDate) {
		this.shiftDate = shiftDate;
	}

	/**
	 * 获取：排班日期
	 */

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Column(name = "SHIFT_DATE", nullable = false, length = 20)
	public Date getShiftDate() {
		return shiftDate;
	}

	/**
	 * 设置：班別代碼
	 */
	public void setShiftNo(String shiftNo) {
		this.shiftNo = shiftNo;
	}

	/**
	 * 获取：班別代碼
	 */

	@Column(name = "SHIFT_NO", nullable = false, length = 20)
	public String getShiftNo() {
		return shiftNo;
	}

	/**
	 * 设置：班別
	 */
	public void setShiftName(String shiftName) {
		this.shiftName = shiftName;
	}

	/**
	 * 获取：班別
	 */

	@Column(name = "SHIFT_NAME", nullable = false, length = 20)
	public String getShiftName() {
		return shiftName;
	}

	/**
	 * 设置：開始時間
	 */
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	/**
	 * 获取：開始時間
	 */

	@Column(name = "START_TIME", nullable = false, length = 20)
	public String getStartTime() {
		return startTime;
	}

	/**
	 * 设置：結束時間
	 */
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	/**
	 * 获取：結束時間
	 */

	@Column(name = "END_TIME", nullable = false, length = 20)
	public String getEndTime() {
		return endTime;
	}

	/**
	 * 设置：工號
	 */
	public void setEmpNo(String empNo) {
		this.empNo = empNo;
	}

	/**
	 * 获取：工號
	 */

	@Column(name = "EMP_NO", nullable = false, length = 20)
	public String getEmpNo() {
		return empNo;
	}

	/**
	 * 设置：姓名
	 */
	public void setEmpName(String empName) {
		this.empName = empName;
	}

	/**
	 * 获取：姓名
	 */

	@Column(name = "EMP_NAME", nullable = false, length = 20)
	public String getEmpName() {
		return empName;
	}

	/**
	 * 设置：比對-狀態：正常為0
	 */
	public void setCheckState(String checkState) {
		this.checkState = checkState;
	}

	/**
	 * 获取：比對-狀態：正常為0
	 */

	@Column(name = "CHECK_STATE", nullable = false, length = 20)
	public String getCheckState() {
		return checkState;
	}

	/**
	 * 设置：比對-異常原因
	 */
	public void setCheckCauses(String checkCauses) {
		this.checkCauses = checkCauses;
	}

	/**
	 * 获取：比對-異常原因
	 */

	@Column(name = "CHECK_CAUSES", nullable = false, length = 20)
	public String getCheckCauses() {
		return checkCauses;
	}

	/**
	 * 设置：創建人IP
	 */
	public void setCreateIp(String createIp) {
		this.createIp = createIp;
	}

	/**
	 * 获取：創建人IP
	 */

	@Column(name = "CREATE_IP", nullable = false, length = 20)
	public String getCreateIp() {
		return createIp;
	}

}
