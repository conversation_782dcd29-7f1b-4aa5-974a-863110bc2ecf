package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * 手持卡機刷卡記錄
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-03-09 16:50:16
 */
@Entity
@Table(name = "BS_HAND_RECORDS")
@DynamicUpdate
@DynamicInsert
public class BsHandRecordsEntity extends DataEntity<BsHandRecordsEntity>
		implements Serializable {
	private static final long serialVersionUID = 1L;

	// 工號
	private String empNo;
	// 姓名
	private String empName;
	// 刷卡記錄
	private Date cardTime;
	// 備註
	private String remark;

	/**
	 * 设置：工號
	 */
	public void setEmpNo(String empNo) {
		this.empNo = empNo;
	}

	/**
	 * 获取：工號
	 */

	@Column(name = "EMP_NO", nullable = false, length = 20)
	public String getEmpNo() {
		return empNo;
	}

	/**
	 * 设置：姓名
	 */
	public void setEmpName(String empName) {
		this.empName = empName;
	}

	/**
	 * 获取：姓名
	 */

	@Column(name = "EMP_NAME", nullable = false, length = 20)
	public String getEmpName() {
		return empName;
	}

	/**
	 * 设置：刷卡記錄
	 */
	public void setCardTime(Date cardTime) {
		this.cardTime = cardTime;
	}

	/**
	 * 获取：刷卡記錄
	 */

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Column(name = "CARD_TIME", nullable = false, length = 20)
	public Date getCardTime() {
		return cardTime;
	}

	/**
	 * 设置：備註
	 */
	public void setRemark(String remark) {
		this.remark = remark;
	}

	/**
	 * 获取：備註
	 */

	@Column(name = "REMARK", nullable = false, length = 20)
	public String getRemark() {
		return remark;
	}
}
