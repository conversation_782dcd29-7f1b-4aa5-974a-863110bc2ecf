package com.foxconn.ipebg.system.utils;

import java.io.IOException;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.Date;
import javax.servlet.http.HttpServletResponse;
import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import com.foxconn.ipebg.common.exception.ExcelException;

/**
 * 导出自定义Excel工具类
 * 通常情况下应使用ExcelUtil，因ExcelUtil封装了jxl，外部无法直接操作于WritableWorkbook对象，需要用此对象自定义样式时使用本类。
 *
 * <AUTHOR>
 * @version 1.0.0 , 2020/08/28
 */
public class CustomExcelUtil {
    // Excel 文档
    private WritableWorkbook workbook;
    // 当前填充的 Excel Sheet
    private WritableSheet currentSheet;
    // 使用 response 构造此类对象
    public CustomExcelUtil(HttpServletResponse response, String filename) throws ExcelException {
        if (filename == null || filename.trim().length() == 0) {
            filename = new SimpleDateFormat("yyyyMMddhhmmss").format(
                    new Date()).toString();
        }
        // 设置 response 头
        response.reset();
        response.setContentType("application/vnd.ms-excel"); // 改成输出excel文件
        response.setHeader("Content-disposition", "attachment; filename=" + filename + ".xls");
        try {
            workbook = Workbook.createWorkbook(response.getOutputStream());
        } catch (IOException e) {
            throw new ExcelException("创建文档输出失败");
        }
    }
    private CustomExcelUtil() {}

    public WritableSheet getCurrentSheet() {
        return currentSheet;
    }
    /**
     * 创建 Sheet
     * 因为2003的Excel一个工作表最多可以有65536条记录，如果数据条数过多时则需要通过此方法创建新的 Sheet
     * Author：S6114893
     * */
    public void createNewSheet(String sheetname, int num) {
        currentSheet = workbook.createSheet(sheetname, num);
    }
    /**
     * 关闭WorkBook
     * */
    public void closeWorkBook() throws ExcelException {
        try {
            workbook.write();
            workbook.close();
        } catch (Exception e) {
            throw new ExcelException("关闭WorkBook失败");
        }
    }
}
