package com.foxconn.ipebg.system.entity;

import javax.persistence.*;

/**
 * 用户机构entity
 * <AUTHOR>
 * @date 2015年5月9日 
 */
@Entity
@Table(name = "sys_user_org")
public class UserOrg implements java.io.Serializable {

	// Fields

	private static final long serialVersionUID = 1L;
	private Integer id;
	private Integer userId;
	private Integer orgId;

	// Constructors

	/** default constructor */
	public UserOrg() {
	}

	/** full constructor */
	public UserOrg(Integer userId, Integer orgId) {
		this.userId = userId;
		this.orgId = orgId;
	}

	// Property accessors SYS_USER_ORG_QEQUENCE
	@Id
	@SequenceGenerator(name = "generator", sequenceName = "sys_user_org_qequence", allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "generator")
	@Column(name = "id", unique = true, nullable = false)
	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "user_id", nullable = false)
	public Integer getUserId() {
		return this.userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	@Column(name = "org_id", nullable = false)
	public Integer getOrgId() {
		return this.orgId;
	}

	public void setOrgId(Integer orgId) {
		this.orgId = orgId;
	}

}