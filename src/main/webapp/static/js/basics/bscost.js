var dg;
var d;
$(function() {
	dg = $('#dg').datagrid({
		method : "get",
		url : ctx + '/bscost/list',
		fit : true,
		fitColumns : true,
		border : false,
	    idField : 'id',
		striped : true,
		pagination : true,
		rownumbers : true,
		pageNumber : 1,
		pageSize : 20,
		pageList : [ 10, 20, 30, 40, 50 ],
		singleSelect : true,
		orderby : 'createDate',
		order: 'desc',
		columns : [ [
		{
			field : 'id',
			title : '資料識別ID  Primary',
			hidden : true
		}, {
			field : 'type',
			title : '類型',
			sortable : true,
			width : 100
		}, {
			field : 'cost',
			title : '月費用',
			sortable : true,
			width : 100
		}, {
			field : 'dayCount',
			title : '月天數',
			sortable : true,
			width : 100
		}, {
			field : 'remark',
			title : '備註',
			sortable : true,
			width : 100
		}, {
			field : 'securityCom',
			title : '保安公司',
			sortable : true,
			width : 100
		}, {
			field : 'createBy',
			title : '創建人',
			sortable : true,
			width : 100
		}, {
			field : 'createDate',
			title : '創建日期',
			sortable : true,
			width : 100
		} ] ],
		enableHeaderClickMenu : true,
		enableHeaderContextMenu : true,
		enableRowContextMenu : false,
		rowTooltip : true,
		toolbar : '#tb'
	});
	$('#dg').datagrid
});

// 弹窗增加
function add() {
	d = $("#dlg").dialog({
		title : '添加費用資料',
		width : 250,
		height : 250,
		href : ctx + '/bscost/create',
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '确认',
			handler : function() {
				$("#mainform").submit();
				$('#dg').datagrid('reload');
				//window.location.reload();
			}
		}, {
			text : '取消',
			handler : function() {
				d.panel('close');
			}
		} ]
	});
}

// 删除
function del() {
	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data) {
		if (data) {
			$.ajax({
				type : 'get',
				url : ctx + "/bscost/delete/" + row.id,
				success : function(data) {
					successTip(data, dg);
					 $('#dg').datagrid('reload');
				}
			});
		}
	});
}

// 创建查询对象并查询
function cx() {
	var obj = $("#searchFrom").serializeObject();
	dg.datagrid('load', obj);
}
// 表單進度查詢頁面查詢條件重置
function listSearchReset() {
	$("#searchFrom").form("reset");
}
