package com.foxconn.ipebg.system.service;

import javax.annotation.Resource;

import com.foxconn.ipebg.system.dao.RedisClientTemplate;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/**
 * Redis 数据源
 * <AUTHOR>
 * @since 2018/11/20
 */
@Repository("redisService")
public class RedisService {
	
	private static final Logger log = Logger.getLogger(RedisService.class);

	@Autowired
	private RedisClientTemplate redisClientTemplate;

	private static final String SPRING_REDIS_SESSION_HEAD = "spring:session:sessions:";
	/**
	 * 從redis中獲取session
	 * @param sessionId
	 * @return
	 */
	public String getUserno(String sessionId){
		String sessionKey = SPRING_REDIS_SESSION_HEAD + sessionId;
		String rs = redisClientTemplate.getHashMapValue(sessionKey,"userno");
		return rs;
	}
}
