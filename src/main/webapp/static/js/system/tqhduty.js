var dg;
var d;
$(function () {
    dg = $('#dg').datagrid({
        method: "get",
        url: ctx + '/tqhduty/list',
        fit: true,
        fitColumns: true,
        border: false,
        idField: 'id',
        striped: true,
        pagination: true,
        rownumbers: true,
        pageNumber: 1,
        pageSize: 20,
        pageList: [10, 20, 30, 40, 50],
        singleSelect: true,
        columns: [[
            {field: 'dutyid', title: '${column.comments}', sortable: true, width: 100}
            ,
            {field: 'dutyname', title: '${column.comments}', sortable: true, width: 100}
            ,
            {field: 'id', title: 'id', hidden: true},
            {field: 'createBy', title: '創建人', sortable: true, width: 100}
            ,
            {field: 'createDate', title: '創建時間', sortable: true, width: 100}
            ,
            {field: 'updateBy', title: '更新者', sortable: true, width: 100}
            ,
            {field: 'updateDate', title: '更新時間', sortable: true, width: 100}
            ,
            {field: 'delFlag', title: '刪除標識', sortable: true, width: 100}
        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        toolbar: '#tb'
    });
});

//弹窗增加
function add() {
    d = $("#dlg").dialog({
        title: '添加${comments}',
        width: 380,
        height: 380,
        href: ctx + '/tqhduty/create',
        maximizable: true,
        modal: true,
        buttons: [{
            text: '确认',
            handler: function () {
                $("#mainform").submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//删除
function del() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function (data) {
        if (data) {
            $.ajax({
                type: 'get',
                url: ctx + "/tqhduty/delete/" + row.id,
                success: function (data) {
                    successTip(data, dg);
                }
            });
        }
    });
}

//弹窗修改
function upd() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    d = $("#dlg").dialog({
        title: '修改${comments}',
        width: 380,
        height: 340,
        href: ctx + '/tqhduty/update/' + row.id,
        maximizable: true,
        modal: true,
        buttons: [{
            text: '修改',
            handler: function () {
                $('#mainform').submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//创建查询对象并查询
function cx() {
    var obj = $("#searchFrom").serializeObject();
    dg.datagrid('load', obj);
}
