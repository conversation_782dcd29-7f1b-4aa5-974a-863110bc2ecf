<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>培訓期費用結帳明細表</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
    var _periodList = ${periodList};
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
        <form id="searchFrom" action="">
			<input type="hidden" id="hidePostRecno" value="${postRecno}"/>
			<input type="hidden" id="hideCostStartDate" value="${startDate}"/>
			<input type="hidden" id="hideCostEndDate" value="${endDate}"/>

			<input type="text" id="empNo" name="filter_LIKES_empNo" class="easyui-validatebox"
				   data-options="width:150,prompt: '工號'"/>
			<input type="text" id="empName" name="filter_LIKES_empName" class="easyui-validatebox"
				   data-options="width:150,prompt: '姓名'"/>
			<input name="filter_EQS_securityCom" class="easyui-combobox"
				   data-options="panelHeight:'auto',valueField:'value', textField:'label',
	   				url: '${ctx}/system/user/userSecurityCom',width: 150, prompt: '保安公司'"  />
			<input id="trainingPeriodNo" name="filter_EQS_trainingPeriodNo" class="easyui-combogrid"
				   data-options="prompt: '培訓期'" />
			<input id="filter_startDate" name="filter_GED_costDate" class="easyui-datebox" data-options="prompt: '開始結算日期'"/>
			<input id="filter_endDate" name="filter_LED_costDate" class="easyui-datebox" data-options="prompt: '截止結算日期'"/>

		<span class="toolbar-item dialog-tool-separator"></span>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh" onclick="listSearchReset()">重置</a>

	</form>

  </div>
<table id="dg"></table>
<div id="dlg"></div>

<script src="${ctx}/static/js/basics/bstrainingdaycost.js?random=<%= Math.random()%>"></script>
</body>
</html>