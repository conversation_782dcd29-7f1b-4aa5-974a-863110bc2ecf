<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>培訓人員匯總表</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
    var _periodList = ${periodList};
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
	<style type="text/css">
		.modal-table {
			border-collapse: collapse;
			margin: 0 auto;
			text-align: center;
			border: 1px solid #cad9ea;
			width: 100%;
			font-size: 14px;
		}

		.modal-table td, table th {
			border: 1px solid #cad9ea;
			color: #333;
		}

	</style>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">

        <form id="searchFrom" action="">

		<input type="text" name="startDate" class="easyui-my97" datefmt="yyyy-MM-dd"
			   data-options="width:150,prompt: '开始日期'"/>
		- <input type="text" name="endDate" class="easyui-my97" datefmt="yyyy-MM-dd"
				 data-options="width:150,prompt: '结束日期'"/>
		<input type="text" id="trainingPeriodNo" name="trainingPeriodNo" class="easyui-combogrid"
				   data-options="width:150,prompt: '培訓期名稱'"/>

		<span class="toolbar-item dialog-tool-separator"></span>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
		   onclick="listSearchReset()">重置</a>

	</form>

  </div>
<table id="dg"></table>

<div id="modal" class="easyui-window" title="明細" data-options="modal:true,closed:true,iconCls:'icon-save'" style="width:600px;height:500px;padding:10px;">
	<table class="modal-table">
		<thead>
			<tr>
				<th>序號</th>
				<th>培訓日期</th>
				<th>應到人數</th>
				<th>實到人數</th>
				<th>缺勤人數</th>
			</tr>
		</thead>
		<tbody id="reportDetail">


		</tbody>
	</table>

</div>

<script src="${ctx}/static/js/basics/bstrainingarrangereport.js?random=<%= Math.random()%>"></script>
</body>
</html>