package com.foxconn.ipebg.basics.entity;

        import java.io.Serializable;
import java.util.Date;

import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;
        import com.foxconn.ipebg.common.entity.DataEntity;

        import org.hibernate.annotations.ColumnTransformer;
        import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 合格人員
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-16 10:12:01
 */
@Entity
@Table(name = "PER_INFO")
@DynamicUpdate
@DynamicInsert
public class PerInfoEntity extends DataEntity<PerInfoEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    //硬卡號
    private String cardId;
    //公司
    private String company;
    //身份證號
    private String psnId;
    //考試合格日期
    private Date passDate;
    // 入职日期
    private Date entryDate;
    //工號
    private String empNo;
    //姓名
    private String empName;

    @Transient
    protected boolean isResign = false;
    @Transient
    public boolean isResign() {
        return delFlag.equalsIgnoreCase(DataEntity.DEL_FLAG_DELETE);
    }

    public void setResign(boolean resign) {
        isResign = resign;
    }

    /**
     * 设置：硬卡號
     */
    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    /**
     * 获取：硬卡號
     */

    @Column(name = "CARD_ID", nullable = false, length = 20)
    public String getCardId() {
        return cardId;
    }

    /**
     * 设置：公司
     */
    public void setCompany(String company) {
        this.company = company;
    }

    /**
     * 获取：公司
     */

    @Column(name = "COMPANY", nullable = false, length = 20)
    public String getCompany() {
        return company;
    }

    /**
     * 设置：身份證號
     */
    public void setPsnId(String psnId) {
        this.psnId = psnId;
    }

    /**
     * 获取：身份證號
     */
    @Column(name = "PSN_ID", nullable = false, length = 20)
    @ColumnTransformer(forColumn = "PSN_ID", read = "CONVERT_FROM(decrypt(decode(PSN_ID, 'base64'), 'sgas', 'aes'), 'UTF-8')", write = "encode(encrypt(cast(? as bytea), 'sgas', 'aes'), 'base64')")
    public String getPsnId() {
        return psnId;
    }

    /**
     * 设置：考試合格日期
     */
    public void setPassDate(Date passDate) {
        this.passDate = passDate;
    }

    /**
     * 获取：考試合格日期
     */

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "PASS_DATE", nullable = false, length = 20)
    public Date getPassDate() {
        return passDate;
    }

    /**
     * 设置：工號
     */
    public void setEmpNo(String empNo) {
        this.empNo = empNo;
    }

    /**
     * 获取：工號
     */

    @Column(name = "EMP_NO", nullable = false, length = 20)
    public String getEmpNo() {
        return empNo;
    }

    /**
     * 设置：姓名
     */
    public void setEmpName(String empName) {
        this.empName = empName;
    }

    /**
     * 获取：姓名
     */

    @Column(name = "EMP_NAME", nullable = false, length = 20)
    public String getEmpName() {
        return empName;
    }

    /**
     * 入职日期
     * @return
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "ENTRY_DATE", nullable = false, length = 20)
    public Date getEntryDate() {
        return entryDate;
    }

    public void setEntryDate(Date entryDate) {
        this.entryDate = entryDate;
    }
}
