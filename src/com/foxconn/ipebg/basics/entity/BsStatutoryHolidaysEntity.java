package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 法定節假日表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-17 09:33:40
 */
@Entity
@Table(name = "BS_STATUTORY_HOLIDAYS")
@DynamicUpdate
@DynamicInsert
public class BsStatutoryHolidaysEntity extends DataEntity<BsStatutoryHolidaysEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    //日期
    private Date holidayDate;
    //描述
    private String holidayDesc;


    /**
     * 设置：日期
     */
    public void setHolidayDate(Date holidayDate) {
        this.holidayDate = holidayDate;
    }

    /**
     * 获取：日期
     */

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "HOLIDAY_DATE", nullable = false, length = 20)
    public Date getHolidayDate() {
        return holidayDate;
    }

    /**
     * 设置：描述
     */
    public void setHolidayDesc(String holidayDesc) {
        this.holidayDesc = holidayDesc;
    }

    /**
     * 获取：描述
     */

    @Column(name = "HOLIDAY_DESC", nullable = false, length = 20)
    public String getHolidayDesc() {
        return holidayDesc;
    }


}
