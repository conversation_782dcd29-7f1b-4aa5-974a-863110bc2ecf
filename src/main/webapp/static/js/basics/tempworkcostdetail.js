var dg;
var d;
var _legalPersonDicts = [];//企業法人字典
var _legalPersonObj = {};
var _postTypeDicts = [];
var _bsDptList = [];
$(function () {
    initDatas();
    dg = $('#dg').datagrid({
        method: "get",
        url: ctx + '/tempworkcostdetail/list',
        fit: true,
        fitColumns: false,
        border: false,
        idField: 'id',
        striped: true,
        pagination: true,
        rownumbers: true,
        pageNumber: 1,
        pageSize: 20,
        pageList: [10, 20, 30, 40, 50],
        singleSelect: true,
        frozenColumns: [[
            {field: 'id', title: 'PKID', hidden: true, halign: 'center'}
            ,
            {field: 'workMonth', title: '月份', sortable: true, width: 80, halign: 'center'}
            ,
            {field: 'buId', title: '事業群ID', sortable: true, width: 80, hidden: true, halign: 'center'}
            ,
            {field: 'buNam', title: '事業群名稱', sortable: true, width: 80, halign: 'center'}
            ,
            {
                field: 'legalId', title: '法人', sortable: true, width: 80, halign: 'center',
                formatter: function (value, row, index) {
                    return _legalPersonObj[value];
                }
            }
            ,
            {field: 'dptId', title: '部門ID', sortable: true, width: 80, hidden: true}
            ,
            {field: 'dptNam', title: '部門名稱', sortable: true, width: 80, halign: 'center'}
            ,
            {field: 'costId', title: '費用代碼', sortable: true, width: 80, halign: 'center'}

        ]],
        columns: [[
            {field: 'postType', title: '崗位類別', sortable: true, width: 80, halign: 'center', rowspan: 2}
            ,
            {field: 'postPosition', title: '崗位詳細位置', sortable: true, width: 100, halign: 'center', rowspan: 2}
            ,
            {field: 'postNam', title: '崗位名稱', sortable: true, width: 80, halign: 'center', rowspan: 2}
            ,
            {field: '', title: '上班人員信息', sortable: true, width: 100, colspan: 31}
            ,
            {field: 'normalDay', title: '正常上班天數（天）', sortable: true, width: 100, rowspan: 2}
            ,
            {
                field: 'normalCost', title: '常規費用（RMB）', sortable: true, width: 100, rowspan: 2,
                formatter: function (value, row, index) {
                    return value / 100.0;
                }
            }
            ,
            {field: 'otHour', title: '加班時數（小時）', sortable: true, width: 100, rowspan: 2}
            ,
            {
                field: 'otCost', title: '加班費用（RMB）', sortable: true, width: 100, rowspan: 2,
                formatter: function (value, row, index) {
                    return value / 100.0;
                }
            }
            ,

            {
                field: 'totalCost', title: '總費用（RMB）', sortable: true, width: 100, rowspan: 2,
                formatter: function (value, row, index) {
                    return value / 100.0;
                }
            }
            ,
            {field: 'createBy', title: '創建人', sortable: true, width: 100, hidden: true}
            ,
            {field: 'createDate', title: '創建時間', sortable: true, width: 100, hidden: true}
            ,
            {field: 'updateBy', title: '更新人', sortable: true, width: 100, hidden: true}
            ,
            {field: 'updateDate', title: '更新時間', sortable: true, width: 100, hidden: true}
            ,
            {field: 'delFlag', title: '刪除標示', sortable: true, width: 100, hidden: true}
        ], [

            {field: 'l1', title: '1', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l2', title: '2', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l3', title: '3', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l4', title: '4', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l5', title: '5', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l6', title: '6', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l7', title: '7', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l8', title: '8', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l9', title: '9', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l10', title: '10', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l11', title: '11', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l12', title: '12', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l13', title: '13', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l14', title: '14', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l15', title: '15', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l16', title: '16', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l17', title: '17', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l18', title: '18', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l19', title: '19', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l20', title: '20', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l21', title: '21', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l22', title: '22', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l23', title: '23', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l24', title: '24', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l25', title: '25', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l26', title: '26', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l27', title: '27', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l28', title: '28', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l29', title: '29', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l30', title: '30', sortable: true, width: 100, halign: 'center'}
            ,
            {field: 'l31', title: '31', sortable: true, width: 100, halign: 'center'}
        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        rowTooltip: true,
        toolbar: '#tb'
    });
});
function formatterUser(str) {
    var html="";
    var arr=str.split(",");
    for(var i=0;i<arr.length;i+=2){
        html+="<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('用戶排班詳情',ctx+'/workdetail/view/" + arr[i] + "','icon-hamburg-basket')\">" + arr[i+1] + "</a>&nbsp;&nbsp;";
    }
    return html;
}

function initDatas() {
    $.ajax({
        type: 'get',
        url: ctx + "/tempworkcostdetail/initDatas",
        success: function (data) {
            _legalPersonDicts = data.legalPersonDicts;
            _legalPersonObj = arr2obj(_legalPersonDicts, "value", "label");
            _postTypeDicts = data.postTypeDicts;
            _bsDptList = data.bsDptList;

            //绑定事業處下拉框
            $("#filter_buNam").combobox({
                data: _bsDptList,
                valueField: "dptQun",
                textField: "dptQun",
                editable: false,
                panelHeight: 'auto',
                loadFilter: function (data) {
                    data.unshift({
                        dptQun: '請選擇事業處'
                    });
                    return data;
                }
            });
            $("#filter_buNam").combobox("select",'請選擇事業處');
            //绑定崗位類別下拉框
            $("#filter_postType").combobox({
                data: _postTypeDicts,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 'auto',
                loadFilter: function (data) {
                    data.unshift({
                        value: '',
                        label: '請選擇崗位類別'
                    });
                    return data;
                }
            });

            //绑定法人下拉框
            $("#filter_legalId").combobox({
                data: _legalPersonDicts,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 'auto',
                loadFilter: function (data) {
                    data.unshift({
                        value: '',
                        label: '請選擇法人'
                    });
                    return data;
                }
            });

        }
    });

}

//數組轉對象
function arr2obj(arr, key, value) {
    var obj = {};
    for (var i = 0; i < arr.length; i++) {
        var o = arr[i];
        obj[o[key]] = o[value];
    }
    return obj;
}

//弹窗增加
function add() {
    d = $("#dlg").dialog({
        title: '添加警衛費用結賬明細表',
        width: 380,
        height: 380,
        href: ctx + '/tempworkcostdetail/create',
        maximizable: true,
        modal: true,
        buttons: [{
            text: '确认',
            handler: function () {
                $("#mainform").submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//删除
function del() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function (data) {
        if (data) {
            $.ajax({
                type: 'get',
                url: ctx + "/tempworkcostdetail/delete/" + row.id,
                success: function (data) {
                    successTip(data, dg);
                }
            });
        }
    });
}

//弹窗修改
function upd() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    d = $("#dlg").dialog({
        title: '修改警衛費用結賬明細表',
        width: 380,
        height: 340,
        href: ctx + '/tempworkcostdetail/update/' + row.id,
        maximizable: true,
        modal: true,
        buttons: [{
            text: '修改',
            handler: function () {
                $('#mainform').submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//创建查询对象并查询
function cx() {
    var obj = $("#searchFrom").serializeObject();
    if(obj.filter_EQS_buNam == "請選擇事業處"){
        obj.filter_EQS_buNam = '';
    }
    dg.datagrid('load', obj);
}

//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("reset");
    $("#filter_buNam").combobox("select",'請選擇事業處');
}

//导出excel
function exportExcel() {
    var form = document.getElementById("searchFrom");
    searchFrom.action = ctx + '/tempworkcostdetail/exportExcel';
    form.submit();
}