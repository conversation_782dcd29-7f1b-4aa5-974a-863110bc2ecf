package com.foxconn.ipebg.buessness.audit.dao;

import com.foxconn.ipebg.buessness.audit.entity.EGuardItemAplyEntity;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;
import com.foxconn.ipebg.common.persistence.HibernateDao;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 崗位申請明細表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-02-23 16:41:23
 */
@Repository
public class EGuardItemAplyDao extends HibernateDao<EGuardItemAplyEntity,String> {

    /**
     * 判斷是否允許申請臨時崗位增撤（相同時間不允許重複申請）S6114893 2022-07-22
     * @param recno 崗位編碼
     * @param startDate 異動開始時間
     * @param endDate 異動結束時間
     * @return 是否允許申請
     */
    public boolean allowApplyTempModify(String recno, Date startDate, Date endDate) {
        List<Criterion> criterionList = new ArrayList<Criterion>();
        // 查询同一个岗位编码
        criterionList.add(buildCriterion("recno", recno, PropertyFilter.MatchType.EQ));
        // 查询状态是正在申请/申请通过的临时增撤岗
        criterionList.add(buildCriterion("delFlag", Arrays.asList("4", "5"), PropertyFilter.MatchType.IN));
        // 查询开始结束时间是否有重叠
        criterionList.add(Restrictions.or(Restrictions.between("postStartDate", startDate, endDate), Restrictions.between("postEndDate", startDate, endDate)));
        List<EGuardItemAplyEntity> duplicateList = find(criterionList.toArray(new Criterion[0]));
        return duplicateList.size() == 0;
    }
}
