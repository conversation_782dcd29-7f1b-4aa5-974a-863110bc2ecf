var dg;
var d;
$(function() {
	dg = $('#dg').datagrid({
		method : "get",
		url : ctx + '/bssecper/list',
		// url:'/waterQualityTesting/bsdpt/list',
		fit : true,
		fitColumns : true,
		border : false,
	    idField : 'id',
		striped : true,
		pagination : true,
		rownumbers : true,
		pageNumber : 1,
		pageSize : 20,
		pageList : [ 10, 20, 30, 40, 50 ],
		singleSelect : true,
		columns : [ [
		{field : 'id',title : '資料識別ID  Primary',hidden : true},
		{field : 'empNo',title : '工號',sortable : true,width : 100},
		{field : 'empName',title : '姓名',sortable : true,width : 100},
		//{field : 'psnId',title : '身份證號碼',sortable : true,width : 100},
		{field : 'company',title : '保安公司',sortable : true,width : 100}
		] ],
		enableHeaderClickMenu : true,
		enableHeaderContextMenu : true,
		enableRowContextMenu : false,
		rowTooltip : true,
		toolbar : '#tb'
	});
	$('#dg').datagrid;

	$("#btnBatchImportTpl").click(batchImportTpl);
});

// 创建查询对象并查询
function cx() {
	var obj = $("#searchFrom").serializeObject();
	dg.datagrid('load', obj);
}
// 表單進度查詢頁面查詢條件重置
function listSearchReset() {
	$("#searchFrom").form("reset");
}
// 导出excel
function exportExcel() {
	var form = document.getElementById("searchFrom");
	searchFrom.action = ctx + '/bssecper/exportExcel';
	form.submit();
}

//弹窗增加
function add() {
	d=$("#dlg").dialog({
		title: '添加警衛人員',
		width: 380,
		height: 380,
		href:ctx+'/bssecper/create',
		maximizable:true,
		modal:true,
		buttons:[{
			text:'确认',
			handler:function(){
				//驗證
				//var postShift = $.trim($("#postShift").val());
				//var shiftNo = $('#shiftNo').combobox('getValue');
				var empNo = $.trim($("#empNo").val());
				var company=$.trim($("#company").val());
				var psnId=$.trim($("#psnId").val());
				var empName=$.trim($("#empName").val());

				var intStrLen =psnId.length;

				if (intStrLen != 18) {
					error = "輸入身份證長度不正確！";
					alert(error);
					return false;
				}
				if (empNo == "") {
					$.messager.alert("溫馨提示", "工號不能為空！", "info");
					return;
				}
				if (psnId == "") {
					$.messager.alert("溫馨提示", "身份證號不能為空！", "info");
					return;
				}
				if (empName == "") {
					$.messager.alert("溫馨提示", "姓名不能為空！", "info");
					return;
				}
				//test()
				$("#mainform").submit();
			}
		},{
			text:'取消',
			handler:function(){
				d.panel('close');
			}
		}]
	});
}

//删除
function del(){
	var row = dg.datagrid('getSelected');
	if(rowIsNull(row)) return;
	parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data){
		if (data){
			$.ajax({
				type:'get',
				url:ctx+"/bssecper/delete/"+row.id,
				success: function(data){
					successTip(data,dg);
				}
			});
		}
	});
}

//弹窗修改
function upd(){
	var row = dg.datagrid('getSelected');
	if(rowIsNull(row)) return;
	d=$("#dlg").dialog({
		title: '修改警衛人員',
		width: 380,
		height: 340,
		href:ctx+'/bssecper/update/'+row.id,
		maximizable:true,
		modal:true,
		buttons:[{
			text:'修改',
			handler:function(){
				var empNo = $.trim($("#empNo").val());
				var company=$.trim($("#company").val());
				var psnId=$.trim($("#psnId").val());
				var empName=$.trim($("#empName").val());
				var intStrLen =psnId.length;

				if (intStrLen != 18) {
					error = "輸入身份證長度不正確！";
					alert(error);
					return false;
				}
				if (empNo == "") {
					$.messager.alert("溫馨提示", "工號不能為空！", "info");
					return;
				}
				if (psnId == "") {
					$.messager.alert("溫馨提示", "身份證號不能為空！", "info");
					return;
				}
				if (empName == "") {
					$.messager.alert("溫馨提示", "姓名不能為空！", "info");
					return;
				}
				//test()
				$('#mainform').submit();
			}
		},{
			text:'取消',
			handler:function(){
				d.panel('close');
			}
		}]
	});
}

// 批量導入模板下載
function batchImportTpl() {
	document.location.href = ctx + '/bssecper/downLoad/batchImportTpl';
}

// 打开选择文件导入页面
function openBatchImportWin() {
	var optionWin = $("#optionWin").window();

	optionWin.window({
		center : true
	});
	// 顯示於畫面中
	optionWin.slideDown();
	$("#batchImportForm").form("clear");
	$("#labelListAddResult").html("");
	$("#downloadError").hide();
	// optionWin.find("input").val("");
	optionWin.window("open");
}

// 导入文件上传
function btnUploadExcel() {
	var file_data = $("#batchFile").prop("files")[0];
	if (file_data.name == "") {
		$.messager.alert('系統提示', '請選擇文件，再進行導入！', 'info');
		return;
	}
	var suffix = /\.[^\.]+$/.exec(file_data.name);
	var isExcel2003 = "";
	if (suffix == ".xls") {
		isExcel2003 = "true";
	} else if (suffix == ".xlsx") {
		isExcel2003 = "false";
	} else {
		$.messager.alert('系統提示', '導入文件格式不正確！', 'info');
		return;
	}
	// 创建
	var form_data = new FormData();
	// 获取文件
	form_data.append("file", file_data);
	form_data.append("isExcel2003", isExcel2003);

	// 把所以表单信息
	$.ajax({
		type : "POST",
		url : ctx + "/bssecper/upload",
		// dataType : "json",
		processData : false, // 注意：让jQuery不要处理数据
		contentType : false, // 注意：让jQuery不要设置contentType
		data : form_data
	}).success(function(msg) {
		// console.log(msg)
		if (msg == 'success') {
			$("#labelListAddResult").html("導入成功");
			$("#downloadError").hide();
		} else {
			if (msg == '2') {
				$("#labelListAddResult").html("導入失敗, ");
				$("#downloadError").show();
			} else {
				$("#labelListAddResult").html(msg);
				$("#downloadError").show();
			}
		}
	}).fail(function(msg) {
		$("#labelListAddResult").html("導入失敗");
	});
}
