package com.foxconn.ipebg.buessness.workflow.entity;

import java.io.Serializable;

/**
 * 處理任務信息
 *
 * <AUTHOR>
 */
public class TaskInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 任務id
     */
    private String taskId;
    /**
     * 任務處理人
     */
    private String assignee;
    /**
     * 流程實例id
     */
    private String processId;
    /**
     * 任務名稱(節點名稱)
     */
    private String taskName;
    /**
     * 流程描述
     */
    private String pdId;
    /**
     * 待簽核表單數
     */
    private String tableNum;

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getAssignee() {
        return assignee;
    }

    public void setAssignee(String assignee) {
        this.assignee = assignee;
    }

    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getPdId() {
        return pdId;
    }

    public void setPdId(String pdId) {
        this.pdId = pdId;
    }

    public String getTableNum() {
        return tableNum;
    }

    public void setTableNum(String tableNum) {
        this.tableNum = tableNum;
    }
}
