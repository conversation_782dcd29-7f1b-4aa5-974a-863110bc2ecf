package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.entity.BsArrangeEntity;
import com.foxconn.ipebg.basics.entity.BsPostEntity;
import com.foxconn.ipebg.basics.entity.BsTraineeEntity;
import com.foxconn.ipebg.basics.entity.BsTrainingPeriodEntity;
import com.foxconn.ipebg.basics.dao.BsTraineeDao;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.Dict;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WorkFlowEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.system.service.DictService;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 培訓人員基本資料表
 *
 * <AUTHOR>
 * @email <EMAIL> 
 * @date 2019-08-29 10:42:45
 */
@Service
@Transactional(readOnly=true)
public class  BsTraineeService extends BaseBusinessService<BsTraineeEntity, String>{
    @Autowired
    private BsTraineeDao bsTraineeDao;

    @Autowired
    private UserServiceUtil serviceUtil;
    @Autowired
    private DictService dictService;
	public static final String BSTRAINEEERRORDTOS = "bstraineeerrordtos";

    @Override
    public HibernateDao<BsTraineeEntity, String> getEntityDao() {
        return bsTraineeDao;
    }

    public BsTraineeEntity findById(String id) {
        return this.bsTraineeDao.findUniqueBy("id", id);
    }

	public List<BsTraineeEntity> findPersonList(BsTraineeEntity bsTrainee) {

		StringBuffer sb = new StringBuffer();
		sb.append(" select * from v_bs_trainee t ").append(" WHERE 1=1  ");
        if (!bsTrainee.getEmpNo().equals(""))//||!serialnoQry.equals(null)
        {
        	sb.append("  and t.emp_no like '%"+bsTrainee.getEmpNo()+"%' ");	
        }
        if (!bsTrainee.getEmpName().equals(""))//||!serialnoQry.equals(null)
        {
        	sb.append("  and t.emp_name like '%"+bsTrainee.getEmpName()+"%' ");	
        }
        if (StringUtils.isNotEmpty(bsTrainee.getCompany())) {
        	String coms = bsTrainee.getCompany().replaceAll(",","','");
        	sb.append(" and t.company in ('"+coms+"') ");
		}
		sb.append("ORDER BY create_date desc ");
		@SuppressWarnings("unchecked")
		List<BsTraineeEntity> personList = this.bsTraineeDao
				.createSQLQuery(sb.toString()).addEntity(BsTraineeEntity.class)
				.list();
		return personList;

	}


	// 人員導入驗證
	public BsTraineeEntity isExistEmpInfo1(String empNo) {
		List<BsTraineeEntity> list = bsTraineeDao.find("from BsTraineeEntity t where t.empNo=?0 ", empNo);
		if (list.size() == 1) {
			return list.get(0);
		} else {
			return null;
		}
	}


	public List<BsTraineeEntity> findPersonListByEmpNos(String empNos) {
        String inStr="";
		StringBuffer sb = new StringBuffer();
		sb.append(" select * from BS_TRAINEE t ").append(" WHERE 1=1  ");
        if (!empNos.equals(""))//||!serialnoQry.equals(null)
        {
        	 String [] empNoArray=empNos.split(",");
        	for (int j=0;j<empNoArray.length;j++)
			{
				//"'" + projectCode + "',"
				inStr=inStr+"'"+empNoArray[j]+"',"	;
			}	
        	sb.append("  and t.emp_no in ("+inStr.substring(0, inStr.length()-1)+") ");	
        }
       
        
		sb.append("ORDER BY emp_no ");
		@SuppressWarnings("unchecked")
		List<BsTraineeEntity> personList = this.bsTraineeDao
				.createSQLQuery(sb.toString()).addEntity(BsTraineeEntity.class)
				.list();
		return personList;

	}
	public BsTraineeEntity findPersonByEmpNo(String empNo) {
		return this.bsTraineeDao.findUniqueBy("empNo", empNo);
	}
    
	 /**
     * 方法描述: 生成下拉數據 
     **/


	public List<BsTraineeEntity> getAllCompany() {
		List<BsTraineeEntity> btList=new ArrayList<BsTraineeEntity>();
		//select employee form Employee as employee 
		//select distinct employee.name from Employee as employee 
		List bt=this.bsTraineeDao.createSQLQuery(" select distinct company from BS_TRAINEE t where  t.company is not null ").list();
		
    if (bt.size()>0)
    {
    	for (int i=0;i<bt.size();i++)
    	{
    	BsTraineeEntity bte=new BsTraineeEntity();
    	bte.setCompany(bt.get(i).toString());
    	System.out.print(bt.get(i).toString());
    	btList.add(bte);
    	}
    	
    }
     return btList ;

	}
	
	// 驗證身份證唯一性
	public boolean isExistPsnId(BsTraineeEntity bsTraineeEntity) {
		boolean result = false;
		List<BsTraineeEntity> list = bsTraineeDao
				.find("from BsTraineeEntity t where t.psnId=?0  and id !=?1",//and t.empNo=?1
						
						bsTraineeEntity.getPsnId(),
						bsTraineeEntity.getId() == null ? "888888" : bsTraineeEntity
								.getId());
		if (list.size() > 0) {
			result = true;
		} else {
			result = false;
		}
		return result;
	}
	// 工號唯一性
	public boolean isExistEmpNo(BsTraineeEntity bsTraineeEntity) {
		boolean result = false;
		List<BsTraineeEntity> list = bsTraineeDao
				.find("from BsTraineeEntity t where t.empNo=?0  and id !=?1",//and t.empNo=?1
						
						bsTraineeEntity.getEmpNo(),
						bsTraineeEntity.getId() == null ? "888888" : bsTraineeEntity
								.getId());
		if (list.size() > 0) {
			result = true;
		} else {
			result = false;
		}
		return result;
	}
}
