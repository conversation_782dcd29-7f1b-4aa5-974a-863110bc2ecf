package com.foxconn.ipebg.buessness.workflow.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 流程參數配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-06 11:04:36
 */
@Entity
@Table(name = "WF_CONFIGPARAM")
@DynamicUpdate
@DynamicInsert
public class WfConfigparamEntity extends DataEntity<WfConfigparamEntity>  implements Serializable {
    private static final long serialVersionUID = 1L;

    //流程編碼
    private String workflowid;
    //參數名稱
    private String paramename;
    //條件字段sqrjbpd=1 派課級主管 sqrjbpd=2派部級主管 sqrjbpd=3派廠級主管 sqrjbpd=4派製造處 sqrjbpd=5 派製造總處 sqrjbpd=6派產品處主管
    private String paramvalue;
    //參數類型
    private String paramtype;

    private String version;

    /**
     * 设置：流程編碼
     */
    public void setWorkflowid(String workflowid) {
        this.workflowid = workflowid;
    }

    /**
     * 获取：流程編碼
     */
    @Column(name = "WORKFLOWID", nullable = false, length = 20)
    public String getWorkflowid() {
        return workflowid;
    }

    /**
     * 设置：參數名稱
     */
    public void setParamename(String paramename) {
        this.paramename = paramename;
    }

    /**
     * 获取：參數名稱
     */
    @Column(name = "PARAMENAME", nullable = false, length = 20)
    public String getParamename() {
        return paramename;
    }

    /**
     * 设置：條件字段sqrjbpd=1 派課級主管 sqrjbpd=2派部級主管 sqrjbpd=3派廠級主管 sqrjbpd=4派製造處 sqrjbpd=5 派製造總處 sqrjbpd=6派產品處主管
     */
    public void setParamvalue(String paramvalue) {
        this.paramvalue = paramvalue;
    }

    /**
     * 获取：條件字段sqrjbpd=1 派課級主管 sqrjbpd=2派部級主管 sqrjbpd=3派廠級主管 sqrjbpd=4派製造處 sqrjbpd=5 派製造總處 sqrjbpd=6派產品處主管
     */
    @Column(name = "PARAMVALUE", nullable = false, length = 20)
    public String getParamvalue() {
        return paramvalue;
    }

    /**
     * 设置：參數類型
     */
    public void setParamtype(String paramtype) {
        this.paramtype = paramtype;
    }

    /**
     * 获取：參數類型
     */
    @Column(name = "PARAMTYPE", nullable = false, length = 20)
    public String getParamtype() {
        return paramtype;
    }

    @Column(name = "VERSION", nullable = false, length = 20)
    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
}
