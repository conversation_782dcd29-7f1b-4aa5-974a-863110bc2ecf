package com.foxconn.ipebg.basics.entity;

        import java.io.Serializable;
        import java.util.Date;
        import javax.persistence.*;

        import com.foxconn.ipebg.common.entity.DataEntity;
        import org.hibernate.annotations.DynamicInsert;
        import org.hibernate.annotations.DynamicUpdate;


/**
 * 培訓期日排班表人員表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-09-09 13:50:29
 */
@Entity
@Table(name = "BS_TRAINING_ARRANGE_PER")
@DynamicUpdate
@DynamicInsert
public class BsTrainingArrangePerEntity extends DataEntity<BsTrainingArrangePerEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

                                    //工號
            private String empNo;
                                //姓名
            private String empName;
                                //bs_training_arrange表主鍵
            private String bstaId;
               
            
                    /**
             * 设置：工號
             */
            public void setEmpNo(String empNo) {
                this.empNo = empNo;
            }

        /**
         * 获取：工號
         */
        
                            @Column(name = "EMP_NO", nullable = false, length = 20)
                public String getEmpNo() {
                    return empNo;
                }
                    
                    /**
             * 设置：姓名
             */
            public void setEmpName(String empName) {
                this.empName = empName;
            }

        /**
         * 获取：姓名
         */
        
                            @Column(name = "EMP_NAME", nullable = false, length = 20)
                public String getEmpName() {
                    return empName;
                }
                    
                    /**
             * 设置：bs_training_arrange表主鍵
             */
            public void setBstaId(String bstaId) {
                this.bstaId = bstaId;
            }

        /**
         * 获取：bs_training_arrange表主鍵
         */
        
                            @Column(name = "BSTA_ID", nullable = false, length = 20)
                public String getBstaId() {
                    return bstaId;
                }
                    
}
