<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>警衛服務費用匯總</title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
	_summary = ${ summary };
</script>

<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
	<table id="summaryDG"></table>
	<script type="text/javascript">
		$('#summaryDG').datagrid({
			method: "get",
			url: ctx + '/bsservicecost/getSummary',
			fit: true,
			fitColumns: false,
			border: false,
			queryParams:{ bu:_bu, legalId: _legalId, workMonth: _workMonth },
			striped: true,
			pagination: false,
			rownumbers: false,
			columns: [[
				{
					field: 'key',
					title: '條目',
					sortable: false,
					halign: 'center',
					width: 150
				},
				{
					field: 'value',
					title: '內容',
					sortable: false,
					halign: 'center',
					width: 100
				}
			]],
			loadFilter: function (data) {
				var array = new Array();
				array.push({
					key: '正常上班費用（RMB/元）',
					value: data['normalCost'] != null ? data['normalCost'] : 0
				});
				array.push({
					key: '培訓費用（RMB/元）',
					value: data['trainingCost'] != null ? data['trainingCost'] : 0
				});
				array.push({
					key: '常規費用（RMB/元）',
					value: data['totalCost'] != null ? data['totalCost'] : 0
				});
				array.push({
					key: '加班時數（小時）',
					value: data['otHour'] != null ? data['otHour'] : 0
				});
				array.push({
					key: '加班費用（RMB/元）',
					value: data['otCost'] != null ? data['otCost'] : 0
				});
				array.push({
					key: '總費用（RMB/元）',
					value: data['totalCostAll'] != null ? data['totalCostAll'] : 0
				});
				return {
					total: 6,
					rows: array
				};
			}
		});
	</script>
</body>
</html>