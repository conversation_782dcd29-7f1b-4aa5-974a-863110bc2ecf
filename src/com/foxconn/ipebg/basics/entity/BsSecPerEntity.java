package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import javax.persistence.*;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.ColumnTransformer;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * 警衛人員資料
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-12-21 14:54:46
 */
@Entity
@Table(name = "BS_SEC_PER")
@DynamicUpdate
@DynamicInsert
public class BsSecPerEntity extends DataEntity<BsSecPerEntity> implements
		Serializable {
	private static final long serialVersionUID = 1L;
	
	//工號
	private String empNo;
	//姓名
	private String empName;
	//身份證號
	private String psnId;
	//保安公司
	private String company;
	
	
	@Column(name = "EMP_NO", nullable = false, length = 20)
	public String getEmpNo() {
		return empNo;
	}

	public void setEmpNo(String empNo) {
		this.empNo = empNo;
	}
	
	@Column(name = "EMP_NAME", nullable = false, length = 20)
	public String getEmpName() {
		return empName;
	}

	public void setEmpName(String empName) {
		this.empName = empName;
	}
	
	
	@Column(name = "PSN_ID", nullable = false, length = 20)
	@ColumnTransformer(forColumn = "PSN_ID", read = "CONVERT_FROM(decrypt(decode(PSN_ID, 'base64'), 'sgas', 'aes'), 'UTF-8')", write = "encode(encrypt(cast(? as bytea), 'sgas', 'aes'), 'base64')")
	public String getPsnId() {
		return psnId;
	}

	public void setPsnId(String psnId) {
		this.psnId = psnId;
	}

	@Column(name = "COMPANY", nullable = false, length = 20)
	public String getCompany() {
		return company;
	}

	public void setCompany(String company) {
		this.company = company;
	}




}
