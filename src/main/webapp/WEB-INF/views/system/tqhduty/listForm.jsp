<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>${comments}</title>
	<script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
	</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/tqhduty/${action}" method="post">
		<table class="formTable">
		    			<tr>
				<td>${column.comments}：</td>
				<td>
				    										<input id="dutyid" name="dutyid" class="easyui-validatebox" data-options="width: 150" value="${tQhDuty.dutyid }" />
									</td>
			</tr>
						<tr>
				<td>${column.comments}：</td>
				<td>
				    										<input id="dutyname" name="dutyname" class="easyui-validatebox" data-options="width: 150" value="${tQhDuty.dutyname }" />
									</td>
			</tr>
						<tr>
				<td>${column.comments}：</td>
				<td>
				    															<input id="id" name="id" class="easyui-validatebox" data-options="width: 150" value="${tQhDuty.id }" />
									</td>
			</tr>
						<tr>
				<td>創建人：</td>
				<td>
				    										<input id="createBy" name="createBy" class="easyui-validatebox" data-options="width: 150" value="${tQhDuty.createBy }" />
									</td>
			</tr>
						<tr>
				<td>創建時間：</td>
				<td>
				    										<input id="createDate" name="createDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 150" value="<fmt:formatDate value="${tQhDuty.createDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>更新者：</td>
				<td>
				    										<input id="updateBy" name="updateBy" class="easyui-validatebox" data-options="width: 150" value="${tQhDuty.updateBy }" />
									</td>
			</tr>
						<tr>
				<td>更新時間：</td>
				<td>
				    										<input id="updateDate" name="updateDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 150" value="<fmt:formatDate value="${tQhDuty.updateDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>刪除標識：</td>
				<td>
				    										<input id="delFlag" name="delFlag" class="easyui-validatebox" data-options="width: 150" value="${tQhDuty.delFlag }" />
									</td>
			</tr>
					</table>
	</form>
  </div>
<script src='${ctx}/static/js/system/tqhduty.js?"+Math.random()"'></script>
<script type="text/javascript">
   //提交表单
$('#mainform').form({
    onSubmit: function(){
        var isValid = $(this).form('validate');
        return isValid;	// 返回false终止表单提交
    },
    success:function(data){
        successTip(data,dg,d);
    }
});
</script>
</body>
</html>