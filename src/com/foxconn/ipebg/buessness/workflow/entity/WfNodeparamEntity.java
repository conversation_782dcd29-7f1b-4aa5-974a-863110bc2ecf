package com.foxconn.ipebg.buessness.workflow.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 流程節點出口信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-06 11:04:39
 */
@Entity
@Table(name = "WF_NODEPARAM")
@DynamicUpdate
@DynamicInsert
public class WfNodeparamEntity extends DataEntity<WfNodeparamEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    //流程編碼
    private String workflowid;
    //節點編碼
    private String nodeid;
    //參數名稱
    private String paramename;
    //參數值
    private String paramvalue;
    //參數類型
    private String paramtype;
    //去向描述
    private String describ;
    //表單目錄狀態 參考字典:dict_workstatus
    private String toworkstatus;
    //操作標識
    private String ispass;

    private String version;

    /**
     * 设置：流程編碼
     */
    public void setWorkflowid(String workflowid) {
        this.workflowid = workflowid;
    }

    /**
     * 获取：流程編碼
     */
    @Column(name = "WORKFLOWID", nullable = false, length = 20)
    public String getWorkflowid() {
        return workflowid;
    }

    /**
     * 设置：節點編碼
     */
    public void setNodeid(String nodeid) {
        this.nodeid = nodeid;
    }

    /**
     * 获取：節點編碼
     */
    @Column(name = "NODEID", nullable = false, length = 20)
    public String getNodeid() {
        return nodeid;
    }

    /**
     * 设置：參數名稱
     */
    public void setParamename(String paramename) {
        this.paramename = paramename;
    }

    /**
     * 获取：參數名稱
     */
    @Column(name = "PARAMENAME", nullable = false, length = 20)
    public String getParamename() {
        return paramename;
    }

    /**
     * 设置：參數值
     */
    public void setParamvalue(String paramvalue) {
        this.paramvalue = paramvalue;
    }

    /**
     * 获取：參數值
     */
    @Column(name = "PARAMVALUE", nullable = false, length = 20)
    public String getParamvalue() {
        return paramvalue;
    }

    /**
     * 设置：參數類型
     */
    public void setParamtype(String paramtype) {
        this.paramtype = paramtype;
    }

    /**
     * 获取：參數類型
     */
    @Column(name = "PARAMTYPE", nullable = false, length = 20)
    public String getParamtype() {
        return paramtype;
    }

    /**
     * 设置：去向描述
     */
    public void setDescrib(String describ) {
        this.describ = describ;
    }

    /**
     * 获取：去向描述
     */
    @Column(name = "DESCRIB", nullable = false, length = 20)
    public String getDescrib() {
        return describ;
    }

    /**
     * 设置：表單目錄狀態 參考字典:dict_workstatus
     */
    public void setToworkstatus(String toworkstatus) {
        this.toworkstatus = toworkstatus;
    }

    /**
     * 获取：表單目錄狀態 參考字典:dict_workstatus
     */
    @Column(name = "TOWORKSTATUS", nullable = false, length = 20)
    public String getToworkstatus() {
        return toworkstatus;
    }

    /**
     * 设置：操作標識
     */
    public void setIspass(String ispass) {
        this.ispass = ispass;
    }

    /**
     * 获取：操作標識
     */
    @Column(name = "ISPASS", nullable = false, length = 20)
    public String getIspass() {
        return ispass;
    }



    @Column(name = "VERSION", nullable = false, length = 20)
    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
}
