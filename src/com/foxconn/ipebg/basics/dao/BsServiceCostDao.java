package com.foxconn.ipebg.basics.dao;

import com.foxconn.ipebg.basics.entity.BsServiceCostEntity;
import org.hibernate.jdbc.Work;
import org.springframework.stereotype.Repository;
import com.foxconn.ipebg.common.persistence.HibernateDao;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Types;
import java.util.Date;

/**
 * 警衛服務費用結賬明細主表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-10-07 13:15:24
 */
@Repository
public class BsServiceCostDao extends HibernateDao<BsServiceCostEntity,String> {

    /**
     * 調用存儲過程生成警衛服務費用報表數據
     * @param month 生成月份
     */
	public void generateServiceCost(final Date month) throws SQLException {
        getSession().doWork(new Work() {
            @Override
            public void execute(Connection connection) throws SQLException {
                CallableStatement callable = connection.prepareCall("{CALL pk_rptws_cal_select_workstatus_detail(?, ?)}");
                callable.setDate(1, new java.sql.Date(month.getTime()));
                callable.registerOutParameter(2, Types.VARCHAR);
                callable.execute();
                callable.close();
            }
        });
    }
}
