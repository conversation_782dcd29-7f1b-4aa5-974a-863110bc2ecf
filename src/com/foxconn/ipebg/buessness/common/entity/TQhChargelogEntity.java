package com.foxconn.ipebg.buessness.common.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 主管簽核日誌表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-08 09:28:35
 */
@Entity
@Table(name = "T_QH_CHARGELOG")
@DynamicUpdate
@DynamicInsert
public class TQhChargelogEntity extends DataEntity<TQhChargelogEntity>  implements Serializable {
    private static final long serialVersionUID = 1L;

            //簽核日誌ID
        private String logid;
            //工單ID
        private String serialno;
            //流程編碼
        private String workflowid;
            //簽核節點
        private String chargenode;
            //記錄創建時間
        private Date createtime;
            //簽核主管工號
        private String chargeno;
            //字典：DICT_ISPASS
        private String ispass;
            //批註
        private String decrib;
            //操作IP
        private String operateip;
            //簽核主管姓名
        private String chargename;
            /**
         * 设置：簽核日誌ID
         */
        public void setLogid(String logid) {
            this.logid = logid;
        }
    /**
     * 获取：簽核日誌ID
     */
                @Column(name = "LOGID", nullable = false, length = 20)
        public String getLogid() {
            return logid;
        }
        
            /**
         * 设置：工單ID
         */
        public void setSerialno(String serialno) {
            this.serialno = serialno;
        }
    /**
     * 获取：工單ID
     */
                    @Column(name = "SERIALNO", nullable = false, length = 20)
            public String getSerialno() {
                return serialno;
            }
        
            /**
         * 设置：流程編碼
         */
        public void setWorkflowid(String workflowid) {
            this.workflowid = workflowid;
        }
    /**
     * 获取：流程編碼
     */
                    @Column(name = "WORKFLOWID", nullable = false, length = 20)
            public String getWorkflowid() {
                return workflowid;
            }
        
            /**
         * 设置：簽核節點
         */
        public void setChargenode(String chargenode) {
            this.chargenode = chargenode;
        }
    /**
     * 获取：簽核節點
     */
                    @Column(name = "CHARGENODE", nullable = false, length = 20)
            public String getChargenode() {
                return chargenode;
            }
        
            /**
         * 设置：記錄創建時間
         */
        public void setCreatetime(Date createtime) {
            this.createtime = createtime;
        }
    /**
     * 获取：記錄創建時間
     */
                    @Column(name = "CREATETIME", nullable = false, length = 20)
                    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
            public Date getCreatetime() {
                return createtime;
            }
        
            /**
         * 设置：簽核主管工號
         */
        public void setChargeno(String chargeno) {
            this.chargeno = chargeno;
        }
    /**
     * 获取：簽核主管工號
     */
                    @Column(name = "CHARGENO", nullable = false, length = 20)
            public String getChargeno() {
                return chargeno;
            }
        
            /**
         * 设置：字典：DICT_ISPASS
         */
        public void setIspass(String ispass) {
            this.ispass = ispass;
        }
    /**
     * 获取：字典：DICT_ISPASS
     */
                    @Column(name = "ISPASS", nullable = false, length = 20)
            public String getIspass() {
                return ispass;
            }
        
            /**
         * 设置：批註
         */
        public void setDecrib(String decrib) {
            this.decrib = decrib;
        }
    /**
     * 获取：批註
     */
                    @Column(name = "DECRIB", nullable = false, length = 20)
            public String getDecrib() {
                return decrib;
            }
        
            /**
         * 设置：操作IP
         */
        public void setOperateip(String operateip) {
            this.operateip = operateip;
        }
    /**
     * 获取：操作IP
     */
                    @Column(name = "OPERATEIP", nullable = false, length = 20)
            public String getOperateip() {
                return operateip;
            }
        
            /**
         * 设置：簽核主管姓名
         */
        public void setChargename(String chargename) {
            this.chargename = chargename;
        }
    /**
     * 获取：簽核主管姓名
     */
                    @Column(name = "CHARGENAME", nullable = false, length = 20)
            public String getChargename() {
                return chargename;
            }
        
    }
