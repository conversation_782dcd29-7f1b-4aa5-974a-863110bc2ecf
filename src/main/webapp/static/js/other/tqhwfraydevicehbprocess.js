var dg;
var d;
$(function () {
    dg = $('#dg').datagrid({
        method: "get",
        url: ctx+'/tqhwfraydevicehbprocess/list',
        fit: true,
        fitColumns: true,
        border: false,
        idField: 'id',
        striped: true,
        pagination: true,
        rownumbers: true,
        pageNumber: 1,
        pageSize: 20,
        pageList: [10, 20, 30, 40, 50],
        singleSelect: true,
        columns: [[
            {field: 'id', title: '主鍵', hidden: true},
            {field: 'serialno', title: '任務編號', sortable: true, width: 150, formatter: operation},
            {field: 'dealno', title: '承辦人工號', sortable: true, width: 100},
            {field: 'dealname', title: '承辦人名稱', sortable: true, width: 100},
            {field: 'dealdeptno', title: '承辦人單位代碼', sortable: true, width: 100},
            {field: 'dealdeptname', title: '承辦人單位名稱', sortable: true, width: 100},
            {field: 'dealtime', title: '提報日期', sortable: true, width: 100},
            {field: 'corporateid', title: '法人', sortable: true, width: 100},
            {field: 'workstatus', title: '表單狀態', sortable: true, width: 100},
            {field: 'nodeName', title: '當前簽核節點', sortable: true, width: 100},
            {field: 'auditUser', title: '當前簽核人', sortable: true, width: 100},
            {field: 'complettime', title: '簽核完成時間', sortable: true, width: 100}
        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        toolbar: '#tb'
    });
    $(":input").removeClass("validatebox-invalid");
    //獲取廠區
    $.get(ctx+'/tqhfactoryidconfig/allFactorys/',
        function (result) {
            $("#dealfactoryid").combobox({
                data: result,
                valueField: "factoryid",
                textField: "factoryname",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({factoryid: '', factoryname: '請選擇'});
                    return data;
                }
            });
        });
    //創建下拉查詢條件
    $.ajax({
        url: ctx+"/system/dict/getDictByType/audit_status",
        //dataType:"json",
        type: "GET",
        success: function (data) {
            //绑定第一个下拉框
            $("#qysjzt").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({value: '', label: '請選擇'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });
    //獲取項目法人
    $.ajax({
        url: ctx+"/system/dict/getDictByType/project_entity",
        type: "GET",
        success: function (data) {
            $("#corporateid").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({value: '', label: '請選擇'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });
    //獲取是否具有輻射安全許可證
    $.ajax({
        type:"GET",
        url:ctx+"/system/dict/getDictByType/ishave_permit",
        success: function (data) {
            var html = '';
            if ($("#ishavepermit").val() == null || $("#ishavepermit").val() == '') {
                $.each(data, function (i, v) {
                    html += "<input type='radio' value=" + v.value + " name='ishavepermitName'/>" + v.label + "&nbsp;&nbsp;&nbsp;&nbsp;";
                })
            } else {
                var str = $("#ishavepermit").val().split(",");
                var disOrEnabled=$("#disOrEnabled").val();
                $.each(data, function (n, item) {
                    var flag=false;
                    for (var i = 0; i < str.length; i++) {
                        if (str[i] == item.value) {
                            html += "<input type='radio'  checked='checked' "+disOrEnabled+" value=" + item.value + " name='ishavepermitName'/>" + item.label + "&nbsp;&nbsp;&nbsp;&nbsp;";
                            flag=true;
                        }
                    }
                    if(!flag)
                        html += "<input type='radio' "+disOrEnabled+" value=" + item.value + " name='ishavepermitName'/>" + item.label + "&nbsp;&nbsp;&nbsp;&nbsp;";
                });
            }
            $(".ishavepermitDiv").html(html);
        }
    });
    //獲取建設性質
    $.ajax({
        type:"GET",
        url:ctx+"/system/dict/getDictByType/build_properties",
        success: function (data) {
            var html = '';
            if ($("#buildproperties").val() == null || $("#buildproperties").val() == '') {
                $.each(data, function (i, v) {
                    html += "<input type='radio' value=" + v.value + " name='buildpropertiesName'/>" + v.label + "&nbsp;&nbsp;&nbsp;&nbsp;";
                })
            } else {
                var str = $("#buildproperties").val().split(",");
                var disOrEnabled=$("#disOrEnabled").val();
                $.each(data, function (n, item) {
                    var flag=false;
                    for (var i = 0; i < str.length; i++) {
                        if (str[i] == item.value) {
                            html += "<input type='radio'  checked='checked' "+disOrEnabled+"  value=" + item.value + " name='buildpropertiesName'/>" + item.label + "&nbsp;&nbsp;&nbsp;&nbsp;";
                            flag=true;
                        }
                    }
                    if(!flag)
                        html += "<input type='radio'  "+disOrEnabled+" value=" + item.value + " name='buildpropertiesName'/>" + item.label + "&nbsp;&nbsp;&nbsp;&nbsp;";
                });
            }
            $(".buildpropertiesDiv").html(html);
        }
    });
    //添加一筆費用代碼
    $("#costItemAdd").click(function () {
        var len=parseInt($("#rayDevicehbCostItemTableIndex").val());
        var b = $("#rayDevicehbCostItemTable tr").length;
        $("#rayDevicehbCostItemTable").append("<tr align='center' id='costItem" + len + "'>"
            + "<td>" + b + "</td>"
            + "<td><input id='costname" + len + "' name='tQhWfraydevicehbcostitems[" + (len - 1) + "].costname' class='easyui-validatebox' required='true' style='width:400px;'><font color='red'>*</font></td>"
            + "<td><input id='costno" + len + "' name='tQhWfraydevicehbcostitems[" + (len - 1) + "].costno' class='easyui-validatebox' required='true' style='width:400px;'><font color='red'>*</font></td>"
            + "<td><input type='image' src='"+ctx+"/static/images/deleteRow.png' onclick='costdeltr(" + len + ");return false;'/></td>" + "</tr>");
        $.parser.parse($("#costItem"+len));
        $("#rayDevicehbCostItemTableIndex").val(len+1);
        $("#costname" + len).val('');
        $("#costno" + len).val('');
    });
    //添加一筆裝置代碼
    $("#deviceItemAdd").click(function () {
        var len=parseInt($("#rayDevicehbItemTableIndex").val());
        var b = $("#rayDevicehbItemTable tr").length;
        $("#rayDevicehbItemTable").append("<tr align='center' id='deviceItem" + len + "'>"
            + "<td>" + b + "</td>"
            + "<td><input id='devicename"+len+ "' name='tQhWfraydevicehbitems["+(len - 1)+"].devicename' required='true' class='easyui-validatebox' style='width:100px;'><font color='red'>*</font></td>"
            + "<td><input id='devicetype"+len+ "' name='tQhWfraydevicehbitems["+(len - 1)+"].devicetype' required='true' class='easyui-validatebox' style='width:100px;'><font color='red'>*</font></td>"
            + "<td><input id='devicenumber"+len+ "' name='tQhWfraydevicehbitems["+(len - 1)+"].devicenumber' required='true' onblur='valdMoney(this)' class='easyui-validatebox' style='width:80px;'><font color='red'>*</font></td>"
            + "<td><input id='nameanddescription"+len+"' name='tQhWfraydevicehbitems["+(len - 1)+"].nameanddescription' required='true' class='easyui-validatebox' style='width:100px;'><font color='red'>*</font></td>"
            + "<td><input id='floorspace"+len+"' name='tQhWfraydevicehbitems["+(len - 1)+"].floorspace' required='true' onblur='valdMoney(this)' class='easyui-validatebox' style='width:80px;'><font color='red'>*</font></td>"
            + "<td><input id='projectcost"+len+"' name='tQhWfraydevicehbitems["+(len - 1)+"].projectcost' required='true' onblur='valdMoney(this)' class='easyui-validatebox' style='width:80px;'><font color='red'>*</font></td>"
            + "<td><input id='enviromentalcost"+len + "' name='tQhWfraydevicehbitems["+(len - 1)+"].enviromentalcost' onblur='valdMoney(this)' required='true' class='easyui-validatebox' style='width:80px;'><font color='red'>*</font></td>"
            + "<td><input id='equipmentsource"+len + "' name='tQhWfraydevicehbitems["+(len - 1)+"].equipmentsource' required='true' class='easyui-validatebox' style='width:100px;'><font color='red'>*</font></td>"
            + "<td><input id='equipmenttype"+len + "' name='tQhWfraydevicehbitems["+(len - 1)+"].equipmenttype' "
            + "    required='true' class='easyui-combobox' data-options=\"panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadEquipmentType("+len+");}\"   style='width:80px;'><font color='red'>*</font></td>"
            + "<td><input id='deviceuse"+len+"' name='tQhWfraydevicehbitems["+(len - 1)+"].deviceuse' required='true' class='easyui-validatebox' style='width:100px;'><font color='red'>*</font></td>"
            + "<td><input id='remark"+len+"' name='tQhWfraydevicehbitems["+(len - 1)+"].remark' required='true' class='easyui-validatebox' style='width:100px;'><font color='red'>*</font></td>"
            + "<td><input type='image' src='"+ctx+"/static/images/deleteRow.png' onclick='deviceItemdeltr(" + len + ");return false;'/></td>" + "</tr>");
        $.parser.parse($("#deviceItem"+len));
        $("#rayDevicehbItemTableIndex").val(len+1);
        $("#devicename" + len).val('');
        $("#devicetype" + len).val('');
        $("#devicenumber" + len).val('');
        $("#nameanddescription" + len).val('');
        $("#floorspace" + len).val('');
        $("#projectcost" + len).val('');
        $("#enviromentalcost" + len).val('');
        $("#equipmentsource" + len).val('');
        $("#deviceuse" + len).val('');
        $("#remark" + len).val('');
    });
});


//刪除一筆費用裝置
function costdeltr(index) {
    debugger;
    if(confirm("確定刪除此筆記錄？")){
        if($("#rayDevicehbCostItemTable tr").length==2){
            alert("至少保留一筆！")
            return;
        }
        $("#costItem" + index).remove();
        var b = $("#rayDevicehbCostItemTable tr").length;
        for (var i = 1 ; i <= b; i++) {
            $("#rayDevicehbCostItemTable").find("tr").eq(i).find("td").eq(0).html(i);
            $("#rayDevicehbCostItemTable").find("tr").eq(i).find("td").eq(1).find("input[name^='tQhWfraydevicehbcostitems']").attr("name","tQhWfraydevicehbcostitems["+(i-1) +"].costname");
            $("#rayDevicehbCostItemTable").find("tr").eq(i).find("td").eq(2).find("input[name^='tQhWfraydevicehbcostitems']").attr("name","tQhWfraydevicehbcostitems["+(i-1) +"].costno");
        }
    }
}
//刪除一筆射線代碼
function deviceItemdeltr(index) {
    debugger;
    if(confirm("確定刪除此筆記錄？")) {
        if ($("#rayDevicehbItemTable tr").length == 2) {
            alert("至少保留一筆！")
            return;
        }
        $("#deviceItem" + index).remove();
        var b = $("#rayDevicehbItemTable tr").length;
        for (var i = 1 ; i <= b; i++) {
            $("#rayDevicehbItemTable").find("tr").eq(i).find("td").eq(0).html(i);
            $("#rayDevicehbItemTable").find("tr").eq(i).find("td").eq(1).find("input[name^='tQhWfraydevicehbitems']").attr("name","tQhWfraydevicehbitems["+(i-1) +"].devicename");
            $("#rayDevicehbItemTable").find("tr").eq(i).find("td").eq(2).find("input[name^='tQhWfraydevicehbitems']").attr("name","tQhWfraydevicehbitems["+(i-1) +"].devicetype");
            $("#rayDevicehbItemTable").find("tr").eq(i).find("td").eq(3).find("input[name^='tQhWfraydevicehbitems']").attr("name","tQhWfraydevicehbitems["+(i-1)+"].devicenumber");
            $("#rayDevicehbItemTable").find("tr").eq(i).find("td").eq(4).find("input[name^='tQhWfraydevicehbitems']").attr("name","tQhWfraydevicehbitems["+(i-1) +"].nameanddescription");
            $("#rayDevicehbItemTable").find("tr").eq(i).find("td").eq(5).find("input[name^='tQhWfraydevicehbitems']").attr("name","tQhWfraydevicehbitems["+(i-1) +"].floorspace");
            $("#rayDevicehbItemTable").find("tr").eq(i).find("td").eq(6).find("input[name^='tQhWfraydevicehbitems']").attr("name","tQhWfraydevicehbitems["+(i-1) +"].projectcost");
            $("#rayDevicehbItemTable").find("tr").eq(i).find("td").eq(7).find("input[name^='tQhWfraydevicehbitems']").attr("name","tQhWfraydevicehbitems["+(i-1) +"].enviromentalcost");
            $("#rayDevicehbItemTable").find("tr").eq(i).find("td").eq(8).find("input[name^='tQhWfraydevicehbitems']").attr("name","tQhWfraydevicehbitems["+(i-1) +"].equipmentsource");
            $("#rayDevicehbItemTable").find("tr").eq(i).find("td").eq(9).find("input[name^='tQhWfraydevicehbitems']").attr("name","tQhWfraydevicehbitems["+(i-1) +"].equipmenttype");
            $("#rayDevicehbItemTable").find("tr").eq(i).find("td").eq(10).find("input[name^='tQhWfraydevicehbitems']").attr("name","tQhWfraydevicehbitems["+(i-1) +"].deviceuse");
            $("#rayDevicehbItemTable").find("tr").eq(i).find("td").eq(11).find("input[name^='tQhWfraydevicehbitems']").attr("name","tQhWfraydevicehbitems["+(i-1) +"].remark");
        }

    }
}
//點擊添加一筆射線裝置加載設備類別
function loadEquipmentType(flag) {
    $.ajax({
        url: ctx+"/system/dict/getDictByType/equipment_type",
        type: "GET",
        success: function (data) {
            var themecombo2 = [{ 'label': '請選擇', 'value': ''}];
            for (var i = 0; i < data.length; i++) {
                themecombo2.push({ "label": data[i].label, "value": data[i].value });
            }
            //重新載入所有明細
            $("#equipmenttype" + flag).combobox("loadData", themecombo2);
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });

}
function operation(value, row, index) {
    return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('詳情',ctx+'/tqhwfraydevicehbprocess/view/" + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
};

//弹窗增加
function add() {
    d = $("#dlg").dialog({
        title: '添加射線裝置環保手續辦理委託申請單',
        width: 380,
        height: 380,
        href: ctx+'/tqhwfraydevicehbprocess/create',
        maximizable: true,
        modal: true,
        buttons: [{
            text: '确认',
            handler: function () {
                $("#mainform").submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//删除
function del() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function (data) {
        if (data) {
            $.ajax({
                type: 'get',
                url: ctx+"/tqhwfraydevicehbprocess/delete/" + row.id,
                success: function (data) {
                    successTip(data, dg);
                }
            });
        }
    });
}

//弹窗修改
function upd() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    d = $("#dlg").dialog({
        title: '修改射線裝置環保手續辦理委託申請單',
        width: 380,
        height: 340,
        href: ctx+'/tqhwfraydevicehbprocess/update/' + row.id,
        maximizable: true,
        modal: true,
        buttons: [{
            text: '修改',
            handler: function () {
                $('#mainform').submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//创建查询对象并查询
function cx() {
    var obj = $("#searchFrom").serializeObject();
    dg.datagrid('load', obj);
}
//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("reset");
}
function saveInfo(flag) {
    if(flag=='0'){
        var empno = $.trim($("#dealno").val());
        if (empno=='') {
            alert("承辦人工號不能為空！");
            return;
        }
    }else{
        var isValid = $("#mainform").form('validate');
        if (!isValid) {
            return;
        }
        if(!validEquipmentType()){
            return;
        }
    }
    if (!valdIshavepermitRadio(flag)) {
        return;
    }
    if (!valdBuildpropertiesNameRadio(flag)) {
        return;
    }
    $.ajax({
        url: ctx+'/tqhwfraydevicehbprocess/create/' + flag,
        type: 'POST',
        beforeSend: ajaxLoading,
        //dataType: 'json',
        data: $('#mainform').serialize(),
        success: function (data) {
            ajaxLoadEnd();
            successTip(data, dg, d);
            window.parent.mainpage.mainTabs.closeCurrentTab();
        }
    });
}

//validatebox驗證擴展
$.extend($.fn.validatebox.defaults.rules, {
    endTime: {
        validator: function (value, param) {
            var startDate = $('#buildcyclebegindate').datebox('getValue');
            var startTmp = new Date(startDate.replace(/-/g, "/"));
            var endTmp = new Date(value.replace(/-/g, "/"));
            return startTmp <= endTmp;
        },
        message: '结束时间要大于开始时间！'
    }
});

//驗證申請人聯繫電話
function valdApplyTel(obj) {
    var phoneL = $.trim($("#dealtel").val()).split("").length;
    var tel = obj.value;
    if (tel != "") {
        var temp = tel;
        var regex = /^[0-9]{3}\+[0-9]{5}$/;
        var patt1 = new RegExp(regex);
        var result = patt1.test(tel);
        if (!result) {
            alert("分機格式有誤!");
            obj.value = "";
            obj.focus();
            return;
        }
    }
}

//驗證郵箱
function valdEmail(obj) {
    var email = obj.value;
    var temp = email;
    if (email == "") return;
    var regex = /\w+([-/+.]\w+)*@(mail.foxconn.com|foxconn.com|fii-foxconn.com)$/;
    var patt1 = new RegExp(regex);
    var result = patt1.test(email);
    if (!result) {
        alert("郵箱的格式不正確!");
        obj.value = "";
        //obj.focus();
        obj.value = temp;
        return;
    }
    ;
}

//驗證數字
function valdMoney(obj) {
    var money = obj.value;
    var temp = money;
    if (money == "") return;
    var regex = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
    var patt1 = new RegExp(regex);
    var result = patt1.test(money);
    if (!result) {
        alert("格式不正確!");
        obj.value = "";
        //obj.focus();
        obj.value = temp;
        return;
    }
    ;
}
//驗證radio
function valdIshavepermitRadio(flag) {
    var chk_value = [];
    $('input[name="ishavepermitName"]:checked').each(function () {
        chk_value.push($(this).val());
    })
    if (chk_value.length == 0 && flag!='0') {
        alert("請選擇是否具有輻射安全許可證 ");
        return false;
    } else {
        var chk_values = '';
        $.each(chk_value, function (index, value) {
            chk_values += value + ',';
        })
        var chk_vals = chk_values.substring(0, chk_values.length - 1);
        $('#ishavepermit').val(chk_vals);
        return true;
    }
}

//驗證radio
function valdBuildpropertiesNameRadio(flag) {
    var chk_value = [];
    $('input[name="buildpropertiesName"]:checked').each(function () {
        chk_value.push($(this).val());
    })
    if (chk_value.length == 0 && flag!='0') {
        alert("請選擇建設性質");
        return false;
    } else {
        var chk_values = '';
        $.each(chk_value, function (index, value) {
            chk_values += value + ',';
        })
        var chk_vals = chk_values.substring(0, chk_values.length - 1);
        $('#buildproperties').val(chk_vals);
        return true;
    }
}
//驗證設備類別
function validEquipmentType(){
    var len=parseInt($("#rayDevicehbItemTableIndex").val());
    var j=0;
    for (var i = 1 ; i < len; i++) {
        if($("#equipmenttype"+i).combobox('getValue')==''){
            j++;
        }
    }
    if(j>0){
        alert("請選擇設備類別")
        return false;
    }
    return true;
}

//根據工號查出當前申請人的信息
function queryUserInfo() {
    var empno = $.trim($("#dealno").val().toUpperCase());
    if (empno != null && empno != "") {
        $.ajax({
            url: ctx+'/system/user/getUserInfo/',
            type: 'POST',
            beforeSend: ajaxLoading,
            //dataType: 'json',
            data: {empno: empno},
            success: function (data) {
                ajaxLoadEnd();
                if (!data) {
                    $.messager.alert("溫馨提示", "工號不存在", "error");
                    $('#dealno').val('');
                    $('#dealname').val('');
                    $('#dealdeptno').val('');
                    $('#dealdeptname').val('');
                    $('#dealemail').val('');
                } else {
                    $('#dealname').val(data.empname);
                    $('#dealdeptno').val(data.deptno);
                    $('#dealdeptname').val(data.deptname);
                    $('#dealfactoryid').combobox('setValue', data.factoryid);
                    $('#dealemail').val(data.email);
                }
            }
        });
    }
}

//附件上傳
function uploadFile() {
    var attachIds = $.trim($("#attachids").val());
    var attachIdsL = attachIds.split(",").length;
    if (attachIdsL >= 6) {
        alert("上傳附件個數不能超過五個！");
        return;
    }
    // 创建
    var form_data = new FormData();
    // 获取文件
//        var file = document.getElementById('fileToUpload').files[0];
    var file_data = $("#attachidsUpload").prop("files")[0];
    form_data.append("file", file_data);
    form_data.append("path", "test");
    // 把所以表单信息
    $.ajax({
        type: "POST",
        url: ctx+"/admin/upload",
        beforeSend: ajaxLoading,
//            dataType : "json",
        processData: false,  // 注意：让jQuery不要处理数据
        contentType: false,  // 注意：让jQuery不要设置contentType
        data: form_data
    }).success(function (msg) {
        ajaxLoadEnd();
        $("#dowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href='+ctx+'"/admin/download/' + msg.id + '">' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delAtt(\'' + msg.id + '\')"></div></div>');
        $("#attachids").val($("#attachids").val() + msg.id + ",");
        // $("#attachids").val(msg.id);
    }).fail(function (msg) {
    });
}

function delAtt(fileid) {
    $("#" + fileid).remove();
    var kk = $("#attachids").val();
    kk = kk.replace(fileid + ",", "");
    $("#attachids").val(kk);
    $.post(ctx+'/admin/delete/' + fileid, {}, function (data) {
        console.log(data);
        successTip(data);
    });
}

function reUploadFile() {
    var attachIds = $.trim($("#reattachids").val());
    var attachIdsL = attachIds.split(",").length;
    if (attachIdsL >= 6) {
        alert("上傳附件個數不能超過五個！");
        return;
    }
    // 创建
    var form_data = new FormData();
    // 获取文件
//        var file = document.getElementById('fileToUpload').files[0];
    var file_data = $("#attachidsUpload").prop("files")[0];
    form_data.append("file", file_data);
    form_data.append("path", "test");
    // 把所以表单信息
    $.ajax({
        type: "POST",
        url: ctx+"/admin/upload",
        beforeSend: ajaxLoading,
//            dataType : "json",
        processData: false,  // 注意：让jQuery不要处理数据
        contentType: false,  // 注意：让jQuery不要设置contentType
        data: form_data
    }).success(function (msg) {
        ajaxLoadEnd();
        $("#reDowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href='+ctx+'"/admin/download/' + msg.id + '">' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delReAtt(\'' + msg.id + '\')"></div></div>');
        $("#reattachids").val($("#reattachids").val() + msg.id + ",");
        // $("#attachids").val(msg.id);
    }).fail(function (msg) {
    });
}

function delReAtt(fileid) {
    $("#" + fileid).remove();
    var kk = $("#reattachids").val();
    kk = kk.replace(fileid + ",", "");
    $("#reattachids").val(kk);
    $.post(ctx+'/admin/delete/' + fileid, {}, function (data) {
        successTip(data);
    });
}
//导出excel
function exportExcel(){
    var form = document.getElementById("searchFrom");
    searchFrom.action=ctx+'/tqhwfraydevicehbprocess/exportExcel';
    form.submit();
}