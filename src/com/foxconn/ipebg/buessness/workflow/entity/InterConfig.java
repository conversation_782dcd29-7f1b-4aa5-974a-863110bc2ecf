package com.foxconn.ipebg.buessness.workflow.entity;

import java.io.Serializable;

public class InterConfig implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 接口服務ip
	 */
	private String serverUrl;
	/**
	 * 流程模板編碼
	 */
	private String workFlowId;
	/**
	 * 申請人賬號
	 */
	private String applyUserId;
	/**
	 * 任務處理人信息
	 * taskUsers=產工課部級審核:G4247468;
	 * 
	 */
	private String taskUsers;
	/**
	 * 會簽任務
	 * huiqian=assigneeList:H2201968,F1649647;
	 */
	private String huiqian;
	/**
	 * 約定的流程可選變量
	 * variables=變更名稱,變更值,變更類型
	 * 如：variables=assign_agree,true,B;
	 */
	private String variables;
	
	/**
	 * 處理人賬號
	 */
	private String userId;
	/**
	 * 工單任務id
	 */
	private String taskId;
	/**
	 * 節點名稱
	 */
	private String taskName;
	/**
	 * 流程實例id
	 */
	private String processId;
	/**
	 * 其他預留參數
	 */
	private String otherParam;
	
	/**
	 * 操作人工號
	 */
	private String empNo;
	/**
	 * 操作者ip
	 */
	private String requestIp;
	/**
	 * 會簽核時主管工號
	 */
	private String assignee;
	/**
	 * 會簽結果0駁回 1通過
	 */
	private String voteResult;
	/**
	 * 會簽時，節點會簽核主管串
	 */
	private String candidateUsers;
	
	private String variableName;
	private String variableType;
	public InterConfig(){
		
	}
	public InterConfig(String userId){
		this.userId=userId;
	}
	
	public String getServerUrl() {
		return serverUrl;
	}
	public void setServerUrl(String serverUrl) {
		this.serverUrl = serverUrl;
	}
	public String getWorkFlowId() {
		return workFlowId;
	}
	public void setWorkFlowId(String workFlowId) {
		this.workFlowId = workFlowId;
	}
	public String getApplyUserId() {
		return applyUserId;
	}
	public void setApplyUserId(String applyUserId) {
		this.applyUserId = applyUserId;
	}
	public String getTaskUsers() {
		return taskUsers;
	}
	public void setTaskUsers(String taskUsers) {
		this.taskUsers = taskUsers;
	}
	public String getHuiqian() {
		return huiqian;
	}
	public void setHuiqian(String huiqian) {
		this.huiqian = huiqian;
	}
	public String getVariables() {
		return variables;
	}
	public void setVariables(String variables) {
		this.variables = variables;
	}
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getTaskId() {
		return taskId;
	}
	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}
	public String getProcessId() {
		return processId;
	}
	public void setProcessId(String processId) {
		this.processId = processId;
	}
	public String getEmpNo() {
		return empNo;
	}
	public void setEmpNo(String empNo) {
		this.empNo = empNo;
	}
	public String getRequestIp() {
		return requestIp;
	}
	public void setRequestIp(String requestIp) {
		this.requestIp = requestIp;
	}
	public String getOtherParam() {
		return otherParam;
	}
	public void setOtherParam(String otherParam) {
		this.otherParam = otherParam;
	}
	public String getTaskName() {
		return taskName;
	}
	public void setTaskName(String taskName) {
		this.taskName = taskName;
	}
	public String getAssignee() {
		return assignee;
	}
	public void setAssignee(String assignee) {
		this.assignee = assignee;
	}
	public String getVoteResult() {
		return voteResult;
	}
	public void setVoteResult(String voteResult) {
		this.voteResult = voteResult;
	}
	public String getCandidateUsers() {
		return candidateUsers;
	}
	public void setCandidateUsers(String candidateUsers) {
		this.candidateUsers = candidateUsers;
	}
	public String getVariableName() {
		return variableName;
	}
	public void setVariableName(String variableName) {
		this.variableName = variableName;
	}
	public String getVariableType() {
		return variableType;
	}
	public void setVariableType(String variableType) {
		this.variableType = variableType;
	}
}
