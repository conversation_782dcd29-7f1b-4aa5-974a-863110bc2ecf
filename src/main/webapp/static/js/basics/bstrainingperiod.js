var dg;
var d;
$(function() {
	dg = $('#dg').datagrid({
		method : "get",
		url : ctx + '/bstrainingperiod/list',
		fit : true,
		queryParams:{
            order:'desc',
            sort:'createDate'
       },
		fitColumns : true,
		border : false,
		idField : 'id',
		striped : true,
		pagination : true,
		rownumbers : true,
		loadFilter : function(data) {
			// 过滤数据
			var value = {
				total : data.total,
				rows : []
			};
			var x = 0;
			for ( var i = 0; i < data.rows.length; i++) {
				
					data.rows[i].startTime = data.rows[i].startTime+"-"+data.rows[i].endTime+"    "+data.rows[i].startTime1+"-"+data.rows[i].endTime1;
			

				value.rows[x++] = data.rows[i];
			}
			return value;

		},
		pageNumber : 1,
		pageSize : 20,
		pageList : [ 10, 20, 30, 40, 50 ],
		singleSelect : true,
		columns : [ [ {
			field : 'id',
			title : 'id',
			hidden : true
		}, {
			field : 'trainingPeriodNo',
			title : '培訓期編碼',
			sortable : true,
			width : 100
		}, {
			field : 'trainingPeriodName',
			title : '培訓期名稱',
			sortable : true,
			width : 100
		}, {
			field : 'startDate',
			title : '培訓開始時間',
			sortable : true,
			width : 100,
			formatter : formatDate
		}, {
			field : 'endDate',
			title : '培訓結束時間',
			sortable : true,
			width : 100,
			formatter : formatDate
		}, {
			field : 'startTime',
			title : '培訓時段',
			sortable : true,
			width : 100
		}, {
			field : 'securityCom',
			title : '保安公司',
			sortable : true,
			width : 100
		}, {
			field : 'createBy',
			title : '創建人',
			sortable : true,
			width : 100
		}, {
			field : 'createDate',
			title : '創建時間',
			sortable : true,
			width : 100
		}, {
			field : 'updateBy',
			title : '更新人',
			sortable : true,
			width : 100
		}, {
			field : 'updateDate',
			title : '更新時間',
			sortable : true,
			width : 100
		}] ],
		enableHeaderClickMenu : true,
		enableHeaderContextMenu : true,
		enableRowContextMenu : false,
		rowTooltip : true,
		toolbar : '#tb'
	});
	 $.ajax({
	        url: ctx + "/bstrainingperiod/getAllTrainingPeriodList/"+"ALL",
	        //dataType:"json",
	        type: "GET",
	        success: function (data) {
	            //绑定第一个下拉框
	            $("#filter_EQS_trainingPeriodNo").combobox({
	                data: data,
	                valueField: "trainingPeriodNo",
	                textField: "trainingPeriodName",
	                editable: false,
	                panelHeight: 400,
	                loadFilter: function (data) {
	                    data.unshift({
	                        trainingPeriodNo: '',
	                        trainingPeriodName: '請選擇'
	                    });
	                    return data;
	                }
	            });
	        },
	        error: function (error) {
	            alert("初始化下拉控件失败");
	        }
	    });
});

// 弹窗增加
function add() {
	d = $("#dlg").dialog({
		title : '添加培訓期基本資料',
		width : 300,
		height : 280,
		href : ctx + '/bstrainingperiod/create',
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '确认',
			handler : function() {
				console.log('8888');
				var startDate= $('#startDate').datebox('getValue');
				var endDate= $('#endDate').datebox('getValue');
				var securityCom = $('#securityCom').combobox('getValue');
			
				if (startDate=="")
				{
					$.messager.alert("溫馨提示", "開始日期不能為空！", "info");
					return;
				}else if (endDate=="")
				{
					$.messager.alert("溫馨提示", "結束日期不能為空！", "info");
					return;
				}
				   var startTmp = new Date(startDate.replace(/-/g, "/"));
			       var endTmp = new Date(endDate.replace(/-/g, "/"));
			         
					if (startTmp>endTmp)
				{
					$.messager.alert("溫馨提示", "開始日期不能大於結束日期！", "info");
					return;
				}
				
					
					var startTime= $('#startTime').datebox('getValue');
					var endTime= $('#endTime').datebox('getValue');
					
					if (startTime=="")
					{
						$.messager.alert("溫馨提示", "上午開始時間不能為空！", "info");
						return;
					}else if (endTime=="")
					{
						$.messager.alert("溫馨提示", "上午結束時間不能為空！", "info");
						return;
					}
					   var startTmp1 = startTime.replace(":", "");
				       var endTmp1 = endTime.replace(":", "");
				         
						if (startTmp1>endTmp1)
					{
						$.messager.alert("溫馨提示", "上午開始時間不能大於上午結束時間！", "info");
						return;
					}
					var startTime1= $('#startTime1').datebox('getValue');
					var endTime1= $('#endTime1').datebox('getValue');
					
					if (startTime1=="")
					{
						$.messager.alert("溫馨提示", "下午開始時間不能為空！", "info");
						return;
					}else if (endTime1=="")
					{
						$.messager.alert("溫馨提示", "下午結束時間不能為空！", "info");
						return;
					}
					  var startTmp2 = startTime1.replace(":", "");
				       var endTmp2 = endTime1.replace(":", "");
				         
						if (startTmp2>endTmp2)
					{
						$.messager.alert("溫馨提示", "下午開始時間不能大於下午結束時間！", "info");
						return;
					}
						
						if (startTmp2<endTmp1)
						{
							$.messager.alert("溫馨提示", "上午結束時間不能大於下午開始時間！", "info");
							return;
						}

						if (securityCom == "") {
							$.messager.alert("溫馨提示", "請選擇保安公司", "info");
							return;
						}
				$("#mainform").submit();
			}
		}, {
			text : '取消',
			handler : function() {
				d.panel('close');
			}
		} ]
	});
}

// 删除
function del() {
	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data) {
		if (data) {
			$.ajax({
				type : 'get',
				url : ctx + "/bstrainingperiod/delete/" + row.id,
				success : function(data) {
					successTip(data, dg);
				}
			});
		}
	});
}

// 弹窗修改
function upd() {
	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	d = $("#dlg").dialog({
		title : '修改培訓期基本資料',
		width : 380,
		height : 340,
		href : ctx + '/bstrainingperiod/update/' + row.id,
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '修改',
			handler : function() {
				console.log('666');
				
				var startDate= $('#startDate').datebox('getValue');
				var endDate= $('#endDate').datebox('getValue');
				var endDateOld= $('#endDateOld').val(); 
				var securityCom = $('#securityCom').combobox('getValue');
			
				if (startDate=="")
				{
					$.messager.alert("溫馨提示", "開始日期不能為空！", "info");
					return;
				}else if (endDate=="")
				{
					$.messager.alert("溫馨提示", "結束日期不能為空！", "info");
					return;
				}
				   var startTmp = new Date(startDate.replace(/-/g, "/"));
			       var endTmp = new Date(endDate.replace(/-/g, "/"));
			       var endTmpOld = new Date(endDateOld.replace(/-/g, "/"));
			         
					if (startTmp>endTmp)
				{
					$.messager.alert("溫馨提示", "開始日期不能大於結束日期！", "info");
					return;
				}
					if (endTmp<endTmpOld)
					{
						$.messager.alert("溫馨提示", "結束日期只能往後延遲！", "info");
						return;
					}
				   
			   
				
					
					var startTime= $('#startTime').datebox('getValue');
					var endTime= $('#endTime').datebox('getValue');
					
					if (startTime=="")
					{
						$.messager.alert("溫馨提示", "上午開始時間不能為空！", "info");
						return;
					}else if (endTime=="")
					{
						$.messager.alert("溫馨提示", "上午結束時間不能為空！", "info");
						return;
					}
					   var startTmp1 = startTime.replace(":", "");
				       var endTmp1 = endTime.replace(":", "");
				         
						if (startTmp1>endTmp1)
					{
						$.messager.alert("溫馨提示", "上午開始時間不能大於上午結束時間！", "info");
						return;
					}
					var startTime1= $('#startTime1').datebox('getValue');
					var endTime1= $('#endTime1').datebox('getValue');
					
					if (startTime1=="")
					{
						$.messager.alert("溫馨提示", "下午開始時間不能為空！", "info");
						return;
					}else if (endTime1=="")
					{
						$.messager.alert("溫馨提示", "下午結束時間不能為空！", "info");
						return;
					}
					  var startTmp2 = startTime1.replace(":", "");
				       var endTmp2 = endTime1.replace(":", "");
				         
						if (startTmp2>endTmp2)
					{
						$.messager.alert("溫馨提示", "下午開始時間不能大於下午結束時間！", "info");
						return;
					}
						
						if (startTmp2<endTmp1)
						{
							$.messager.alert("溫馨提示", "上午結束時間不能大於下午開始時間！", "info");
							return;
						}
						if (securityCom == "") {
							$.messager.alert("溫馨提示", "請選擇保安公司", "info");
							return;
						}
				$('#mainform').submit();
			}
		}, {
			text : '取消',
			handler : function() {
				d.panel('close');
			}
		} ]
	});
}

// 创建查询对象并查询
function cx() {
	var obj = $("#searchFrom").serializeObject();
	dg.datagrid('load', obj);
}
// 表單進度查詢頁面查詢條件重置
function listSearchReset() {
	$("#searchFrom").form("reset");
}
// 导出excel
function exportExcel() {
	var form = document.getElementById("searchFrom");
	searchFrom.action = ctx + '/bstrainingperiod/exportExcel';
	form.submit();
}

/// 设置培训期开始日期后，根据培训时长自动设置培训结束日期
function onPeriodStartDateChanged() {
	var startDate = $('#startDate').datebox('getValue');
	console.log("onPeriodStartDateChanged");
	if (startDate!=="") {
		$.ajax({
			type : 'get',
			url : ctx + "/bstrainingperiod/defaultPeriodEndDate/" + startDate,
			success : function(data) {
				$('#endDate').my97('setValue', data);
			}
		});
	}
}
