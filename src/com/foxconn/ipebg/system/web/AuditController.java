package com.foxconn.ipebg.system.web;

import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Global;
import com.foxconn.ipebg.system.entity.TPubMailrecordEntity;
import com.foxconn.ipebg.system.service.TPubMailrecordService;
import com.foxconn.ipebg.system.utils.IPUtil;
import com.foxconn.ipebg.system.utils.UsernamePasswordCaptchaToken;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.authc.FormAuthenticationFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import sun.misc.IOUtils;

import javax.servlet.http.HttpServletRequest;

/**
 * 登录controller
 * <AUTHOR>
 * @date 2015年1月14日
 */
@Controller
@RequestMapping(value = "audit")
public class AuditController {
	@Autowired
	private WorkFlowService workFlowService;
	@Autowired
	private TPubMailrecordService mailrecordService;
	
	/**
	 * 默认页面
	 * @return
	 */
	@RequestMapping(value="login",method = RequestMethod.GET)
	public String login(String username,String loginType,String utoken,String uphoneNum,String captcha,String url,Model model,HttpServletRequest request) {
		Subject subject = SecurityUtils.getSubject();
		String password = Constant.DEF_PASSWORD;
		String host = Global.getAdminPath();
		boolean rememberMe = false;
		captcha=(String) SecurityUtils.getSubject().getSession().getAttribute(com.google.code.kaptcha.Constants.KAPTCHA_SESSION_KEY);
		AuthenticationToken authenticationToken = new UsernamePasswordCaptchaToken(username,password.toCharArray(), rememberMe, host, captcha,loginType,utoken,uphoneNum, IPUtil.getIpAddress(request));

		if(!subject.isAuthenticated()){
			try{
				subject.login(authenticationToken);
			}catch(Exception e){
				return "system/login";
			}
		}
//		return "redirect:"+ url;
		if(subject.isAuthenticated()){
			TPubMailrecordEntity entity = mailrecordService.findByValidStr(utoken);
			if(username.equals(workFlowService.getAssigneeInfo(entity.getSerialno()))){
				return "redirect:"+host+"?url="+url;
			}else {
				return "redirect:" + host+"?flag=0";
			}
		}
		return "system/login";
	}

}
