package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.ColumnTransformer;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * 培訓人員基本資料表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-08-29 10:42:45
 */
@Entity
@Table(name = "BS_TRAINEE")
@DynamicUpdate
@DynamicInsert
public class BsTraineeEntity extends DataEntity<BsTraineeEntity> implements
		Serializable {
	private static final long serialVersionUID = 1L;

	// 工號
	private String empNo;
	// 姓名
	private String empName;
	// 身份證
	private String psnId;
	// 硬卡號
	private String cardId;
	// 保安公司
	private String company;
	// 性別（0.男1.女）
	private String status;

	/**
	 * 设置：工號
	 */
	public void setEmpNo(String empNo) {
		this.empNo = empNo;
	}

	/**
	 * 获取：工號
	 */

	@Column(name = "EMP_NO", nullable = false, length = 20)
	public String getEmpNo() {
		return empNo;
	}

	/**
	 * 设置：姓名
	 */
	public void setEmpName(String empName) {
		this.empName = empName;
	}

	/**
	 * 获取：姓名
	 */

	@Column(name = "EMP_NAME", nullable = false, length = 20)
	public String getEmpName() {
		return empName;
	}

	/**
	 * 设置：身份證
	 */
	public void setPsnId(String psnId) {
		this.psnId = psnId;
	}

	/**
	 * 获取：身份證
	 */

	@Column(name = "PSN_ID", nullable = false, length = 20)
	@ColumnTransformer(forColumn = "PSN_ID", read = "CONVERT_FROM(decrypt(decode(PSN_ID, 'base64'), 'sgas', 'aes'), 'UTF-8')", write = "encode(encrypt(cast(? as bytea), 'sgas', 'aes'), 'base64')")
	public String getPsnId() {
		return psnId;
	}

	/**
	 * 设置：硬卡號
	 */
	public void setCardId(String cardId) {
		this.cardId = cardId;
	}

	/**
	 * 获取：硬卡號
	 */

	@Column(name = "CARD_ID", nullable = false, length = 20)
	public String getCardId() {
		return cardId;
	}

	/**
	 * 设置：保安公司
	 */
	public void setCompany(String company) {
		this.company = company;
	}

	/**
	 * 获取：保安公司
	 */

	@Column(name = "COMPANY", nullable = false, length = 20)
	public String getCompany() {
		return company;
	}

	/**
	 * 设置：性別（0.男1.女）
	 */
	public void setStatus(String status) {
		this.status = status;
	}

	/**
	 * 获取：性別（0.男1.女）
	 */

	@Column(name = "STATUS", nullable = false, length = 20)
	public String getStatus() {
		return status;
	}

}
