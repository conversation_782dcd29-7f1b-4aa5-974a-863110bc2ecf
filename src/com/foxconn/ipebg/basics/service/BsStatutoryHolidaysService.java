package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.entity.BsStatutoryHolidaysEntity;
import com.foxconn.ipebg.basics.dao.BsStatutoryHolidaysDao;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.Dict;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WorkFlowEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.system.service.DictService;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 法定節假日表
 *
 * <AUTHOR>
 * @email <EMAIL> 
 * @date 2019-04-17 09:33:40
 */
@Service
@Transactional(readOnly=true)
public class  BsStatutoryHolidaysService extends BaseBusinessService<BsStatutoryHolidaysEntity, String>{
    @Autowired
    private BsStatutoryHolidaysDao bsStatutoryHolidaysDao;

    @Override
    public HibernateDao<BsStatutoryHolidaysEntity, String> getEntityDao() {
        return bsStatutoryHolidaysDao;
    }

    public BsStatutoryHolidaysEntity findBySerialno(String serialno) {
        return this.bsStatutoryHolidaysDao.findUniqueBy("serialno", serialno);
    }

    public Integer holidayCount(Date from, Date to) {
        List<PropertyFilter> filters = new ArrayList<>();
        filters.add(new PropertyFilter("GED_holidayDate", from));
        filters.add(new PropertyFilter("LED_holidayDate", to));
        List<BsStatutoryHolidaysEntity> entities = search(filters);
        return entities.size();
    }
}
