package com.foxconn.ipebg.system.service;

import com.foxconn.ipebg.common.service.BaseService;
import com.foxconn.ipebg.system.entity.Role;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.system.dao.RoleDao;

/**
 * 角色service
 * <AUTHOR>
 * @date 2015年1月13日
 */
@Service
@Transactional(readOnly = true)
public class RoleService extends BaseService<Role, Integer> {

	@Autowired
	private RoleDao roleDao;

	@Override
	public HibernateDao<Role, Integer> getEntityDao() {
		return roleDao;
	}
}
