package com.foxconn.ipebg.system.web;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;

import com.foxconn.ipebg.common.utils.ConvertUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.system.entity.TQhUserdutyEntity;
import com.foxconn.ipebg.system.service.TQhUserdutyService;
import com.foxconn.ipebg.common.persistence.Page;


/**
 * ${comments}
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-17 09:10:19
 */
@Controller
@RequestMapping("tqhuserduty")
public class TQhUserdutyController extends BaseController {

    @Autowired
    private TQhUserdutyService tQhUserdutyService;

    /**
      * 方法描述: 列表信息
      * @Author: S6114648
      * @CreateDate:   2018-10-17 09:10:19
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    @RequiresPermissions("system:tqhuserduty:list")
    public String list() {
        //查询列表数据
        return "system/tqhuserduty/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6114648
      * @CreateDate:   2018-10-17 09:10:19
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    @RequiresPermissions("system:tqhuserduty:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<TQhUserdutyEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        page = tQhUserdutyService.search(page, filters);
        //審核狀態替換為字典值
        ConvertUtils.convertPropertyToDictLabel(page,"dutyid","user_duty");
        ConvertUtils.convertFactoryIdToFactoryName(page,"factoryid");
        return getEasyUIData(page);
    }
    /**
      * 方法描述: 添加跳轉
      * @Author: S6114648
      * @CreateDate:   2018-10-17 09:10:19
      * @Return
      **/

    @RequiresPermissions("system:tqhuserduty:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("tQhUserduty", new TQhUserdutyEntity());
        model.addAttribute("action", "create");
        return "system/tqhuserduty/listForm";
    }
    /**
      * 方法描述: 保存
      * @Author: S6114648
      * @CreateDate:   2018-10-17 09:10:19
      * @Return
      **/

    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("system:tqhuserduty:add")
    @ResponseBody
    public String create(@Valid TQhUserdutyEntity tQhUserduty, Model model) {
        tQhUserduty.setNewRecord(true);
        tQhUserdutyService.save(tQhUserduty);
        return "success";
    }
    /**
      * 方法描述: 修改跳轉
      * @Author: S6114648
      * @CreateDate:   2018-10-17 09:10:19
      * @Return
      **/

    @RequiresPermissions("system:tqhuserduty:update")
    @RequestMapping(value = "update/{dutyid}", method = RequestMethod.GET)
    public String updateForm(@PathVariable("dutyid") String dutyid, Model model) {
        model.addAttribute("tQhUserduty", tQhUserdutyService.get(dutyid));
        model.addAttribute("action", "update");
        return "system/tqhuserduty/listForm";
    }
    /**
      * 方法描述: 修改
      * @Author: S6114648
      * @CreateDate:   2018-10-17 09:10:19
      * @Return
      **/

    @RequestMapping(value="update", method = RequestMethod.POST)
    @RequiresPermissions("system:tqhuserduty:update")
    @ResponseBody
    public String update(@Valid TQhUserdutyEntity tQhUserduty) {
        tQhUserdutyService.update(tQhUserduty);
        return "success";
    }

    /**
      * 方法描述: 根據主鍵刪除
      * @Author: S6114648
      * @CreateDate:   2018-10-17 09:10:19
      * @Return
      **/

    @RequestMapping("delete/{dutyid}")
    @RequiresPermissions("system:tqhuserduty:delete")
    @ResponseBody
    public String delete(@PathVariable("dutyid") String dutyid) {
        tQhUserdutyService.delete(dutyid);
        return "success";
    }

}
