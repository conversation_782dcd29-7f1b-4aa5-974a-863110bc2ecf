package com.foxconn.ipebg.buessness.workflow.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.buessness.workflow.entity.WfConfigparamEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConfigparamService;
import com.foxconn.ipebg.common.persistence.Page;


/**
 * 流程參數配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-06 11:04:36
 */
@Controller
@RequestMapping("wfconfigparam")
public class WfConfigparamController extends BaseController {

    @Autowired
    private WfConfigparamService wfConfigparamService;

    /**
      * 方法描述: 列表信息
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:36
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    @RequiresPermissions("workflow:wfconfigparam:list")
    public String list() {
        //查询列表数据
        return "buessness/workflow/wfconfigparam/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:36
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    @RequiresPermissions("workflow:wfconfigparam:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<WfConfigparamEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        page = wfConfigparamService.search(page, filters);
        return getEasyUIData(page);
    }
    /**
      * 方法描述: 添加跳轉
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:36
      * @Return
      **/

    @RequiresPermissions("workflow:wfconfigparam:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("wfConfigparam", new WfConfigparamEntity());
        model.addAttribute("action", "create");
        return "buessness/workflow/wfconfigparam/listForm";
    }
    /**
      * 方法描述: 保存
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:36
      * @Return
      **/

    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("workflow:wfconfigparam:add")
    @ResponseBody
    public String create(@Valid WfConfigparamEntity wfConfigparam, Model model) {
        wfConfigparam.setNewRecord(true);
        wfConfigparamService.save(wfConfigparam);
        return "success";
    }
    /**
      * 方法描述: 修改跳轉
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:36
      * @Return
      **/

    @RequiresPermissions("workflow:wfconfigparam:update")
    @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
    public String updateForm(@PathVariable("id") String id, Model model) {
        model.addAttribute("wfConfigparam", wfConfigparamService.get(id));
        model.addAttribute("action", "update");
        return "buessness/workflow/wfconfigparam/listForm";
    }
    /**
      * 方法描述: 修改
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:36
      * @Return
      **/

    @RequestMapping(value="update", method = RequestMethod.POST)
    @RequiresPermissions("workflow:wfconfigparam:update")
    @ResponseBody
    public String update(@Valid WfConfigparamEntity wfConfigparam) {
        wfConfigparamService.update(wfConfigparam);
        return "success";
    }

    /**
      * 方法描述: 根據主鍵刪除
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:36
      * @Return
      **/

    @RequestMapping("delete/{id}")
    @RequiresPermissions("workflow:wfconfigparam:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        wfConfigparamService.delete(id);
        return "success";
    }

}
