package com.foxconn.ipebg.report.controller;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;


import com.foxconn.ipebg.system.entity.User;
import com.foxconn.ipebg.system.utils.UserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.basics.entity.BsSecPerEntity;
import com.foxconn.ipebg.report.entity.AbsentReportEntity;

import com.foxconn.ipebg.report.service.AbsentReportService;


/**
 * 部門基本資料
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-12-21 14:54:46
 */
@Controller
@RequestMapping("absentreport")
public class AbsentReportController extends BaseController {

    @Autowired
    private AbsentReportService absentReportService;


    /**
      * 方法描述: 列表信息
      * @Author: S6113712
      * @CreateDate:   2018-12-21 14:54:46
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:bsdpt:list")
    public String list() {
        //查询列表数据
        return "report/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6113712
      * @CreateDate:   2018-12-21 14:54:46
      * @Return
      **/
    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:bsdpt:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<AbsentReportEntity> page = getPage(request);
        //name = name == null ? "" : URLDecoder.decode(name, "UTF-8");
        
        //List<String> abnormalState=new ArrayList<String>();
        //abnormalState.add("0");
        String abnormalState = "0";
        
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);

        User user = UserUtil.getCurrentUser();
        if (user != null) {
            // 按保安公司查询
            PropertyFilter filter = new PropertyFilter("INS_company", user.getSecurityComs());
            filters.add(filter);
        }
        PropertyFilter filter = new PropertyFilter("EQS_abnormalState",abnormalState);
		filters.add(filter);
        
        page = absentReportService.search(page, filters);
        return getEasyUIData(page);
    }
    /**
      * 导出excel
      * @Author: S6113712
      * @CreateDate:   2018-12-21 14:54:46
      * @Return
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {}

    /**
     * List<Object>轉換為List<Map<String, Object>>
     * @param strs strs
     * @param result result
     * @return result
     */
    public static List<Map<String, Object>> listToMapList(String[] strs, List<?> result) {
        List<Map<String, Object>> mapList = new ArrayList<Map<String, Object>>();
        for (int i = 0; i < result.size(); i++) {
            Object[] objs = (Object[]) result.get(i);
            Map<String, Object> map = new HashMap<String, Object>();
            for (int j = 0; j < strs.length; j++) {
                String[] strss = strs[j].split(";");
                if (strss.length == 1) {
                    map.put(strss[0], objs[j].toString());
                } else if (strss.length == 2) {
                    if (strss[1].toUpperCase().equals("D")) {
                        map.put(strss[0], getDateStr((Date) objs[j], null));
                    }
                }
            }
            mapList.add(map);
        }
        return mapList;
    }
    /**
     * @param date date
     * @param dateFormat dateFormat
     * @return dateFormat
     */
    public static String getDateStr(Date date, String dateFormat) {
        String df = dateFormat;
        String strNow;
        if (df == null || df.equals("")) {
            df = "yyyy/MM/dd HH:mm:ss";
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(df);
            strNow = sdf.format(date);
        } catch (Exception e) {
            strNow = "";
        }
        return strNow;
    }
}
