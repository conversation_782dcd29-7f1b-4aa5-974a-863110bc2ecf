package com.foxconn.ipebg.buessness.workflow.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 流程節點配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-06 11:04:38
 */
@Entity
@Table(name = "WF_NODEINFO")
@DynamicUpdate
@DynamicInsert
public class WfNodeinfoEntity extends DataEntity<WfNodeinfoEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    //節點編碼
    private String nodeid;
    //流程編碼
    private String workflowid;
    //節點名稱
    private String nodename;
    //排序號
    private Integer orderby;
    //簽核節點類型 0 普通簽核 1會簽
    private String signtype;
    //節點簽核人是否必填 Y 必填 N非必填
    private String required;
    //表單表保存簽核主管的字段名稱
    private String colname;
    //節點別名（用於簽核路徑的展示，方便修改節點名稱）
    private String nodealain;
    //該結點是否可以參與批量簽核
    private String canbatch;
    //擴展字段
    private String dynfield01;
    //擴展字段
    private String dynfield02;
    //擴展字段
    private String dynfield03;
    //擴展字段
    private String dynfield04;
    //擴展字段
    private String dynfield05;

    private String version;

    /**
     * 设置：節點編碼
     */
    public void setNodeid(String nodeid) {
        this.nodeid = nodeid;
    }

    /**
     * 获取：節點編碼
     */
    @Column(name = "NODEID", nullable = false, length = 20)
    public String getNodeid() {
        return nodeid;
    }

    /**
     * 设置：流程編碼
     */
    public void setWorkflowid(String workflowid) {
        this.workflowid = workflowid;
    }

    /**
     * 获取：流程編碼
     */
    @Column(name = "WORKFLOWID", nullable = false, length = 20)
    public String getWorkflowid() {
        return workflowid;
    }

    /**
     * 设置：節點名稱
     */
    public void setNodename(String nodename) {
        this.nodename = nodename;
    }

    /**
     * 获取：節點名稱
     */
    @Column(name = "NODENAME", nullable = false, length = 20)
    public String getNodename() {
        return nodename;
    }
    /**
     * 获取：節點別名（用於簽核路徑的展示，方便修改節點名稱）
     */
    @Column(name = "NODEALAIN", nullable = false, length = 20)
    public String getNodealain() {
        return nodealain;
    }

    public void setNodealain(String nodealain) {
        this.nodealain = nodealain;
    }

    /**
     * 设置：排序號
     */
    public void setOrderby(Integer orderby) {
        this.orderby = orderby;
    }

    /**
     * 获取：排序號
     */
    @Column(name = "ORDERBY", nullable = false, length = 20)
    public Integer getOrderby() {
        return orderby;
    }

    /**
     * 设置：簽核節點類型 0 普通簽核 1會簽
     */
    public void setSigntype(String signtype) {
        this.signtype = signtype;
    }

    /**
     * 获取：簽核節點類型 0 普通簽核 1會簽
     */
    @Column(name = "SIGNTYPE", nullable = false, length = 20)
    public String getSigntype() {
        return signtype;
    }

    /**
     * 设置：節點簽核人是否必填 Y 必填 N非必填
     */
    public void setRequired(String required) {
        this.required = required;
    }

    /**
     * 获取：節點簽核人是否必填 Y 必填 N非必填
     */
    @Column(name = "REQUIRED", nullable = false, length = 20)
    public String getRequired() {
        return required;
    }

    /**
     * 设置：表單表保存簽核主管的字段名稱
     */
    public void setColname(String colname) {
        this.colname = colname;
    }

    /**
     * 获取：表單表保存簽核主管的字段名稱
     */
    @Column(name = "COLNAME", nullable = false, length = 20)
    public String getColname() {
        return colname;
    }

    /**
     * 设置：該結點是否可以參與批量簽核
     */
    public void setCanbatch(String canbatch) {
        this.canbatch = canbatch;
    }

    /**
     * 获取：該結點是否可以參與批量簽核
     */
    @Column(name = "CANBATCH", nullable = false, length = 20)
    public String getCanbatch() {
        return canbatch;
    }

    /**
     * 设置：擴展字段
     */
    public void setDynfield01(String dynfield01) {
        this.dynfield01 = dynfield01;
    }

    /**
     * 获取：擴展字段
     */
    @Column(name = "DYNFIELD01", nullable = false, length = 20)
    public String getDynfield01() {
        return dynfield01;
    }

    /**
     * 设置：擴展字段
     */
    public void setDynfield02(String dynfield02) {
        this.dynfield02 = dynfield02;
    }

    /**
     * 获取：擴展字段
     */
    @Column(name = "DYNFIELD02", nullable = false, length = 20)
    public String getDynfield02() {
        return dynfield02;
    }

    /**
     * 设置：擴展字段
     */
    public void setDynfield03(String dynfield03) {
        this.dynfield03 = dynfield03;
    }

    /**
     * 获取：擴展字段
     */
    @Column(name = "DYNFIELD03", nullable = false, length = 20)
    public String getDynfield03() {
        return dynfield03;
    }

    /**
     * 设置：擴展字段
     */
    public void setDynfield04(String dynfield04) {
        this.dynfield04 = dynfield04;
    }

    /**
     * 获取：擴展字段
     */
    @Column(name = "DYNFIELD04", nullable = false, length = 20)
    public String getDynfield04() {
        return dynfield04;
    }

    /**
     * 设置：擴展字段
     */
    public void setDynfield05(String dynfield05) {
        this.dynfield05 = dynfield05;
    }

    /**
     * 获取：擴展字段
     */
    @Column(name = "DYNFIELD05", nullable = false, length = 20)
    public String getDynfield05() {
        return dynfield05;
    }

    @Column(name = "VERSION", nullable = false, length = 20)
    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

}
