<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>輻射安全許可證變更申請單</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src='${ctx}/static/js/other/safetyPermit.js?random=<%= Math.random()%>'></script>
</head>
<body>
<form id="mainform" action="${ctx}/safetyPermit/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${safetyPermitEntity.id }"/>
    <input id="serialno" name="safetyPermitEntity.serialno" type="hidden" value="${safetyPermitEntity.serialno }"/>
    <input id="createtime" name="safetyPermitEntity.createtime" type="hidden"
           value="${safetyPermitEntity.createtime }"/>
    <div class="commonW">
        <div class="headTitle">輻射安全許可證變更申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                <c:choose>
                    <c:when test="${safetyPermitEntity.serialno==null}">
                        提交成功后自動編碼
                    </c:when>
                    <c:otherwise>
                        ${safetyPermitEntity.serialno}
                    </c:otherwise>
                </c:choose>
            </span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
                <c:choose>
                    <c:when test="${safetyPermitEntity.createtime==null}">
                        YYYY/MM/DD
                    </c:when>
                    <c:otherwise>
                        <input class="inputCss" style="width: 120px" readonly="true"
                               value="<fmt:formatDate value='${safetyPermitEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>">
                    </c:otherwise>
                </c:choose>
            </span>
        </div>
        <div class="position_R margin_R"> 填單人：${user.loginName}/${user.name}</div>
        <br>
        <div class="clear"></div>
        <table class="formList" id="buildprojecttable">
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">承辦人詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td width="6%">承辦人工號&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="dealno" name="safetyPermitEntity.dealno" class="easyui-validatebox"
                                       data-options="width: 80,required:true"
                                       value="${safetyPermitEntity.dealno }" onblur="queryUserInfo(this);"/>
                            </td>
                            <td width="6%">承辦人</td>
                            <td width="6%" class="td_style1">
                                <input id="dealname" name="safetyPermitEntity.dealname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${safetyPermitEntity.dealname }"/>
                            </td>
                            <td width="6%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="dealdeptno" name="safetyPermitEntity.dealdeptno"
                                       class="easyui-validatebox inputCss"
                                       data-options="width: 90"
                                       readonly value="${safetyPermitEntity.dealdeptno }"/>
                            </td>
                            <td width="6%">提報日期&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="dealtime" name="safetyPermitEntity.dealtime" class="easyui-my97"
                                       datefmt="yyyy-MM-dd"
                                       data-options="width: 100,required:true" minDate="%y-%M-%d"
                                       value="<fmt:formatDate value="${safetyPermitEntity.dealtime}"/>"/>
                            </td>
                            <td width="6%">廠區&nbsp;<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="dealfactoryid" name="safetyPermitEntity.dealfactoryid"
                                       class="easyui-combobox"
                                       panelHeight="auto" value="${safetyPermitEntity.dealfactoryid }"
                                       data-options="width: 80,required:true"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>單位</td>
                            <td colspan="3" class="td_style1">
                                <input id="dealdeptname" name="safetyPermitEntity.dealdeptname"
                                       class="easyui-validatebox"
                                       data-options="width: 400"
                                       value="${safetyPermitEntity.dealdeptname }"/>
                            </td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="dealemail" name="safetyPermitEntity.dealemail" class="easyui-validatebox"
                                       value="${safetyPermitEntity.dealemail }" style="width:300px;"
                                       data-options="required:true" onblur="valdEmail(this)"/>
                            </td>
                            <td>聯繫分機&nbsp;<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="dealtel" name="safetyPermitEntity.dealtel" class="easyui-validatebox"
                                       style="width:90px;"
                                       value="${safetyPermitEntity.dealtel }"
                                       data-options="required:true,prompt:'579+66666'"
                                       onblur="valdApplyTel(this)"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請單內容</td>
                        </tr>

                        <tr>
                            <td colspan="2"></td>
                            <td colspan="4" style="text-align: center">原核准事項</td>
                            <td colspan="4" style="text-align: center">變更后的事項</td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">單位名稱&nbsp;<font color="red">*</font></td>
                            <td colspan="4" style="text-align: center">
                                <input id="applydeptname" name="safetyPermitEntity.applydeptname"
                                       class="easyui-validatebox"
                                       style="width:80%;"
                                       data-options="required:true" value="${safetyPermitEntity.applydeptname }"/>
                            </td>
                            <td colspan="4" style="text-align: center">
                                <input id="newapplydeptname" name="safetyPermitEntity.newapplydeptname"
                                       class="easyui-validatebox"
                                       style="width:80%;"
                                       data-options="required:true" value="${safetyPermitEntity.newapplydeptname }"/>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">地址&nbsp;<font color="red">*</font></td>
                            <td colspan="4" style="text-align: center">
                                <input id="address" name="safetyPermitEntity.address" class="easyui-validatebox"
                                       style="width:80%;"
                                       data-options="required:true" value="${safetyPermitEntity.address }"/>
                            </td>
                            <td colspan="4" style="text-align: center">
                                <input id="newaddress" name="safetyPermitEntity.newaddress" class="easyui-validatebox"
                                       style="width:80%;"
                                       data-options="required:true" value="${safetyPermitEntity.newaddress }"/>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">法定代表人&nbsp;<font color="red">*</font></td>
                            <td colspan="4" style="text-align: center">
                                <input id="legalperson" name="safetyPermitEntity.legalperson" class="easyui-validatebox"
                                       style="width:80%;"
                                       data-options="required:true" value="${safetyPermitEntity.legalperson }"/>
                            </td>
                            <td colspan="4" style="text-align: center">
                                <input id="newlegalperson" name="safetyPermitEntity.newlegalperson"
                                       class="easyui-validatebox"
                                       style="width:80%;"
                                       data-options="required:true" value="${safetyPermitEntity.newlegalperson }"/>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">法定代表人身份證號碼&nbsp;<font color="red">*</font></td>
                            <td colspan="4" style="text-align: center">
                                <input id="legalpersonidno" name="safetyPermitEntity.legalpersonidno"
                                       class="easyui-validatebox"
                                       style="width:80%;"
                                       data-options="required:true" value="${safetyPermitEntity.legalpersonidno }"/>
                            </td>
                            <td colspan="4" style="text-align: center">
                                <input id="newlegalpersonidno" name="safetyPermitEntity.newlegalpersonidno"
                                       class="easyui-validatebox"
                                       style="width:80%;"
                                       data-options="required:true" value="${safetyPermitEntity.newlegalpersonidno }"/>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">許可證編號&nbsp;<font color="red">*</font></td>
                            <td colspan="3" style="text-align: center">
                                <input id="licenseno" name="safetyPermitEntity.licenseno" class="easyui-validatebox"
                                       style="width:80%;"
                                       data-options="required:true" value="${safetyPermitEntity.licenseno }"/>
                            </td>
                            <td colspan="2" style="text-align: center">種類和範圍&nbsp;<font color="red">*</font></td>
                            <td colspan="3" style="text-align: center">
                                <input id="typescope" name="safetyPermitEntity.typescope" class="easyui-validatebox"
                                       style="width:80%;"
                                       data-options="required:true" value="${safetyPermitEntity.typescope }"/>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">發證日期&nbsp;<font color="red">*</font></td>
                            <td colspan="3" style="text-align: left">
                                <input id="licensedate" name="safetyPermitEntity.licensedate" class="easyui-my97"
                                       datefmt="yyyy-MM-dd" data-options="width: 100,required:true"
                                       value="<fmt:formatDate value="${safetyPermitEntity.licensedate}"/>"/>
                            </td>
                            <td colspan="2" style="text-align: center">有效期&nbsp;<font color="red">*</font></td>
                            <td colspan="3" style="text-align: center">
                                <input id="effectivedate" name="safetyPermitEntity.effectivedate"
                                       class="easyui-validatebox"
                                       style="width:80%;"
                                       data-options="required:true" value="${safetyPermitEntity.effectivedate }"/>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: center">聯繫人&nbsp;<font color="red">*</font></td>
                            <td colspan="3" style="text-align: center">
                                <input id="contactperson" name="safetyPermitEntity.contactperson"
                                       class="easyui-validatebox"
                                       style="width:80%;"
                                       data-options="required:true" value="${safetyPermitEntity.contactperson }"/>
                            </td>
                            <td colspan="2" style="text-align: center">聯繫電話&nbsp;<font color="red">*</font></td>
                            <td colspan="3" style="text-align: center">
                                <input id="contactphone" name="safetyPermitEntity.contactphone"
                                       class="easyui-validatebox"
                                       style="width:80%;"
                                       data-options="required:true" value="${safetyPermitEntity.contactphone }"/>
                            </td>

                        </tr>

                        <tr align="center">
                            <td colspan="2">附件</td>
                            <td colspan="8" class="td_style1">
						<span class="sl-custom-file">
						<input type="button" value="点击上传文件" class="btn-file"/>
						<input id="attachidsUpload" name="attachidsUpload"
                               type="file" onchange="uploadFile111();" class="ui-input-file">
						</span>
                                <input type="hidden" id="attachids" name="safetyPermitEntity.attachids"
                                       value="${safetyPermitEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L"><a
                                                    href="${ctx}/admin/download/${item.id}">${item.name}</a></div>
                                            <div class="float_L deleteBtn" onclick="delAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">說明</td>
                            <td colspan="9" class="td_style1">
                                1.變更后的企業法人營業執照或事業單位法人證書正、副本複印件；
                                2.變更后的法定代表人身份證複印件；
                                3.其它。
                            </td>
                        </tr>

                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${workFlowId}','輻射安全許可證變更申請單流程圖','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList" style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">課級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'kchargeno','kchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="kchargeno" name="safetyPermitEntity.kchargeno"
                                                               readonly="true"
                                                               class="easyui-validatebox" data-options="width: 80"
                                                               value="${safetyPermitEntity.kchargeno }"/>
                                                        /<input id="kchargename" name="safetyPermitEntity.kchargename"
                                                                readonly="true"
                                                                class="easyui-validatebox" data-options="width: 80"
                                                                value="${safetyPermitEntity.kchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">部級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'bchargeno','bchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="bchargeno" name="safetyPermitEntity.bchargeno"
                                                               readonly="true"
                                                               class="easyui-validatebox" data-options="width: 80"
                                                               value="${safetyPermitEntity.bchargeno }"/>
                                                        /<input id="bchargename" name="safetyPermitEntity.bchargename"
                                                                readonly="true"
                                                                class="easyui-validatebox" data-options="width: 80"
                                                                value="${safetyPermitEntity.bchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">廠級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole($('#dealdeptno').val(),'cchargeno','cchargename',$('#dealfactoryid').combobox('getValue'))"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="cchargeno" name="safetyPermitEntity.cchargeno"
                                                               readonly="true"
                                                               class="easyui-validatebox" data-options="width: 80"
                                                               value="${safetyPermitEntity.cchargeno }"/>
                                                        /<input id="cchargename" name="safetyPermitEntity.cchargename"
                                                                readonly="true"
                                                                class="easyui-validatebox" data-options="width: 80"
                                                                value="${safetyPermitEntity.cchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zchargeTable',$('#dealdeptno').val(),'zchargeno','zchargename',$('#dealfactoryid').combobox('getValue'),'safetyPermitEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zchargeno" name="safetyPermitEntity.zchargeno"
                                                               readonly="true"
                                                               class="easyui-validatebox" data-options="width: 80"
                                                               value="${safetyPermitEntity.zchargeno }"/>
                                                        /<input id="zchargename" name="safetyPermitEntity.zchargename"
                                                                readonly="true"
                                                                class="easyui-validatebox" data-options="width: 80"
                                                                value="${safetyPermitEntity.zchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;" id="zcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">製造總處級主管</td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole3('zcchargeTable',$('#dealdeptno').val(),'zcchargeno','zcchargename',$('#dealfactoryid').combobox('getValue'),'safetyPermitEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="zcchargeno" name="safetyPermitEntity.zcchargeno"
                                                               readonly="true"
                                                               class="easyui-validatebox" data-options="width: 80"
                                                               value="${safetyPermitEntity.zcchargeno }"/>
                                                        /<input id="zcchargename" name="safetyPermitEntity.zcchargename"
                                                                readonly="true"
                                                                class="easyui-validatebox" data-options="width: 80"
                                                                value="${safetyPermitEntity.zcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border:none">
                                            <table width="18%" style="float: left;margin-left: 5px;" id="hbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環保科技處對應窗口
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(3,'hbchargeTable','hbchargeno','hbchargename',$('#dealfactoryid').combobox('getValue'),'safetyPermitEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="hbchargeno" name="safetyPermitEntity.hbchargeno"
                                                               readonly="true"
                                                               class="easyui-validatebox" data-options="required:true"
                                                               style="width: 35%"
                                                               value="${safetyPermitEntity.hbchargeno }"/>
                                                        <font color="red">*</font>/<input id="hbchargename"
                                                                                          name="safetyPermitEntity.hbchargename"
                                                                                          class="easyui-validatebox"
                                                                                          style="width: 35%"
                                                                                          readonly="true"
                                                                                          data-options="required:true"
                                                                                          value="${safetyPermitEntity.hbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="hbkchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環保科技處課級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(4,'hbkchargeTable','hbkchargeno','hbkchargename',$('#dealfactoryid').combobox('getValue'),'safetyPermitEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="hbkchargeno" name="safetyPermitEntity.hbkchargeno"
                                                               readonly="true"
                                                               class="easyui-validatebox" data-options="required:true"
                                                               style="width: 35%"
                                                               value="${safetyPermitEntity.hbkchargeno }"/>
                                                        <font color="red">*</font>/<input id="hbkchargename"
                                                                                          readonly="true"
                                                                                          name="safetyPermitEntity.hbkchargename"
                                                                                          class="easyui-validatebox"
                                                                                          data-options="required:true"
                                                                                          style="width: 35%"
                                                                                          value="${safetyPermitEntity.hbkchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="hbbchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環保科技處部級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(5,'hbbchargeTable','hbbchargeno','hbbchargename',$('#dealfactoryid').combobox('getValue'),'safetyPermitEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="hbbchargeno" name="safetyPermitEntity.hbbchargeno"
                                                               readonly="true"
                                                               class="easyui-validatebox" data-options="required:true"
                                                               style="width: 35%"
                                                               value="${safetyPermitEntity.hbbchargeno }"/>
                                                        <font color="red">*</font>/<input id="hbbchargename"
                                                                                          readonly="true"
                                                                                          name="safetyPermitEntity.hbbchargename"
                                                                                          class="easyui-validatebox"
                                                                                          data-options="required:true"
                                                                                          style="width: 35%"
                                                                                          value="${safetyPermitEntity.hbbchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="18%" style="float: left;margin-left: 5px;"
                                                   id="hbcchargeTable">
                                                <tr>
                                                    <td>
                                                        <table width="100%">
                                                            <tr>
                                                                <td style="border: none;text-align: right;">環保科技處處級主管
                                                                </td>
                                                                <td style="border: none;">
                                                                    <div class="float_L qhUserIcon"
                                                                         onclick="selectRole2(6,'hbcchargeTable','hbcchargeno','hbcchargename',$('#dealfactoryid').combobox('getValue'),'safetyPermitEntity')"></div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input id="hbcchargeno" name="safetyPermitEntity.hbcchargeno"
                                                               readonly="true"
                                                               class="easyui-validatebox" data-options="required:true"
                                                               style="width: 35%"
                                                               value="${safetyPermitEntity.hbcchargeno }"/>
                                                        <font color="red">*</font>/<input id="hbcchargename"
                                                                                          name="safetyPermitEntity.hbcchargename"
                                                                                          class="easyui-validatebox"
                                                                                          readonly="true"
                                                                                          data-options="required:true"
                                                                                          style="width: 35%"
                                                                                          value="${safetyPermitEntity.hbcchargename }"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>

                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-add'"
                                   style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                                   style="width: 100px;" onclick="saveInfo(2);">提交</a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
</form>
<div id="dlg"></div>
</body>
</html>