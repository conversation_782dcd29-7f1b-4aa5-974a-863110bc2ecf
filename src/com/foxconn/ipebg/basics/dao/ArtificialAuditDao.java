package com.foxconn.ipebg.basics.dao;

import com.foxconn.ipebg.basics.entity.ArtificialAuditEntity;
import org.hibernate.jdbc.Work;
import org.springframework.stereotype.Repository;
import com.foxconn.ipebg.common.persistence.HibernateDao;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Types;
import java.util.Date;

/**
 * 人工稽核錄入基本資料
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-18 09:09:19
 */
@Repository
public class ArtificialAuditDao extends HibernateDao<ArtificialAuditEntity,String> {
    /**
     * 調用存儲過程比對出勤狀況
     * @param date 比對日期
     */
    public void checkAbnormalPerson(final Date date) throws SQLException {
        getSession().doWork(new Work() {
            @Override
            public void execute(Connection connection) throws SQLException {
                CallableStatement callable = connection.prepareCall("{CALL pk_workstatus_rpt_bs_abnormal_personnel_check_sp(?, ?)}");
                callable.setDate(1, new java.sql.Date(date.getTime()));
                callable.registerOutParameter(2, Types.OTHER);
                callable.execute();
                callable.close();
            }
        });
    }
}
