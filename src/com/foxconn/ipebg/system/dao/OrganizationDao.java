package com.foxconn.ipebg.system.dao;

import com.foxconn.ipebg.common.persistence.HibernateDao;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.springframework.stereotype.Repository;

import com.foxconn.ipebg.system.entity.Organization;

import java.util.List;


/**
 * 机构DAO
 * <AUTHOR>
 * @date 2015年5月09日
 */
@Repository
public class OrganizationDao extends HibernateDao<Organization, Integer> {
    /**
     * 方法描述: 根據機構獲取所有下級機構數據
     * @Author: S6114648
     * @CreateDate:   2018/11/3  上午 09:52
     * @Return
     **/
    public List<Organization> findFatherOrganiza(Integer organizId){
        String hql = "select * from sys_organization t start with t.id =:pid connect by prior t.id=t.pid";
        Session session = getSession();
        SQLQuery query = (SQLQuery) session.createSQLQuery(hql).addEntity(Organization.class).setParameter("pid",organizId);
        return query.list();
    }
}
