var dg;
var d;
var f = false;
//var lujingUrl;
$(function() {
	dg = $('#dg').datagrid({
		method : "get",
		url : ctx + '/tqhwfreportofaccidents/list',
		fit : true,
		fitColumns : false,
		border : false,
		idField : 'id',
		striped : true,
		pagination : true,
		rownumbers : true,
		pageNumber : 1,
		pageSize : 20,
		pageList : [ 10, 20, 30, 40, 50 ],
		singleSelect : true,
		columns : [ [ {field : 'id',title : '主鍵',hidden : true}, 
		{field : 'serialno',title : '任務編碼',width : 150,formatter : operation}, 
		{field : 'dealno',title : '受傷人',width : 100,formatter:function(value,row){
			return row.dealno+"/"+row.dealname;
		}},  
		{field : 'hurtsex',title : '受傷人性別',width : 80}, 
		{field : 'hurtage',title : '受傷人年齡',width : 80},
		{field : 'hurtjointime',title : '受傷人入集團日期',width : 100,formatter : formatDate}, 
		{field : 'hurtphone',title : '受傷人聯繫方式',width : 100},
		{field : 'hurtdeptno',title : '受傷人單位代碼',width : 100}, 
		{field : 'hurtdeptname',title : '受傷人單位名稱',width : 100}, 
		{field : 'hurttime',title : '受傷時間',width : 100,formatter : formatDatebox}, 
		{field : 'hurtreporttime',title : '工傷提報時間',width : 100,formatter : formatDatebox}, 
		{field : 'witnessnam',title : '證明人',width : 100}, 
		{field : 'witnessphone',title : '證明人聯繫方式',width : 100}, 
		{field : 'reportchargeno',title : '工傷提報單位負責人工號',width : 100}, 
		{field : 'reportchargenam',title : '工傷提報單位負責人姓名',width : 100}, 
		{field : 'reportchargephone',title : '工傷提報單位負責人電話',width : 100}, 
		{field : 'makerno',title : '填單人',width : 100,formatter:function(value,row){
			return row.makerno+"/"+row.makername;
		}},
		{field : 'createtime',title : '填單時間',width : 100,formatter : formatDatebox}, 
		{field : 'workstatus', title : '表單狀態', sortable : true, width : 100},
		{field : 'nodeName', title : '當前簽核節點', sortable : true, width : 100},
		{field : 'auditUser', title : '當前簽核人', sortable : true, width : 100} ,
	    {field: 'complettime', title: '簽核完成時間', sortable: true, width: 120,formatter:formatDatebox
	    }] ],
		enableHeaderClickMenu : true,
		enableHeaderContextMenu : true,
		enableRowContextMenu : false,
		toolbar : '#tb'
	});
	$(":input").removeClass("validatebox-invalid");
	//查詢條件狀態
	$.ajax({
		url : ctx + "/system/dict/getDictByType/audit_status",
		//dataType:"json",
		type : "GET",
		success : function(data) {
			//绑定第一个下拉框
			$("#qysjzt").combobox({
				data : data,
				valueField : "value",
				textField : "label",
				editable : false,
				panelHeight : "auto",
				loadFilter : function(data) {
					data.unshift({
						value : '',
						label : '請選擇'
					});
					return data;
				}
			});
		},
		error : function(error) {
			alert("初始化下拉控件失败");
		}
	});
	//獲取項目法人
	$.ajax({
		url : ctx + "/system/dict/getDictByType/project_entity",
		type : "GET",
		success : function(data) {
			$("#hurtcorporate").combobox({
				data : data,
				valueField : "value",
				textField : "label",
				editable : false,
				panelHeight : "auto",
				loadFilter : function(data) {
					data.unshift({
						value : '',
						label : '請選擇'
					});
					return data;
				}
			});
		},
		error : function(error) {
			alert("初始化下拉控件失败");
		}
	});
	
	//獲取廠區內外
	$.ajax({
				type : "GET",
				url : ctx + "/system/dict/getDictByType/hurtkind",
				success : function(data) {
					var html = '';
					debugger;
					if ($("#hurtinoutfac").val() == null || $("#hurtinoutfac").val() == '') {
						$.each(data,function(i, v) {
						html += "<input type='radio' onclick='selectIf()' value=" + v.value
							 + " name='hurtinoutfacName'/>" + v.label + "&nbsp;&nbsp;&nbsp;&nbsp;";
						});
						neiFac();
					} else {
						if ($('input[name=hurtinoutfacName]:checked').val() == "hurtkind_02"||$("#hurtinoutfac").val() == "hurtkind_02"){//廠區外
							waiFac();
						}else{
							neiFac();
						}
						var str = $("#hurtinoutfac").val().split(",");
						var disOrEnabled = $("#disOrEnabled").val();
						$.each(data,function(n, item) {
							var flag = false;
							for (var i = 0; i < str.length; i++) {
							 if (str[i] == item.value) {
							 html += "<input type='radio' onclick='selectIf()' checked='checked' "
								  + disOrEnabled + " value=" + item.value + " name='hurtinoutfacName'/>" + item.label
								  + "&nbsp;&nbsp;&nbsp;&nbsp;";
								  flag = true;
							}
						}
						if (!flag)
							html += "<input type='radio' " + disOrEnabled
								 + "  value=" + item.value
								 + " onclick='selectIf()' name='hurtinoutfacName'/>" + item.label
								 + "&nbsp;&nbsp;&nbsp;&nbsp;";
							});
					}
					$(".hurtDiv").html(html);
				}
			});
	
	//獲取廠區
	$.get(ctx + '/tqhfactoryidconfig/allFactorys/', function(result) {
		$("#hurtstayfac").combobox({
			data : result,
			valueField : "factoryid",
			textField : "factoryname",
			editable : false,
			panelHeight : "auto",
			loadFilter : function(data) {
				data.unshift({
					factoryid : '',
					factoryname : '請選擇'
				});
				return data;
			}
		});
	});
});
//任務編號查看頁面
function operation(value, row, index) {
	return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('詳情',ctx+'/tqhwfreportofaccidents/view/"
			+ row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
};
//驗證郵箱
function valdEmail(obj) {
	var email = obj.value;
	var temp = email;
	if (email == "")
		return;
	var regex = /\w+([-/+.]\w+)*@(mail.foxconn.com|foxconn.com|fii-foxconn.com)$/;
	var patt1 = new RegExp(regex);
	var result = patt1.test(email);
	if (!result) {
		alert("郵箱的格式不正確!");
		obj.value = "";
		obj.value = temp;
		return;
	}
	;
}

//驗證申請人聯繫電話,不可輸入漢字
function valdApplyTel(obj) {
debugger;
    var tel = obj.value;
    if (tel != "") {
        var regex = /[\u4E00-\u9FA5]/g;
        var patt1 = new RegExp(regex);
        var result = patt1.test(tel);
        if (result) {
            alert("該欄位不可輸入漢字!");
            obj.value = "";
            obj.focus();
            return;
        }
    }
}

//驗證申請人聯繫手機號
function valdMobilephone(obj) {
debugger;
    var tel = obj.value;
    if (tel != "") {
        //var regex = /^[1][2,3,4,5,6,7,8,9][0-9]{9}$/;//以1開頭，3 4 5 6 7 8 9 任意第二位  0-9的整數9個結尾 
    	var regex = /^[1][0-9]{10}$/;
    	var patt1 = new RegExp(regex);
        var result = patt1.test(tel);
        if (!result) {
            alert("請輸入11位手機號碼!");
            obj.value = "";
            obj.focus();
            return;
        }
    }
}

//受傷日期彈框
/*$(function(){
	debugger;
	$("#hurttime").datebox({
		onSelect:function(){
			debugger;
			var myDate = new Date();
			var year = myDate.getFullYear(); //获取完整的年份
			var month = myDate.getMonth() + 1; //获取当前月份(0-11,0代表1月)
			var day = myDate.getDate(); //获取当前日(1-31)
			var sysdate = year + "-" + month + "-" + day;
			//var date1 = new Date(sysdate.replace(/-/g, "\/"));
			var hurtTime = $("#hurttime").datebox('getValue');
			//var date2 = new Date(hurtTime.replace(/-/g, "\/"));
			if (sysdate>hurtTime){
				alert("此單已超過錄入時間，請確保工傷發生后15日內完成審核");
				}
		}
		
	});
});*/

//驗證當前時間大於受傷時間提示
//當受傷時間和當前時間差超72小時，系統會彈出（請跟進工傷呈報表在受傷日期后15個工作日內簽核完成！）   72h
//當受傷時間和工傷呈報提交時間超15天還繼續可以錄，系統會彈出對話框（工傷呈報時間已超出15天，請儘快跟進審核！） 360h
function compareTime() {
	debugger;
	var myDate = new Date();
	var hurtTime = $("input[name='hurttime']").val();
	
	var year = myDate.getFullYear(); //获取完整的年份
	var month = myDate.getMonth() + 1; //获取当前月份(0-11,0代表1月)
	var day = myDate.getDate(); //获取当前日(1-31)
	var hour = myDate.getHours();
	var minute = myDate.getMinutes();
	var second = myDate.getSeconds();
	
	var stryear =hurtTime.substring(0,4); 
	var strmonth = hurtTime.substring(5,7);
	var strday = hurtTime.substring(8,10);
	var strhour = hurtTime.substring(11,13);
	var strminute = hurtTime.substring(14,16);
	var strsecond = hurtTime.substring(17,19);
	/*alert(stryear);
	alert(strmonth);alert(strday);alert(strhour);alert(strminute);alert(strsecond);*/
	var nian = (year - stryear)*365*24*60*60;
	var yue = (month - strmonth)*30*24*60*60;
	var ri = (day - strday)*24*60*60;
	var shi = (hour - strhour)*60*60;
	var fen = (minute - strminute)*60;
	var miao = (second - strsecond);
	 
	var cha = (nian + yue + ri + shi + fen + miao)/60/60;
	if(cha >= 360){
		alert("工傷呈報時間已超出15天，請儘快跟進審核！！");
	}
	else if(cha >= 72){
	    alert("請跟進工傷呈報表在受傷日期后15個工作日內簽核完成！");
	}
}
//驗證radio 廠區內外是否為空
function valdInOrOut(flag) {
	var chk_value = [];
	$('input[name="hurtinoutfacName"]:checked').each(function() {
		chk_value.push($(this).val());
	});
	if (chk_value.length == 0 && flag != '0') {
		alert("請選擇事故類型 ");
		return false;
	} else {
		var chk_values = '';
		$.each(chk_value, function(index, value) {
			chk_values += value + ',';
		});
		var chk_vals = chk_values.substring(0, chk_values.length - 1);
		$('#hurtinoutfac').val(chk_vals);
		return true;
	}
}
//驗證radio 具體工傷類型是否為空
function valdHurtKind(flag) {
	var chk_value = [];
	$('input[name="hurtkindName"]:checked').each(function() {
		chk_value.push($(this).val());
	});
	if (chk_value.length == 0 && flag != '0') {
		alert("請選擇具體事故類型 ");
		return false;
	} else {
		var chk_values = '';
		$.each(chk_value, function(index, value) {
			chk_values += value + ',';
		});
		var chk_vals = chk_values.substring(0, chk_values.length - 1);
		$('#hurtkind').val(chk_vals);
		return true;
	}
}

//驗證受傷時間是否為空
function validHurtTime(flag) {
	if ($("#hurttime").val() =='' || $("#hurttime").val() == null){
	alert("請選擇受傷時間！");
	return false;
	}
    return true;
}

//驗證受傷廠區是否為空
function validHurtFac(flag) {
	if ($("#hurtstayfac").combobox('getValue') == '') {
		alert("請選擇受傷廠區");
		return false;
	}
    return true;
}



//獲取工傷種類外
function waiFac(){
$.ajax({
		type : "GET",
		asycn:false,
		url : ctx + "/system/dict/getDictByType/hurtkind_outfac",
		success : function(data) {
		debugger;
		var html = '';
		if ($("#hurtkind").val() == null || $("#hurtkind").val() == '') {
			$.each(data,function(i, v) {
					html += "<input type='radio' onclick='selectQt()' value="
						+ v.value
						+ " name='hurtkindName'/>"
						+ v.label
						+ "&nbsp;&nbsp;&nbsp;&nbsp;";
		    });
					html += "<input id='hurtothermeno' name='hurtothermeno' disabled='disabled' class='easyui-validatebox' data-options='width: 30'/>";
				} else {
					selectQt();
					var str = $("#hurtkind").val().split(",");
					var disOrEnabled = $("#disOrEnabled").val();
					$.each(data,function(n, item) {
						var flag = false;
						for (var i = 0; i < str.length; i++) {
							if (str[i] == item.value) {
							html += "<input type='radio' onclick='selectQt()' checked='checked' "
								 + disOrEnabled + " value=" + item.value
								 + " name='hurtkindName'/>"  + item.label + "&nbsp;&nbsp;&nbsp;&nbsp;";
							flag = true;
						}
					}
					if (!flag)
						html += "<input type='radio'  onclick='selectQt()' " 
							 + disOrEnabled + "  value=" + item.value
							 + " name='hurtkindName'/>" + item.label + "&nbsp;&nbsp;&nbsp;&nbsp;";
				});
					var hurtothermeno = $("#hurtothermeno1").val();
					if( hurtothermeno == null || hurtothermeno == ""){
					 html += "<input id='hurtothermeno' name='hurtothermeno' disabled='disabled' class='easyui-validatebox' " 
						  + " data-options='width: 30'/>"; 
					 }else{
					 html += "<input id='hurtothermeno' name='hurtothermeno'  class='easyui-validatebox' " 
						  + " value=" +hurtothermeno
						  + " data-options='width: 30'/>";
					 }
					 //alert($("#hurtothermeno1").val());
            }
		$(".hurtkindDiv").html(html);
	}
});
}
//獲取工傷種類內
function neiFac(){
$.ajax({
		type : "GET",
		asycn:false,
		url : ctx + "/system/dict/getDictByType/hurtkind_infac",
		success : function(data) {
		debugger;
		var html = '';
		if ($("#hurtkind").val() == null || $("#hurtkind").val() == '') {
			$.each(data,function(i, v) {
					html += "<input type='radio' onclick='selectQt()' value="
						+ v.value
						+ " name='hurtkindName'/>"
						+ v.label
						+ "&nbsp;&nbsp;&nbsp;&nbsp;";
		    });
					html += "<input id='hurtothermeno' name='hurtothermeno' disabled='disabled' class='easyui-validatebox' data-options='width: 30'/>";
				} else {
					selectQt();
					var str = $("#hurtkind").val().split(",");
					var disOrEnabled = $("#disOrEnabled").val();
					$.each(data,function(n, item) {
						var flag = false;
						for (var i = 0; i < str.length; i++) {
							if (str[i] == item.value) {
							html += "<input type='radio' onclick='selectQt()' checked='checked' "
								 + disOrEnabled
									 + " value="
									 + item.value
									 + " name='hurtkindName'/>"
									 + item.label
									 + "&nbsp;&nbsp;&nbsp;&nbsp;";
							flag = true;
						}
					}
					if (!flag)
						html += "<input type='radio'  onclick='selectQt()' " 
							 + disOrEnabled
							 + "  value="
							 + item.value
							 + " name='hurtkindName'/>"
							 + item.label
							 + "&nbsp;&nbsp;&nbsp;&nbsp;";
				});
					var hurtothermeno = $("#hurtothermeno1").val();
					if( hurtothermeno == null || hurtothermeno == ""){
					 html += "<input id='hurtothermeno' name='hurtothermeno' disabled='disabled' class='easyui-validatebox' " 
						  + " data-options='width: 30'/>"; 
					 }else{
					 html += "<input id='hurtothermeno' name='hurtothermeno' class='easyui-validatebox' " 
						  + " value=" +hurtothermeno
					      + " data-options='width: 30'/>";
					 }
					 //alert($("#hurtothermeno1").val());
			}
		$(".hurtkindDiv").html(html);
	}
});
}
//廠區內外聯動不同的工傷種類
function selectIf() {
	var url = null;
	if ($('input[name=hurtinoutfacName]:checked').val() == "hurtkind_01") {
		url = ctx + "/system/dict/getDictByType/hurtkind_infac";
	} else {
		url = ctx + "/system/dict/getDictByType/hurtkind_outfac";
	}
	$.ajax({
				type : "GET",
				url : url,
				asycn:true,
				success : function(data) {
					var html = '';
					if ($("#hurtkind").val() == null || $("#hurtkind").val() == '') {
						$.each(data,function(i, v) {
						html += "<input type='radio' onclick='selectQt()' value="+ v.value
							 + " name='hurtkindName'/>" + v.label + "&nbsp;&nbsp;&nbsp;&nbsp;";
					});
					} else {
						var str = $("#hurtkind").val().split(",");
						var disOrEnabled = $("#disOrEnabled").val();
						$.each(data,function(n, item) {
							var flag = false;
							for (var i = 0; i < str.length; i++) {
							  if (str[i] == item.value) {
								html += "<input type='radio' onclick='selectQt()' checked='checked' " + disOrEnabled + " value=" + item.value + " name='hurtkindName'/>"
									 + item.label + "&nbsp;&nbsp;&nbsp;&nbsp;";
								flag = true;
							}
						}
						if (!flag)
								html += "<input type='radio' onclick='selectQt()' " + disOrEnabled
									 + "  value=" + item.value + " name='hurtkindName'/>" + item.label + "&nbsp;&nbsp;&nbsp;&nbsp;";
							});
					}
					var hurtothermeno = $("#hurtothermeno1").val();
					debugger;
					if( hurtothermeno == null || hurtothermeno == "" ||hurtothermeno == " "){
					 html += "<input id='hurtothermeno' name='hurtothermeno' disabled='disabled' class='easyui-validatebox' " 
						  + " data-options='width: 30'/>"; 
					 }else{
					 html += "<input id='hurtothermeno' name='hurtothermeno' class='easyui-validatebox' " 
						  + " value=" +hurtothermeno
						  + " data-options='width: 30'/>";
					 }
					selectQt();
					$(".hurtkindDiv").html(html);
				}
			});
}

//其它框可編輯狀態 
function selectQt() {
	if ($("input[name=hurtkindName]:checked").val() == "hurtkind_outfac_02"||$("input[name=hurtkindName]:checked").val() == "hurtkind_infac_08") {
		$("#hurtothermeno").removeAttr("disabled");
		//$("#hurtothermeno").val($("#hurtothermeno1").val());
	} else {
		$("#hurtothermeno").attr("disabled", true);
	}
}

//統計事故說明數量
function wordStatic(flag){
	debugger;
	//獲取要顯示已輸入字數文本框對象
/*	var content = document.getElementById('num');
	if (content && input)
	{
		//獲取輸入框輸入內容廠區并更新到介面
		var value = input.value;
		//將換行符不計為單詞數
		value = value.replace(/\n|\r/gi,"");
		//更新計數
		content.innerText = value.length;
	}*/
	if ($("#accidentsmemo").val() =='' ||  null == $("#accidentsmemo").val() ){
		    alert("事故說明必填，請輸入事故說明！");
		    return false;
	}
	return true;
}

//統計事故說明數量
function attachValue(flag){
	debugger;
	if ($("#attachids").val() =='' ||  null == $("#attachids").val() ){
		    alert("請上傳附件！");
		    return false;
	}
	return true;
}
//獲取廠區名稱
function queryFacnamByFacid(facid) {
	$.get(ctx + '/tqhfactoryidconfig/getFactryNameById/' + facid, function(
			result) {
		$('#hurtfac').val(result.factoryname);
	});
}

//通過工號獲取姓名
function queryNameByNo(obj) {
	debugger;
	var empno = $.trim($("#reportchargeno").val());
	if (empno != null && empno != "") {
		var regex = /[\u4E00-\u9FA5]/g;
		var patt1 = new RegExp(regex);
		var result = patt1.test(empno);
		if (result) {
			alert("該欄位不可輸入漢字!");
			obj.value = "";
			obj.focus();
			return;
		} else {
			$.ajax({
				url : ctx + '/system/user/getUserInfo/',
				type : 'POST',
				beforeSend : ajaxLoading,
				// dataType: 'json',
				data : {
					empno : empno
				},
				success : function(data) {
					ajaxLoadEnd();
					if (!data||data.empname == null) {
						$.messager.alert("溫馨提示", "工號不存在", "error");
						$('#reportchargeno').val('');
						$('#reportchargenam').val('');
					} else {
						$('#reportchargenam').val(data.empname);
					}
				}
			});
		}
	}
}

// 根據工號查出當前申請人的信息
function queryUserInfo() {
	var empno = $.trim($("#dealno").val().toUpperCase());
	if (empno != null && empno != "") {
		$.ajax({
			url : ctx + '/system/user/getUserInfo/',
			type : 'POST',
			beforeSend : ajaxLoading,
			//dataType: 'json',
			data : {
				empno : empno
			},
			success : function(data) {
				ajaxLoadEnd();
				if (!data||data.empname == null) {
					$.messager.alert("溫馨提示", "工號不存在", "error");
					$('#dealno').val('');
					$('#dealname').val('');
					$('#hurtsex').val('');
					$('#hurtage').val('');
					$('#hurtjointime').val('');
					$('#hurtposition').val('');
					$('#hurtphone').val('');
					$('#hurtdeptno').val('');
					$('#hurtdeptname').val('');
					$('#hurtidcard').val('');
					$('#hurtfac').val('');
					$('#hurtfacid').val('');
					$('#insurancedeptno').val('');
					$('#insurancecomputer').val('');
					$('#insurancetime').val('');
				} else {
					var myDate = new Date();
					myDate.getFullYear(); //获取完整的年份
					var age = myDate.getFullYear() - data.datBirV.substr(0, 4);
					$('#dealname').val(data.empname);
					$('#hurtsex').val(data.empSex);
					$('#hurtage').val(age);
					$('#hurtjointime').val(data.datGroupV);
					$('#hurtposition').val(data.leveltypename);
					$('#hurtdeptno').val(data.deptno);
					$('#hurtdeptname').val(data.deptname);
					$('#hurtidcard').val(data.psnid);
					// $('#hurtfac').val(data.factoryname);
					$('#hurtfacid').val(data.factoryid);
					queryFacnamByFacid(data.factoryid);
					$('#insurancedeptno').val(data.insurancedeptno);
					$('#insurancecomputer').val(data.insurancedeptno);
					//alert(data.insurancetime);
					if(null == data.insurancetime ||"" == data.insurancetime ){
					    $('#insurancetime').datebox('setValue','');
					}else{
						$('#insurancetime').datebox('setValue',timestampToTime(data.insurancetime));	
					}
				}
			}
		});
	}
}

//時間戳與日期格式相互轉換
function timestampToTime(timestamp) {
	//var date = new Date(timestamp * 1000);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
	var date = new Date(timestamp);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
	var Y = date.getFullYear() + '-';
	var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date
			.getMonth() + 1)
			+ '-';
	var D = date.getDate() + ' ';
	/*var h = date.getHours() + ':';
	var m = date.getMinutes() + ':';
	var s = date.getSeconds();*/
	return Y + M + D;
}

//時間戳與日期格式相互轉換  yyyy-MM-dd HH:mm
function timestampToTimeHM(timestamp) {
	//var date = new Date(timestamp * 1000);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
	var date = new Date(timestamp);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
	var Y = date.getFullYear() + '-';
	var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date
			.getMonth() + 1)
			+ '-';
	var D = date.getDate() + ' ';
	var h = date.getHours() + ':';
	var m = date.getMinutes() + ':';
	var s = date.getSeconds();
	return Y + M + D + h + m +s;
}

//附件上傳
function uploadFile() {
	var attachIds = $.trim($("#attachids").val());
	var attachIdsL = attachIds.split(",").length;
	if (attachIdsL >= 6) {
		alert("上傳附件個數不能超過五個！");
		return;
	}
	// 创建
	var form_data = new FormData();
	// 获取文件
	var file_data = $("#attachidsUpload").prop("files")[0];
	form_data.append("file", file_data);
	form_data.append("path", "test");
	$.ajax({
				type : "POST",
				url : ctx + "/newEsign/upload",
				beforeSend : ajaxLoading,
				processData : false, // 注意：让jQuery不要处理数据
				contentType : false, // 注意：让jQuery不要设置contentType
				data : form_data
			}).success(
					function(msg) {
						ajaxLoadEnd();
						$("#dowloadUrl").append(
										'&nbsp;&nbsp;&nbsp;<div class="float_L" id="'
												+ msg.id
												+ '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href="/newEsign/download/'
												+ msg.id
												+ '">'
												+ msg.name
												+ '</a></div><div class="float_L deleteBtn" onclick="delAtt(\''
												+ msg.id + '\')"></div></div>');
						$("#attachids").val(
						$("#attachids").val() + msg.id + ",");
					}).fail(function(msg) {
			});
}

//附件刪除
function delAtt(fileid) {
	$("#" + fileid).remove();
	var kk = $("#attachids").val();
	kk = kk.replace(fileid + ",", "");
	$("#attachids").val(kk);
	$.post(ctx + '/newEsign/delete/' + fileid, {}, function(data) {
		console.log(data);
		successTip(data);
	});
}

//保存0和提交2方法 
function saveInfo(flag) {
	debugger;
	if (flag == '0') {
		var empno = $.trim($("#dealno").val());
		if (empno == '') {
			alert("受傷員工工號不能為空！");
			return;
		}
		if (!valdHurtKind(flag)) {
			return;
		}
		if (!valdInOrOut(flag)) {
			return;
		}
	} else {
		var isValid = $("#mainform").form('validate');
		if (!isValid) {
			return;
		}
		if (!valdInOrOut(flag)) {
			return;
		}
		if (!valdHurtKind(flag)) {
			return;
		}
		if (!validHurtFac(flag)) {
			return;
		}
		if (!validHurtTime(flag)) {
			return;
		}
		if (!wordStatic(flag)) {
			return;
		}
		if (!attachValue(flag)) {
			return;
		}
		compareTime();
	}
	$.ajax({
		url : ctx + '/tqhwfreportofaccidents/create/' + flag,
		type : 'POST',
		beforeSend : ajaxLoading,
		data : $('#mainform').serialize(),
		success : function(data) {
			ajaxLoadEnd();
			successTip(data, dg, d);
			window.parent.mainpage.mainTabs.closeCurrentTab();
		}
	});
}
//创建查询对象并查询
function cx() {
	var obj = $("#searchFrom").serializeObject();
	dg.datagrid('load', obj);
}
//表單進度查詢頁面查詢條件重置
function listSearchReset() {
	$("#searchFrom").form("reset");
}
//导出excel
function exportExcel() {
	var form = document.getElementById("searchFrom");
	searchFrom.action = ctx + '/tqhwfreportofaccidents/exportExcel';
	form.submit();
}
//修改醫保信息
function updateInsurance(flag){
	debugger;
	$.ajax({
		url : ctx + '/tqhwfreportofaccidents/update',
		type : 'POST',
		async: false,
		beforeSend : ajaxLoading,
		data : $('#mainform').serialize(),
		//data:data,
		success : function(data) {
			debugger;
			ajaxLoadEnd();
			successTip(data, dg, d);
			f=true;
		}
	});
	//alert(f);
	return f;
}