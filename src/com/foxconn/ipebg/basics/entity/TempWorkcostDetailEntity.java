package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 警衛費用結賬明細表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-17 10:04:54
 */
@Entity
@Table(name = "TEMP_WORKCOST_DETAIL")
@DynamicUpdate
@DynamicInsert
public class TempWorkcostDetailEntity extends DataEntity<TempWorkcostDetailEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    //事業群ID
    private String buId;
    //事業群名稱
    private String buNam;
    //法人
    private String legalId;
    //部門ID
    private String dptId;
    //部門名稱
    private String dptNam;
    //費用代碼
    private String costId;
    //崗位類別
    private String postType;
    //崗位詳細位置
    private String postPosition;
    //崗位名稱
    private String postNam;
    //$column.comments
    private String l1;
    //$column.comments
    private String l2;
    //$column.comments
    private String l3;
    //$column.comments
    private String l4;
    //$column.comments
    private String l5;
    //$column.comments
    private String l6;
    //$column.comments
    private String l7;
    //$column.comments
    private String l8;
    //$column.comments
    private String l9;
    //$column.comments
    private String l10;
    //$column.comments
    private String l11;
    //$column.comments
    private String l12;
    //$column.comments
    private String l13;
    //$column.comments
    private String l14;
    //$column.comments
    private String l15;
    //$column.comments
    private String l16;
    //$column.comments
    private String l17;
    //$column.comments
    private String l18;
    //$column.comments
    private String l19;
    //$column.comments
    private String l20;
    //$column.comments
    private String l21;
    //$column.comments
    private String l22;
    //$column.comments
    private String l23;
    //$column.comments
    private String l24;
    //$column.comments
    private String l25;
    //$column.comments
    private String l26;
    //$column.comments
    private String l27;
    //$column.comments
    private String l28;
    //$column.comments
    private String l29;
    //$column.comments
    private String l30;
    //$column.comments
    private String l31;
    //正常上班天數（天）
    private String normalDay;
    //常規費用（RMB分）
    private Integer normalCost;
    //加班時數（小時）
    private Float otHour;
    //加班費用（RMB分）
    private Integer otCost;
    //總費用（RMB分）
    private Integer totalCost;
    //工作年月（YYYY-MM）
    private String workMonth;
    //臨時勤務費用（RMB分）
    private Integer tsCost;


    /**
     * 设置：事業群ID
     */
    public void setBuId(String buId) {
        this.buId = buId;
    }

    /**
     * 获取：事業群ID
     */

    @Column(name = "BU_ID", nullable = false, length = 20)
    public String getBuId() {
        return buId;
    }

    /**
     * 设置：事業群名稱
     */
    public void setBuNam(String buNam) {
        this.buNam = buNam;
    }

    /**
     * 获取：事業群名稱
     */

    @Column(name = "BU_NAM", nullable = false, length = 20)
    public String getBuNam() {
        return buNam;
    }

    /**
     * 设置：法人
     */
    public void setLegalId(String legalId) {
        this.legalId = legalId;
    }

    /**
     * 获取：法人
     */

    @Column(name = "LEGAL_ID", nullable = false, length = 20)
    public String getLegalId() {
        return legalId;
    }

    /**
     * 设置：部門ID
     */
    public void setDptId(String dptId) {
        this.dptId = dptId;
    }

    /**
     * 获取：部門ID
     */

    @Column(name = "DPT_ID", nullable = false, length = 20)
    public String getDptId() {
        return dptId;
    }

    /**
     * 设置：部門名稱
     */
    public void setDptNam(String dptNam) {
        this.dptNam = dptNam;
    }

    /**
     * 获取：部門名稱
     */

    @Column(name = "DPT_NAM", nullable = false, length = 20)
    public String getDptNam() {
        return dptNam;
    }

    /**
     * 设置：費用代碼
     */
    public void setCostId(String costId) {
        this.costId = costId;
    }

    /**
     * 获取：費用代碼
     */

    @Column(name = "COST_ID", nullable = false, length = 20)
    public String getCostId() {
        return costId;
    }

    /**
     * 设置：崗位類別
     */
    public void setPostType(String postType) {
        this.postType = postType;
    }

    /**
     * 获取：崗位類別
     */

    @Column(name = "POST_TYPE", nullable = false, length = 20)
    public String getPostType() {
        return postType;
    }

    /**
     * 设置：崗位詳細位置
     */
    public void setPostPosition(String postPosition) {
        this.postPosition = postPosition;
    }

    /**
     * 获取：崗位詳細位置
     */

    @Column(name = "POST_POSITION", nullable = false, length = 20)
    public String getPostPosition() {
        return postPosition;
    }

    /**
     * 设置：崗位名稱
     */
    public void setPostNam(String postNam) {
        this.postNam = postNam;
    }

    /**
     * 获取：崗位名稱
     */

    @Column(name = "POST_NAM", nullable = false, length = 20)
    public String getPostNam() {
        return postNam;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL1(String l1) {
        this.l1 = l1;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L1", nullable = false, length = 20)
    public String getL1() {
        return l1;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL2(String l2) {
        this.l2 = l2;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L2", nullable = false, length = 20)
    public String getL2() {
        return l2;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL3(String l3) {
        this.l3 = l3;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L3", nullable = false, length = 20)
    public String getL3() {
        return l3;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL4(String l4) {
        this.l4 = l4;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L4", nullable = false, length = 20)
    public String getL4() {
        return l4;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL5(String l5) {
        this.l5 = l5;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L5", nullable = false, length = 20)
    public String getL5() {
        return l5;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL6(String l6) {
        this.l6 = l6;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L6", nullable = false, length = 20)
    public String getL6() {
        return l6;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL7(String l7) {
        this.l7 = l7;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L7", nullable = false, length = 20)
    public String getL7() {
        return l7;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL8(String l8) {
        this.l8 = l8;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L8", nullable = false, length = 20)
    public String getL8() {
        return l8;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL9(String l9) {
        this.l9 = l9;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L9", nullable = false, length = 20)
    public String getL9() {
        return l9;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL10(String l10) {
        this.l10 = l10;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L10", nullable = false, length = 20)
    public String getL10() {
        return l10;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL11(String l11) {
        this.l11 = l11;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L11", nullable = false, length = 20)
    public String getL11() {
        return l11;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL12(String l12) {
        this.l12 = l12;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L12", nullable = false, length = 20)
    public String getL12() {
        return l12;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL13(String l13) {
        this.l13 = l13;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L13", nullable = false, length = 20)
    public String getL13() {
        return l13;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL14(String l14) {
        this.l14 = l14;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L14", nullable = false, length = 20)
    public String getL14() {
        return l14;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL15(String l15) {
        this.l15 = l15;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L15", nullable = false, length = 20)
    public String getL15() {
        return l15;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL16(String l16) {
        this.l16 = l16;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L16", nullable = false, length = 20)
    public String getL16() {
        return l16;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL17(String l17) {
        this.l17 = l17;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L17", nullable = false, length = 20)
    public String getL17() {
        return l17;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL18(String l18) {
        this.l18 = l18;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L18", nullable = false, length = 20)
    public String getL18() {
        return l18;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL19(String l19) {
        this.l19 = l19;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L19", nullable = false, length = 20)
    public String getL19() {
        return l19;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL20(String l20) {
        this.l20 = l20;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L20", nullable = false, length = 20)
    public String getL20() {
        return l20;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL21(String l21) {
        this.l21 = l21;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L21", nullable = false, length = 20)
    public String getL21() {
        return l21;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL22(String l22) {
        this.l22 = l22;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L22", nullable = false, length = 20)
    public String getL22() {
        return l22;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL23(String l23) {
        this.l23 = l23;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L23", nullable = false, length = 20)
    public String getL23() {
        return l23;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL24(String l24) {
        this.l24 = l24;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L24", nullable = false, length = 20)
    public String getL24() {
        return l24;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL25(String l25) {
        this.l25 = l25;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L25", nullable = false, length = 20)
    public String getL25() {
        return l25;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL26(String l26) {
        this.l26 = l26;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L26", nullable = false, length = 20)
    public String getL26() {
        return l26;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL27(String l27) {
        this.l27 = l27;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L27", nullable = false, length = 20)
    public String getL27() {
        return l27;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL28(String l28) {
        this.l28 = l28;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L28", nullable = false, length = 20)
    public String getL28() {
        return l28;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL29(String l29) {
        this.l29 = l29;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L29", nullable = false, length = 20)
    public String getL29() {
        return l29;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL30(String l30) {
        this.l30 = l30;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L30", nullable = false, length = 20)
    public String getL30() {
        return l30;
    }

    /**
     * 设置：${column.comments}
     */
    public void setL31(String l31) {
        this.l31 = l31;
    }

    /**
     * 获取：${column.comments}
     */

    @Column(name = "L31", nullable = false, length = 20)
    public String getL31() {
        return l31;
    }

    /**
     * 设置：正常上班天數（天）
     */
    public void setNormalDay(String normalDay) {
        this.normalDay = normalDay;
    }

    /**
     * 获取：正常上班天數（天）
     */

    @Column(name = "NORMAL_DAY", nullable = false, length = 20)
    public String getNormalDay() {
        return normalDay;
    }

    /**
     * 设置：常規費用（RMB分）
     */
    public void setNormalCost(Integer normalCost) {
        this.normalCost = normalCost;
    }

    /**
     * 获取：常規費用（RMB分）
     */

    @Column(name = "NORMAL_COST", nullable = false, length = 20)
    public Integer getNormalCost() {
        return normalCost;
    }

    /**
     * 设置：加班時數（小時）
     */
    public void setOtHour(Float otHour) {
        this.otHour = otHour;
    }

    /**
     * 获取：加班時數（小時）
     */

    @Column(name = "OT_HOUR", nullable = false, length = 20)
    public Float getOtHour() {
        return otHour;
    }

    /**
     * 设置：加班費用（RMB分）
     */
    public void setOtCost(Integer otCost) {
        this.otCost = otCost;
    }

    /**
     * 获取：加班費用（RMB分）
     */

    @Column(name = "OT_COST", nullable = false, length = 20)
    public Integer getOtCost() {
        return otCost;
    }

    /**
     * 设置：總費用（RMB分）
     */
    public void setTotalCost(Integer totalCost) {
        this.totalCost = totalCost;
    }

    /**
     * 获取：總費用（RMB分）
     */

    @Column(name = "TOTAL_COST", nullable = false, length = 20)
    public Integer getTotalCost() {
        return totalCost;
    }


    /**
     * 设置：工作年月（YYYY-MM）
     */
    public void setWorkMonth(String workMonth) {
        this.workMonth = workMonth;
    }

    /**
     * 获取：工作年月（YYYY-MM）
     */

    @Column(name = "WORK_MONTH", nullable = false, length = 20)
    public String getWorkMonth() {
        return workMonth;
    }

    /**
     * 设置：臨時勤務費用（RMB分）
     */
    public void setTsCost(Integer tsCost) {
        this.tsCost = tsCost;
    }

    /**
     * 获取：臨時勤務費用（RMB分）
     */

    @Column(name = "TS_COST", nullable = false, length = 20)
    public Integer getTsCost() {
        return tsCost;
    }

}
