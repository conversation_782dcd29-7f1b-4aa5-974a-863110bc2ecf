package com.foxconn.ipebg.system.entity;

        import java.io.Serializable;
        import java.util.Date;
        import javax.persistence.*;

        import com.foxconn.ipebg.common.entity.DataEntity;
        import org.hibernate.annotations.DynamicInsert;
        import org.hibernate.annotations.DynamicUpdate;


/**
 * ${comments}
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-17 18:41:34
 */
@Entity
@Table(name = "t_qh_duty")
@DynamicUpdate
@DynamicInsert
public class TQhDutyEntity extends DataEntity<TQhDutyEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

                        //$column.comments
            private String dutyid;
                                //$column.comments
            private String dutyname;
                                                                                    
                        /**
             * 设置：${column.comments}
             */
            public void setDutyid(String dutyid) {
                this.dutyid = dutyid;
            }

            /**
             * 获取：${column.comments}
             */
            
            @Column(name = "dutyid", nullable = false, length = 20)
            public String getDutyid() {
                return dutyid;
            }
                
                        /**
             * 设置：${column.comments}
             */
            public void setDutyname(String dutyname) {
                this.dutyname = dutyname;
            }

            /**
             * 获取：${column.comments}
             */
            
            @Column(name = "dutyname", nullable = false, length = 20)
            public String getDutyname() {
                return dutyname;
            }
                
            
            
            
            
            
            
    }
