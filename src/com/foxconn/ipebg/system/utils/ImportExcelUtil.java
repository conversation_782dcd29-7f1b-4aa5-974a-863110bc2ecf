package com.foxconn.ipebg.system.utils;

import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 導入excel工具類，變更日期格式
 * <AUTHOR> 王磊
 *
 * @date 2017/5/18 下午3:52:05
 */
public class ImportExcelUtil {
	private static Logger logger = Logger.getLogger(ImportExcelUtil.class);
	/** 总行数 */
	private int totalRows = 0;
	/** 总列数 */
	private int totalCells = 0;
	/** 错误信息 */
	private String errorInfo;

	public int getTotalRows() {
		return totalRows;
	}

	public int getTotalCells() {
		return totalCells;
	}

	public String getErrorInfo() {
		return errorInfo;
	}

	/**
	 * 
	 * @Description:  驗證excel
	 * <AUTHOR> 王磊
	 * @date 2017/5/18 下午3:52:40 
	 * @param filePath 文件路徑
	 * @return boolean 是否成功
	 */
	public boolean validateExcel(String filePath) {
		/** 检查文件名是否为空或者是否是Excel格式的文件 */
		if (filePath == null
				|| !(WDWUtil.isExcel2003(filePath) || WDWUtil
				.isExcel2007(filePath))) {
			errorInfo = "文件名不是excel格式或檢測不到您上傳的附件，請確認是否安裝flash插件";
			return false;
		}
		/** 检查文件是否存在 */
		File file = new File(filePath);
		if (file == null || !file.exists()) {
			System.out.println(file.getPath());
			logger.error("validateExcel 文件不存在:"+file.getPath());
			errorInfo = "文件不存在";
			return false;
		}
		return true;
	}
	/**
	 * 
	 * @Description:  讀取文件
	 * <AUTHOR> 王磊
	 * @date 2017/5/18 下午3:53:30 
	 * @param filePath 文件路徑
	 * @return List<List<String>> 結果集合
	 */
	public List<List<String>> read(String filePath) {
		List<List<String>> dataLst = new ArrayList<List<String>>();
		InputStream is = null;
		try {
			/** 验证文件是否合法 */
			if (!validateExcel(filePath)) {
				System.out.println(errorInfo);
				return null;
			}
			/** 判断文件的类型，是2003还是2007 */
			boolean isExcel2003 = true;
			if (WDWUtil.isExcel2007(filePath)) {
				isExcel2003 = false;
			}
			/** 调用本类提供的根据流读取的方法 */
			File file = new File(filePath);
			is = new FileInputStream(file);
			dataLst = read(is, isExcel2003);
			is.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			if (is != null) {
				try {
					is.close();
				} catch (IOException e) {
					is = null;
					
				}
			}
		}
		/** 返回最后读取的结果 */
		return dataLst;
	}
	/**
	 * 
	 * @Description:  edit by 劉濤   （傳入模板表頭的size,解決  多讀列數問題）  start
	 * <AUTHOR> 王磊
	 * @date 2017/5/18 下午3:54:24 
	 * @param inputStream 輸入流
	 * @param isExcel2003 是否2003
	 * @return List<List<String>> 結果集合
	 */
	public List<List<String>> read(InputStream inputStream, boolean isExcel2003) {
		return read(inputStream,isExcel2003,-1);
	}
	/**
	 * 
	 * @Description:  讀取EXCEL
	 * <AUTHOR> 王磊
	 * @date 2017/5/23 上午9:32:41 
	 * @param inputStream 輸入流
	 * @param isExcel2003 是否2003
	 * @param cellCount 單元格數量
	 * @return List<List<String>> Excel集合
	 */
	public List<List<String>> read(InputStream inputStream, boolean isExcel2003,int cellCount) {
		List<List<String>> dataLst = null;
		try {
			/** 根据版本选择创建Workbook的方式 */
			Workbook wb = null;
			if (isExcel2003) {
				wb = new HSSFWorkbook(inputStream);
			} else {
				wb = new XSSFWorkbook(inputStream);
			}
			dataLst = read(wb,cellCount);
		} catch (IOException e) {
			
		}
		return dataLst;
	}

	/**
	 * 
	 * @Description:  讀取數據
	 * <AUTHOR> 王磊
	 * @date 2017/5/18 下午3:55:33 
	 * @param wb 數據源
	 * @param cellCount 列數
	 * @return List<List<String>> 結果集合
	 */
	private List<List<String>> read(Workbook wb,int cellCount) {
		List<List<String>> dataLst = new ArrayList<List<String>>();
		/** 得到第一个shell */
		Sheet sheet = wb.getSheetAt(0);
		/** 得到Excel的行数 */
		this.totalRows = sheet.getPhysicalNumberOfRows();
		/** 得到Excel的列数 */
		if (this.totalRows >= 1 && sheet.getRow(0) != null) {
			this.totalCells = sheet.getRow(0).getPhysicalNumberOfCells();
		}
		if(cellCount == -1){
			cellCount=this.totalCells;
		}
		/** 循环Excel的行 */
		for (int r = 0; r < this.totalRows; r++) {
			Row row = sheet.getRow(r);
			if (row == null) {
				continue;
			}
			List<String> rowLst = new ArrayList<String>();
			/** 循环Excel的列 */
			for (int c = 0; c < cellCount; c++) {
				Cell cell = row.getCell(c);
				String cellValue = "";
				if (null != cell) {
					// 以下是判断数据的类型
					switch (cell.getCellType()) {
						case HSSFCell.CELL_TYPE_NUMERIC: // 数字
							if (HSSFDateUtil.isCellDateFormatted(cell)) {
								Date date = cell.getDateCellValue();
								SimpleDateFormat sdf = new SimpleDateFormat(
										"yyyy-MM-dd");
								cellValue = sdf.format(date);
							} else {
								cell.setCellType(Cell.CELL_TYPE_STRING);
								cellValue = cell.getStringCellValue();
//								HSSFDataFormatter dataFormatter = new HSSFDataFormatter();
//								cellValue = dataFormatter.formatCellValue(cell);
//								if(numCell != null && numCell.contains(c)){
//									cellValue = cell.getNumericCellValue()+ "";
//								}else{
//									cell.setCellType(Cell.CELL_TYPE_STRING);
//									cellValue = cell.getStringCellValue();
//								}
							}
							break;
						case HSSFCell.CELL_TYPE_STRING: // 字符串
							cellValue = cell.getStringCellValue();
							break;
						case HSSFCell.CELL_TYPE_BOOLEAN: // Boolean
							cellValue = cell.getBooleanCellValue() + "";
							break;
						case HSSFCell.CELL_TYPE_FORMULA: // 公式
							cellValue = cell.getCellFormula() + "";
							break;
						case HSSFCell.CELL_TYPE_BLANK: // 空值
							cellValue = "";
							break;
						case HSSFCell.CELL_TYPE_ERROR: // 故障
							cellValue = "非法字符";
							break;
						default:
							cellValue = "未知类型";
							break;
					}
				}
				rowLst.add(cellValue);
			}
			/** 保存第r行的第c列 */
			dataLst.add(rowLst);
		}
		return dataLst;
	}
//  edit by 劉濤   （傳入模板表頭的size,解決  多讀列數問題）  end


//	public static void main(String[] args) throws Exception {
//		ImportExcelUtil poi = new ImportExcelUtil();
//		// List<List<String>> list = poi.read("d:/aaa.xls");
//		List<List<String>> list = poi.read("F:/importApply.xls");
//		if (list != null) {
//			for (int i = 0; i < list.size(); i++) {
//				List<String> cellList = list.get(i);
//				if (!cellList.get(0).equals("")) {
//					System.out.print("第" + (i) + "行");
//					for (int j = 0; j < cellList.size(); j++) {
//						// System.out.print("    第" + (j + 1) + "列值：");
//						System.out.print("    " + cellList.get(j));
//					}
//					System.out.println();
//				}
//			}
//		}
//	}
}


class WDWUtil {
	public static boolean isExcel2003(String filePath) {
		//return filePath.matches("\\.[^\\.]+(xls)$");
		return filePath.matches("^.+\\.(?i)(xls)$");
	}

	public static boolean isExcel2007(String filePath) {
		//return filePath.matches("\\.[^\\.]+(xlsx)$");
		return filePath.matches("^.+\\.(?i)(xlsx)$");
	}
}
