<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>異常人員信息</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>

<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/temparrange/${action}" method="post">
		<table class="formTable">
		    			<tr>
				<td>主鍵：</td>
				<td>
				    					<input type="hidden" name="id" value="${tempArrange.id}"/>
															<input id="id" name="id" class="easyui-validatebox" data-options="width: 150" value="${tempArrange.id }" />
									</td>
			</tr>
						<tr>
				<td>崗位編號：</td>
				<td>
				    										<input id="postRecno" name="postRecno" class="easyui-validatebox" data-options="width: 150" value="${tempArrange.postRecno }" />
									</td>
			</tr>
						<tr>
				<td>崗位名稱：</td>
				<td>
				    										<input id="postName" name="postName" class="easyui-validatebox" data-options="width: 150" value="${tempArrange.postName }" />
									</td>
			</tr>
						<tr>
				<td>班制：</td>
				<td>
				    										<input id="postShift" name="postShift" class="easyui-validatebox" data-options="width: 150" value="${tempArrange.postShift }" />
									</td>
			</tr>
						<tr>
				<td>詳細位置（小崗位名稱）：</td>
				<td>
				    										<input id="location" name="location" class="easyui-validatebox" data-options="width: 150" value="${tempArrange.location }" />
									</td>
			</tr>
						<tr>
				<td>排班日期：</td>
				<td>
				    										<input id="shiftDate" name="shiftDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${tempArrange.shiftDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>班別代碼：</td>
				<td>
				    										<input id="shiftNo" name="shiftNo" class="easyui-validatebox" data-options="width: 150" value="${tempArrange.shiftNo }" />
									</td>
			</tr>
						<tr>
				<td>班別：</td>
				<td>
				    										<input id="shiftName" name="shiftName" class="easyui-validatebox" data-options="width: 150" value="${tempArrange.shiftName }" />
									</td>
			</tr>
						<tr>
				<td>開始時間：</td>
				<td>
				    										<input id="startTime" name="startTime" class="easyui-validatebox" data-options="width: 150" value="${tempArrange.startTime }" />
									</td>
			</tr>
						<tr>
				<td>結束時間：</td>
				<td>
				    										<input id="endTime" name="endTime" class="easyui-validatebox" data-options="width: 150" value="${tempArrange.endTime }" />
									</td>
			</tr>
						<tr>
				<td>工號：</td>
				<td>
				    										<input id="empNo" name="empNo" class="easyui-validatebox" data-options="width: 150" value="${tempArrange.empNo }" />
									</td>
			</tr>
						<tr>
				<td>姓名：</td>
				<td>
				    										<input id="empName" name="empName" class="easyui-validatebox" data-options="width: 150" value="${tempArrange.empName }" />
									</td>
			</tr>
						<tr>
				<td>創建人：</td>
				<td>
				    										<input id="createBy" name="createBy" class="easyui-validatebox" data-options="width: 150" value="${tempArrange.createBy }" />
									</td>
			</tr>
						<tr>
				<td>創建時間：</td>
				<td>
				    										<input id="createDate" name="createDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${tempArrange.createDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>更新者：</td>
				<td>
				    										<input id="updateBy" name="updateBy" class="easyui-validatebox" data-options="width: 150" value="${tempArrange.updateBy }" />
									</td>
			</tr>
						<tr>
				<td>更新時間：</td>
				<td>
				    										<input id="updateDate" name="updateDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${tempArrange.updateDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>刪除標識：</td>
				<td>
				    										<input id="delFlag" name="delFlag" class="easyui-validatebox" data-options="width: 150" value="${tempArrange.delFlag }" />
									</td>
			</tr>
						<tr>
				<td>比對-狀態：正常為0：</td>
				<td>
				    										<input id="checkState" name="checkState" class="easyui-validatebox" data-options="width: 150" value="${tempArrange.checkState }" />
									</td>
			</tr>
						<tr>
				<td>比對-異常原因：</td>
				<td>
				    										<input id="checkCauses" name="checkCauses" class="easyui-validatebox" data-options="width: 150" value="${tempArrange.checkCauses }" />
									</td>
			</tr>
						<tr>
				<td>創建人IP：</td>
				<td>
				    										<input id="createIp" name="createIp" class="easyui-validatebox" data-options="width: 150" value="${tempArrange.createIp }" />
									</td>
			</tr>
					</table>
	</form>
  </div>
<script src='${ctx}/static/js/basics/temparrange.js?"+Math.random()"'></script>
<script type="text/javascript">
   //提交表单
$('#mainform').form({
    onSubmit: function(){
        var isValid = $(this).form('validate');
        return isValid;	// 返回false终止表单提交
    },
    success:function(data){ 
        successTip(data,dg,d);
    }
});
</script>
</body>
</html>