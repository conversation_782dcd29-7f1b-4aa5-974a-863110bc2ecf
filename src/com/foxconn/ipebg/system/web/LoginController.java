package com.foxconn.ipebg.system.web;

import cn.hutool.log.LogFactory;
import com.foxconn.cbb.pojo.UserInfo;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Global;
import com.foxconn.ipebg.system.service.RedisService;
import com.foxconn.ipebg.system.utils.IPUtil;
import com.foxconn.ipebg.system.utils.UsernamePasswordCaptchaToken;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.authc.FormAuthenticationFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

/**
 * 登录controller
 * <AUTHOR>
 * @date 2015年1月14日
 */
@Controller
@RequestMapping(value = "{adminPath}")
public class LoginController{

	@Autowired
	RedisService RedisService;
	/**
	 * 默认页面
	 * @return
	 */
	@RequestMapping(value="login",method = RequestMethod.GET)
	public String login(HttpServletRequest request,RedirectAttributes redirectAttributes,String urlFromOld,String titleFromOld, Model model) {
		Subject subject = SecurityUtils.getSubject();

		model.addAttribute("ip", IPUtil.getIpAddress(request));

//		Cookie[] cookies = request.getCookies();
//		//注意：判断null,否则空指针
//		if(cookies!=null){
//			//遍历
//			for(Cookie c:cookies){
//				String name = c.getName();
//				String value = c.getValue();
//				System.out.println(name+"="+value);
//			}
//		}else{
//			System.out.println("没有接收cookie数据");
//		}
//		String userno = null;
//		if(cookies!=null && cookies.length == 4){
//			String name = cookies[3].getName();
//			if("SESSION".equals(name)) {
//				String value = cookies[3].getValue();
//				userno = RedisService.getUserno(value);
//			}
//		}
//
//		System.out.println(userno);
		Session session = subject.getSession();
		UserInfo u =(UserInfo)session.getAttribute("loginuser");
		System.out.println("sessionId = " + session.getId());
//		String s = (String)session.getAttribute("userno");
//		String sessionId = session.getId();
		if(u != null&&!subject.isAuthenticated()){
			String password = Constant.DEF_PASSWORD;
			String host = Global.getAdminPath();
			String captcha = (String) SecurityUtils.getSubject().getSession().getAttribute(com.google.code.kaptcha.Constants.KAPTCHA_SESSION_KEY);
			boolean rememberMe = false;
			AuthenticationToken authenticationToken = new UsernamePasswordCaptchaToken(u.getEmpNo(),password.toCharArray(), rememberMe, host, captcha,Constant.LoginType.SESSION_LOGIN.getValue(),"","",IPUtil.getIpAddress(request));
			try{
				subject.login(authenticationToken);
			}catch(Exception e){

				return "system/login";
			}
		}
		try {
			if(subject.isAuthenticated()||subject.isRemembered()){
				redirectAttributes.addFlashAttribute("urlFromOld",urlFromOld);
				redirectAttributes.addFlashAttribute("titleFromOld",titleFromOld);
                return "redirect:"+ Global.getAdminPath();
            }
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "system/login";
	}

	/**
	 * 登录失败
	 * @param userName
	 * @param model
	 * @return
	 */
	@RequestMapping(value="login",method = RequestMethod.POST)
	public String fail(@RequestParam(FormAuthenticationFilter.DEFAULT_USERNAME_PARAM) String userName, HttpServletRequest request, Model model) {
		model.addAttribute(FormAuthenticationFilter.DEFAULT_USERNAME_PARAM, userName);
		model.addAttribute("ip", IPUtil.getIpAddress(request));
		return "system/login";
	}

	/**
	 * 登出
	 * @param model
	 * @return
	 */
	@RequestMapping(value="logout")
	public String logout(HttpServletRequest request, Model model) {
		Subject subject = SecurityUtils.getSubject();
		subject.logout();
		model.addAttribute("ip", IPUtil.getIpAddress(request));
		return "system/login";
	}
	
}
