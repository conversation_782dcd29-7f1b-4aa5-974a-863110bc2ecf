var dg;
var d;
// 用於保存崗位異動/臨時撤崗模式中原始崗位列表
var originSelectedPostList;
$(function() {
	dg = $('#dg').datagrid({
		method : "get",
		url : ctx + '/eguardaply/list',
		fit : true,
		fitColumns : true,
		border : false,
		idField : 'id',
		striped : true,
		pagination : true,
		rownumbers : true,
		sortName : 'createtime',
		sortOrder : 'desc',
		pageNumber : 1,
		pageSize : 20,
		pageList : [ 10, 20, 30, 40, 50 ],
		singleSelect : true,
		columns : [ [ {
			field : 'id',
			title : '主鍵',
			hidden : true
		},{
			field : 'serialno',
			title : '任務編號',
			sortable : true,
			width : 100,
			formatter: operation
		}, {
			field : 'makerno',
			title : '填單人工號',
			sortable : true,
			width : 100
		}, {
			field : 'makername',
			title : '填單人名稱',
			sortable : true,
			width : 100
		}, {
			field : 'makerip',
			title : '填單人IP',
			sortable : true,
			width : 100
		}, {
			field : 'createtime',
			title : '填單時間',
			sortable : true,
			width : 100
		}, {
			field : 'dptQun',
			title : '事業群',
			sortable : true,
			width : 100
		}, {
			field : 'legalPerson',
			title : '所屬法人',
			sortable : true,
			width : 100
		}, {
			field : 'dealtel',
			title : '分機',
			sortable : true,
			width : 100
		}, {
			field : 'linkman',
			title : '聯繫人',
			sortable : true,
			width : 100
		}, {
			field : 'requirememtType',
			title : '需求類型',
			sortable : true,
			width : 100
		}, {
			field : 'applyStat',
			title : '申請單狀態(E：結案，0：申請，O：，駁回 Q：異常結案)',
			sortable : true,
			width : 100
		}, {
			field : 'signEmpNo',
			title : '審核人工號',
			sortable : true,
			width : 100
		}, {
			field : 'signEmpNam',
			title : '審核人姓名',
			sortable : true,
			width : 100
		}, {
			field : 'signDate',
			title : '審核時間',
			sortable : true,
			width : 100
		}] ],
		enableHeaderClickMenu : true,
		enableHeaderContextMenu : true,
		enableRowContextMenu : false,
		rowTooltip : true,
		toolbar : '#tb'
	});
	
	queryUserInfo();
	// 初始化去除驗證
	$(":input").removeClass("validatebox-invalid");
	
	
	// 添加一筆
	$("#add").click(function(){
	var len = $("#buildprojectApplyItemTable tr").length;
	$("#buildprojectApplyItemTable").append("<tr align='center' id=Item"+len+">"
	    +"<td>"+len+"</td>"
	   
	    +"<td><input id='area"+len+"' name='eGuardItemAplyEntity["+(len-1)+"].area'   class=\"easyui-combobox rayOptionitemclass\" data-tr-index=\""+len+"\" data-options=\"required:true, panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadArea(this.dataset.trIndex);},onSelect:function(){loadBlock(this.dataset.trIndex,true);}\" style='width:80px;'/></td>"
	    +"<td><input id='block"+len+"' name='eGuardItemAplyEntity["+(len-1)+"].block'   class=\"easyui-combobox rayOptionitemclass\" data-tr-index=\""+len+"\" data-options=\"required:true, panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadBlock(this.dataset.trIndex);},onSelect:function(){loadFloor(this.dataset.trIndex,true);}\" style='width:80px;'/></td>"
	    +"<td><input id='floor"+len+"' name='eGuardItemAplyEntity["+(len-1)+"].floor'   class=\"easyui-combobox rayOptionitemclass\" data-options=\"required:true, panelHeight:'auto',valueField:'value', textField:'label',editable:false \" style='width:80px;'/></td>"
	    +"<td><input id='position"+len+"' name='eGuardItemAplyEntity["+(len-1)+"].position'   class=\"easyui-combobox rayOptionitemclass\" data-options=\"required:true, panelHeight:'auto',valueField:'value', textField:'label',url: ctx+'/system/dict/getDictByType/guard_position'\" style='width:80px;'/></td>"
	    
	   // +"<td><input id='location"+len+"' name='eGuardItemAplyEntity["+(len-1)+"].location' class='easyui-validatebox' required='true' style='width:150px;;'></td>"
	    +"<td><input id='postType"+len+"' name='eGuardItemAplyEntity["+(len-1)+"].postType' class=\"easyui-combobox rayOptionitemclass\" data-options=\"required:true, panelHeight:'auto',valueField:'value', textField:'label',url: ctx+'/system/dict/getDictByType/guard_postType'\" style='width:80px;;'></td>"
	    +"<td><input id='postShift"+len+"' name='eGuardItemAplyEntity["+(len-1)+"].postShift'   class=\"easyui-combobox rayOptionitemclass\" data-options=\"required:true, panelHeight:'auto',valueField:'value', textField:'label',url: ctx+'/system/dict/getDictByType/guard_postShift'\" style='width:80px;'/></td>"
	    +"<td><input id='postName"+len+"' name='eGuardItemAplyEntity["+(len-1)+"].postName' class='easyui-validatebox' required='true' style='width:100px;' onblur='isExistPostName("+len+");'></td>"
	    //+"<td><input id='postName"+len+"' name='eGuardItemAplyEntity["+(len-1)+"].postName' class='easyui-validatebox' required='true' style='width:150px;;' onblur='isExistPostName(this);'></td>"
	    +"<td><input id='postPerNu"+len+"' name='eGuardItemAplyEntity["+(len-1)+"].postPerNu' class='easyui-validatebox'  required='true' style='width:80px;'  onkeyup=onlyNumber(this) onafterpaste=onlyNumber(this) onblur='onlyNumber(this)' ></td>"
	    // onkeyup=\"this.value=this.value.replace(/[^\d]/g,'') \" onafterpaste=\"this.value=this.value.replace(/[^\d]/g,'') \"
	   
	    +"<td><input id='postStartDate"+len+"' name='eGuardItemAplyEntity["+(len-1)+"].postStartDate' class='easyui-my97' datefmt='yyyy-MM-dd' minDate='%y-%M-%d' data-options='width: 100,required:true'/></td>"
	   
	  
	    +"<td><input type='image' src='"+ctx+"/static/images/deleteRow.png' onclick='deltr("+len+");return false;'/></td>"+"</tr>");
	
	$.parser.parse($("#Item" + len));

	$("#area"+len).val('');
	$("#block"+len).val('');
	$("#floor"+len).val('');
	$("#position"+len).val('');
	//$("#location"+len).val('');
	$("#postType"+len).val('');
	$("#postShift"+len).val('');
	$("#postName"+len).val('');
	$("#postPerNu"+len).val('');


	});
	// 查詢事業群列表
	$.ajax({
		url: ctx + "/bsdpt/getAllBusinessGroup",
		type: "GET",
		success: function(data) {
			$("#filter_EQS_dptQun").combobox({
				data: data,
				valueField: "dptQun",
				textField: "dptQun",
				editable: false,
				panelHeight: 200
			});
		},
		error: function(error) {
			alert("初始化下拉控件失敗");
		}
	});

	// 批量上傳模板下載
	$("#btnBatchImportTpl").click(batchImportTpl);
});

// 初始化結束**********

// 刪除一筆
function deltr(index) {
	$("#Item" + index).remove();
	var len = $("#buildprojectApplyItemTable tr").length;
	for (var i = 1; i <= len; i++) {

		$("#buildprojectApplyItemTable").find("tr").eq(i).attr("id", "Item" + (i));
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(0).html(i);


		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(1).find("input[id^='area']").attr("id", "area" + i);
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(2).find("input[id^='block']").attr("id", "block" + i);
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(3).find("input[id^='floor']").attr("id", "floor" + i);
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(4).find("input[id^='position']").attr("id", "position" + i);
		//$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(5).find("input[id^='location']").attr("id","location"+i);
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(5).find("input[id^='postType']").attr("id", "postType" + i);
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(6).find("input[id^='postShift']").attr("id", "postShift" + i);
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(7).find("input[id^='postName']").attr("id", "postName" + i);
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(8).find("input[id^='postPerNu']").attr("id", "postPerNu" + i);
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(9).find("input[id^='postStartDate']").attr("id", "postStartDate" + i);
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(9).find("input[id^='postEndDate']").attr("id", "postEndDate" + i);


		$("#area" + len).val('');
		$("#block" + len).val('');
		$("#floor" + len).val('');
		$("#position" + len).val('');
		//$("#location"+len).val('');
		$("#postType" + len).val('');
		$("#postShift" + len).val('');
		$("#postName" + len).val('');
		$("#postPerNu" + len).val('');
		// ***************
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(1).find("input[name^='eGuardItemAplyEntity']").attr("name", "eGuardItemAplyEntity[" + (i - 1) + "].area");
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(2).find("input[name^='eGuardItemAplyEntity']").attr("name", "eGuardItemAplyEntity[" + (i - 1) + "].block");
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(3).find("input[name^='eGuardItemAplyEntity']").attr("name", "eGuardItemAplyEntity[" + (i - 1) + "].floor");
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(4).find("input[name^='eGuardItemAplyEntity']").attr("name", "eGuardItemAplyEntity[" + (i - 1) + "].position");
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(5).find("input[name^='eGuardItemAplyEntity']").attr("name", "eGuardItemAplyEntity[" + (i - 1) + "].postType");
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(6).find("input[name^='eGuardItemAplyEntity']").attr("name", "eGuardItemAplyEntity[" + (i - 1) + "].postShift");
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(7).find("input[name^='eGuardItemAplyEntity']").attr("name", "eGuardItemAplyEntity[" + (i - 1) + "].postName");
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(8).find("input[name^='eGuardItemAplyEntity']").attr("name", "eGuardItemAplyEntity[" + (i - 1) + "].postPerNu");
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(9).find("input[name^='postStartDate']").attr("name", "eGuardItemAplyEntity[" + (i - 1) + "].postStartDate");
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(9).find("input[name^='postEndDate']").attr("name", "eGuardItemAplyEntity[" + (i - 1) + "].postEndDate");


		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(1).find("input[id^='area']").attr("comboname", "eGuardItemAplyEntity[" + (i - 1) + "].area");
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(2).find("input[id^='block']").attr("comboname", "eGuardItemAplyEntity[" + (i - 1) + "].block");
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(3).find("input[id^='floor']").attr("comboname", "eGuardItemAplyEntity[" + (i - 1) + "].floor");
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(4).find("input[id^='position']").attr("comboname", "eGuardItemAplyEntity[" + (i - 1) + "].position");
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(6).find("input[id^='postShift']").attr("comboname", "eGuardItemAplyEntity[" + (i - 1) + "].postShift");


// $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(1).find("input[id^='area']").attr("data-options","panelHeight:'auto',valueField:'value',
// textField:'label',editable:false,onBeforeLoad:function(){loadTestType("+i+");}");
// $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(1).find("input[id^='block']").attr("data-options","panelHeight:'auto',valueField:'value',
// textField:'label',editable:false,onBeforeLoad:function(){loadTestType("+i+");}");


		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(1).find("input[id^='area']").attr("data-tr-index", i);
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(2).find("input[id^='block']").attr("data-tr-index", i);
		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(7).find("input[id^='postName']").attr("onblur", "isExistPostName(" + i + ");");//onblur='isExistPostName("+len+");'
		//$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(3).find("input[id^='location']").attr("data-options","panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadLocation("+i+");}");

		$("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(10).find("input[type='image']").attr("onclick", "deltr(" + i + ");return false;");

	}

}

function operation(value, row, index) {
return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('詳情',ctx+'/eguardaply/view/" + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
};
// 弹窗增加
function add() {
	d = $("#dlg").dialog({
		title : '添加飲用水檢測申請主表',
		width : 380,
		height : 380,
		href : ctx + '/eguardaply/create',
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '确认',
			handler : function() {
				$("#mainform").submit();
			}
		}, {
			text : '取消',
			handler : function() {
				d.panel('close');
			}
		} ]
	});
}

// 删除
function del() {

	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data) {
		if (data) {
			$.ajax({
				type : 'get',
				url : ctx + "/eguardaply/delete/" + row.updateBy,
				success : function(data) {
					successTip(data, dg);
				}
			});
		}
	});
}

// 弹窗上傳附檔
function uploadFileForm() {
	// console.log("ff333");
	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	if (row.applyStat!="簽核完成")
		{
		alert("未簽核完成的單子不可上傳附檔!");
		return;
		}
	if (row.attachids!=null)
	{
	alert("該單號已經上傳預算核准檔！");
	return;
	}
	d = $("#dlg").dialog({
		title : '飲用水檢測預算核准檔上傳',
		width : 380,
		height : 340,
		href : ctx + '/eguardaply/uploadFileForm/' + row.id,
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '確定',
			handler : function() {
				$('#mainform').submit();
			}
		}, {
			text : '取消',
			handler : function() {
				d.panel('close');
			}
		} ]
	});
}
// 创建查询对象并查询
function cx() {
	var obj = $("#searchFrom").serializeObject();
	dg.datagrid('load', obj);
}
// 表單進度查詢頁面查詢條件重置
function listSearchReset() {
	$("#searchFrom").form("reset");
}
// 导出excel
function exportExcel() {
	var form = document.getElementById("searchFrom");
	searchFrom.action = ctx + '/eguardaply/exportExcel';
	form.submit();
}

function saveInfo(flag) {
	if (flag == '2') {
		var isValid = $("#mainform").form('validate');
		if (!isValid) {
			return;
		}
	}
	var count = $("#buildprojectApplyItemTable tr").length - 1;
	if (count < 1) {
		$.messager.show("溫馨提示", "崗位列表不能為空", "bottomRight");
		return;
	}
	var requirementType = $('input:radio[name="eGuardAplyEntity.requirememtType"]:checked').val();
	if (requirementType === '2') {
		// 臨時撤崗
		for (var i = 0; i < count; i++) {
			var post = originSelectedPostList[i];
			var perNum = parseInt($('#postPerNu'+(i+1)).validatebox('getValue'));
			var shift = $('#postShift'+(i+1)).combobox('getValue');
			if (shift === '2班制' && post.postShift === '1班制') {
				// 從 1班制 變成 2班制
				$.messager.show("溫馨提示", "臨時撤崗不允許增加"+post.postName+"班制", "bottomRight");
				return;
			}
			if (perNum > post.postPerNu) {
				// 人數變多
				$.messager.show("溫馨提示", "臨時撤崗不允許增加"+post.postName+"人數", "bottomRight");
				return;
			}
		}
	}
	$.ajax({
		url: ctx + '/eguardaply/create/' + flag,
		type: 'POST',
		beforeSend: ajaxLoading,
		data: $('#mainform').serialize(),
		success: function (data) {
			ajaxLoadEnd();
			successTip(data, dg, d);
			if (data === "success") {
				window.parent.mainpage.mainTabs.closeCurrentTab();
			}
		}
	});
}
function auditInfo(flag) {
	if (flag == 'Y' && $("#securityCom1").length > 0) {
		// 判斷是否存在保安公司元素
		 if($("#securityCom1").combobox('getValue')==null||$("#securityCom1").combobox('getValue')==''){
			 alert("請選擇派駐保安公司！");
			 return;
		 }
	}
 	if(flag == 'N') {
 	 var isValid = $("#mainform").form('validate');
	  if (!isValid) {
		  return;
	  }
 }
 $.ajax({
     url: ctx+'/eguardaply/auditSave/' + flag,
     type: 'POST',
     beforeSend: ajaxLoading,
     // dataType: 'json',
     data: $('#mainform').serialize(),
     success: function (data) {
         ajaxLoadEnd();
         successTip(data, dg, d);
         $("#btnPass").linkbutton('disable');	
         $("#btnReject").linkbutton('disable');	
         
       /*  if ($("#applyStatRe").val()=='11'  || $("#applyStatRe").val()=='17') 
         {
       	 
        	 $.ajax({
 				type : 'get',
 				url : ctx + "/eformsign/index",
 				success : function(data) {
 					//successTip(data, dg);
 					window.location=data;
 				}
 			});
         
         }*/
        
         window.parent.mainpage.mainTabs.closeCurrentTab();
         
         //window.close();
     }
 });
}
// validatebox驗證擴展
$.extend($.fn.validatebox.defaults.rules, {
 endTime: {
     validator: function (value, param) {
         var startDate = $('#buildcyclebegindate').datebox('getValue');
         var startTmp = new Date(startDate.replace(/-/g, "/"));
         var endTmp = new Date(value.replace(/-/g, "/"));
         return startTmp <= endTmp;
     },
     message: '结束时间要大于开始时间！'
 }
});

// 驗證申請人聯繫電話
function valdApplyTel(obj){
 var phoneL=$.trim($("#dealtel").val()).split("").length;
 var tel = obj.value;
 if(tel!=""){
     var temp = tel;
     var regex  = /^[0-9]{3}\+[0-9]{5}$/;
     var patt1=new RegExp(regex);
     var result = patt1.test(tel);
     if(!result){
         alert("分機格式有誤!");
         obj.value="";
         obj.focus();
         return;
     }
 }
}
// 驗證手機號碼
function checkMobile(obj) {
 var
     re = /^1\d{10}$/

 if (obj.value!="")
 {
     if (re.test(obj.value)) {
         // alert("正确");
     } else {
         alert("手機格式有誤！");
         obj.value="";
         obj.focus();
         return;
     }

 }
}

// 驗證郵箱
function valdEmail(obj){
 var email = obj.value;
 var temp = email;
 if(email=="")return;
 var regex = /\w+([-/+.]\w+)*@(mail.foxconn.com|foxconn.com|fii-foxconn.com)$/;
 var patt1=new RegExp(regex);
 var result = patt1.test(email);
 if(!result){
     alert("郵箱的格式不正確!");
     obj.value="";
     // obj.focus();
     obj.value =temp;
     return;
 };
}
// 驗證數字 onkeyup="this.value=this.value.replace(/[^\d]/g,'') "
// onafterpaste="this.value=this.value.replace(/[^\d]/g,'') "
//大於零的正整數
function onlyNumber(o) {
     if (o.value.length==1 )
	 {
		o.value=o.value.replace(/[^1-9]/g,''); 
	 }
     else
	 {
    	 o.value=o.value.replace(/\D/g,'');
	 }
 
 }

//大於等於零的正整數
function onlyNumberZero(o) {
     if (o.value.length==1 )
	 {
		o.value=o.value.replace(/[^0-9]/g,''); 
	 }
     else
	 {
    	 o.value=o.value.replace(/\D/g,'');
	 }
 
 }
function valdMoney(obj){
 var money = obj.value;
 var temp = money;
 if(money=="")return;
 var regex = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
 var patt1=new RegExp(regex);
 var result = patt1.test(money);
 if(!result){
     alert("格式不正確!");
     obj.value="";
     // obj.focus();
     obj.value =temp;
     return;
 };
}
// 根據工號查出當前申請人的信息
function queryUserInfo() {
 var empno = $.trim($("#UserNo").val());
 if (empno != null && empno != "") {
     $.ajax({
         url: ctx+'/system/user/getUserInfo/',
         type: 'POST',
         beforeSend: ajaxLoading,
         // dataType: 'json',
         data: {empno: empno},
         success: function (data) {
             ajaxLoadEnd();
             if (!data) {
                 $.messager.alert("溫馨提示", "工號不存在", "error");
           
                 $('#dptQun').val('');
                 
             } else {
                
                 $('#dptQun').val(data.dptQun);
                //  if (data.dptQun=='IPEBG')
                //  {
                // 	 $("#IDPBG").hide();
                //      $("#PUBLICAREA").hide();
                //  }
                //  else if (data.dptQun=='IDPBG'){
                // 	 $("#IPEBG").hide();
                // 	 $("#PUBLICAREA").hide();
                // }
                //  else {
                // 	 $("#IPEBG").hide();
                // 	 $("#IDPBG").hide();
                //  }
             }
         }
     });
 }
}
// 普通文件上傳
function uploadFile() {
 var attachIds = $.trim($("#attachids").val());
 var attachIdsL = attachIds.split(",").length;
 if (attachIdsL >= 6) {
     $.messager.alert("溫馨提示", "上傳附件個數不能超過五個！", "info");
     return;
 }
 var explorer = window.navigator.userAgent;
 if (!!window.ActiveXObject || "ActiveXObject" in window) {
     ajaxLoading();
     $.ajaxFileUpload({  // Jquery插件上传文件
         url: ctx + "/admin/uploadCompatible",
         secureuri: false,// 是否启用安全提交 默认为false
         fileElementId: "attachidsUpload", // type="file"的id
         dataType: "JSON",  // 返回值类型
         success: function (msg, status) {
             msg = jQuery.parseJSON(msg);
             $("#dowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href=' + ctx + '/admin/download/' + msg.id + ' target="_blank">' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delAtt(\'' + msg.id + '\')"></div></div>');
             $("#attachids").val($("#attachids").val() + msg.id + ",");
             ajaxLoadEnd();
         },
         error: function (data, status, e) {
             ajaxLoadEnd();
         }
     });
 }else{
// 创建
     var form_data = new FormData();
     // 获取文件
// var file = document.getElementById('fileToUpload').files[0];
     var file_data = $("#attachidsUpload").prop("files")[0];
     form_data.append("file", file_data);
     form_data.append("path", "upload");
     // 把所以表单信息
     $.ajax({
         type: "POST",
         url: ctx+"/admin/upload",
         beforeSend: ajaxLoading,
        dataType : "json",
         processData: false,  // 注意：让jQuery不要处理数据
         contentType: false,  // 注意：让jQuery不要设置contentType
         data: form_data
     }).success(function (msg) {
         ajaxLoadEnd();
         $("#dowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href='+ctx+'/admin/download/' + msg.id + ' target="_blank">' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delAtt(\'' + msg.id + '\')"></div></div>');
         $("#attachids").val($("#attachids").val() + msg.id + ",");
         // $("#attachids").val(msg.id);
     }).fail(function (msg) {
         ajaxLoadEnd();
     });
 }
}



function delAtt(fileid) {
 $("#" + fileid).remove();
 var kk = $("#attachids").val();
 kk = kk.replace(fileid + ",", "");
 $("#attachids").val(kk);
 ajaxLoading();
 $.post(ctx + '/admin/delete/' + fileid, {}, function (data) {
     successTip(data);
     ajaxLoadEnd();
 }, 'text').error(function() { ajaxLoadEnd(); });
}

function reUploadFile() {
 var attachIds = $.trim($("#reattachids").val());
 var attachIdsL = attachIds.split(",").length;
 if (attachIdsL >= 6) {
     alert("上傳附件個數不能超過五個！");
     return;
 }
 // 创建
 var form_data = new FormData();
 // 获取文件
// var file = document.getElementById('fileToUpload').files[0];
 var file_data = $("#attachidsUpload").prop("files")[0];
 form_data.append("file", file_data);
 form_data.append("path", "test");
 // 把所以表单信息
 $.ajax({
     type: "POST",
     url: ctx+"/admin /upload",
     beforeSend: ajaxLoading,
// dataType : "json",
     processData: false,  // 注意：让jQuery不要处理数据
     contentType: false,  // 注意：让jQuery不要设置contentType
     data: form_data
 }).success(function (msg) {
     ajaxLoadEnd();
     $("#reDowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href='+ctx+'"/admin /download/' + msg.id + '" target="_blank">' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delReAtt(\'' + msg.id + '\')"></div></div>');
     $("#reattachids").val($("#reattachids").val() + msg.id + ",");
     // $("#attachids").val(msg.id);
 }).fail(function (msg) {
 });
}

function delReAtt(fileid) {
 $("#" + fileid).remove();
 var kk = $("#reattachids").val();
 kk = kk.replace(fileid + ",", "");
 $("#reattachids").val(kk);
 $.post(ctx+'/admin /delete/' + fileid, {}, function (data) {
     successTip(data);
 });
}

// 加載保安公司
function loadCompany(){
	 $.ajax({
	     url: ctx+"/system/dict/getDictByType/guard_securityCom",
	     type: "GET",
	     success: function (data) {
	         var themecombo3 = [{'label': '請選擇', 'value': ''}];
	         for (var i = 0; i < data.length; i++) {
	             themecombo3.push({"label": data[i].label, "value": data[i].value});
	         }
	         
	             $("#securityCom1").combobox("loadData", themecombo3);
	     },
	     error: function (error) {
	         alert("初始化下拉控件失败");
	     }
	 });	
}

// 加載區域
function loadArea(flag, resetSubLevel) {
	$.ajax({
		url: ctx + "/system/dict/getDictByType/guard_area",
		type: "GET",
		success: function (data) {
			var themecombo2 = [{'label': '請選擇', 'value': ''}];
			for (var i = 0; i < data.length; i++) {
				themecombo2.push({"label": data[i].label, "value": data[i].value});
			}
			$("#area" + flag).combobox("loadData", themecombo2);
			if (resetSubLevel != undefined) {
				$('#block' + flag).combobox('setValue', '');
				$('#floor' + flag).combobox('setValue', '');
			}
		},
		error: function (error) {
			alert("初始化下拉控件失败");
		}
	});
}

// 加載樓棟
function loadBlock(flag, resetSubLevel) {
	var area = $('#area' + flag).combobox('getValue');
	$.ajax({
		url: ctx + "/system/dict/getDictByType/guard_block",
		type: "GET",
		success: function (data) {
			var themecombo2 = [{'label': '請選擇', 'value': ''}];
			for (var i = 0; i < data.length; i++) {
				if (data[i].remark == area) {
					themecombo2.push({"label": data[i].label, "value": data[i].value});
				}
			}
			$("#block" + flag).combobox("loadData", themecombo2);
			if (resetSubLevel != undefined) {
				$('#block' + flag).combobox('setValue', '');
				$('#floor' + flag).combobox('setValue', '');
			}
		},
		error: function (error) {
			alert("初始化下拉控件失败");
		}
	});
}
// 加載樓層
function loadFloor(idx) {
	debugger;
	var block = $('#block' + idx).combobox('getValue');
	$.ajax({
		url: ctx + "/system/dict/getDictByType/guard_floor",
		type: "GET",
		success: function (data) {
			var themecombo2 = [{'label': '請選擇', 'value': ''}];
			for (var i = 0; i < data.length; i++) {
				if (data[i].remark == block) {
					themecombo2.push({"label": data[i].label, "value": data[i].value});
				}
			}
			$("#floor" + idx).combobox("loadData", themecombo2);
		},
		error: function (error) {
			alert("初始化下拉控件失败");
		}
	});
}

// 刪除table處表頭之外的所有行
function deleteAllRow(tableName){
//console.log('8888');
 var table = document.getElementById(tableName);
 var len = table.rows.length;
 for(var i = len-1;i > 0;i--){
     table.deleteRow(i);
 }
}
// 格式化日期
function crtTimeFtt(val) {
 if (val != null) {
     var date = new Date(val);
     return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
 }
}

function kanchaCheck(){
	var manpowerDemand = $.trim($('input[id="manpowerDemand"]:checked').val());	
	var safeManagement = $.trim($('input[id="safeManagement"]:checked').val());	
	if (manpowerDemand=='1' || safeManagement=='1')
	{
		$("#btnPass").linkbutton('disable');		
	}
	else
	{
		$("#btnPass").linkbutton('enable');
	}
	
}

// 增崗
function addPost(){
	$("#add").attr("disabled", false);
	$("#reattachids").val("");
	deleteAllRow("buildprojectApplyItemTable");
	// document.getElementById("operation").style.visibility= "visible";
}

// 崗位異動
function cancelPost(){
	deleteAllRow("buildprojectApplyItemTable");
	$("#add").attr("disabled", true);
	selectPost();
	// document.getElementById("operation").style.visibility= "hidden";
}

// 臨時增撤崗
function tempModify() {
	$("#add").attr("disabled", true);
	$("#reattachids").val("");
	deleteAllRow("buildprojectApplyItemTable");
	// document.getElementById("operation").style.visibility= "visible";
	showTempModifyImportPanel();
}

// 顯示臨時撤崗批量導入頁面
function showTempModifyImportPanel() {
	var optionWin = $("#optionWin").window();

	optionWin.window({
		center : true
	});
	// 顯示於畫面中
	optionWin.slideDown();
	// 清空optionWin頁面中所有元件的值
	$("#batchImportForm").form("clear");
	$("#labelListAddResult").html("");
	$("#downloadError").hide();
	optionWin.window("open");
}

//下一單
function next(serialno)
{
	var json="";
	var nextTask = $("#mytask").val();
	if (nextTask=="")
	{
		$.messager.alert("溫馨提示", "已經是最後一張單了！", "info");
		return ;
		}
	var urlFree = $("#urlFree").val();
	var auditType = $("#auditType").val();
	if (auditType=="auditMail")
    {
		window.location.href=urlFree; 
	}else
	{
		window.location.href=ctx+'/eguardaply/audit/'+nextTask;  
	};
}

//上一單
function previous(serialno)
{
	var json="";
	var previousTask = $("#previousTask").val();
	if (previousTask=="")
	{
		$.messager.alert("溫馨提示", "已經是第一張單了！", "info");
		return ;
		}
	var urlFreePrevious = $("#urlFreePrevious").val();
	var auditType = $("#auditType").val();
	if (auditType=="auditMail")
    {
		window.location.href=urlFreePrevious; 
	}else
	{
		window.location.href=ctx+'/eguardaply/audit/'+previousTask;  
	};
}

//選擇撤崗崗位
function selectPost() {    
    d = $("#win").dialog({
        title: '撤崗崗位選擇',
        width: 1200,
        height: 800,
        async: false,
        href: ctx + '/bspost/cancelPostList',
        maximizable: false,
        modal: true,
        buttons: [{
            text: '確定',
            handler: function () {
                var rows = $('#cancelPostList').datagrid('getSelections');
                // var ids = "";
                layoutPostList(rows, "1");
                // ids=ids.substring(0,ids.length-1);
            	// $("#reattachids").val(ids);
                d.panel('close');
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

// 在頁面上佈局崗位列表(用於崗位異動和臨時增撤銷崗)
function layoutPostList(postList, requirememtType) {
	originSelectedPostList = postList;
	for (var i = 1, j = postList.length; i < j+1; i++) {
		// ids=ids+rows[i-1].recno+',';
		var trText = "<tr align='center' id='Item"+i+"'>        "
			+"	<td>"+i+" <input  id='recno"+i+"' name='eGuardItemAplyEntity["+(i-1)+"].recno'  value='"+ postList[i-1].recno+"' type='hidden' /></td>  "
			+"	<td><input id='area"+i+"'" + (requirememtType === "2" ? " disabled " : "")
			+"		name='eGuardItemAplyEntity["+(i-1)+"].area'                              "
			+"		class='easyui-combobox rayOptionitemclass' data-tr-index='"+i+"' data-options=\"required:true, panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadArea(this.dataset.trIndex);},onSelect:function(){loadBlock(this.dataset.trIndex, true);}\"             "
			+"		style='width:80px;' value='"+ postList[i-1].area+"' /></td>             "

			+"	<td><input id='block"+i+"'" + (requirememtType === "2" ? " disabled " : "")
			+"		name='eGuardItemAplyEntity["+(i-1)+"].block'                             "
			+"		class='easyui-combobox rayOptionitemclass' data-tr-index='"+i+"' data-options=\"required:true, panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadBlock(this.dataset.trIndex);},onSelect:function(){loadFloor(this.dataset.trIndex, true);}\"             "
			+"		style='width:80px;' value='"+ postList[i-1].block+"' /></td>             "

			+"	<td><input id='floor"+i+"'" + (requirememtType === "2" ? " disabled " : "")
			+"		name='eGuardItemAplyEntity["+(i-1)+"].floor'                             "
			+"		class='easyui-combobox rayOptionitemclass' data-tr-index='"+i+"' data-options=\"required:true, panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadFloor(this.dataset.trIndex);}\"             "
			+"		style='width:80px;' value='"+ postList[i-1].floor+"' /></td>             "

			+"	<td><input id='position"+i+"'" + (requirememtType === "2" ? " disabled " : "")
			+"		name='eGuardItemAplyEntity["+(i-1)+"].position'                          "
			+"		class='easyui-combobox rayOptionitemclass' data-options=\"required:true, panelHeight:'auto',valueField:'value', textField:'label',url: ctx+'/system/dict/getDictByType/guard_position'\"             "
			+"		style='width:80px;' value='"+ postList[i-1].position+"' /></td>          "

			+"	<td><input id='postType"+i+"'" + (requirememtType === "2" ? " disabled " : "")
			+"		name='eGuardItemAplyEntity["+(i-1)+"].postType'                          "
			+"		class='easyui-combobox rayOptionitemclass' data-options=\"required:true, panelHeight:'auto',valueField:'value', textField:'label',url: ctx+'/system/dict/getDictByType/guard_postType'\"             "
			+"		style='width:80px;' value='"+ postList[i-1].postType+"' /></td>          "
			+"	<td><input id='postShift"+i+"'"
			+"		name='eGuardItemAplyEntity["+(i-1)+"].postShift'                         "
			+"		class='easyui-combobox rayOptionitemclass' data-options=\"required:true, panelHeight:'auto',valueField:'value', textField:'label',url: ctx+'/system/dict/getDictByType/guard_postShift'\"             "
			+"		style='width:80px;' value='"+ postList[i-1].postShift+"' /></td>         "

			+"	<td><input id='postName"+i+"'" + (requirememtType === "2" ? " readonly " : "")
			+"		name='eGuardItemAplyEntity["+(i-1)+"].postName'                          "
			+"		class='easyui-validatebox' required='true'            "
			+"		style='width:100px;' value='"+ postList[i-1].postName+"' "+ (requirememtType === "1" ? "onblur='isExistPostName("+i+");'" : "") +"/></td>          "

			+"	<td><input   id='postPerNu"+i+"'  onkeyup=onlyNumberZero(this) onafterpaste=onlyNumberZero(this) onblur='onlyNumberZero(this)'                                         "
			+"		name='eGuardItemAplyEntity["+(i-1)+"].postPerNu'"
			+"		class='easyui-validatebox' data-options='required:true'             "
			+"		style='width:80px;' value='"+ postList[i-1].postPerNu+"' /></td>         ";
		if (requirememtType === "2") {
			trText += "	<td><input id='postStartDate"+i+"'                                       "
				+"		name='eGuardItemAplyEntity["+(i-1)+"].postStartDate'                     "
				+"		value='"+ postList[i-1].postStartDate.substring(0,10)+"'        "
				+"		class='easyui-my97'                            "
				+"		datefmt='yyyy-MM-dd' minDate='%y-%M-%d' data-options='width: 100,required:true' />"
				+"~<input id='postEndDate"+i+"'                                       "
				+"		name='eGuardItemAplyEntity["+(i-1)+"].postEndDate'                     "
				+"		value='"+ postList[i-1].postEndDate.substring(0,10)+"'        "
				+"		class='easyui-my97'                            "
				+"		datefmt='yyyy-MM-dd' minDate='%y-%M-%d' data-options='width: 100,required:true' />"
				+"  </td>                     ";
		} else {
			trText += "	<td><input id='postStartDate"+i+"'" + (requirememtType === "2" ? " readonly " : "")
				+"		name='eGuardItemAplyEntity["+(i-1)+"].postStartDate'                     "
				+"		value='"+ postList[i-1].postStartDate.substring(0,10)+"'        "
				+"		class='easyui-my97'                            "
				+"		datefmt='yyyy-MM-dd' minDate='%y-%M-%d' data-options='width: 100,required:true' />"
				+"  </td>                     ";
		}
		trText += "<td><input type='image' src='"+ctx+"/static/images/deleteRow.png' onclick='deltr("+i+");return false;'/></td>"
			+"</tr>";
		$("#buildprojectApplyItemTable").append(trText);
		$.parser.parse($("#Item" + i));
	}
}

//是否存在崗位
function isExistPostName(flag) {
	var postName = $.trim($("#postName" + flag).val());
	var recno = "";
	var requirementType = $('input:radio[name="eGuardAplyEntity.requirememtType"]:checked').val();
	if (requirementType === "1") {
		recno = $("#recno" + flag).val();
	}
	debugger;
	//var postName = $.trim(postNameObject.value);
	$.ajax({//
		url: ctx + '/eguarditemaply/isExistPostName?postName=' + postName + '&recno' + recno,
		date: '',
		cache: false,
		async: false,
		type: 'POST',
		timeout: 100000,
		error: function (xml) {
			// alert('响应失败！');
		},
		success: function (xml) {
			if (xml == 'yes') {
				$.messager.alert("溫馨提示", "“" + postName + "”崗位名稱已經存在！", "info");
				$("#postName" + flag).val('');
				//postNameObject.value="";
			} else {
				var num = $("#buildprojectApplyItemTable tr").length;

				for (var i = 1; i < num; i++) {
					if ($("#postName" + i).val() == postName && i != flag && postName != "") {
						alert("與第" + i + "行崗位信息重複！");
						$("#postName" + flag).val('');
						return false;
					}
				}
			}
		}
	});
}

// 批量導入模板下載
function batchImportTpl() {
	document.location.href = ctx + '/eguardaply/downLoad/batchImportTpl';
}

//導入
function btnUploadExcel() {
	var file_data = $("#batchFile").prop("files")[0];
	if (file_data.name == "") {
		$.messager.alert('系統提示', '請選擇文件，再進行導入！', 'info');
		return;
	}
	var suffix = /\.[^\.]+$/.exec(file_data.name);
	var isExcel2003 = "";
	if (suffix == ".xls") {
		isExcel2003 = "true";
	} else if (suffix == ".xlsx") {
		isExcel2003 = "false";
	} else {
		$.messager.alert('系統提示', '導入文件格式不正確！', 'info');
		return;
	}
	// 创建
	var form_data = new FormData();
	// 获取文件
	form_data.append("file", file_data);
	form_data.append("isExcel2003", isExcel2003);

	// 把所以表单信息
	$.ajax({
		type : "POST",
		url : ctx + "/eguardaply/upload",
		// dataType : "json",
		processData : false, // 注意：让jQuery不要处理数据
		contentType : false, // 注意：让jQuery不要设置contentType
		data : form_data
	}).success(function(ret) {
		// console.log(msg)
		if (ret.code == 1) {
			// 導入成功
			var optionWin = $("#optionWin").window();
			optionWin.window("close");
			// 添加崗位到頁面上
			layoutPostList(ret.data, "2");
		} else {
			$("#labelListAddResult").html(ret.msg);
			if (ret.code == -1) {
				$("#downloadError").show();
			} else {
				$("#downloadError").hide();
			}
		}
	}).fail(function(msg) {
		$("#labelListAddResult").html("導入失敗");
	});
}
