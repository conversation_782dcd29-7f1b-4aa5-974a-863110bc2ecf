package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.entity.TestitemEntity;
import com.foxconn.ipebg.basics.dao.TestitemDao;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;

import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.Dict;


import com.foxconn.ipebg.system.service.DictService;

import java.util.Date;
import java.util.List;

/**
 * 水質檢測項目維護
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-12-26 08:57:03
 */
@Service
@Transactional(readOnly=true)
public class  TestitemService extends BaseBusinessService<TestitemEntity, String>{
    @Autowired
    private TestitemDao testitemDao;

    @Autowired
    private UserServiceUtil serviceUtil;
    @Autowired
    private DictService dictService;

    @Override
    public HibernateDao<TestitemEntity, String> getEntityDao() {
        return testitemDao;
    }

    public TestitemEntity findBySerialno(String serialno) {
        return this.testitemDao.findUniqueBy("serialno", serialno);
    }
    //下拉框
	public List<TestitemEntity> getTestItemsByType(String sampleTyp) {
	      return testitemDao.findAllByOrder("sampleTyp",sampleTyp,"testItems",true);
		 //return testitemDao.findAll("testItems",true);
		}
	 //帶出單價
	public List<TestitemEntity> getTestCost(String sampleTyp,String testItems) {
		      return testitemDao.find("from TestitemEntity t where t.sampleTyp=?0 and t.testItems=?1",sampleTyp,testItems);
			}
    
}
