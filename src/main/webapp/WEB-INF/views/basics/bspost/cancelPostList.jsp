<%--suppress ALL --%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title></title>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
	type="text/javascript"></script>
</head>
<body>
	<div>
	<form id="searchFrom" action="">
<!-- 		<input type="text" name="filter_EQS_dealno" class="easyui-validatebox"
			   data-options="width:150,prompt: '承辦人工號'"/>
		<input type="text" name="filter_EQS_dealdeptno" class="easyui-validatebox"
			   data-options="width:150,prompt: '承辦人單位代碼'"/> -->
		 <input type="text" id="serialnoQry" name="serialnoQry" class="easyui-validatebox"
			   data-options="width:150,prompt: '崗位編碼'"/>
<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
	</form>
	</div>
	<table id="cancelPostList"></table>
	<script type="text/javascript">
		var dg; 
		var d;
		$(function() {
			var serialnoQry=$('#serialnoQry').val();
			var param = {
				"filter_EQS_recno" : serialnoQry
			};
			var isCheckSort = false;
			dg = $('#cancelPostList').datagrid(
					{
						method : "get",
						url : '${ctx}/bspost/getCancelPostList',
						queryParams : param,
						nowrap : false,
						fit : true,
						fitColumns : true,
						border : false,
						striped : true,
						pagination : false,
						remoteSort : false,
						onClickRow : function(index, row) {
							row.num == 1;
						},
						checkOnSelect : true,
						columns : [ [ {
							field : 'itemid',
							title : 'Order ID',
							width : 200,
							checkbox : true
						},{
							field : 'id',
							title : '主鍵',
							hidden : true
						},/*  {
							field : 'serialno',
							title : '任務單號',
							sortable : true,
							width : 150
						}, */ {
							field : 'recno',
							title : '崗位編碼',
							sortable : true,
							width : 150
						}, {
							field : 'area',
							title : '區域',
							sortable : true,
							width : 50
						}, {
							field : 'block',
							title : '棟',
							sortable : true,
							width : 50
						}, {
							field : 'floor',
							title : '層',
							sortable : true,
							width : 80
						}, {
							field : 'position',
							title : '方位',
							sortable : true,
							width : 100
						}, /* {
							field : 'location',
							title : '詳細位置',
							sortable : true,
							width : 100
						}, */ {
							field : 'postType',
							title : '崗位類別',
							sortable : true,
							width : 100
						}, {
							field : 'postShift',
							title : '班制',
							sortable : true,
							width : 100
						}, {
							field : 'postName',
							title : '崗位名稱',
							sortable : true,
							width : 100
						}, {
							field : 'postPerNu',
							title : '人數(不含調休)',
							sortable : true,
							width : 50
						}, {
							field : 'postStartDate',
							title : '需求-開始時間',
							sortable : true,
							width : 100
						}, {
							field : 'postEndDate',
							title : '需求-結束時間',
							sortable : true,
							width : 100
						}, {
							field : 'securityCom',
							title : '派駐保安公司',
							sortable : true,
							width : 100
						},  {
							field : 'postEffectDate',
							title : '生效時間',
							sortable : true,
							width : 60
						}] ],
						onClickRow : function(rowIndex, rowData) {
							//            console.log("onClickRow11111111");
							if (!isCheckSort) {
								//sortByClick('cancelPostList', rowIndex);
								isCheckSort = false;
							}
						},
						onCheck : function(rowIndex, rowData) {
							var isSelected = false;
							var selectItems = dg.datagrid('getSelections');
							for ( var i = 0; i < selectItems.length; i++) {
								var index = dg.datagrid('getRowIndex',
										selectItems[i]);
								// console.log(rowIndex);
								if (rowIndex == index) {
									isSelected = true;
								}
							}
							var dr = dg.datagrid('getRows')[rowIndex];
							if ((undefined == dr["num"] || "" == dr["num"])
									&& isSelected) {
								//sortByClick('cancelPostList', rowIndex);
								isCheckSort = true;
							} else {
								isCheckSort = false;
							}
						},
						onUncheck : function(rowIndex, rowData) {
							//            console.log("onUncheck111111111111");
							var isSelected = false;
							var selectItems = dg.datagrid('getSelections');
							for ( var i = 0; i < selectItems.length; i++) {
								var index = dg.datagrid('getRowIndex',
										selectItems[i]);
								// console.log(rowIndex);
								if (rowIndex == index) {
									isSelected = true;
								}
							}
							var dr = dg.datagrid('getRows')[rowIndex];
							if ((undefined == dr["num"] || "" != dr["num"])
									&& !isSelected) {
								//sortByClick('cancelPostList', rowIndex);
								isCheckSort = true;
							} else {
								isCheckSort = false;
							}
						},
						onUncheckAll : function(rowIndex, rowData) {
							var alllRows = dg.datagrid('getRows');
							for ( var i = 0; i < alllRows.length; i++) {
								alllRows[i]["num"] = "";
								dg.datagrid('refreshRow', i);
							}
						},
						onSelectAll : function(rowIndex, rowData) {
							var alllRows = dg.datagrid('getRows');
							for ( var i = 0; i < alllRows.length; i++) {
								alllRows[i]["num"] = i + 1;
								dg.datagrid('refreshRow', i);
							}
						}
					});
		}

		);
// 创建查询对象并查询
function cx() {
	var obj = $("#searchFrom").serializeObject();
	dg.datagrid('load', obj);
}
	</script>
</body>
</html>