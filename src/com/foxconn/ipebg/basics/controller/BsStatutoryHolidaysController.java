package com.foxconn.ipebg.basics.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Date;

import com.foxconn.ipebg.basics.entity.BsArrangeEntity;
import com.foxconn.ipebg.common.utils.ConvertUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.basics.entity.BsStatutoryHolidaysEntity;
import com.foxconn.ipebg.basics.service.BsStatutoryHolidaysService;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;


/**
 * 法定節假日表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-17 09:33:40
 */
@Controller
@RequestMapping("bsstatutoryholidays")
public class BsStatutoryHolidaysController extends BaseController {

    @Autowired
    private BsStatutoryHolidaysService bsStatutoryHolidaysService;
   
    /**
      * 方法描述: 列表信息
      * @Author: S6114894
      * @CreateDate:   2019-04-17 09:33:40
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:bsstatutoryholidays:list")
    public String list() {
        //查询列表数据
        return "basics/bsstatutoryholidays/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6114894
      * @CreateDate:   2019-04-17 09:33:40
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:bsstatutoryholidays:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<BsStatutoryHolidaysEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        page = bsStatutoryHolidaysService.search(page, filters);
        return getEasyUIData(page);
    }
    /**
     * 方法描述: 保存
     * @Author: S6114894
     * @CreateDate:   2019-04-17 09:33:40
     * @Return
     **/
   
    @RequestMapping(value="create", method = RequestMethod.POST)
//    @RequiresPermissions("basics:bsstatutoryholidays:add")
    @ResponseBody
    public String create(@Valid BsStatutoryHolidaysEntity bsStatutoryHolidays, Model model) {
        bsStatutoryHolidays.setNewRecord(true);
    	bsStatutoryHolidays.setCreateDate(new Date());
    	bsStatutoryHolidays.setCreateBy(UserUtil.getCurrentUser().getLoginName());
        bsStatutoryHolidaysService.save(bsStatutoryHolidays);
        return "success";
    }
    
    /**
      * 方法描述: 添加跳轉
      * @Author: S6114894
      * @CreateDate:   2019-04-17 09:33:40
      * @Return
      **/

//    @RequiresPermissions("basics:bsstatutoryholidays:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("bsStatutoryHolidays", new BsStatutoryHolidaysEntity());
        model.addAttribute("action", "create");
        return "basics/bsstatutoryholidays/listForm";
    }
    
     /**
     * 方法描述: 修改跳轉
     * @Author: S6113712
     * @CreateDate:   2018-10-06 11:04:39
     * @Return
     **/

//   @RequiresPermissions("basics:bsstatutoryholidays:update")
   @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
   public String updateForm(@PathVariable("id") String id, Model model) {
       model.addAttribute("bsStatutoryHolidays", bsStatutoryHolidaysService.get(id));
       model.addAttribute("action", "update");
      return "basics/bsstatutoryholidays/listForm";
   }
   
	/**
	 * 方法描述: 修改
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-11 10:55:00
	 * @Return
	 **/

   @RequestMapping(value = "update", method = RequestMethod.POST)
//   @RequiresPermissions("basics:bsstatutoryholidays:update")
   @ResponseBody
   public String update(@Valid BsStatutoryHolidaysEntity bsStatutoryHolidays,@RequestParam(value = "ids") String ids) {
       bsStatutoryHolidays.setId(ids);
	   bsStatutoryHolidays.setUpdateDate(new Date());
	   bsStatutoryHolidays.setUpdateBy(UserUtil.getCurrentUser().getLoginName());
	   bsStatutoryHolidaysService.update(bsStatutoryHolidays);
       return "success";
   }
    /**
      * 方法描述: 根據主鍵刪除
      * @Author: S6114894
      * @CreateDate:   2019-04-17 09:33:40
      * @Return
      **/

    @RequestMapping("delete/{id}")
//    @RequiresPermissions("basics:bsstatutoryholidays:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        bsStatutoryHolidaysService.delete(id);
        return "success";
    }
    /**
      * 导出excel
      * @Author: S6114894
      * @CreateDate:   2019-04-17 09:33:40
      * @Return
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {

        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        Page<BsStatutoryHolidaysEntity> page = getPage(request);
        page = bsStatutoryHolidaysService.search(page, filters);

        List<BsStatutoryHolidaysEntity> listFormQuery = page.getResult();
        ConvertUtils.confertDateToStanded(listFormQuery);// 日期格式化
        if (listFormQuery.size() > 0) {
            // 導出excel2007
            LinkedHashMap<String, String> fieldMap = new LinkedHashMap<String, String>();
            fieldMap.put("holidayDate", "日期");
            fieldMap.put("holidayDesc", "描述");
            fieldMap.put("createBy", "創建人");
            fieldMap.put("createDate", "創建時間");

            String name = "法定假日時間表";
            ExcelUtil.listToExcel(listFormQuery, fieldMap, name, response);
        }
    }
}
