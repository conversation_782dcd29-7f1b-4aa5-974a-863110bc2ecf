package com.foxconn.ipebg.basics.controller;

import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import com.foxconn.ipebg.basics.entity.PerInfoEntity;
import com.foxconn.ipebg.basics.service.PerInfoService;
import com.foxconn.ipebg.basics.vo.BsTempServiceVO;
import com.foxconn.ipebg.system.service.DictService;
import com.google.common.collect.Lists;
import org.apache.poi.ss.formula.functions.T;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.basics.entity.BsTempServiceEntity;
import com.foxconn.ipebg.basics.service.BsTempServiceService;
import com.foxconn.ipebg.system.utils.UserUtil;


/**
 * 臨時勤務信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-04 09:10:27
 */
@Controller
@RequestMapping("bstempservice")
public class BsTempServiceController extends BaseController {

    @Autowired
    private BsTempServiceService bsTempServiceService;
    @Autowired
    private PerInfoService perInfoService;
    @Autowired
    private DictService dictService;
    /**
      * 方法描述: 列表信息
      * @Author: S6114894
      * @CreateDate:   2019-05-04 09:10:27
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:bstempservice:list")
    public String list() {
        //查询列表数据
        return "basics/bstempservice/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6114894
      * @CreateDate:   2019-05-04 09:10:27
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:bstempservice:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<BsTempServiceEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        page = bsTempServiceService.search(page, filters);
        return getEasyUIData(page);
    }
    /**
     * 方法描述: 保存
     * @Author: S6114894
     * @CreateDate:   2019-05-04 09:10:27
     * @Return
     **/
   
    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("basics:bstempservice:add")
    @ResponseBody
    public String create(@Valid BsTempServiceVO bsTempServiceVO) {
        BsTempServiceEntity tempService = bsTempServiceVO.getTempService();
        PerInfoEntity[] perInfoArr = bsTempServiceVO.getPerInfoArr();
        if(perInfoArr!=null && perInfoArr.length>0){
            tempService.setExpenses(tempService.getExpenses()*100);
            tempService.setCreateBy( UserUtil.getCurrentUser().getLoginName());
            tempService.setCreateDate(new Date());
            bsTempServiceService.save(tempService);
            for(PerInfoEntity entity:perInfoArr){
                bsTempServiceService.savePerinfo(tempService.getId(),entity.getId());
            }
            return "success";
        }
        return "false";
    }
    
    /**
      * 方法描述: 添加跳轉
      * @Author: S6114894
      * @CreateDate:   2019-05-04 09:10:27
      * @Return
      **/

    @RequiresPermissions("basics:bstempservice:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("bsTempService", new BsTempServiceEntity());
        model.addAttribute("action", "create");
        return "basics/bstempservice/batchAddForm";
    }
    
     /**
     * 方法描述: 修改跳轉
     * @Author: S6113712
     * @CreateDate:   2018-10-06 11:04:39
     * @Return
     **/

   @RequiresPermissions("basics:bstempservice:update")
   @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
   public String updateForm(@PathVariable("id") String id, Model model) {
       model.addAttribute("bsTempService", bsTempServiceService.get(id));
       model.addAttribute("action", "update");
      return "basics/bstempservice/batchAddForm";
   }
   
	/**
	 * 方法描述: 修改
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-11 10:55:00
	 * @Return
	 **/

   @RequestMapping(value = "update", method = RequestMethod.POST)
   @RequiresPermissions("basics:bstempservice:update")
   @ResponseBody
   public String update(@Valid BsTempServiceVO bsTempServiceVO,@RequestParam(value = "ids") String ids) {
       BsTempServiceEntity tempService = bsTempServiceVO.getTempService();
       PerInfoEntity[] perInfoArr = bsTempServiceVO.getPerInfoArr();
       if(perInfoArr!=null && perInfoArr.length>0){
           tempService.setId(ids);
           tempService.setExpenses(tempService.getExpenses()*100);
           tempService.setUpdateDate(new Date());
           tempService.setUpdateBy( UserUtil.getCurrentUser().getLoginName());
           bsTempServiceService.update(tempService);
           bsTempServiceService.deletePerinfoByBtsId(tempService.getId());
           for(PerInfoEntity entity:perInfoArr){
               bsTempServiceService.savePerinfo(tempService.getId(),entity.getId());
           }
           return "success";
       }
       return "false";
   }
    /**
      * 方法描述: 根據主鍵刪除
      * @Author: S6114894
      * @CreateDate:   2019-05-04 09:10:27
      * @Return
      **/

    @RequestMapping("delete/{id}")
    @RequiresPermissions("basics:bstempservice:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        bsTempServiceService.delete(id);
        bsTempServiceService.deletePerinfoByBtsId(id);
        return "success";
    }
    /**
      * 导出excel
      * @Author: S6114894
      * @CreateDate:   2019-05-04 09:10:27
      * @Return
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {}

    /**
     * 查詢可分配人員
     * @param model
     * @return
     */
    @RequestMapping(value = "initDatas",method = RequestMethod.POST)
    //@RequiresPermissions("basics:ftestresultapply:list")
    @ResponseBody
    public Map<String, Object> initDatas(@RequestParam Map<String,Object> params) {
        Map<String, Object> result = new HashMap<>();
        //查詢可安排臨時勤務人員
        if(params.get("startTime")==null || params.get("endTime")==null){
            result.put("personArr", Lists.newArrayList());
        }else {
            result.put("personArr", perInfoService.listTempService(params));
        }
        //查詢企業法人
        if(params.get("dictType")==null){
            result.put("legalPersonDicts", Lists.newArrayList());
        }else{
            result.put("legalPersonDicts", dictService.getDictByType(params.get("dictType").toString()));
        }

        //查询列表数据
        return result;
    }

    /**
     * 條件查詢臨時勤務人員
     * @param params
     * @return
     */
    @RequestMapping(value = "listPerinfo",method = RequestMethod.GET)
    @ResponseBody
    public Map<String, Object> listPerinfo(@RequestParam Map<String,Object> params) {
        Map<String, Object> result = new HashMap<>();
        if(params.get("btsId")==null){
            return result;
        }
        result.put("rows",bsTempServiceService.listPerinfo(params));
        return result;
    }
}
