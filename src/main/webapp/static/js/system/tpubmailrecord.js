var dg;
var d;
$(function () {
    dg = $('#dg').datagrid({
        method: "get",
        url: ctx + '/tpubmailrecord/list',
        fit: true,
        fitColumns: true,
        border: false,
        idField: 'id',
        striped: true,
        pagination: true,
        rownumbers: true,
        pageNumber: 1,
        pageSize: 20,
        pageList: [10, 20, 30, 40, 50],
        singleSelect: true,
        columns: [[
            {field: 'id', title: '主鍵', hidden: true},
            {field: 'ordertype', title: '表單類型', sortable: true, width: 150},
            {field: 'serialno', title: '任務編號', sortable: true, width: 130},
            {field: 'username', title: '用戶', sortable: true, width: 50},
            {field: 'chargerman', title: '駁回主管', sortable: true, width: 50},
            {field: 'dusername', title: '審核主管', sortable: true, width: 50},
            {field: 'orderstatus', title: '表單狀態', sortable: true, width: 40},
            {
                field: 'sendStatus', title: '發送狀態', sortable: true, width: 40,
                formatter: function (value, row, index) {
                    if (value == '0') {
                        return "待發送";
                    } else {
                        return "已發送";
                    }
                }
            },
            {field: 'usermail', title: '接收郵箱', sortable: true, width: 200},
            /*,
            {field: 'validStr', title: '驗證字符串', sortable: true, width: 100}*/
            {field: 'url', title: '審核地址', sortable: true, width: 200},
            {field: 'urlip', title: '審核ip地址', sortable: true, width: 200},
            {
                field: 'operation', title: '操作', width: 50,
                formatter: function (value, rec, index) {
                      // if (rec.sendStatus == '0') {
                        var del = '<a href="#" class="mybutton" data-options="plain:true,iconCls:\'icon-hamburg-contact\'" onclick="sendMail(\'' + rec.id + '\')">重新發送</a>';
                        return del;
                      // }
                }
            }
        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        toolbar: '#tb'
    });
});

//弹窗增加
function add() {
    d = $("#dlg").dialog({
        title: '添加郵件發送記錄表',
        width: 380,
        height: 380,
        href: ctx + '/tpubmailrecord/create',
        maximizable: true,
        modal: true,
        buttons: [{
            text: '确认',
            handler: function () {
                $("#mainform").submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//删除
function del() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function (data) {
        if (data) {
            $.ajax({
                type: 'get',
                url: ctx + "/tpubmailrecord/delete/" + row.id,
                success: function (data) {
                    successTip(data, dg);
                }
            });
        }
    });
}

//弹窗修改
function upd() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    d = $("#dlg").dialog({
        title: '修改郵件發送記錄表',
        width: 380,
        height: 340,
        href: ctx + '/tpubmailrecord/update/' + row.id,
        maximizable: true,
        modal: true,
        buttons: [{
            text: '修改',
            handler: function () {
                $('#mainform').submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//创建查询对象并查询
function cx() {
    var obj = $("#searchFrom").serializeObject();
    dg.datagrid('load', obj);
}

function sendMail(id) {
    parent.$.messager.confirm('提示', '確認信息無誤后發送？', function (data) {
        if (data) {
            $.ajax({
                type: 'get',
                url: ctx + "/tpubmailrecord/sendMail/" + id,
                success: function (data) {
                    successTip(data, dg);
                }
            });
        }
    });
}
