package com.foxconn.ipebg.basics.controller;

import java.io.OutputStream;
import java.sql.SQLException;
import java.sql.Types;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.foxconn.ipebg.basics.entity.BsServiceCostTbEntity;
import com.foxconn.ipebg.basics.service.*;
import com.foxconn.ipebg.basics.vo.BsServiceCostVO;
import com.foxconn.ipebg.common.exception.ExcelException;
import com.foxconn.ipebg.common.utils.Result;
import com.foxconn.ipebg.system.service.DictService;
import com.foxconn.ipebg.system.utils.CustomExcelUtil;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.Colour;
import jxl.format.UnderlineStyle;
import jxl.format.VerticalAlignment;
import jxl.write.*;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.postgresql.util.PSQLException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.basics.entity.BsServiceCostEntity;


/**
 * 警衛服務費用結賬明細主表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-10-07 13:15:24
 */
@Controller
@RequestMapping("bsservicecost")
public class BsServiceCostController extends BaseController {

    @Autowired
    private BsServiceCostService bsServiceCostService;
    @Autowired
    private BsServiceCostTbService bsServiceCostTbService;
    @Autowired
    private DictService dictService;
    @Autowired
    private BsDptService bsDptService;
    @Autowired
    private BsArrangePostPersonService bsArrangePostPersonService;
    /**
      * 方法描述: 列表信息
      * @Author: S6114894
      * @CreateDate:   2019-10-07 13:15:24
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:bsservicecost:list")
    public String list(Model model) {

        //法人
        model.addAttribute("legalPersonDicts", JSON.toJSONString(dictService.getDictByType("guard_legalperson")));
        //崗位類別
//        result.put("postTypeDicts",dictService.getDictByType("guard_postType"));
        //部門信息
        model.addAttribute("bsDptList",JSON.toJSONString(bsDptService.getAll()));

        //查询列表数据
        return "basics/bsservicecost/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6114894
      * @CreateDate:   2019-10-07 13:15:24
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:bsservicecost:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<BsServiceCostEntity> page = getPage(request);
        page.setOrderBy("workMonth,recno");
        page.setOrder("desc,asc");
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        page = bsServiceCostService.search(page, filters);
        List<BsServiceCostEntity> costList = page.getResult();

        List<BsServiceCostVO> rows=new ArrayList<>();
        Map<String, Object> result = new HashMap<String, Object>();
        if(costList==null || costList.isEmpty()){
            result.put("rows", rows);
            result.put("total", 0);
            return result;
        }

        for(BsServiceCostEntity cost:costList){
            BsServiceCostVO vo=new BsServiceCostVO();
            vo.setCostEntity(cost);
            List<BsServiceCostTbEntity> tbList = bsServiceCostTbService.getByScId(cost.getId());
            if(tbList!=null && !tbList.isEmpty()){
                for(BsServiceCostTbEntity tb:tbList){
                    if("A".equals(tb.getShiftNo())){
                        vo.setDayTbEntity(tb);
                    }else if("B".equals(tb.getShiftNo())){
                        vo.setNightTbEntity(tb);
                    }
                }
            }
            rows.add(vo);
        }
        result.put("rows", rows);
        result.put("total", page.getTotalCount());
        return result;
    }
    /**
     * 方法描述: 保存
     * @Author: S6114894
     * @CreateDate:   2019-10-07 13:15:24
     * @Return
     **/
   
    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("basics:bsservicecost:add")
    @ResponseBody
    public String create(@Valid BsServiceCostEntity bsServiceCost, Model model) {
        bsServiceCost.setNewRecord(true);
    	bsServiceCost.setCreateDate(new Date());
        bsServiceCostService.save(bsServiceCost);
        return "success";
    }
    
    /**
      * 方法描述: 添加跳轉
      * @Author: S6114894
      * @CreateDate:   2019-10-07 13:15:24
      * @Return
      **/

    @RequiresPermissions("basics:bsservicecost:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("bsServiceCost", new BsServiceCostEntity());
        model.addAttribute("action", "create");
        return "basics/bsservicecost/listForm";
    }
    
     /**
     * 方法描述: 修改跳轉
     * @Author: S6113712
     * @CreateDate:   2018-10-06 11:04:39
     * @Return
     **/

   @RequiresPermissions("basics:bsservicecost:update")
   @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
   public String updateForm(@PathVariable("id") String id, Model model) {
       model.addAttribute("bsServiceCost", bsServiceCostService.get(id));
       model.addAttribute("action", "update");
      return "basics/bsservicecost/listForm";
   }

   /**
    * 方法描述：查看匯總信息
    * */
   @RequestMapping(value = "summary", method = RequestMethod.GET)
   public String summary() {
       return "basics/bsservicecost/summary";
   }

   @ResponseBody
   @RequestMapping(value = "getSummary", method = RequestMethod.GET)
   public BsServiceCostEntity getSummary(@RequestParam("bu") String bu,
                            @RequestParam("legalId") String legalId,
                            @RequestParam("workMonth") String workMonth) {
       BsServiceCostEntity summary = bsServiceCostService.getSummary(bu, legalId, workMonth);
       return summary;
   }
   
	/**
	 * 方法描述: 修改
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-11 10:55:00
	 * @Return
	 **/

   @RequestMapping(value = "update", method = RequestMethod.POST)
   @RequiresPermissions("basics:bsservicecost:update")
   @ResponseBody
   public String update(@Valid BsServiceCostEntity bsServiceCost,@RequestParam(value = "ids") String ids) {
	   bsServiceCost.setId(ids);
	   bsServiceCost.setUpdateDate(new Date());
	   bsServiceCostService.update(bsServiceCost);
	   
       return "success";
   }
    /**
      * 方法描述: 根據主鍵刪除
      * @Author: S6114894
      * @CreateDate:   2019-10-07 13:15:24
      * @Return
      **/

    @RequestMapping("delete/{id}")
    @RequiresPermissions("basics:bsservicecost:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        bsServiceCostService.delete(id);
        return "success";
    }
    /**
      * 导出excel
      * @Author: S6114894
      * @CreateDate:   2019-10-07 13:15:24
      * @Return
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) {
        CustomExcelUtil excelUtil = null;
        try {
            excelUtil = new CustomExcelUtil(response, null);
            String sheetName = "園區警衛服務費用結報明細表";

            Page<BsServiceCostEntity> page = getPage(request);
            page.setPageSize(Page.MAXSIZE);
            page.setPageNo(1);
            List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
            page = bsServiceCostService.search(page, filters);
            List<BsServiceCostEntity> costList = page.getResult();

            String buNam = request.getParameter("filter_EQS_buNam");
            String legalId = request.getParameter("filter_EQS_legalId");
            String workMonth = request.getParameter("filter_EQS_workMonth");
            BsServiceCostEntity summary = bsServiceCostService.getSummary(buNam, legalId, workMonth);

            // 計算需要幾個Sheet
            int sheetSize = 65533 / 2;
            double sheetNum = Math.ceil(costList.size() / new Integer(sheetSize).doubleValue());
            if (sheetNum == 1) {
                excelUtil.createNewSheet(sheetName, 0);
                setupExcelHeader(excelUtil.getCurrentSheet());
                fillSheet(excelUtil.getCurrentSheet(), costList, 0, costList.size() - 1);
                int summaryIndex = 2 + costList.size() * 2;
                if (costList.size() >= sheetSize) {
                    excelUtil.createNewSheet(sheetName, 1);
                    setupExcelHeader(excelUtil.getCurrentSheet());
                    summaryIndex = 2;
                }
                fillSummary(excelUtil.getCurrentSheet(), summary, summaryIndex);
            } else {
                int summaryIndex = 0;
                for (int i = 0; i < sheetNum; i++) {
                    excelUtil.createNewSheet(sheetName, i);
                    setupExcelHeader(excelUtil.getCurrentSheet());
                    int firstIndex = i * sheetSize;
                    int lastIndex = (i + 1) * sheetSize - 1 > costList.size() - 1 ? costList
                            .size() - 1 : (i + 1) * sheetSize - 1;
                    fillSheet(excelUtil.getCurrentSheet(), costList, firstIndex, lastIndex);
                    if (i == sheetNum - 1) {
                        if (lastIndex - firstIndex >= sheetSize) {
                            excelUtil.createNewSheet(sheetName, i+1);
                            setupExcelHeader(excelUtil.getCurrentSheet());
                            summaryIndex = 2;
                        } else {
                            summaryIndex = 2 + (lastIndex - firstIndex + 1) * 2;
                        }
                    }
                }
                fillSummary(excelUtil.getCurrentSheet(), summary, summaryIndex);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (excelUtil != null) {
                try {
                    excelUtil.closeWorkBook();
                } catch (ExcelException e) {}
            }
        }
    }

    private void setupExcelHeader(WritableSheet sheet) throws WriteException {
        // 表头样式
        WritableFont headerFont = new WritableFont(WritableFont.ARIAL, 11, WritableFont.BOLD, false, UnderlineStyle.NO_UNDERLINE, Colour.BLACK);
        WritableCellFormat headerFormat = new WritableCellFormat(headerFont);
        headerFormat.setAlignment(Alignment.CENTRE);
        headerFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
        headerFormat.setBorder(Border.ALL, BorderLineStyle.THIN, Colour.BLACK);
        headerFormat.setWrap(true);
        Label index1 = new Label(0, 0, "序號", headerFormat);
        sheet.addCell(index1);
        sheet.mergeCells(0, 0, 0, 1);
        sheet.setColumnView(0, 5);
        Label month1 = new Label(1, 0, "結算月份", headerFormat);
        sheet.addCell(month1);
        sheet.mergeCells(1, 0, 1, 1);
        sheet.setColumnView(1, 10);
        Label qun1 = new Label(2, 0, "事業群", headerFormat);
        sheet.addCell(qun1);
        sheet.mergeCells(2, 0, 2, 1);
        sheet.setColumnView(2, 15);
        Label faren1 = new Label(3, 0, "法人", headerFormat);
        sheet.addCell(faren1);
        sheet.mergeCells(3, 0, 3, 1);
        sheet.setColumnView(3, 13);
        Label bumen1 = new Label(4, 0, "部門", headerFormat);
        sheet.addCell(bumen1);
        sheet.mergeCells(4, 0, 4, 1);
        sheet.setColumnView(4, 20);
        Label feiyongdaima1 = new Label(5, 0, "費用代碼", headerFormat);
        sheet.addCell(feiyongdaima1);
        sheet.mergeCells(5, 0, 5, 1);
        sheet.setColumnView(5, 12);
        Label gangwei1 = new Label(6, 0, "崗位", headerFormat);
        sheet.addCell(gangwei1);
        sheet.mergeCells(6, 0, 6, 1);
        sheet.setColumnView(6, 18);
        Label banbie1 = new Label(7, 0, "班別", headerFormat);
        sheet.addCell(banbie1);
        sheet.mergeCells(7, 0, 7, 1);
        sheet.setColumnView(7, 5);
        Label shangbanrenyuanxinxi = new Label(8, 0, "上班人員信息", headerFormat);
        sheet.addCell(shangbanrenyuanxinxi);
        sheet.mergeCells(8, 0, 39, 0);
        Label day1  = new Label( 8, 1, "1", headerFormat);
        Label day2  = new Label( 9, 1, "2", headerFormat);
        Label day3  = new Label(10, 1, "3", headerFormat);
        Label day4  = new Label(11, 1, "4", headerFormat);
        Label day5  = new Label(12, 1, "5", headerFormat);
        Label day6  = new Label(13, 1, "6", headerFormat);
        Label day7  = new Label(14, 1, "7", headerFormat);
        Label day8  = new Label(15, 1, "8", headerFormat);
        Label day9  = new Label(16, 1, "9", headerFormat);
        Label day10 = new Label(17, 1, "10", headerFormat);
        Label day11 = new Label(18, 1, "11", headerFormat);
        Label day12 = new Label(19, 1, "12", headerFormat);
        Label day13 = new Label(20, 1, "13", headerFormat);
        Label day14 = new Label(21, 1, "14", headerFormat);
        Label day15 = new Label(22, 1, "15", headerFormat);
        Label day16 = new Label(23, 1, "16", headerFormat);
        Label day17 = new Label(24, 1, "17", headerFormat);
        Label day18 = new Label(25, 1, "18", headerFormat);
        Label day19 = new Label(26, 1, "19", headerFormat);
        Label day20 = new Label(27, 1, "20", headerFormat);
        Label day21 = new Label(28, 1, "21", headerFormat);
        Label day22 = new Label(29, 1, "22", headerFormat);
        Label day23 = new Label(30, 1, "23", headerFormat);
        Label day24 = new Label(31, 1, "24", headerFormat);
        Label day25 = new Label(32, 1, "25", headerFormat);
        Label day26 = new Label(33, 1, "26", headerFormat);
        Label day27 = new Label(34, 1, "27", headerFormat);
        Label day28 = new Label(35, 1, "28", headerFormat);
        Label day29 = new Label(36, 1, "29", headerFormat);
        Label day30 = new Label(37, 1, "30", headerFormat);
        Label day31 = new Label(38, 1, "31", headerFormat);
        sheet.addCell(day1);
        sheet.addCell(day2);
        sheet.addCell(day3);
        sheet.addCell(day4);
        sheet.addCell(day5);
        sheet.addCell(day6);
        sheet.addCell(day7);
        sheet.addCell(day8);
        sheet.addCell(day9);
        sheet.addCell(day10);
        sheet.addCell(day11);
        sheet.addCell(day12);
        sheet.addCell(day13);
        sheet.addCell(day14);
        sheet.addCell(day15);
        sheet.addCell(day16);
        sheet.addCell(day17);
        sheet.addCell(day18);
        sheet.addCell(day19);
        sheet.addCell(day20);
        sheet.addCell(day21);
        sheet.addCell(day22);
        sheet.addCell(day23);
        sheet.addCell(day24);
        sheet.addCell(day25);
        sheet.addCell(day26);
        sheet.addCell(day27);
        sheet.addCell(day28);
        sheet.addCell(day29);
        sheet.addCell(day30);
        sheet.addCell(day31);
        sheet.setColumnView( 8, 4);
        sheet.setColumnView( 9, 4);
        sheet.setColumnView(10, 4);
        sheet.setColumnView(11, 4);
        sheet.setColumnView(12, 4);
        sheet.setColumnView(13, 4);
        sheet.setColumnView(14, 4);
        sheet.setColumnView(15, 4);
        sheet.setColumnView(16, 4);
        sheet.setColumnView(17, 4);
        sheet.setColumnView(18, 4);
        sheet.setColumnView(19, 4);
        sheet.setColumnView(20, 4);
        sheet.setColumnView(21, 4);
        sheet.setColumnView(22, 4);
        sheet.setColumnView(23, 4);
        sheet.setColumnView(24, 4);
        sheet.setColumnView(25, 4);
        sheet.setColumnView(26, 4);
        sheet.setColumnView(27, 4);
        sheet.setColumnView(28, 4);
        sheet.setColumnView(29, 4);
        sheet.setColumnView(30, 4);
        sheet.setColumnView(31, 4);
        sheet.setColumnView(32, 4);
        sheet.setColumnView(33, 4);
        sheet.setColumnView(34, 4);
        sheet.setColumnView(35, 4);
        sheet.setColumnView(36, 4);
        sheet.setColumnView(37, 4);
        sheet.setColumnView(38, 4);
        Label renli1 = new Label(39, 1, "接崗人力", headerFormat);
        sheet.addCell(renli1);
        sheet.setColumnView(39, 9);
        Label zongrenli1 = new Label(40, 0, "接崗總人力", headerFormat);
        sheet.addCell(zongrenli1);
        sheet.mergeCells(40, 0, 40, 1);
        Label banfei1 = new Label(41, 0, "正常上班費用(RMB/元)", headerFormat);
        sheet.addCell(banfei1);
        sheet.mergeCells(41, 0, 41, 1);
        sheet.setColumnView(41, 14);
        Label peifei1 = new Label(42, 0, "培訓費用(RMB/元)", headerFormat);
        sheet.addCell(peifei1);
        sheet.mergeCells(42, 0, 42, 1);
        sheet.setColumnView(42, 14);
        Label changgui1 = new Label(43, 0, "常規費用(RMB/元)", headerFormat);
        sheet.addCell(changgui1);
        sheet.mergeCells(43, 0, 43, 1);
        sheet.setColumnView(43, 14);
        Label jiashi1 = new Label(44, 0, "加班時數(小時)", headerFormat);
        sheet.addCell(jiashi1);
        sheet.mergeCells(44, 0, 44, 1);
        Label jiafei1 = new Label(45, 0, "加班費用(RMB/元)", headerFormat);
        sheet.addCell(jiafei1);
        sheet.mergeCells(45, 0, 45, 1);
        Label zongfei1 = new Label(46, 0, "總費用(RMB/元)", headerFormat);
        sheet.addCell(zongfei1);
        sheet.mergeCells(46, 0, 46, 1);
        sheet.setColumnView(46, 14);
    }

    private void fillSheet(WritableSheet sheet, List<BsServiceCostEntity> list, int firstIndex, int lastIndex) throws WriteException {
        // 内容格式
        WritableFont contentFont = new WritableFont(WritableFont.ARIAL, 11, WritableFont.NO_BOLD, false, UnderlineStyle.NO_UNDERLINE, Colour.BLACK);
        WritableCellFormat contentFormat = new WritableCellFormat(contentFont);
        contentFormat.setAlignment(Alignment.CENTRE);
        contentFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
        contentFormat.setBorder(Border.ALL, BorderLineStyle.THIN, Colour.BLACK);
        for (int i = firstIndex; i <= lastIndex; i++) {
            BsServiceCostEntity cost = list.get(i);
            BsServiceCostVO vo=new BsServiceCostVO();
            vo.setCostEntity(cost);
            List<BsServiceCostTbEntity> tbList = bsServiceCostTbService.getByScId(cost.getId());
            if(tbList!=null && !tbList.isEmpty()){
                for(BsServiceCostTbEntity tb:tbList){
                    if("A".equals(tb.getShiftNo())){
                        vo.setDayTbEntity(tb);
                    }else if("B".equals(tb.getShiftNo())){
                        vo.setNightTbEntity(tb);
                    }
                }
            }
            Label index1 = new Label(0, 2 + i * 2, (1 + firstIndex + i) + "", contentFormat);
            sheet.addCell(index1);
            sheet.mergeCells(0, 2 + i * 2, 0, 3 + i * 2);
            Label month1 = new Label(1, 2 + i * 2, vo.getCostEntity().getWorkMonth(), contentFormat);
            sheet.addCell(month1);
            sheet.mergeCells(1, 2 + i * 2, 1, 3 + i * 2);
            Label qun1 = new Label(2, 2 + i * 2, vo.getCostEntity().getBuNam(), contentFormat);
            sheet.addCell(qun1);
            sheet.mergeCells(2, 2 + i * 2, 2, 3 + i * 2);
            String faren = dictService.getDictByTypeAndVlaue("guard_legalperson", vo.getCostEntity().getLegalId()).getLabel();
            Label faren1 = new Label(3, 2 + i * 2, faren, contentFormat);
            sheet.addCell(faren1);
            sheet.mergeCells(3, 2 + i * 2, 3, 3 + i * 2);
            Label bumen1 = new Label(4, 2 + i * 2, vo.getCostEntity().getDptNam(), contentFormat);
            sheet.addCell(bumen1);
            sheet.mergeCells(4, 2 + i * 2, 4, 3 + i * 2);
            Label costId1 = new Label(5, 2 + i * 2, vo.getCostEntity().getCostId(), contentFormat);
            sheet.addCell(costId1);
            sheet.mergeCells(5, 2 + i * 2, 5, 3 + i * 2);
            Label post1 = new Label(6, 2 + i * 2, vo.getCostEntity().getPostNam(), contentFormat);
            sheet.addCell(post1);
            sheet.mergeCells(6, 2 + i * 2, 6, 3 + i * 2);
            Label day = new Label(7, 2 + i * 2, "白班", contentFormat);
            Label night = new Label(7, 3 + i * 2, "夜班", contentFormat);
            sheet.addCell(day);
            sheet.addCell(night);
            Label day1 = new Label(8, 2 + i * 2, vo.getDayTbEntity().getL1(), contentFormat);
            Label night1 = new Label(8, 3 + i * 2, vo.getNightTbEntity().getL1(), contentFormat);
            sheet.addCell(day1);
            sheet.addCell(night1);
            Label day2 = new Label(9, 2 + i * 2, vo.getDayTbEntity().getL2(), contentFormat);
            Label night2 = new Label(9, 3 + i * 2, vo.getNightTbEntity().getL2(), contentFormat);
            sheet.addCell(day2);
            sheet.addCell(night2);
            Label day3 = new Label(10, 2 + i * 2, vo.getDayTbEntity().getL3(), contentFormat);
            Label night3 = new Label(10, 3 + i * 2, vo.getNightTbEntity().getL3(), contentFormat);
            sheet.addCell(day3);
            sheet.addCell(night3);
            Label day4 = new Label(11, 2 + i * 2, vo.getDayTbEntity().getL4(), contentFormat);
            Label night4 = new Label(11, 3 + i * 2, vo.getNightTbEntity().getL4(), contentFormat);
            sheet.addCell(day4);
            sheet.addCell(night4);
            Label day5 = new Label(12, 2 + i * 2, vo.getDayTbEntity().getL5(), contentFormat);
            Label night5 = new Label(12, 3 + i * 2, vo.getNightTbEntity().getL5(), contentFormat);
            sheet.addCell(day5);
            sheet.addCell(night5);
            Label day6 = new Label(13, 2 + i * 2, vo.getDayTbEntity().getL6(), contentFormat);
            Label night6 = new Label(13, 3 + i * 2, vo.getNightTbEntity().getL6(), contentFormat);
            sheet.addCell(day6);
            sheet.addCell(night6);
            Label day7 = new Label(14, 2 + i * 2, vo.getDayTbEntity().getL7(), contentFormat);
            Label night7 = new Label(14, 3 + i * 2, vo.getNightTbEntity().getL7(), contentFormat);
            sheet.addCell(day7);
            sheet.addCell(night7);
            Label day8 = new Label(15, 2 + i * 2, vo.getDayTbEntity().getL8(), contentFormat);
            Label night8 = new Label(15, 3 + i * 2, vo.getNightTbEntity().getL8(), contentFormat);
            sheet.addCell(day8);
            sheet.addCell(night8);
            Label day9 = new Label(16, 2 + i * 2, vo.getDayTbEntity().getL9(), contentFormat);
            Label night9 = new Label(16, 3 + i * 2, vo.getNightTbEntity().getL9(), contentFormat);
            sheet.addCell(day9);
            sheet.addCell(night9);
            Label day10 = new Label(17, 2 + i * 2, vo.getDayTbEntity().getL10(), contentFormat);
            Label night10 = new Label(17, 3 + i * 2, vo.getNightTbEntity().getL10(), contentFormat);
            sheet.addCell(day10);
            sheet.addCell(night10);
            Label day11 = new Label(18, 2 + i * 2, vo.getDayTbEntity().getL11(), contentFormat);
            Label night11 = new Label(18, 3 + i * 2, vo.getNightTbEntity().getL11(), contentFormat);
            sheet.addCell(day11);
            sheet.addCell(night11);
            Label day12 = new Label(19, 2 + i * 2, vo.getDayTbEntity().getL12(), contentFormat);
            Label night12 = new Label(19, 3 + i * 2, vo.getNightTbEntity().getL12(), contentFormat);
            sheet.addCell(day12);
            sheet.addCell(night12);
            Label day13 = new Label(20, 2 + i * 2, vo.getDayTbEntity().getL13(), contentFormat);
            Label night13 = new Label(20, 3 + i * 2, vo.getNightTbEntity().getL13(), contentFormat);
            sheet.addCell(day13);
            sheet.addCell(night13);
            Label day14 = new Label(21, 2 + i * 2, vo.getDayTbEntity().getL14(), contentFormat);
            Label night14 = new Label(21, 3 + i * 2, vo.getNightTbEntity().getL14(), contentFormat);
            sheet.addCell(day14);
            sheet.addCell(night14);
            Label day15 = new Label(22, 2 + i * 2, vo.getDayTbEntity().getL15(), contentFormat);
            Label night15 = new Label(22, 3 + i * 2, vo.getNightTbEntity().getL15(), contentFormat);
            sheet.addCell(day15);
            sheet.addCell(night15);
            Label day16 = new Label(23, 2 + i * 2, vo.getDayTbEntity().getL16(), contentFormat);
            Label night16 = new Label(23, 3 + i * 2, vo.getNightTbEntity().getL16(), contentFormat);
            sheet.addCell(day16);
            sheet.addCell(night16);
            Label day17 = new Label(24, 2 + i * 2, vo.getDayTbEntity().getL17(), contentFormat);
            Label night17 = new Label(24, 3 + i * 2, vo.getNightTbEntity().getL17(), contentFormat);
            sheet.addCell(day17);
            sheet.addCell(night17);
            Label day18 = new Label(25, 2 + i * 2, vo.getDayTbEntity().getL18(), contentFormat);
            Label night18 = new Label(25, 3 + i * 2, vo.getNightTbEntity().getL18(), contentFormat);
            sheet.addCell(day18);
            sheet.addCell(night18);
            Label day19 = new Label(26, 2 + i * 2, vo.getDayTbEntity().getL19(), contentFormat);
            Label night19 = new Label(26, 3 + i * 2, vo.getNightTbEntity().getL19(), contentFormat);
            sheet.addCell(day19);
            sheet.addCell(night19);
            Label day20 = new Label(27, 2 + i * 2, vo.getDayTbEntity().getL20(), contentFormat);
            Label night20 = new Label(27, 3 + i * 2, vo.getNightTbEntity().getL20(), contentFormat);
            sheet.addCell(day20);
            sheet.addCell(night20);
            Label day21 = new Label(28, 2 + i * 2, vo.getDayTbEntity().getL21(), contentFormat);
            Label night21 = new Label(28, 3 + i * 2, vo.getNightTbEntity().getL21(), contentFormat);
            sheet.addCell(day21);
            sheet.addCell(night21);
            Label day22 = new Label(29, 2 + i * 2, vo.getDayTbEntity().getL22(), contentFormat);
            Label night22 = new Label(29, 3 + i * 2, vo.getNightTbEntity().getL22(), contentFormat);
            sheet.addCell(day22);
            sheet.addCell(night22);
            Label day23 = new Label(30, 2 + i * 2, vo.getDayTbEntity().getL23(), contentFormat);
            Label night23 = new Label(30, 3 + i * 2, vo.getNightTbEntity().getL23(), contentFormat);
            sheet.addCell(day23);
            sheet.addCell(night23);
            Label day24 = new Label(31, 2 + i * 2, vo.getDayTbEntity().getL24(), contentFormat);
            Label night24 = new Label(31, 3 + i * 2, vo.getNightTbEntity().getL24(), contentFormat);
            sheet.addCell(day24);
            sheet.addCell(night24);
            Label day25 = new Label(32, 2 + i * 2, vo.getDayTbEntity().getL25(), contentFormat);
            Label night25 = new Label(32, 3 + i * 2, vo.getNightTbEntity().getL25(), contentFormat);
            sheet.addCell(day25);
            sheet.addCell(night25);
            Label day26 = new Label(33, 2 + i * 2, vo.getDayTbEntity().getL26(), contentFormat);
            Label night26 = new Label(33, 3 + i * 2, vo.getNightTbEntity().getL26(), contentFormat);
            sheet.addCell(day26);
            sheet.addCell(night26);
            Label day27 = new Label(34, 2 + i * 2, vo.getDayTbEntity().getL27(), contentFormat);
            Label night27 = new Label(34, 3 + i * 2, vo.getNightTbEntity().getL27(), contentFormat);
            sheet.addCell(day27);
            sheet.addCell(night27);
            Label day28 = new Label(35, 2 + i * 2, vo.getDayTbEntity().getL28(), contentFormat);
            Label night28 = new Label(35, 3 + i * 2, vo.getNightTbEntity().getL28(), contentFormat);
            sheet.addCell(day28);
            sheet.addCell(night28);
            Label day29 = new Label(36, 2 + i * 2, vo.getDayTbEntity().getL29(), contentFormat);
            Label night29 = new Label(36, 3 + i * 2, vo.getNightTbEntity().getL29(), contentFormat);
            sheet.addCell(day29);
            sheet.addCell(night29);
            Label day30 = new Label(37, 2 + i * 2, vo.getDayTbEntity().getL30(), contentFormat);
            Label night30 = new Label(37, 3 + i * 2, vo.getNightTbEntity().getL30(), contentFormat);
            sheet.addCell(day30);
            sheet.addCell(night30);
            Label day31 = new Label(38, 2 + i * 2, vo.getDayTbEntity().getL31(), contentFormat);
            Label night31 = new Label(38, 3 + i * 2, vo.getNightTbEntity().getL31(), contentFormat);
            sheet.addCell(day31);
            sheet.addCell(night31);
            // 接岗人力
            Label renli1 = new Label(39, 2 + i * 2, vo.getDayTbEntity().getPernu().toString(), contentFormat);
            Label renli2 = new Label(39, 3 + i * 2, vo.getNightTbEntity().getPernu().toString(), contentFormat);
            sheet.addCell(renli1);
            sheet.addCell(renli2);
            // 接岗总人力
            Label zongrenli1 = new Label(40, 2 + i * 2, vo.getCostEntity().getNormalDay(), contentFormat);
            sheet.addCell(zongrenli1);
            sheet.mergeCells(40, 2 + i * 2, 40, 3 + i * 2);
            // 正常上班费用
            Label normalFei1 = new Label(41, 2 + i * 2, vo.getCostEntity().getNormalCost().toString(), contentFormat);
            sheet.addCell(normalFei1);
            sheet.mergeCells(41, 2 + i * 2, 41, 3 + i * 2);
            // 培训费用
            Label peifei1 = new Label(42, 2 + i * 2, vo.getCostEntity().getTrainingCost().toString(), contentFormat);
            sheet.addCell(peifei1);
            sheet.mergeCells(42, 2 + i * 2, 42, 3 + i * 2);
            // 常规费用
            Label changgui1 = new Label(43, 2 + i * 2, vo.getCostEntity().getTotalCost().toString(), contentFormat);
            sheet.addCell(changgui1);
            sheet.mergeCells(43, 2 + i * 2, 43, 3 + i * 2);
            Label jiashi1 = new Label(44, 2 + i * 2, vo.getCostEntity().getOtHour().toString(), contentFormat);
            sheet.addCell(jiashi1);
            sheet.mergeCells(44, 2 + i * 2, 44, 3 + i * 2);
            Label jiafei1 = new Label(45, 2 + i * 2, vo.getCostEntity().getOtCost().toString(), contentFormat);
            sheet.addCell(jiafei1);
            sheet.mergeCells(45, 2 + i * 2, 45, 3 + i * 2);
            Label allCost1 = new Label(46, 2 + i * 2, vo.getCostEntity().getTotalCostAll().toString(), contentFormat);
            sheet.addCell(allCost1);
            sheet.mergeCells(46, 2 + i * 2, 46, 3 + i * 2);
        }
    }

    private void fillSummary(WritableSheet sheet, BsServiceCostEntity summary, int index) throws WriteException {
        // 内容格式
        WritableFont contentFont = new WritableFont(WritableFont.ARIAL, 11, WritableFont.NO_BOLD, false, UnderlineStyle.NO_UNDERLINE, Colour.BLACK);
        WritableCellFormat contentFormat = new WritableCellFormat(contentFont);
        contentFormat.setAlignment(Alignment.CENTRE);
        contentFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
        contentFormat.setBorder(Border.ALL, BorderLineStyle.THIN, Colour.BLACK);

        Label summaryLabel = new Label(0, index, "總計", contentFormat);
        sheet.addCell(summaryLabel);
        sheet.mergeCells(0, index, 40, index);
        // 正常上班费用
        Label normalFei1 = new Label(41, index, summary.getNormalCost().toString(), contentFormat);
        sheet.addCell(normalFei1);
        // 培训费用
        Label peifei1 = new Label(42, index, summary.getTrainingCost().toString(), contentFormat);
        sheet.addCell(peifei1);
        // 常规费用
        Label changgui1 = new Label(43, index, summary.getTotalCost().toString(), contentFormat);
        sheet.addCell(changgui1);
        Label jiashi1 = new Label(44, index, summary.getOtHour().toString(), contentFormat);
        sheet.addCell(jiashi1);
        Label jiafei1 = new Label(45, index, summary.getOtCost().toString(), contentFormat);
        sheet.addCell(jiafei1);
        Label allCost1 = new Label(46, index, summary.getTotalCostAll().toString(), contentFormat);
        sheet.addCell(allCost1);
    }

    @RequestMapping(value="listArrangeTrainee", method = RequestMethod.POST)
    @ResponseBody
    public List listArrangeTrainee(@RequestParam Map<String,String> params) {
        return bsArrangePostPersonService.listNormalTrainee(params);
    }

    /**
     * 按月份生成報表，調用存儲過程
     * */
    @RequestMapping(value = "generate", method = RequestMethod.POST)
    @ResponseBody
    public Result generate(@RequestParam(value = "month") String month) {
        Result result = new Result();
        try {
            Date genMonth = DateUtil.parse(month, "yyyy-MM");
            bsServiceCostService.generateServiceCostByMonth(genMonth);
            result.setCode(Result.SUCCESS);
            result.setMsg("生成成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.setCode(Result.FAILURE);
            result.setMsg("生成失敗："+e.getMessage());
        }
        return result;
    }
}
