var dg = null;
var d = null;
$(function() {
	dg = $('#dg').datagrid();
});

$(function() {
	// 初始化去除驗證
	$(":input").removeClass("validatebox-invalid");
});

function saveInfo(flag) {
	if (flag == '2') {
		var isValid = $("#mainform").form('validate');
		if (!isValid) {
			return;
		}
	}
	$.ajax({
		url: ctx + '/tempSecurityAply/' + action + '/' + flag,
		type: 'POST',
		beforeSend: ajaxLoading,
		// dataType: 'json',
		data: $('#mainform').serialize(),
		success: function (data) {
			ajaxLoadEnd();
			successTip(data, dg, d);
			window.parent.mainpage.mainTabs.closeCurrentTab();
		}
	});
}

function auditInfo(flag) {
	//console.log('haha');
	if (flag == 'Y') {
		if ($("#securityCom1").length > 0) {
			if ($("#securityCom1").combobox('getValue') == null || $("#securityCom1").combobox('getValue') == '') {
				alert("請選擇服務公司！");
				return;
			}
		}
	} else if (flag == 'N') {
		var isValid = $("#mainform").form('validate');
		if (!isValid) {
			return;
		}
	}
	$.ajax({
		url: ctx + '/tempSecurityAply/auditSave/' + flag,
		type: 'POST',
		beforeSend: ajaxLoading,
		// dataType: 'json',
		data: $('#mainform').serialize(),
		success: function (data) {
			ajaxLoadEnd();
			successTip(data, dg, d);
			$("#btnPass").linkbutton('disable');
			$("#btnReject").linkbutton('disable');

			window.parent.mainpage.mainTabs.closeCurrentTab();

			//window.close();
		}
	});
}

// 重新提交
function resubmit() {
	window.location.href = ctx+'/tempSecurityAply/resubmit/' + $("#serialno").val();
}

// 取消申請
function cancelApply() {
	$.easyui.messager.confirm("操作提醒", "確定要取消該申請嗎？", function (c) {
		if (c) {
			$.ajax({
				url: ctx + '/tempSecurityAply/cancelApply/' + $("#serialno").val(),
				type: 'POST',
				beforeSend: ajaxLoading,
				success: function (data) {
					ajaxLoadEnd();
					successTip(data, dg, d);

					if (data === 'success') window.parent.mainpage.mainTabs.closeCurrentTab();
				}
			});
		}
	});
}

// 驗證申請人聯繫電話
function valdApplyTel(obj){
 var phoneL=$.trim($("#dealtel").val()).split("").length;
 var tel = obj.value;
 if(tel!=""){
     var temp = tel;
     var regex  = /^[0-9]{3}\+[0-9]{5}$/;
     var patt1=new RegExp(regex);
     var result = patt1.test(tel);
     if(!result){
         alert("分機格式有誤!");
         obj.value="";
         obj.focus();
         return;
     }
 }
}
