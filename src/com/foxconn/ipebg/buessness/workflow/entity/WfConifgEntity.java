package com.foxconn.ipebg.buessness.workflow.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 流程信息配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-06 11:04:38
 */
@Entity
@Table(name = "WF_CONIFG")
@DynamicUpdate
@DynamicInsert
public class WfConifgEntity extends DataEntity<WfConifgEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    //流程編碼
    private String workflowid;
    //流程名稱
    private String workflowname;
    //表單簽核處理方法
    private String action;
    //表單修改處理方法
    private String modaction;
    //表單臨時保存方法
    private String saveaction;
    //$column.comments
    private String detailaction;
    //$column.comments
    private String dynfield01;
    //$column.comments
    private String dynfield02;
    //$column.comments
    private String dynfield03;

    private String workflowcode;
    private String version;

    /**
     * 设置：流程編碼
     */
    public void setWorkflowid(String workflowid) {
        this.workflowid = workflowid;
    }

    /**
     * 获取：流程編碼
     */
    @Column(name = "WORKFLOWID", nullable = false, length = 20)
    public String getWorkflowid() {
        return workflowid;
    }

    /**
     * 设置：流程名稱
     */
    public void setWorkflowname(String workflowname) {
        this.workflowname = workflowname;
    }

    /**
     * 获取：流程名稱
     */
    @Column(name = "WORKFLOWNAME", nullable = false, length = 20)
    public String getWorkflowname() {
        return workflowname;
    }

    /**
     * 设置：表單簽核處理方法
     */
    public void setAction(String action) {
        this.action = action;
    }

    /**
     * 获取：表單簽核處理方法
     */
    @Column(name = "ACTION", nullable = false, length = 20)
    public String getAction() {
        return action;
    }

    /**
     * 设置：表單修改處理方法
     */
    public void setModaction(String modaction) {
        this.modaction = modaction;
    }

    /**
     * 获取：表單修改處理方法
     */
    @Column(name = "MODACTION", nullable = false, length = 20)
    public String getModaction() {
        return modaction;
    }

    /**
     * 设置：表單臨時保存方法
     */
    public void setSaveaction(String saveaction) {
        this.saveaction = saveaction;
    }

    /**
     * 获取：表單臨時保存方法
     */
    @Column(name = "SAVEACTION", nullable = false, length = 20)
    public String getSaveaction() {
        return saveaction;
    }

    /**
     * 设置：${column.comments}
     */
    public void setDetailaction(String detailaction) {
        this.detailaction = detailaction;
    }

    /**
     * 获取：${column.comments}
     */
    @Column(name = "DETAILACTION", nullable = false, length = 20)
    public String getDetailaction() {
        return detailaction;
    }

    /**
     * 设置：${column.comments}
     */
    public void setDynfield01(String dynfield01) {
        this.dynfield01 = dynfield01;
    }

    /**
     * 获取：${column.comments}
     */
    @Column(name = "DYNFIELD01", nullable = false, length = 20)
    public String getDynfield01() {
        return dynfield01;
    }

    /**
     * 设置：${column.comments}
     */
    public void setDynfield02(String dynfield02) {
        this.dynfield02 = dynfield02;
    }

    /**
     * 获取：${column.comments}
     */
    @Column(name = "DYNFIELD02", nullable = false, length = 20)
    public String getDynfield02() {
        return dynfield02;
    }

    /**
     * 设置：${column.comments}
     */
    public void setDynfield03(String dynfield03) {
        this.dynfield03 = dynfield03;
    }

    /**
     * 获取：${column.comments}
     */
    @Column(name = "DYNFIELD03", nullable = false, length = 20)
    public String getDynfield03() {
        return dynfield03;
    }
    @Column(name = "WORKFLOWCODE", nullable = false, length = 20)
    public String getWorkflowcode() {
        return workflowcode;
    }

    public void setWorkflowcode(String workflowcode) {
        this.workflowcode = workflowcode;
    }
    @Column(name = "VERSION", nullable = false, length = 20)
    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
}
