package com.foxconn.ipebg.buessness.audit.service;

import com.foxconn.ipebg.audit.entity.EFormSignEntity;
import com.foxconn.ipebg.audit.service.EBsCodeService;
import com.foxconn.ipebg.audit.service.EFormSignService;
import com.foxconn.ipebg.basics.service.ESignUserinfoService;
import com.foxconn.ipebg.buessness.audit.dao.TempSecurityAplyDao;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.common.exception.ExcelException;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import com.foxconn.ipebg.buessness.audit.entity.TempSecurityAplyEntity;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.system.dao.UserDao;
import com.foxconn.ipebg.system.service.TPubMailrecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service
@Transactional(readOnly = true)
public class TempSecurityAplyService extends BaseBusinessService<TempSecurityAplyEntity, String> {
    @Autowired
    private TempSecurityAplyDao aplyDao;
    @Autowired
    private UserDao userDao;
    @Autowired
    private UserServiceUtil serviceUtil;
    @Autowired
    private ESignUserinfoService eSignUserinfoService;
    @Autowired
    private EBsCodeService eBsCodeService;
    @Autowired
    private EFormSignService eFormSignService;
    @Autowired
    private TPubMailrecordService mailrecordService;

    @Override
    public HibernateDao<TempSecurityAplyEntity, String> getEntityDao() {
        return aplyDao;
    }

    public TempSecurityAplyEntity findBySerialno(String serialno) {
        return aplyDao.findUniqueBy("serialno", serialno);
    }

    /**
     * 方法描述: 啟動流程
     *
     * @Author: S6114893
     * @CreateDate: 2022-07-07
     * @Return
     **/
    @Transactional
    public String startProcess(TempSecurityAplyEntity aplyEntity, EFormSignEntity[] eFormSignEntities, String contextPath, String formType, String flag) {
        try {
            if (flag.equals(Constant.RESULT.CODE_RUNNING.getValue())) {
                // 設置流水號
                if (StringUtils.isEmpty(aplyEntity.getSerialno())) {
                    aplyEntity.setNewRecord(true);
                    aplyEntity.setSerialno(serviceUtil.createSerialno("LSAB"));
                } else {
                    aplyEntity.setNewRecord(false);
                }
                aplyEntity.setSignEmpNo(eFormSignEntities[0].getEserId());
                aplyEntity.setSignEmpNam(eFormSignEntities[0].getEserNam());
                aplyEntity.setWorkstatus(Constant.RESULT.CODE_RUNNING.getValue());
                this.save(aplyEntity);

                String applyStat = "";//第一個審核節點狀態
                String signEmpNo = "", signEmpNam = "";
                for (int i = 0; i < eFormSignEntities.length; i++) {
                    EFormSignEntity node = eFormSignEntities[i];
                    if (node.getEserId().isEmpty()) {
                        continue;
                    }
                    if (applyStat == "") {
                        applyStat = node.getSignOrd().toString();
                        signEmpNo = node.getEserId();
                        signEmpNam = node.getEserNam();
                    }
                    node.setApplyId(aplyEntity.getSerialno());
                    node.setEserMail(eSignUserinfoService
                            .findByeserIdTypNodeName(node.getEserId(),
                                    formType, node.getSignOrd().toString())
                            .getEserMail());
                    node.setCreateDate(new Date());
                    node.setSignNam(eBsCodeService.getCode(formType, node.getSignOrd()).getCodeNam());
                    eFormSignService.save(node);
                }
                // 初始化主表表單狀態
                auditUpdateAply(aplyEntity.getSerialno(), applyStat,
                        signEmpNo, signEmpNam);
                mailrecordService.sendMail(aplyEntity, "Y", contextPath);
            }
        } catch (Exception e) {
            this.logger.info(e.getMessage(), e);
            return Constant.RESULT.CODE_NO.getValue();
        }
        return Constant.RESULT.CODE_YES.getValue();
    }

    @Transactional
    public String auditProcess(TempSecurityAplyEntity aplyEntity, String flag, String signOrd,
                               String clientIP, String memo, String contextPath) {
        try {
            String minStatWait = "", maxStatWait = "";
            EFormSignEntity currentNode = eFormSignService
                    .currentNode(aplyEntity.getSerialno());
            if (!signOrd.equals(currentNode.getSignOrd().toString())) {
                return Constant.RESULT.CODE_NO.getValue();
            }
            String formType = "LSAB";
            if (flag.equals("Y") && StringUtils.isNotEmpty(aplyEntity.getSerialno())) {
                // 同意
                minStatWait = currentNode.getSignOrd().toString();
                maxStatWait = eFormSignService.maxStatWait(aplyEntity.getSerialno());
                if (StringUtils.isNotEmpty(minStatWait)) {
                    List<Integer> securityComSelectableOrder = eBsCodeService.getConfigedSignOrder(formType, "LSAB_securityCom_selectable", aplyEntity.getSerialno());
                    List<Integer> hasCostSelectableOrder = eBsCodeService.getConfigedSignOrder(formType, "LSAB_hasCost_selectable", aplyEntity.getSerialno());
                    if (securityComSelectableOrder.contains(Integer.parseInt(minStatWait))) {
                        // 该节点可以设置服务保安公司
                        auditUpdateSecurityCom(aplyEntity.getSerialno(), aplyEntity.getSecurityCom());
                    }
                    if (hasCostSelectableOrder.contains(Integer.parseInt(minStatWait))) {
                        // 该节点可以设置服务单是否产生费用
                        auditUpdateHasCost(aplyEntity.getSerialno(), aplyEntity.getHasCost());
                    }
                    eFormSignService.auditUpdate(aplyEntity.getSerialno(),
                            minStatWait, clientIP, "Y", memo);
                    EFormSignEntity nextNode = eFormSignService
                            .currentNode(aplyEntity.getSerialno());
                    String signEmpNo = "", signEmpNam = "";

                    if (minStatWait.equals(maxStatWait))
                        minStatWait = "E";
                    if (nextNode != null) {
                        signEmpNo = nextNode.getEserId();
                        signEmpNam = nextNode.getEserNam();
                        minStatWait = nextNode.getSignOrd().toString();
                    }
                    auditUpdateAply(aplyEntity.getSerialno(),
                            minStatWait, signEmpNo, signEmpNam);
                    if (minStatWait.equals("E")) {
                        mailrecordService.sendMail(aplyEntity, "E", contextPath);
                    } else {
                        mailrecordService.sendMail(aplyEntity, "Y", contextPath);
                    }
                }
            }
            // 駁回
            else if (flag.equals("N")
                    && StringUtils.isNotEmpty(aplyEntity.getSerialno())) {
                minStatWait = currentNode.getSignOrd().toString();
                maxStatWait = eFormSignService.maxStatWait(aplyEntity
                        .getSerialno());
                if (!minStatWait.equals("")) {
                    eFormSignService.auditUpdate(aplyEntity.getSerialno(),
                            minStatWait, clientIP, "N", memo);
                    eFormSignService.batchDeleteWait(aplyEntity.getSerialno());
                    // 駁回時發回申請人重新提交
                    EFormSignEntity node = new EFormSignEntity();
                    node.setApplyId(aplyEntity.getSerialno());
                    node.setEserId(aplyEntity.getMakerno());
                    node.setEserNam(aplyEntity.getMakername());
                    node.setEserMail(userDao.findUniqueBy("loginName", aplyEntity.getMakerno()).getEmail());
                    node.setCreateDate(new Date());
                    node.setSignOrd(currentNode.getSignOrd()+1);
                    node.setSignNam("駁回處理");
                    node.setCreateBy(currentNode.getEserId());
                    node.setCreateDate(new Date());
                    node.setEserStat("W");
                    eFormSignService.save(node);

                    auditUpdateAply(aplyEntity.getSerialno(), node.getSignOrd().toString(),
                            node.getEserId(), node.getEserNam());
                }
                mailrecordService.sendMail(aplyEntity, "N", contextPath);
            }
        } catch (Exception e) {
            this.logger.info(e.getMessage(), e);
            return Constant.RESULT.CODE_NO.getValue();
        }
        return Constant.RESULT.CODE_YES.getValue();
    }

    /**
     * 方法描述: 重新提交保存
     *
     * @Author: S6114893
     * @CreateDate: 2022-12-22
     * @Return
     **/
    @Transactional
    public String reStartProcess(TempSecurityAplyEntity aplyEntity, EFormSignEntity[] eFormSignEntities, String contextPath, String formType, String flag, String ip) {
        try {
            if (flag.equals(Constant.RESULT.CODE_RUNNING.getValue())) {

                aplyEntity.setNewRecord(false);
                aplyEntity.setSignEmpNo(eFormSignEntities[0].getEserId());
                aplyEntity.setSignEmpNam(eFormSignEntities[0].getEserNam());
                aplyEntity.setWorkstatus(Constant.RESULT.CODE_RUNNING.getValue());
                save(aplyEntity);

                // 獲取駁回處理節點
                EFormSignEntity rejectHandleNode = eFormSignService.currentNode(aplyEntity.getSerialno());
                rejectHandleNode.setSignNam("重新提交");
                rejectHandleNode.setEserIp(ip);
                rejectHandleNode.setEserStat("Y");
                rejectHandleNode.setEserDate(new Date());
                eFormSignService.save(rejectHandleNode);

                String applyStat = "";//第一個審核節點狀態
                String signEmpNo = "", signEmpNam = "";
                for (int i = 0; i < eFormSignEntities.length; i++) {
                    EFormSignEntity node = eFormSignEntities[i];
                    if (node.getEserId().isEmpty()) {
                        continue;
                    }
                    // 後續的簽核節點設置偏移
                    if (applyStat == "") {
                        applyStat = String.valueOf(rejectHandleNode.getSignOrd()+node.getSignOrd());
                        signEmpNo = node.getEserId();
                        signEmpNam = node.getEserNam();
                    }
                    node.setApplyId(aplyEntity.getSerialno());
                    node.setEserMail(eSignUserinfoService
                            .findByeserIdTypNodeName(node.getEserId(),
                                    formType, node.getSignOrd().toString())
                            .getEserMail());
                    node.setCreateDate(new Date());
                    node.setSignNam(eBsCodeService.getCode(formType, node.getSignOrd()).getCodeNam());
                    node.setSignOrd(rejectHandleNode.getSignOrd()+node.getSignOrd());
                    eFormSignService.save(node);
                }
                // 初始化主表表單狀態
                auditUpdateAply(aplyEntity.getSerialno(), applyStat,
                        signEmpNo, signEmpNam);
                mailrecordService.sendMail(aplyEntity, "Y", contextPath);
            }
        } catch (Exception e) {
            this.logger.info(e.getMessage(), e);
            return Constant.RESULT.CODE_NO.getValue();
        }
        return Constant.RESULT.CODE_YES.getValue();
    }

    /**
     * 被駁回後，申請人取消申請
     * @param aplyEntity 申請單
     * @param ip 當前操作人IP地址
     * @return
     */
    @Transactional(readOnly = false)
    public String cancelApply(TempSecurityAplyEntity aplyEntity, String ip) {
        try {
            // 更新簽核記錄
            EFormSignEntity rejectHandleNode = eFormSignService.currentNode(aplyEntity.getSerialno());
            rejectHandleNode.setSignNam("取消申請");
            rejectHandleNode.setEserIp(ip);
            rejectHandleNode.setEserStat("Y");
            rejectHandleNode.setEserDate(new Date());
            eFormSignService.save(rejectHandleNode);
            // 更新申請單狀態
            auditUpdateAply(aplyEntity.getSerialno(), "C", "", "");
        } catch (Exception e) {
            this.logger.info(e.getMessage(), e);
            return Constant.RESULT.CODE_NO.getValue();
        }
        return Constant.RESULT.CODE_YES.getValue();
    }

    // 審核變更申請主表審核狀態
    public int auditUpdateAply(String serialno, String applyStat,
                                    String signEmpNo, String signEmpNam) {
        String hql = "update TempSecurityAplyEntity t  set " + "t.applyStat = '"
                + applyStat + "',t.signEmpNo = '" + signEmpNo + "',"
                + "t.signEmpNam   = '" + signEmpNam + "'  "
                + "where  t.serialno='" + serialno + "' ";
        return aplyDao.batchExecute(hql);
    }

    // 设置临时性服务申请单保安公司
    private int auditUpdateSecurityCom(String serialno, String securityCom) {
        String hql = "update TempSecurityAplyEntity t set t.securityCom = ?0 where t.serialno = ?1 ";
        return aplyDao.batchExecute(hql, securityCom, serialno);
    }

    // 设置是否产生费用
    private int auditUpdateHasCost(String serialno, String hasCost) {
        String hql = "update TempSecurityAplyEntity t set t.hasCost = ?0 where t.serialno = ?1 ";
        return aplyDao.batchExecute(hql, hasCost, serialno);
    }
}
