package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.entity.BsArrangeEntity;
import com.foxconn.ipebg.basics.entity.BsDptEntity;
import com.foxconn.ipebg.basics.entity.BsSecPerEntity;
import com.foxconn.ipebg.basics.dao.BsDptDao;
import com.foxconn.ipebg.basics.dao.BsSecPerDao;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foxconn.ipebg.system.service.DictService;

import java.util.Date;
import java.util.List;

/**
 * 警衛人員資料
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-12-21 14:54:46
 */
@Service
@Transactional(readOnly = true)
public class BsSecPerService extends
		BaseBusinessService<BsSecPerEntity, String> {
	@Autowired
	private BsSecPerDao bsSecPerDao;

	public static final String PERINFOERRORDTOS = "secperinfoerrordtos";
	@Autowired
	private UserServiceUtil serviceUtil;
	@Autowired
	private DictService dictService;

	@Override
	public HibernateDao<BsSecPerEntity, String> getEntityDao() {
		return bsSecPerDao;
	}

	public BsSecPerEntity findByEmpNo(String empNo) {
		//return this.bsSecPerDao.findUniqueBy("empNo", empNo);
		List<BsSecPerEntity> list = bsSecPerDao.find(
				"from BsSecPerEntity t where  t.empNo=?0 and  not exists(select empNo from PerInfoEntity a where a.empNo=t.empNo ) ", empNo);
		if (list.size() == 1) {
			return list.get(0);
		} else {
			return null;
		}
	}

	/**
	 * 方法描述: 條用存儲過程，有返回值
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018/12/29 下午 03:33
	 * @param TableName
	 *            相關表名
	 * @param FieldName
	 *            相關欄位名
	 * @param FirstStr
	 *            編號頭字符串
	 * @param RecLen
	 *            序號位數
	 * @Return
	 **/
	public String CreateFormNo(String TableName, String FieldName,
			String FirstStr, int RecLen) {
		return this.bsSecPerDao.CreateFormNo(TableName, FieldName, FirstStr,
				RecLen);
	}

	public List getDptInfoId() {
		String sql = "select t.id,  t.dpt_qun || '---' || t.dpt_chu || '---' || t.dpt_bu || '---' || t.dpt_ke dpt_name  from BS_DPT t";

		List list = this.bsSecPerDao.createSQLQuery(sql).list();
		return list;
	}

	public BsSecPerEntity findById(String id) {
		return this.bsSecPerDao.findUniqueBy("id", id);
	}

	public BsSecPerEntity isExistEmpInfo1(String empNo) {
		// TODO Auto-generated method stub
		List<BsSecPerEntity> list = bsSecPerDao.find(
				"from BsSecPerEntity t where  t.empNo=?0 ", empNo);
		if (list.size() == 1) {
			return list.get(0);
		} else {
			return null;
		}
	}

}
