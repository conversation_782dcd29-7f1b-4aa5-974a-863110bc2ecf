<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<html>
</head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
<title>upload</title>
<script src="${ctx}/static/plugins/jquery/jquery-1.9.1.min.js"></script>
<link rel="stylesheet" href="${ctx}/static/css/bootstrap.min.css">
<!-- 最新的 Bootstrap 核心 JavaScript 文件 -->
<script src="${ctx}/static/plugins/bootstrap/bootstrap.min.js"></script>

<style type="text/css">
    .upload {
        width: 100px;
        height: 100px;
        text-align: center;
        line-height: 90px;
        font-size: 50px;
        border: 1px solid #ccc;
    }
</style>
</head>
<body>
<div class="panel panel-primary">
    <div class="panel-heading">oss-server JavaScript 上传示例</div>
    <div class="panel-body">
        <div style="width:100%; height:150px;">
            <!--dom需要设置宽高，上传插件依赖容器宽高，自动覆盖-->
            <div style="float:left;">
                <div class="upload" id="J_upload">+</div>
            </div>
            <div style="float:left;">
                <textarea style="height:100px;width:1024px;margin-left:10px;" id="txtUpload"></textarea>
            </div>
        </div>
    </div>
</div>
<div><img src="http://localhost:18000/3232a20f295a44ccadda10d60dfc58cd" alt=""/></div>
</body>
<script src="${ctx}/static/js/upload/upload.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
    var upp = new Upload({
        el: '#J_upload', //传入#ID值或者dom
        action: 'http://**************:18000/oss/material/province_IIII/uploadMaterial', //上传接口，必填！！！
//        action: 'http://localhost:18000/oss/material/province_IIII/uploadMaterial', //上传接口，必填！！！
        data: {
            module:'/cms',
            appid:'osse29e19',
            appsecret:'3qg9my2a'
        },//其余参数
        multiple: true,
//			accept:'image/jpg', //接受的文件类型
        disabled: false,
//			maxSize:'100', //文件大小限制，单位为 byte
        formatError: function (type, file) {
            console.log(type, file)
        },
        oversize: function (size, file) {
            console.log('Current file size:' + size + ',oversize!')
            console.log(file)
        },
        beforeUpload: function (files) {
//				console.log(files)
        },
        success: function (result) {
            $("#txtUpload").val(JSON.stringify(result));
            console.log('success000', result)
        },
        error: function (error) {
            console.log('error', error)
        }
    })

</script>
</html>
