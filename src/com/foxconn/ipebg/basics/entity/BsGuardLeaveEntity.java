package com.foxconn.ipebg.basics.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.ColumnTransformer;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.LinkedHashMap;

@Entity
@Table(name = "BS_GUARD_LEAVE")
@DynamicUpdate
@DynamicInsert
public class BsGuardLeaveEntity extends DataEntity<BsGuardLeaveEntity>{
    private static final long serialVersionUID = 1L;

    private String empNo;

    private String empName;

    private Date startDate;

    private Date endDate;

    private String psnId;

    private String leaveType;

    private String shiftNo;

    private Integer dayCount;

    private String reason;

    /**
     * 導出Excel字段映射
     * @return
     */
    @Transient
    public static LinkedHashMap<String, String> exportFieldMap() {
        LinkedHashMap<String, String> fieldMap = new LinkedHashMap<String, String>();
        fieldMap.put("empNo", "工號");
        fieldMap.put("empName", "姓名");
        fieldMap.put("psnId", "身份證號碼");
        fieldMap.put("leaveType", "假別");
        fieldMap.put("shiftNo", "請假日班別");
        fieldMap.put("startDate", "請假開始日期");
        fieldMap.put("endDate", "請假結束日期");
        fieldMap.put("dayCount", "請假天數");
        fieldMap.put("reason", "請假事由");
        fieldMap.put("createBy", "創建人");
        fieldMap.put("createDate", "創建時間");
        fieldMap.put("updateBy", "修改人");
        fieldMap.put("updateDate", "修改時間");
        return fieldMap;
    }

    /**
     * 導入Excel字段映射
     * @return
     */
    @Transient
    public static LinkedHashMap<String, String> importFieldMap() {
        LinkedHashMap<String, String> fieldMap = new LinkedHashMap<String, String>();
        fieldMap.put("工號", "empNo");
        fieldMap.put("姓名", "empName");
        fieldMap.put("假別", "leaveType");
        fieldMap.put("請假日班別", "shiftNo");
        fieldMap.put("請假開始日期", "startDate");
        fieldMap.put("請假結束日期", "endDate");
        fieldMap.put("請假天數", "dayCount");
        fieldMap.put("請假事由", "reason");
        return fieldMap;
    }

    @Column(name = "EMP_NO", nullable = false, length = 20)
    public String getEmpNo() {
        return empNo;
    }

    public void setEmpNo(String empNo) {
        this.empNo = empNo;
    }

    @Column(name = "EMP_NAME", nullable = false, length = 20)
    public String getEmpName() {
        return empName;
    }

    public void setEmpName(String empName) {
        this.empName = empName;
    }

    @Column(name = "START_DATE", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    @Column(name = "END_DATE", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    @Column(name = "PSN_ID", nullable = false, length = 20)
    @ColumnTransformer(forColumn = "PSN_ID", read = "CONVERT_FROM(decrypt(decode(PSN_ID, 'base64'), 'sgas', 'aes'), 'UTF-8')", write = "encode(encrypt(cast(? as bytea), 'sgas', 'aes'), 'base64')")
    public String getPsnId() {
        return psnId;
    }

    public void setPsnId(String psnId) {
        this.psnId = psnId;
    }

    @Column(name = "LEAVE_TYPE", nullable = false, length = 20)
    public String getLeaveType() {
        return leaveType;
    }

    public void setLeaveType(String leaveType) {
        this.leaveType = leaveType;
    }

    @Column(name = "SHIFT_NO", nullable = false, length = 20)
    public String getShiftNo() {
        return shiftNo;
    }

    public void setShiftNo(String shiftNo) {
        this.shiftNo = shiftNo;
    }

    @Column(name = "DAY_COUNT")
    public Integer getDayCount() {
        return dayCount;
    }

    public void setDayCount(Integer dayCount) {
        this.dayCount = dayCount;
    }

    @Column(name = "REASON", nullable = false, length = 20)
    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}
