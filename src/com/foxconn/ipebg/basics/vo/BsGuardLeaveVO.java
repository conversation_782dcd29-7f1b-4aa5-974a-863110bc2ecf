package com.foxconn.ipebg.basics.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.foxconn.ipebg.basics.entity.BsGuardLeaveEntity;

import java.util.Date;

public class BsGuardLeaveVO {
    private String id;
    private String createBy;	// 创建者
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createDate;	// 创建日期
    private String updateBy;	// 更新者
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateDate;	// 更新日期
    private String delFlag; 	// 删除标记（0：正常；1：删除；2：审核）

    private String empNo;

    private String empName;

    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date startDate;

    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date endDate;

    private String psnId;

    private String leaveType;

    private String shiftNo;

    private Integer dayCount;

    private String reason;

    public BsGuardLeaveVO(BsGuardLeaveEntity entity) {
        this.id = entity.getId();
        this.createBy = entity.getCreateBy();
        this.createDate = entity.getCreateDate();
        this.updateBy = entity.getUpdateBy();
        this.updateDate = entity.getUpdateDate();
        this.delFlag = entity.getDelFlag();
        this.empNo = entity.getEmpNo();
        this.empName = entity.getEmpName();
        this.startDate = entity.getStartDate();
        this.endDate = entity.getEndDate();
        this.psnId = entity.getPsnId();
        this.leaveType = entity.getLeaveType();
        this.shiftNo = entity.getShiftNo();
        this.dayCount = entity.getDayCount();
        this.reason = entity.getReason();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getEmpNo() {
        return empNo;
    }

    public void setEmpNo(String empNo) {
        this.empNo = empNo;
    }

    public String getEmpName() {
        return empName;
    }

    public void setEmpName(String empName) {
        this.empName = empName;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getPsnId() {
        return psnId;
    }

    public void setPsnId(String psnId) {
        this.psnId = psnId;
    }

    public String getLeaveType() {
        return leaveType;
    }

    public void setLeaveType(String leaveType) {
        this.leaveType = leaveType;
    }

    public String getShiftNo() {
        return shiftNo;
    }

    public void setShiftNo(String shiftNo) {
        this.shiftNo = shiftNo;
    }

    public Integer getDayCount() {
        return dayCount;
    }

    public void setDayCount(Integer dayCount) {
        this.dayCount = dayCount;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}
