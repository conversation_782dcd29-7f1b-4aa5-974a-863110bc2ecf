package com.foxconn.ipebg.system.service;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.Date;
import java.util.UUID;

import com.foxconn.ipebg.buessness.audit.entity.TempSecurityAplyEntity;
import com.foxconn.ipebg.buessness.audit.entity.TempSecurityCostAplyEntity;
import com.foxconn.ipebg.system.dao.UserDao;
import com.foxconn.ipebg.system.entity.Mail;
import com.foxconn.ipebg.system.entity.TPubMailrecordEntity;
import com.foxconn.ipebg.system.dao.TPubMailrecordDao;
import com.foxconn.ipebg.audit.entity.EFormSignEntity;
import com.foxconn.ipebg.audit.service.EFormSignService;
import com.foxconn.ipebg.basics.service.ESignUserinfoService;
import com.foxconn.ipebg.buessness.audit.dao.EGuardAplyDao;
import com.foxconn.ipebg.buessness.audit.entity.EGuardAplyEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import com.foxconn.ipebg.system.utils.SendMailUtil;
import com.foxconn.ipebg.system.utils.UsernamePasswordCaptchaToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 郵件發送記錄表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-25 08:45:27
 */
@Service
@Transactional(readOnly=true)
public class  TPubMailrecordService extends BaseBusinessService<TPubMailrecordEntity, String>{
    @Autowired
    private TPubMailrecordDao tPubMailrecordDao;
	@Autowired
	private EFormSignService eFormSignService;
	@Autowired
	private UserDao userDao;
	@Autowired
	private DictService dictService;
    @Override
    public HibernateDao<TPubMailrecordEntity, String> getEntityDao() {
        return tPubMailrecordDao;
    }

    public TPubMailrecordEntity findByProperty(UsernamePasswordCaptchaToken token){
        return this.tPubMailrecordDao.findUnique("from TPubMailrecordEntity t where t.empno=?0 and t.validStr=?1",token.getUsername(),token.getUtoken());
    }

    public TPubMailrecordEntity findByValidStr(String validStr){
        return this.tPubMailrecordDao.findUniqueBy("validStr",validStr);
    }

    private String upgradeProtocol(String contextPath) {
		try {
			URL url = new URL(contextPath);
			if ("http".equals(url.getProtocol())) {
				return contextPath.replaceAll("http", "https");
			}
		} catch (MalformedURLException e) {
			return contextPath;
		}
		return contextPath;
	}

    public TPubMailrecordEntity findById(String id){
        return this.tPubMailrecordDao.findUniqueBy("id",id);
    }
    /**
	 * 郵件發送 警衛服務申請單
	 * @param aduitStat 審核狀態  Y通過 N駁回  E結案
	 * @param eGuardAply 申請單實體
	 * <AUTHOR>
	 * @date 2019-03-09 13:29:33
	 */
	//@Transactional
	public void sendMail(EGuardAplyEntity eGuardAply, String contextPath,String aduitStat){
		// 升級協議
		contextPath = upgradeProtocol(contextPath);
		// 發郵件
		// 獲取當前審核節點
		String validStr = UUID.randomUUID().toString();
		EFormSignEntity currentNode = eFormSignService
				.currentNode(eGuardAply.getSerialno());

		Mail mail = new Mail();
		mail.setUsername(eGuardAply.getMakername());
		mail.setSystemname(dictService.getDictByTypeAndVlaue(
				"sys_property", "sys_name").getLabel());
		mail.setOrdertype("警衛服務申請單");
		mail.setSerialno(eGuardAply.getSerialno());
//		mail.setDusername(currentNode.getEserNam());
//		mail.setChargerman(currentNode.getEserNam());		
//		mail.setOrderstatus(currentNode.getSignOrd().toString());
		String freeLoginUrl="";
		if (aduitStat.equals("Y"))
		{
			
			mail.setDusername(currentNode.getEserNam());
			mail.setChargerman(currentNode.getEserNam());		
			mail.setOrderstatus(currentNode.getSignOrd().toString());
			mail.setUsermail(currentNode.getEserMail());
			freeLoginUrl=contextPath
                            + "/eguardaply/auditMail/" + mail.getSerialno()
							+ "?applyStat=" + mail.getOrderstatus()
							+ "&loginType=1&utoken=" + validStr;
		}
		else if(aduitStat.equals("N")||aduitStat.equals("E"))
		{
			mail.setDusername("");
			mail.setChargerman("");		
			mail.setOrderstatus(aduitStat);
			mail.setUsermail(eGuardAply.getDealemail());
			freeLoginUrl=contextPath
                            + "/eguardaply/viewMail/" + mail.getSerialno()
							+ "?applyStat=" + mail.getOrderstatus()
							+ "&loginType=1&utoken=" + validStr;	
		}
		
		mail.setFreeloginurl(freeLoginUrl);
		mail.setUrl(contextPath);
		mail.setUrlip(contextPath);
		
		 
		

		// mail.setFreeloginurl("");
		// 保存發送記錄
		
		TPubMailrecordEntity mailrecordEntity = new TPubMailrecordEntity();
	
		
		mailrecordEntity.setNewRecord(true);
		mailrecordEntity.setAssignCreateBy(false);
		if (aduitStat.equals("Y") || currentNode!=null)
		{
		  mailrecordEntity.setCreateBy(currentNode.getEserNam());
		}
		mailrecordEntity.setCreateDate(new Date());
		
		
		mailrecordEntity.setChargerman(mail.getChargerman());
		mailrecordEntity.setDusername(mail.getDusername());
		mailrecordEntity.setOrderstatus(mail.getOrderstatus());
		mailrecordEntity.setOrdertype(mail.getOrdertype());
		mailrecordEntity.setSerialno(mail.getSerialno());
		mailrecordEntity.setUsername(mail.getUsername());
		mailrecordEntity.setUsermail(mail.getUsermail());
		mailrecordEntity.setSendStatus("0");
		// mailrecordEntity.setEmpno(info.getAssignee());
		mailrecordEntity.setFreeloginurl(freeLoginUrl);
		mailrecordEntity.setUrl(mail.getUrl());
		mailrecordEntity.setUrlip(mail.getUrlip());
		mailrecordEntity.setValidStr(validStr);

	
		String sendResult = new SendMailUtil().sendMail(mail);
		if ("0".equals(sendResult)) {
			// 發送成功，更新標誌
			mailrecordEntity.setSendStatus("1");			
		}
		
		this.save(mailrecordEntity);
		// ********************************************
		
	}

	/**
	 * 郵件發送 臨時性安保服務申請單
	 * @param aplyEntity 申請單實體
	 * @param aduitStat 審核狀態  Y通過 N駁回  E結案
	 * @param contextPath 系統地址
	 * <AUTHOR>
	 * @date 2022-07-07
	 */
	//@Transactional
	public void sendMail(TempSecurityAplyEntity aplyEntity, String aduitStat, String contextPath){
		// 升級協議
		contextPath = upgradeProtocol(contextPath);
		// 發郵件
		// 獲取當前審核節點
		String validStr = UUID.randomUUID().toString();
		EFormSignEntity currentNode = eFormSignService
				.currentNode(aplyEntity.getSerialno());

		Mail mail = new Mail();
		mail.setUsername(aplyEntity.getMakername());
		mail.setSystemname(dictService.getDictByTypeAndVlaue(
				"sys_property", "sys_name").getLabel());
		mail.setOrdertype("臨時性安保服務申請單");
		mail.setSerialno(aplyEntity.getSerialno());
		String freeLoginUrl="";
		if (aduitStat.equals("Y"))
		{
			mail.setDusername(currentNode.getEserNam());
			mail.setChargerman(currentNode.getEserNam());
			mail.setOrderstatus(currentNode.getSignOrd().toString());
			mail.setUsermail(currentNode.getEserMail());
			freeLoginUrl=contextPath
					+ "/tempSecurityAply/auditMail/" + mail.getSerialno()
					+ "?applyStat=" + mail.getOrderstatus()
					+ "&loginType=1&utoken=" + validStr;
		}
		else if(aduitStat.equals("E") || aduitStat.equals("N"))
		{
			mail.setDusername("");
			mail.setChargerman("");
			mail.setOrderstatus(aduitStat);

			mail.setUsermail(userDao.findUniqueBy("loginName", aplyEntity.getMakerno()).getEmail());
			freeLoginUrl=contextPath
					+ "/tempSecurityAply/viewMail/" + mail.getSerialno()
					+ "?applyStat=" + mail.getOrderstatus()
					+ "&loginType=1&utoken=" + validStr;
		}

		mail.setFreeloginurl(freeLoginUrl);
		mail.setUrl(contextPath);
		mail.setUrlip(contextPath);
		// 保存發送記錄
		TPubMailrecordEntity mailrecordEntity = new TPubMailrecordEntity();
		mailrecordEntity.setNewRecord(true);
		mailrecordEntity.setAssignCreateBy(false);
		if (aduitStat.equals("Y") || currentNode!=null)
		{
			mailrecordEntity.setCreateBy(currentNode.getEserNam());
		}
		mailrecordEntity.setCreateDate(new Date());
		mailrecordEntity.setChargerman(mail.getChargerman());
		mailrecordEntity.setDusername(mail.getDusername());
		mailrecordEntity.setOrderstatus(mail.getOrderstatus());
		mailrecordEntity.setOrdertype(mail.getOrdertype());
		mailrecordEntity.setSerialno(mail.getSerialno());
		mailrecordEntity.setUsername(mail.getUsername());
		mailrecordEntity.setUsermail(mail.getUsermail());
		mailrecordEntity.setSendStatus("0");
		mailrecordEntity.setFreeloginurl(freeLoginUrl);
		mailrecordEntity.setUrl(mail.getUrl());
		mailrecordEntity.setUrlip(mail.getUrlip());
		mailrecordEntity.setValidStr(validStr);

		String sendResult = new SendMailUtil().sendMail(mail);
		if ("0".equals(sendResult)) {
			// 發送成功，更新標誌
			mailrecordEntity.setSendStatus("1");
		}
		this.save(mailrecordEntity);
		// ********************************************
	}

	/**
	 * 郵件發送 臨時性安保服務費用結算申請單
	 * @param aplyEntity 申請單實體
	 * @param aduitStat 審核狀態  Y通過 N駁回  E結案
	 * @param contextPath 系統地址
	 * <AUTHOR>
	 * @date 2022-07-07
	 */
	//@Transactional
	public void sendMail(TempSecurityCostAplyEntity aplyEntity, String aduitStat, String contextPath){
		// 升級協議
		contextPath = upgradeProtocol(contextPath);
		// 發郵件
		// 獲取當前審核節點
		String validStr = UUID.randomUUID().toString();
		EFormSignEntity currentNode = eFormSignService
				.currentNode(aplyEntity.getSerialno());

		Mail mail = new Mail();
		mail.setUsername(aplyEntity.getMakername());
		mail.setSystemname(dictService.getDictByTypeAndVlaue(
				"sys_property", "sys_name").getLabel());
		mail.setOrdertype("臨時性安保服務費用結算申請單");
		mail.setSerialno(aplyEntity.getSerialno());
		String freeLoginUrl="";
		if (aduitStat.equals("Y"))
		{
			mail.setDusername(currentNode.getEserNam());
			mail.setChargerman(currentNode.getEserNam());
			mail.setOrderstatus(currentNode.getSignOrd().toString());
			mail.setUsermail(currentNode.getEserMail());
			freeLoginUrl=contextPath
					+ "/tempSecurityCostAply/auditMail/" + mail.getSerialno()
					+ "?applyStat=" + mail.getOrderstatus()
					+ "&loginType=1&utoken=" + validStr;
		}
		else if(aduitStat.equals("N")||aduitStat.equals("E"))
		{
			mail.setDusername("");
			mail.setChargerman("");
			mail.setOrderstatus(aduitStat);

			mail.setUsermail(userDao.findUniqueBy("loginName", aplyEntity.getMakerno()).getEmail());
			freeLoginUrl=contextPath
					+ "/tempSecurityCostAply/viewMail/" + mail.getSerialno()
					+ "?applyStat=" + mail.getOrderstatus()
					+ "&loginType=1&utoken=" + validStr;
		}

		mail.setFreeloginurl(freeLoginUrl);
		mail.setUrl(contextPath);
		mail.setUrlip(contextPath);
		// 保存發送記錄
		TPubMailrecordEntity mailrecordEntity = new TPubMailrecordEntity();
		mailrecordEntity.setNewRecord(true);
		mailrecordEntity.setAssignCreateBy(false);
		if (aduitStat.equals("Y") || currentNode!=null)
		{
			mailrecordEntity.setCreateBy(currentNode.getEserNam());
		}
		mailrecordEntity.setCreateDate(new Date());
		mailrecordEntity.setChargerman(mail.getChargerman());
		mailrecordEntity.setDusername(mail.getDusername());
		mailrecordEntity.setOrderstatus(mail.getOrderstatus());
		mailrecordEntity.setOrdertype(mail.getOrdertype());
		mailrecordEntity.setSerialno(mail.getSerialno());
		mailrecordEntity.setUsername(mail.getUsername());
		mailrecordEntity.setUsermail(mail.getUsermail());
		mailrecordEntity.setSendStatus("0");
		mailrecordEntity.setFreeloginurl(freeLoginUrl);
		mailrecordEntity.setUrl(mail.getUrl());
		mailrecordEntity.setUrlip(mail.getUrlip());
		mailrecordEntity.setValidStr(validStr);

		String sendResult = new SendMailUtil().sendMail(mail);
		if ("0".equals(sendResult)) {
			// 發送成功，更新標誌
			mailrecordEntity.setSendStatus("1");
		}
		this.save(mailrecordEntity);
		// ********************************************
	}
}
