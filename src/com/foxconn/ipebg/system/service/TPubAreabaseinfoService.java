package com.foxconn.ipebg.system.service;

import com.foxconn.ipebg.system.entity.TPubAreabaseinfoEntity;
import com.foxconn.ipebg.system.dao.TPubAreabaseinfoDao;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * 各廠區樓棟及區域對應關係表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-11-27 09:33:14
 */
@Service
@Transactional(readOnly=true)
public class  TPubAreabaseinfoService extends BaseBusinessService<TPubAreabaseinfoEntity, String>{
    @Autowired
    private TPubAreabaseinfoDao tPubAreabaseinfoDao;
    @Override
    public HibernateDao<TPubAreabaseinfoEntity, String> getEntityDao() {
        return tPubAreabaseinfoDao;
    }

    public List<TPubAreabaseinfoEntity> getAreaInfoBySuperId(String superId){
        return tPubAreabaseinfoDao.getAreaInfoBySuperId(superId);
    }
}
