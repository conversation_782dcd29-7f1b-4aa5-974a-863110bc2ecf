package com.foxconn.ipebg.buessness.common.service;

import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.buessness.common.dao.TQhAllRelationDao;
import com.foxconn.ipebg.buessness.workflow.entity.Gtasks;
import com.foxconn.ipebg.buessness.workflow.entity.MyTaskCount;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import com.foxconn.ipebg.common.utils.Constant;
import org.hibernate.Criteria;
import org.hibernate.Query;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;


/**
 * 簽核業務中間表，保存所有簽核業務主信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-08 09:28:35
 */
@Service
@Transactional(readOnly = true)
public class TQhAllRelationService extends BaseBusinessService<TQhAllRelationEntity, String> {
    @Autowired
    private TQhAllRelationDao tQhAllRelationDao;

    @Override
    public HibernateDao<TQhAllRelationEntity, String> getEntityDao() {
        return tQhAllRelationDao;
    }

    public List<TQhAllRelationEntity> queryByList(List<String> ids) {
        return tQhAllRelationDao.find(ids);
    }

//    public List<MyTaskCount> queryIndexTask(List<String> ids) {
//        String hql = "select distinct m.workflowid,m.wfName as workflowname,count(m.workflowid) as taskCount,'2' as flag" +
//                " from TQhAllRelationEntity m where m.processid in (:processIds) and m.workstatus=2" +
//                " group by m.workflowid, m.wfName";
//
//        return tQhAllRelationDao.findIndexData(hql, ids);
//    }
//
//    public List<MyTaskCount> queryIndexBackTask(List<String> ids) {
//        String hql = "select distinct m.workflowid,m.wfName as workflowname,count(m.workflowid) as taskCount,'4' as flag" +
//                " from TQhAllRelationEntity m where m.processid in (:processIds) and m.workstatus=4" +
//                " group by m.workflowid, m.wfName";
//
//        return tQhAllRelationDao.findIndexData(hql, ids);
//    }

    public TQhAllRelationEntity queryByEntity(String serialno) {
        return tQhAllRelationDao.findUniqueBy("serialno", serialno);
    }

//    public List<Map<String, Object>> findToMapBySql(Object args) {
//        return tQhAllRelationDao.findToMapBySql("from TQhChargelogEntity where serialno=:serialno", args);
//    }
//
//    public String updateFormStatic(String serialno, String workstatus, String entity) {
//        return tQhAllRelationDao.updateFormStatic("update " + entity + " t set t.workstatus=:workstatus where serialno=:serialno", workstatus, serialno);
//    }
//
//    public String updateFormStaticComplete(String serialno, String workstatus, String entity) {
//        return tQhAllRelationDao.updateFormStatic("update " + entity + " t set t.workstatus=:workstatus,t.complettime=sysdate where serialno=:serialno", workstatus, serialno);
//    }
//
//    public String updateFormProcessId(String serialno, String processId, String entity) {
//        return tQhAllRelationDao.updateFormProcessId("update " + entity + " t set t.processid=:processid where serialno=:serialno", serialno, processId);
//    }
//
//    public String updateFormReattachids(String serialno, String reattachids, String entity) {
//        return tQhAllRelationDao.updateFormReattachids("update " + entity + " t set t.reattachids=:reattachids where serialno=:serialno", serialno, reattachids);
//    }


    public List<TQhAllRelationEntity> queryByProcessIds(List<String> processIds) {
//        return tQhAllRelationDao.findIn("processid", processIds);
        return tQhAllRelationDao.findInOrderBy("processid", processIds, "createDate", false);

    }

//    public Gtasks queryMyGtasks(String serialno, String dtoName) {
//        String hql = "select t.makerno,t.makername,t.createtime,t.workstatus from " + dtoName + " t where t.serialno=:serialno";
//        return tQhAllRelationDao.findGtasks(hql, serialno);
//    }

    public List<TQhAllRelationEntity> getMyTaskByWorkId(String workFlowId, List<String> processIds, String status) {
        Criteria criteria = null;
        Criterion criteriaEq = Restrictions.eq("workflowid", workFlowId);
        Criterion criteriaIn = Restrictions.in("processid", processIds);
        if (status != null) {
            Criterion criteriaEq2 = Restrictions.eq("workstatus", status);
            criteria = this.tQhAllRelationDao.createCriteria(criteriaEq, criteriaIn, criteriaEq2);
        } else {
            criteria = this.tQhAllRelationDao.createCriteria(criteriaEq, criteriaIn);
        }
        criteria.addOrder(Order.desc("createDate"));
        return criteria.list();
    }

    @Transactional
    public String deleteRelationEntity(String id) {
        TQhAllRelationEntity entity = this.tQhAllRelationDao.findUniqueBy("id", id);
        String hql = "delete from " + entity.getDtoName() + " t where t.serialno=?0";
        this.tQhAllRelationDao.batchExecute(hql, entity.getSerialno());
        return Constant.RESULT.CODE_YES.getValue();
    }

//    public String findUserEmpno(String classDto, String serialno) {
//        String hql = "select t.makerno from " + classDto + " t where t.serialno=:serialno";
//        return this.tQhAllRelationDao.findUserEmpno(hql, serialno);
//    }

    /**
     * 方法描述: 獲取我的已辦任務
     *
     * @Author: S6114648
     * @CreateDate: 2018/12/12  上午 09:26
     * @Return
     **/

//    public List<TQhAllRelationEntity> getMyDownTask(String empno, Page page) {
//        String hql = "select * from (select s.workflowid,s.serialno,s.processid,s.version,s.dto_name as dtoName," +
//                "s.workstatus,s.wf_name as wfName,s.id,s.create_date as createDate,s.create_by as createBy," +
//                "s.update_by as updateBy,s.update_date as updateDate,ROWNUM RN " +
//                "from t_qh_all_relation s where exists (select distinct t.serialno from t_qh_chargelog t " +
//                "where t.chargeno='" + empno + "' and s.serialno=t.serialno)) WHERE RN BETWEEN " +
//                ((page.getPageNo() - 1) * page.getPageSize() + 1) + " AND " + page.getPageNo() * page.getPageSize()+
//                "order by "+page.getOrderBy()+" "+page.getOrder();
//        return this.tQhAllRelationDao.getMyDownTask(hql);
//    }
//
//    public int getMyDownTaskCount(String empno) {
//        String hql = "select count(*) as num from (select s.workflowid,s.serialno,s.processid,s.version,s.dto_name as dtoName" +
//                ",s.workstatus,s.wf_name as wfName,s.id,s.create_date as createDate,s.create_by as createBy," +
//                "s.update_by as updateBy,s.update_date as updateDate from t_qh_all_relation s where exists " +
//                "(select distinct t.serialno from t_qh_chargelog t where t.chargeno='" + empno +
//                "' and s.serialno=t.serialno))";
//        return this.tQhAllRelationDao.getMyDownTaskCount(hql);
//    }
}
