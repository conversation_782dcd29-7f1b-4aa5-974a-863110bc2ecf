package com.foxconn.ipebg.common.utils;

import cn.hutool.core.date.DateUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhFactoryidconfigEntity;
import com.foxconn.ipebg.buessness.common.service.TQhFactoryidconfigService;
import com.foxconn.ipebg.buessness.workflow.entity.TaskNode;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.system.entity.Dict;
import com.foxconn.ipebg.system.service.DictService;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.beanutils.converters.DateConverter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import java.lang.reflect.Field;
import java.text.DateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

public class ConvertUtils {
    private static final WebApplicationContext wac;

    static {
        registerDateConverter();
        wac = ContextLoader.getCurrentWebApplicationContext();
    }

    /**
     * 提取集合中的对象的属性(通过getter函数), 组合成List.
     *
     * @param collection   来源集合.
     * @param propertyName 要提取的属性名.
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public static List convertElementPropertyToList(final Collection collection, final String propertyName) {
        List list = new ArrayList();

        try {
            for (Object obj : collection) {
                list.add(PropertyUtils.getProperty(obj, propertyName));
            }
        } catch (Exception e) {
            throw Reflections.convertReflectionExceptionToUnchecked(e);
        }

        return list;
    }

    /**
     * 提取集合中的对象的属性(通过getter函数), 组合成由分割符分隔的字符串.
     *
     * @param collection   来源集合.
     * @param propertyName 要提取的属性名.
     * @param separator    分隔符.
     */
    @SuppressWarnings({"rawtypes"})
    public static String convertElementPropertyToString(final Collection collection, final String propertyName,
                                                        final String separator) {
        List list = convertElementPropertyToList(collection, propertyName);
        return StringUtils.join(list, separator);
    }

    /**
     * 转换字符串到相应类型.
     *
     * @param value  待转换的字符串.
     * @param toType 转换目标类型.
     */
    public static Object convertStringToObject(String value, Class<?> toType) {
        try {
            return org.apache.commons.beanutils.ConvertUtils.convert(value, toType);
        } catch (Exception e) {
            throw Reflections.convertReflectionExceptionToUnchecked(e);
        }
    }

    /**
     * 定义日期Converter的格式: yyyy-MM-dd 或 yyyy-MM-dd HH:mm:ss
     */
    private static void registerDateConverter() {
        DateConverter dc = new DateConverter();
       // dc.setUseLocaleFormat(true);
        dc.setPatterns(new String[]{"yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss"});
        org.apache.commons.beanutils.ConvertUtils.register(dc, Date.class);
    }

    /**
     * 方法描述: 將返回的數據替換為字典中的值
     *
     * @Author: S6114648
     * @CreateDate: 2018/12/6  下午 01:46
     * @Return
     **/

    public static void convertPropertyToDictLabel(final Page page, final String propertyName, final String dictType) {
        Assert.notNull(propertyName);
        Assert.notNull(dictType);
        DictService dictService = (DictService) wac.getBean("dictService");
        List<Dict> dtList = dictService.getDictByType(dictType);
        for (Object obj : page.getResult()) {
            for (Dict dict : dtList) {
                if (Reflections.getFieldValue(obj, propertyName) != null) {
                    if (Reflections.getFieldValue(obj, propertyName).equals(dict.getValue())) {
                        Reflections.setFieldValue(obj, propertyName, dict.getLabel());
                        break;
                    }
                }
            }
        }
    }

    public static void convertPropertyToDictLabel(final List list, final String propertyName, final String dictType) {
        Assert.notNull(propertyName);
        Assert.notNull(dictType);
        DictService dictService = (DictService) wac.getBean("dictService");
        List<Dict> dtList = dictService.getDictByType(dictType);
        for (Object obj : list) {
            for (Dict dict : dtList) {
                if (Reflections.getFieldValue(obj, propertyName) != null) {
                    if (Reflections.getFieldValue(obj, propertyName).equals(dict.getValue())) {
                        Reflections.setFieldValue(obj, propertyName, dict.getLabel());
                        break;
                    }
                }
            }
        }
    }

    // 轉換申請單狀體
    public static void convertApplyState(final List list, final String propertyName) {
        for (Object obj : list) {
            if (Reflections.getFieldValue(obj, propertyName) != null) {
                if (Reflections.getFieldValue(obj, propertyName).equals("N")) {
                    Reflections.setFieldValue(obj, propertyName, "已駁回");
                } else if (Reflections.getFieldValue(obj, propertyName).equals("E")) {
                    Reflections.setFieldValue(obj, propertyName, "已結案");
                } else {
                    Reflections.setFieldValue(obj, propertyName, "申請中");
                }
            }
        }
    }

    /**
      * 方法描述: page中的廠區id換成name
      * @Author: S6114648
      * @CreateDate:   2018/12/8  下午 01:41
      * @Return
      **/

    public static void convertFactoryIdToFactoryName(final Page page, final String factoryId) {
        Assert.notNull(factoryId);
        TQhFactoryidconfigService service = (TQhFactoryidconfigService) wac.getBean("TQhFactoryidconfigService");
        TQhFactoryidconfigEntity dtList =null;
        for (Object obj : page.getResult()) {
            if (Reflections.getFieldValue(obj, factoryId) != null) {
                dtList = service.findByFactiryid(Reflections.getFieldValue(obj, factoryId).toString());
                if(dtList!=null) {
                    Reflections.setFieldValue(obj, factoryId, dtList.getFactoryname());
                }
            }
        }
    }

    /**
     * 方法描述: 返回的page對象添加審核節點和當前審核人
     *
     * @Author: S6114648
     * @CreateDate: 2018/12/6  下午 04:43
     * @Return
     **/

    public static void convertPropertyAuditInfo(final Page page, final String[] propertyNames, String serialno) {
        Assert.notNull(propertyNames);
        WorkFlowService workFlowService = (WorkFlowService) wac.getBean("workFlowService");
        for (Object obj : page.getResult()) {
            TaskNode node = workFlowService.getNodeInfo(Reflections.getFieldValue(obj, serialno).toString());
            if (node != null && node.getAuditUser() != null && node.getNodeName() != null) {
                for (String propertyName : propertyNames) {
                    if (Reflections.getFieldValue(node, propertyName) != null) {
                        Reflections.setFieldValue(obj, propertyName, Reflections.invokeGetter(node, propertyName));
                    }
                }
            }
        }
    }

    /**
     * 方法描述: 格式化時間
     *
     * @Author: S6114648
     * @CreateDate: 2018/12/8  上午 11:40
     * @Return
     **/

    public static void confertDateToStanded(Object list) {
        for (Object o : (List) list) {
            Class clazz = o.getClass();
            // 获取实体类的所有属性信息，返回Field数组
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                if (field.getGenericType().toString().equals("class java.util.Date")) {
                    if (Reflections.invokeGetter(o, field.getName()) != null) {
                        Reflections.setFieldValue(o, field.getName(), DateUtil.parse(Reflections.invokeGetter(o, field.getName()).toString(), "yyyy-MM-dd HH:mm:ss"));
                    }
                }
            }
        }
    }
}

