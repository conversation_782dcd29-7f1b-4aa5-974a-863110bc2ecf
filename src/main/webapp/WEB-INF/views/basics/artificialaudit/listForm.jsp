<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>人工稽核錄入基本資料</title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
</script>

<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
	<form id="mainform" action="${ctx}/artificialaudit/${action}"
		method="post">
		<table class="formTable">

			<tr>
				<input id="ids" name="ids" type="hidden"
					value="${artificialAudit.id }" />

			</tr>
			<tr>
				<td>排班日期：</td>
				<td><input id="shiftDate" name="shiftDate" class="easyui-my97"
					datefmt="yyyy-MM-dd" data-options="width: 150"
					value="<fmt:formatDate value="${artificialAudit.shiftDate}"/>" />
				</td>
			</tr>
			<tr>
				<td>工號：</td>
				<td><input id="empNo" name="empNo" class="easyui-validatebox"
					data-options="width: 150" value="${artificialAudit.empNo }" onblur="queryEmpInfo(this);" /></td>
			</tr>
			<tr>
				<td>姓名：</td>
				<td><input id="empName" name="empName"
					class="easyui-validatebox" data-options="width: 150" readonly
					value="${artificialAudit.empName }" /></td>
			</tr>
		 	<tr style="display: none">
				<td>
					<input id="company" name="company"
					class="easyui-validatebox" data-options="width: 150" readonly
					value="${artificialAudit.company }" />
					<input id="postRecno" name="postRecno"
						   class="easyui-validatebox" data-options="width: 150" readonly
						   value="${artificialAudit.postRecno }" />
					<input id="shiftNo" name="shiftNo"
						   class="easyui-validatebox" data-options="width: 150" readonly
						   value="${artificialAudit.shiftNo }" />
				</td>
			</tr>
	<%-- 		<tr>
				<td>崗位編號：</td>
				<td><input id="postRecno" name="postRecno"
					class="easyui-validatebox" data-options="width: 150" readonly
					value="${artificialAudit.postRecno }" /></td>
			</tr> --%>
			<tr>
				<td>崗位名稱/培訓期：</td>
				<td><input id="postName" name="postName"
					class="easyui-validatebox" data-options="width: 150" readonly
					value="${artificialAudit.postName }" /></td>
			</tr>

<%-- 			<tr>
				<td>班別代碼：</td>
				<td><input id="shiftNo" name="shiftNo"
					class="easyui-validatebox" data-options="width: 150" readonly
					value="${artificialAudit.shiftNo }" /></td>
			</tr> --%>
			<tr>
				<td>班別：</td>
				<td><input id="shiftName" name="shiftName"
					class="easyui-validatebox" data-options="width: 150" readonly
					value="${artificialAudit.shiftName }" /></td>
			</tr>
			<%-- <tr>
				<td>上班時間：</td>
				<td><input id="cardTime" name="cardTime" class="easyui-my97"
					datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"
					disabled="disabled"
					value="<fmt:formatDate value="${artificialAudit.cardTime}"/>" /></td>
			</tr> --%>
			<tr>
				<td>狀態：</td>
				<td><select id="abnormalState" class="easyui-combobox"
					name="abnormalState" style="width:150px;">
						<option value="Y">Y</option>
						<option value="N"
							<%-- <c:if test="${artificialAudit.abnormalState !='Y'}"> selected="ture" </c:if> --%>>N</option>
				</select></td>
			</tr>
			<tr>
				<td>第一次稽核結果：</td>
				<td><select id="abnormalResult1" class="easyui-combobox"
					name="abnormalResult1" style="width:150px;">
						<option value="Y">正常</option>
						<option value="N"
							<%-- <c:if test="${artificialAudit.abnormalResult1 !='Y'}"> selected="ture" </c:if> --%>>異常</option>
				</select></td>
			</tr>
			<tr>
				<td>第一次異常原因：</td>
				<td><input id="abnormalCauses1" name="abnormalCauses1"
					class="easyui-validatebox" data-options="width: 150"
					value="${artificialAudit.abnormalCauses1 }" /></td>
			</tr>
			<tr>
				<td>第二次稽核結果：</td>
				<td><select id="abnormalResult2" class="easyui-combobox"
					name="abnormalResult2" style="width:150px;">
						<option value="Y">正常</option>
						<option value="N"
							<%-- <c:if test="${artificialAudit.abnormalResult2 !='Y'}"> selected="ture" </c:if> --%>>異常</option>
				</select></td>
			</tr>
			<tr>
				<td>第二次異常原因：</td>
				<td><input id="abnormalCauses2" name="abnormalCauses2"
					class="easyui-validatebox" data-options="width: 150"
					value="${artificialAudit.abnormalCauses2 }" /></td>
			</tr>
					

		</table>
	</form>

	<script
		src='${ctx}/static/js/basics/artificialaudit.js?"+Math.random()"'></script>
	<script type="text/javascript">
		//提交表单
		$('#mainform').form({
			onSubmit : function() {
				var isValid = $(this).form('validate');
				return isValid; // 返回false终止表单提交
			},
			success : function(data) {
				successTip(data, dg, d);
			}
		});
	</script>
</body>
</html>