package com.foxconn.ipebg.common.persistence;

import java.io.Serializable;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.*;

import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.system.entity.Dict;
import org.hibernate.Criteria;
import org.hibernate.Query;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.*;
import org.hibernate.internal.CriteriaImpl;
import org.hibernate.jdbc.Work;
import org.hibernate.transform.ResultTransformer;
import org.springframework.util.Assert;

import com.foxconn.ipebg.common.utils.Reflections;

/**
 * 封装SpringSide扩展功能的Hibernat DAO泛型基类.
 * <p>
 * 扩展功能包括分页查询,按属性过滤条件列表查询. 可在Service层直接使用,也可以扩展泛型DAO子类使用,见两个构造函数的注释.
 * 
 * @param <T>
 *            DAO操作的对象类型
 * @param <PK>
 *            主键类型
 * <AUTHOR>
 */
public class HibernateDao<T, PK extends Serializable> extends
		SimpleHibernateDao<T, PK> {
	/**
	 * 用于Dao层子类的构造函数. 通过子类的泛型定义取得对象类型Class. eg. public class UserDao extends
	 * HibernateDao<User, Long>{ }
	 */
	public HibernateDao() {
		super();
	}

	/**
	 * 用于省略Dao层, Service层直接使用通用HibernateDao的构造函数. 在构造函数中定义对象类型Class. eg.
	 * HibernateDao<User, Long> userDao = new HibernateDao<User,
	 * Long>(sessionFactory, User.class);
	 */
	public HibernateDao(final SessionFactory sessionFactory,
			final Class<T> entityClass) {
		super(sessionFactory, entityClass);
	}

	// -- 分页查询函数 --//

	/**
	 * 分页获取全部对象.
	 */
	public Page<T> getAll(final Page<T> page) {
		return findPage(page);
	}

	/**
	 * 按HQL分页查询.
	 * 
	 * @param page
	 *            分页参数. 注意不支持其中的orderBy参数.
	 * @param hql
	 *            hql语句.
	 * @param values
	 *            数量可变的查询参数,按顺序绑定.
	 * @return 分页查询结果, 附带结果列表及所有查询输入参数.
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Page<T> findPage(final Page<T> page, final String hql,
			final Object... values) {
		Assert.notNull(page, "page不能为空");

		Query q = createQuery(hql, values);

		if (page.isAutoCount()) {
			long totalCount = countHqlResult(hql, values);
			page.setTotalCount(totalCount);
		}

		setPageParameterToQuery(q, page);

		List result = q.list();
		page.setResult(result);
		return page;
	}

	/**
	 * 按HQL分页查询.
	 * 
	 * @param page
	 *            分页参数. 注意不支持其中的orderBy参数.
	 * @param hql
	 *            hql语句.
	 * @param values
	 *            命名参数,按名称绑定.
	 * @return 分页查询结果, 附带结果列表及所有查询输入参数.
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Page<T> findPage(final Page<T> page, final String hql,
			final Map<String, ?> values) {
		Assert.notNull(page, "page不能为空");

		Query q = createQuery(hql, values);

		if (page.isAutoCount()) {
			long totalCount = countHqlResult(hql, values);
			page.setTotalCount(totalCount);
		}

		setPageParameterToQuery(q, page);

		List result = q.list();
		page.setResult(result);
		return page;
	}

	/**
	 * 按Criteria分页查询.
	 * 
	 * @param page
	 *            分页参数.
	 * @param criterions
	 *            数量可变的Criterion.
	 * @return 分页查询结果.附带结果列表及所有查询输入参数.
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Page<T> findPage(final Page<T> page, final Criterion... criterions) {
		Assert.notNull(page, "page不能为空");

		Criteria c = createCriteria(criterions);

		if (page.isAutoCount()) {
			long totalCount = countCriteriaResult(c);
			page.setTotalCount(totalCount);
		}

		setPageParameterToCriteria(c, page);

		List result = c.list();
		page.setResult(result);
		return page;
	}

	/**
	 * 设置分页参数到Query对象,辅助函数.
	 */
	protected Query setPageParameterToQuery(final Query q, final Page<T> page) {
		Assert.isTrue(page.getPageSize() > 0, "Page Size must larger than zero");
		// hibernate的firstResult的序号从0开始
		q.setFirstResult(page.getFirst() - 1);
		q.setMaxResults(page.getPageSize());
		return q;
	}

	/**
	 * 设置分页参数到Criteria对象,辅助函数.
	 */
	protected Criteria setPageParameterToCriteria(final Criteria c,
			final Page<T> page) {

		Assert.isTrue(page.getPageSize() > 0, "Page Size must larger than zero");

		// hibernate的firstResult的序号从0开始
		if (page.getPageSize() != Page.MAXSIZE) {
			c.setFirstResult(page.getFirst() - 1);
			c.setMaxResults(page.getPageSize());
		}

		if (page.isOrderBySetted()) {
			String[] orderByArray = StringUtils.split(page.getOrderBy(), ',');
			String[] orderArray = StringUtils.split(page.getOrder(), ',');

			Assert.isTrue(orderByArray.length == orderArray.length,
					"分页多重排序参数中,排序字段与排序方向的个数不相等");

			for (int i = 0; i < orderByArray.length; i++) {
				if (Page.ASC.equals(orderArray[i])) {
					c.addOrder(Order.asc(orderByArray[i]));
				} else {
					c.addOrder(Order.desc(orderByArray[i]));
				}
			}
		}
		return c;
	}

	/**
	 * 执行count查询获得本次Hql查询所能获得的对象总数.
	 * <p>
	 * 本函数只能自动处理简单的hql语句,复杂的hql查询请另行编写count语句查询.
	 */
	protected long countHqlResult(final String hql, final Object... values) {
		String countHql = prepareCountHql(hql);

		try {
			Long count = findUnique(countHql, values);
			return count;
		} catch (Exception e) {
			throw new RuntimeException("hql can't be auto count, hql is:"
					+ countHql, e);
		}
	}

	/**
	 * 执行count查询获得本次Hql查询所能获得的对象总数.
	 * <p>
	 * 本函数只能自动处理简单的hql语句,复杂的hql查询请另行编写count语句查询.
	 */
	protected long countHqlResult(final String hql, final Map<String, ?> values) {
		String countHql = prepareCountHql(hql);

		try {
			Long count = findUnique(countHql, values);
			return count;
		} catch (Exception e) {
			throw new RuntimeException("hql can't be auto count, hql is:"
					+ countHql, e);
		}
	}

	private String prepareCountHql(String orgHql) {
		String fromHql = orgHql;
		// select子句与order by子句会影响count查询,进行简单的排除.
		fromHql = "from " + StringUtils.substringAfter(fromHql, "from");
		fromHql = StringUtils.substringBefore(fromHql, "order by");

		String countHql = "select count(*) " + fromHql;
		return countHql;
	}

	/**
	 * 执行count查询获得本次Criteria查询所能获得的对象总数.
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	protected long countCriteriaResult(final Criteria c) {
		CriteriaImpl impl = (CriteriaImpl) c;

		// 先把Projection、ResultTransformer、OrderBy取出来,清空三者后再执行Count操作
		Projection projection = impl.getProjection();
		ResultTransformer transformer = impl.getResultTransformer();

		List<CriteriaImpl.OrderEntry> orderEntries = null;
		try {
			orderEntries = (List) Reflections.getFieldValue(impl,
					"orderEntries");
			Reflections.setFieldValue(impl, "orderEntries", new ArrayList());
		} catch (Exception e) {
			logger.error("不可能抛出的异常:{}", e.getMessage());
		}

		// 执行Count查询
		Long totalCountObject = (Long) c.setProjection(Projections.rowCount())
				.uniqueResult();
		long totalCount = (totalCountObject != null) ? totalCountObject : 0;

		// 将之前的Projection,ResultTransformer和OrderBy条件重新设回去
		c.setProjection(projection);

		if (projection == null) {
			c.setResultTransformer(CriteriaSpecification.ROOT_ENTITY);
		}
		if (transformer != null) {
			c.setResultTransformer(transformer);
		}
		try {
			Reflections.setFieldValue(impl, "orderEntries", orderEntries);
		} catch (Exception e) {
			logger.error("不可能抛出的异常:{}", e.getMessage());
		}

		return totalCount;
	}

	// -- 属性过滤条件(PropertyFilter)查询函数 --//

	/**
	 * 按属性查找对象列表,支持多种匹配方式.
	 * 
	 * @param matchType
	 *            匹配方式,目前支持的取值见PropertyFilter的MatcheType enum.
	 */
	public List<T> findBy(final String propertyName, final Object value,
			final PropertyFilter.MatchType matchType) {
		Criterion criterion = buildCriterion(propertyName, value, matchType);
		return find(criterion);
	}

	/**
	 * 按属性过滤条件列表查找对象列表.
	 */
	public List<T> find(List<PropertyFilter> filters) {
		Criterion[] criterions = buildCriterionByPropertyFilter(filters);
		return find(criterions);
	}

	/**
	 * 按属性过滤条件列表查找对象列表,並排序
	 */
	public List<T> findOrder(List<PropertyFilter> filters,
			String orderByProperty, boolean isAsc) {
		Criterion[] criterions = buildCriterionByPropertyFilter(filters);
		return findOrderBy(orderByProperty, isAsc, criterions);
	}

	/**
	 * 按属性过滤条件列表分页查找对象.
	 */
	public Page<T> findPage(final Page<T> page,
			final List<PropertyFilter> filters) {
		Criterion[] criterions = buildCriterionByPropertyFilter(filters);
		return findPage(page, criterions);
	}

	/**
	 * 按属性条件参数创建Criterion,辅助函数.
	 */
	protected Criterion buildCriterion(final String propertyName,
			final Object propertyValue, final PropertyFilter.MatchType matchType) {
		Assert.hasText(propertyName, "propertyName不能为空");
		Criterion criterion = null;
		// 根据MatchType构造criterion
		switch (matchType) {
		case EQ:
			criterion = Restrictions.eq(propertyName, propertyValue);
			break;
		case LIKE:
			criterion = Restrictions.like(propertyName, (String) propertyValue,
					MatchMode.ANYWHERE);
			break;
		case NQ:
			criterion = Restrictions.ne(propertyName, propertyValue);
			break;
		case LE:
			criterion = Restrictions.le(propertyName, propertyValue);
			break;
		case LT:
			criterion = Restrictions.lt(propertyName, propertyValue);
			break;
		case GE:
			criterion = Restrictions.ge(propertyName, propertyValue);
			break;
		case GT:
			criterion = Restrictions.gt(propertyName, propertyValue);
			break;
		case IN:
			criterion = Restrictions.in(propertyName,
					(Collection) propertyValue);
			break;
		case NI:
			criterion = Restrictions.not(Restrictions.in(propertyName, (Collection)propertyValue));
			break;
		}
		return criterion;
	}

	/**
	 * 按属性条件列表创建Criterion数组,辅助函数.
	 */
	protected Criterion[] buildCriterionByPropertyFilter(
			final List<PropertyFilter> filters) {
		List<Criterion> criterionList = new ArrayList<Criterion>();
		for (PropertyFilter filter : filters) {
			if (!filter.hasMultiProperties()) { // 只有一个属性需要比较的情况.
				Criterion criterion = buildCriterion(filter.getPropertyName(),
						filter.getMatchValue(), filter.getMatchType());
				criterionList.add(criterion);
			} else {// 包含多个属性需要比较的情况,进行or处理.
				Disjunction disjunction = Restrictions.disjunction();
				for (String param : filter.getPropertyNames()) {
					Criterion criterion = buildCriterion(param,
							filter.getMatchValue(), filter.getMatchType());
					disjunction.add(criterion);
				}
				criterionList.add(disjunction);
			}
		}
		return criterionList.toArray(new Criterion[criterionList.size()]);
	}

	/*
	 * 全文检索
	 *//**
	 * 获取全文Session
	 */
	/*
	 * public FullTextSession getFullTextSession(){ return
	 * Search.getFullTextSession(getSession()); }
	 *//**
	 * 建立索引
	 */
	/*
	 * public void createIndex(){ try {
	 * getFullTextSession().createIndexer(entityClass).startAndWait(); } catch
	 * (InterruptedException e) { e.printStackTrace(); } }
	 *//**
	 * 全文检索
	 * 
	 * @param page
	 *            分页对象
	 * @param query
	 *            关键字查询对象
	 * @param queryFilter
	 *            查询过滤对象
	 * @param sort
	 *            排序对象
	 * @return 分页对象
	 */
	/*
	 * @SuppressWarnings("unchecked") public Page<T> search(Page<T> page,
	 * BooleanQuery query, BooleanQuery queryFilter, Sort sort){
	 * 
	 * // 按关键字查询 FullTextQuery fullTextQuery =
	 * getFullTextSession().createFullTextQuery(query, entityClass);
	 * 
	 * // 过滤无效的内容 if (queryFilter!=null){ fullTextQuery.setFilter(new
	 * CachingWrapperFilter(new QueryWrapperFilter(queryFilter))); }
	 * 
	 * // 设置排序 if (sort!=null){ fullTextQuery.setSort(sort); }
	 * 
	 * // 定义分页 page.setTotalCount(fullTextQuery.getResultSize());
	 * fullTextQuery.setFirstResult(page.getFirst() - 1);
	 * fullTextQuery.setMaxResults(page.getPageSize());
	 * 
	 * // 先从持久化上下文中查找对象，如果没有再从二级缓存中查找
	 * fullTextQuery.initializeObjectsWith(ObjectLookupMethod
	 * .SECOND_LEVEL_CACHE, DatabaseRetrievalMethod.QUERY);
	 * 
	 * // 返回结果 page.setResult(fullTextQuery.list());
	 * 
	 * return page; }
	 *//**
	 * 获取全文查询对象
	 */
	/*
	 * public BooleanQuery getFullTextQuery(BooleanClause... booleanClauses){
	 * BooleanQuery booleanQuery = new BooleanQuery(); for (BooleanClause
	 * booleanClause : booleanClauses){ booleanQuery.add(booleanClause); }
	 * return booleanQuery; }
	 *//**
	 * 获取全文查询对象
	 * 
	 * @param q
	 *            查询关键字
	 * @param fields
	 *            查询字段
	 * @return 全文查询对象
	 */
	/*
	 * public BooleanQuery getFullTextQuery(String q, String... fields){
	 * Analyzer analyzer = new IKAnalyzer(); BooleanQuery query = new
	 * BooleanQuery(); try { if (StringUtils.isNotBlank(q)){ for (String field :
	 * fields){ QueryParser parser = new QueryParser(Version.LUCENE_36, field,
	 * analyzer); query.add(parser.parse(q), Occur.SHOULD); } } } catch
	 * (ParseException e) { e.printStackTrace(); } return query; }
	 */

	/**
	 * 设置关键字高亮
	 * 
	 * @param query
	 *            查询对象
	 * @param list
	 *            设置高亮的内容列表
	 * @param subLength
	 *            截取长度
	 * @param fields
	 *            字段名
	 * @return 结果集合
	 */
	/*
	 * public List<T> keywordsHighlight(BooleanQuery query, List<T> list, int
	 * subLength, String... fields){ Analyzer analyzer = new IKAnalyzer();
	 * Formatter formatter = new
	 * SimpleHTMLFormatter("<span class=\"highlight\">", "</span>"); Highlighter
	 * highlighter = new Highlighter(formatter, new QueryScorer(query));
	 * highlighter.setTextFragmenter(new SimpleFragmenter(subLength)); for(T
	 * entity : list){ try { for (String field : fields){ String text =
	 * StringUtils.replaceHtml((String)Reflections.invokeGetter(entity, field));
	 * String description = highlighter.getBestFragment(analyzer,field, text);
	 * if(description!=null){ Reflections.invokeSetter(entity, fields[0],
	 * description); break; } Reflections.invokeSetter(entity, fields[0],
	 * StringUtils.abbr(text, subLength*2)); } } catch (IOException e) {
	 * e.printStackTrace(); } catch (InvalidTokenOffsetsException e) {
	 * e.printStackTrace(); } } return list; }
	 */
	/**
	 * 方法描述: 調用存儲過程,沒有返回值
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018/7/19 下午 02:09
	 * @Return
	 **/

	public int callProcNoResult(String procExpression, String... values) {
		Query query = createSQLQuery(procExpression, values);
		return query.executeUpdate();
	}

	public void callProcNoResult1(final String procExpression, final String... values) {
		getSession().doWork(new Work() {
			@Override
			public void execute(Connection connection) throws SQLException {
			CallableStatement statement = connection.prepareCall(procExpression);
			int i = 1;
			for (Object obj:values) {
				statement.setObject(i++, obj);
			}
			statement.execute();
			}
		});
	}

	public void executeProcedure(final String procName, final Object[] in, final int[] outType, final  ProcedureCallback callback) throws SQLException {
		getSession().doWork(new Work() {
			@Override
			public void execute(Connection connection) throws SQLException {
				CallableStatement callable = connection.prepareCall(procName);
				int i = 0;
				if (in != null) {
					for (i = 0; i < in.length; i++) {
						callable.setObject(i + 1, in[i]);
					}
				}
				if (outType != null) {
					for (int j = 0; j < outType.length; j++) {
						callable.registerOutParameter(i + j + 1, outType[j]);
					}
				}
				callable.execute();
				if (callback != null) {
					callback.call(callable);
				}
				callable.close();
			}
		});
	}

	/**
	 * 方法描述: 條用存儲過程，有返回值
	 * 
	 * @Author: S6114648
	 * @CreateDate: 2018/7/19 下午 03:33
	 * @Return
	 **/

	public List<T> callProcWithResult(String procExpression, String... values) {
		List list = null;
		try {
			Query query = createSQLQuery(procExpression, values);
			list = query.list() == null ? null : query.list();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return list;
	}

	/**
	 * 方法描述: 條用存儲過程，有返回值
	 * 
	 * @Author: ********
	 * @CreateDate: 2018/12/29 下午 03:33
	 * @param TableName  相關表名
	 * @param FieldName 相關欄位名
	 * @param FirstStr 編號頭字符串           
	 * @param RecLen 序號位數
	 * @Return
	 **/
	public String CreateFormNo(String TableName, String FieldName,
			String FirstStr, int RecLen) {
		String strRst="";
		int StartLocation = FirstStr.length() + 9;// 字符串截取的起始位置
		// int EndLocation = RecLen;//字符串截取的位數
		

		String sql = "select lpad(coalesce(to_number(substr(max(" + FieldName + "),"
				+ StartLocation + "," + RecLen + ")),0)+1," + String.valueOf(RecLen)
				+ ",0) recno,"
				+ "       to_char(sysdate,'yyyymmdd') date1 from " + TableName
				+ " where " + FieldName + " like '" + FirstStr + "'||'%'";
		List list = this.createSQLQuery(sql).list();				
		if (list.size()>0)
		{ strRst = FirstStr + list.get(0).toString()
				+ list.get(1).toString();
		}
		return strRst;

	}

	/**
	 * 根据查询条件，查询总计数量
	 * @param filters 查询条件
	 * @param properties 汇总的属性列表
	 * @return Dict 列表
	 */
	public List<Dict> getSummary(List<PropertyFilter> filters, List<String> properties, List<String> propertyTitles) {
		assert properties.size() == propertyTitles.size();
		Criterion[] criterions = buildCriterionByPropertyFilter(filters);
		Criteria c = createCriteria(criterions);
		ProjectionList proList = Projections.projectionList();
		for (String property : properties) {
			proList.add(Projections.sum(property));
		}
		c.setProjection(proList);
		List<Dict> dictList = new ArrayList<>();

		List results = c.list();
		Object[] arr = (Object[])results.get(0);
		for (int i=0; i<properties.size(); i++) {
			Dict dict = new Dict();
			dict.setLabel(propertyTitles.get(i));
			dict.setValue(arr[i].toString());
			dictList.add(dict);
		}

		return dictList;
	}

}
