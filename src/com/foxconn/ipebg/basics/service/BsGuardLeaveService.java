package com.foxconn.ipebg.basics.service;

import cn.hutool.core.date.DateException;
import cn.hutool.core.date.DateUtil;
import com.foxconn.ipebg.basics.dao.BsGuardLeaveDao;
import com.foxconn.ipebg.basics.entity.*;
import com.foxconn.ipebg.basics.vo.BsGuardInfoVO;
import com.foxconn.ipebg.basics.vo.BsGuardLeaveVO;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.ConvertUtils;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.system.entity.Dict;
import com.foxconn.ipebg.system.service.DictService;
import com.foxconn.ipebg.system.utils.ImportExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.util.*;

@Service
@Transactional(readOnly=true)
public class BsGuardLeaveService extends BaseBusinessService<BsGuardLeaveEntity, String> {
    public static final String ERRORINFOINDEX = "TASKSETERRORINFO";

    @Autowired
    private BsGuardLeaveDao guardLeaveDao;

    @Autowired
    private BsTrainingPersonService bsTrainingPersonService;

    @Autowired
    private PerInfoService perInfoService;

    @Autowired
    private DictService dictService;

    @Override
    public HibernateDao<BsGuardLeaveEntity, String> getEntityDao() {
        return guardLeaveDao;
    }

    public BsGuardInfoVO getGuardInfoByEmpNo(String empNo) {
        BsGuardInfoVO vo = new BsGuardInfoVO();
        BsTrainingPersonEntity trainingPerson = bsTrainingPersonService.findByEmpNo(empNo);
        if (trainingPerson != null) {
            vo.setEmpNo(trainingPerson.getEmpNo());
            vo.setEmpName(trainingPerson.getEmpName());
            vo.setPsnId(trainingPerson.getPsnId());
        } else {
            PerInfoEntity perInfoEntity = perInfoService.findByEmpNo(empNo);
            if (perInfoEntity != null) {
                vo.setEmpNo(perInfoEntity.getEmpNo());
                vo.setEmpName(perInfoEntity.getEmpName());
                vo.setPsnId(perInfoEntity.getPsnId());
            }
        }
        return vo;
    }

    /**
     * 判断是否可以新增/修改一笔请假信息，需要保证同一个人在同一天不能有两笔请假资料
     * @param entity 将要保存的请假资料
     * @return
     */
    public boolean couldSaveOrUpdate(BsGuardLeaveEntity entity) {
        return couldSaveOrUpdate(entity.getEmpNo(), entity.getStartDate(), entity.getEndDate(), entity.getId());
    }

    private boolean couldSaveOrUpdate(String empNo, Date startDate, Date endDate, String id) {
        List<PropertyFilter> filters = new ArrayList<>();
        filters.add(new PropertyFilter("EQS_empNo", empNo));
        filters.add(new PropertyFilter("LED_startDate", endDate));
        filters.add(new PropertyFilter("GED_endDate", startDate));
        List<BsGuardLeaveEntity> conflictEntities = search(filters);
        if (id != null) {
            // 修改
            boolean couldSave = true;
            for (BsGuardLeaveEntity conflictEntity : conflictEntities) {
                if (!conflictEntity.getId().equals(id)) {
                    couldSave = false;
                }
            }
            return couldSave;
        } else {
            return conflictEntities.isEmpty();
        }
    }

    public List<BsGuardLeaveVO> voListFromEntityList(List<BsGuardLeaveEntity> entityList) {
        List<BsGuardLeaveVO> voList = new ArrayList<>();
        for (BsGuardLeaveEntity entity : entityList) {
            BsGuardLeaveVO vo = new BsGuardLeaveVO(entity);
            voList.add(vo);
        }
        ConvertUtils.convertPropertyToDictLabel(voList, "leaveType", "leave_type");
        ConvertUtils.convertPropertyToDictLabel(voList, "shiftNo", "shift_type");
        return voList;
    }

    public Page<BsGuardLeaveVO> voPageFromEntityPage(Page<BsGuardLeaveEntity> entityPage) {
        Page<BsGuardLeaveVO> voPage = new Page<>();
        voPage.setPageNo(entityPage.getPageNo());
        voPage.setPageSize(entityPage.getPageSize());
        voPage.setResult(voListFromEntityList(entityPage.getResult()));
        voPage.setTotalCount(entityPage.getTotalCount());
        return voPage;
    }

    public List<BsGuardLeaveEntity> leaveEntityListByDate(Date date) {
        List<PropertyFilter> filters = new ArrayList<>();
        filters.add(new PropertyFilter("LED_startDate", date));
        filters.add(new PropertyFilter("GED_endDate", date));
        return guardLeaveDao.findOrder(filters, "empNo", true);
    }


    /**
     * 根据参数查询请假实体列表，用於正式崗排班
     * @param params
     * @return
     */
    public List<Map<String, String>> leaveListByParamForGuardArrange(Map<String, String> params) {
        Date date = DateUtil.parse(params.get("arrangeDate"), "yyyy-MM-dd");
        List<BsGuardLeaveEntity> guardLeaveEntities = leaveEntityListByDate(date);
        List<Map<String, String>> list = new ArrayList<>();
        for (BsGuardLeaveEntity entity : guardLeaveEntities) {
            Map<String, String> map = new HashMap<>();
            map.put("emp_no", entity.getEmpNo());
            map.put("emp_name", entity.getEmpName());
            list.add(map);
        }
        return list;
    }

    /**
     * 根据参数查询请假实体列表，用於培訓崗排班
     * @param params 查詢參數
     * @param allEmpList 所有可排人員
     * @return 請假人員
     */
    public List<Map<String, String>> leaveListByParamForTrainArrange(Map<String, String> params, List<Map<String, String>> allEmpList) {
        List<String> allEmpNoList = new ArrayList<>();
        for (Map<String, String> emp: allEmpList) {
            allEmpNoList.add(emp.get("empno"));
        }

        Date date = DateUtil.parse(params.get("arrangeDate"), "yyyy-MM-dd");
        List<BsGuardLeaveEntity> guardLeaveEntities = leaveEntityListByDate(date);
        List<Map<String, String>> list = new ArrayList<>();
        for (BsGuardLeaveEntity entity : guardLeaveEntities) {
            if (allEmpNoList.contains(entity.getEmpNo())) {
                Map<String, String> map = new HashMap<>();
                map.put("empno", entity.getEmpNo());
                map.put("empname", entity.getEmpName());
                list.add(map);
            }
        }
        return list;
    }

    /**
     * 批量導入
     *
     * @param in
     *            excel文件輸入流
     * @param isExcel2003
     *            是否excel2003
     * @return 是否成功
     */
    @Transactional(readOnly=false)
    public String batchImport(InputStream in, boolean isExcel2003) {
        try {
            List<BsGuardLeaveErrorDto> perinfoErrorDtos=null;
            ImportExcelUtil poi = new ImportExcelUtil();
            int success = 0, failed = 0;
            // 讀取上傳的excel file
            List<List<String>> list = poi.read(in, isExcel2003);
            // 判断需要的字段在Excel中是否都存在
            boolean isExist = true;
            Map<String, String> fieldMap = BsGuardLeaveEntity.importFieldMap();
            Map<String, Integer> fieldIndexMap = new HashMap();
            List<String> excelFieldList = list.get(0);
            // 第一列是序號不需要
            for (int i = 1; i < excelFieldList.size(); i++) {
                if (!fieldMap.containsKey(excelFieldList.get(i))) {
                    isExist = false;
                    break;
                }
                fieldIndexMap.put(fieldMap.get(excelFieldList.get(i)), i);
            }
            // 如果有列名不存在，则抛出异常，提示错误
            if (!isExist) {
                return "有列名不存在，請對照模板";
            }
            //開始導入
            perinfoErrorDtos = listToGuardLeaveErrorDto(
                    list, 1, fieldIndexMap);

            if (perinfoErrorDtos.size()>0)
            {
                UserUtil.getSession().setAttribute(ERRORINFOINDEX,perinfoErrorDtos);
                return "導入成功"+(list.size()-1-perinfoErrorDtos.size())+"筆，失敗"+perinfoErrorDtos.size()+"筆!";
            }


        } catch (Exception e) {
            this.logger.info(e.getMessage(), e);
            return Constant.RESULT.CODE_NO.getValue();
        }
        return Constant.RESULT.CODE_YES.getValue();
    }

    /**
     * @param list
     *            列表
     * @param startIndex
     *            開始的行
     * @param fieldIndexMap
     *            list中的index和类的英文属性的对应关系Map
     * @return
     * @throws IllegalAccessException
     * @throws InstantiationException
     */
    @Transactional(readOnly = false)
    public List<BsGuardLeaveErrorDto> listToGuardLeaveErrorDto(
            List<List<String>> list, int startIndex,
            Map<String, Integer> fieldIndexMap) {
        // 定义要返回的list
        List<BsGuardLeaveErrorDto> resultList = new ArrayList<BsGuardLeaveErrorDto>();
        List<Dict> leaveTypeArray = dictService.getDictByType("leave_type");
        List<Dict> shiftNoArray = dictService.getDictByType("shift_type");
        for (int i = startIndex; i < list.size(); i++) {
            String empNo = list.get(i).get(1).trim();
            String empName = list.get(i).get(2).trim();
            String psnId = null;
            String leaveType = list.get(i).get(3).trim();
            String shiftNo = list.get(i).get(4).trim();
            String startDateString = list.get(i).get(5).trim();
            String endDateString = list.get(i).get(6).trim();
            String dayCountString = list.get(i).get(7).trim();
            String reason = list.get(i).get(8).trim();

            String errorLog = null;
            Date startDate = null;
            Date endDate = null;

            try {
                startDate = DateUtil.parse(startDateString, "yyyy-MM-dd");
            } catch (DateException e) {
                errorLog = "請假開始日期格式錯誤";
            }
            try {
                endDate = DateUtil.parse(endDateString, "yyyy-MM-dd");
            } catch (DateException e) {
                errorLog = "請假結束日期格式錯誤";
            }
            boolean isValidLeaveType = false;
            boolean isValidShiftNo = false;
            for (Dict dict : leaveTypeArray) {
                if (dict.getLabel().equals(leaveType)) {
                    isValidLeaveType = true;
                    leaveType = dict.getValue();
                    break;
                }
            }
            if (!isValidLeaveType) {
                errorLog = "假別不正確";
            }
            for (Dict dict : shiftNoArray) {
                if (dict.getLabel().equals(shiftNo)) {
                    isValidShiftNo = true;
                    shiftNo = dict.getValue();
                    break;
                }
            }
            if (!isValidShiftNo) {
                errorLog = "請假日班別不正確";
            }
            if (errorLog == null) {
                if (StringUtils.isEmpty(empNo)) {
                    errorLog = "工號為空";
                } else if (StringUtils.isEmpty(empName)) {
                    errorLog = "姓名為空";
                } else if (StringUtils.isEmpty(leaveType)) {
                    errorLog = "假別為空";
                } else if (StringUtils.isEmpty(shiftNo)) {
                    errorLog = "請假日班別為空";
                } else if (StringUtils.isEmpty(startDateString)) {
                    errorLog = "請假開始日期為空";
                } else if (StringUtils.isEmpty(endDateString)) {
                    errorLog = "請假結束日期為空";
                } else if (StringUtils.isEmpty(dayCountString)) {
                    errorLog = "請假天數為空";
                } else if (StringUtils.isEmpty(reason)) {
                    errorLog = "請假事由為空";
                } else {
                    BsGuardInfoVO guardInfo = getGuardInfoByEmpNo(empNo);

                    if (StringUtils.isEmpty(guardInfo.getEmpNo())) {
                        errorLog = "工號不存在";
                    } else if (!empName.equals(guardInfo.getEmpName())) {
                        errorLog = "工號與姓名不一致";
                    } else if (startDate.before(DateUtil.endOfDay(new Date()))) {
                        errorLog = "請假開始日期不能早於今天";
                    } else if (startDate.after(endDate)) {
                        errorLog = "請假開始日期不能遲於結束日期";
                    } else if (endDate.getTime() - startDate.getTime() > 1000 * 60 * 60 * 24 * 14) {
                        errorLog = "請假天數不能超過15天";
                    } else if (!couldSaveOrUpdate(empNo, startDate, endDate, null)) {
                        errorLog = "與之前維護的請假資料有衝突";
                    } else {
                        psnId = guardInfo.getPsnId();
                    }
                }
            }

            if (errorLog == null) {
                // 可以導入
                BsGuardLeaveEntity guardLeaveEntity = new BsGuardLeaveEntity();
                guardLeaveEntity.setEmpNo(empNo);
                guardLeaveEntity.setEmpName(empName);
                guardLeaveEntity.setPsnId(psnId);
                guardLeaveEntity.setLeaveType(leaveType);
                guardLeaveEntity.setShiftNo(shiftNo);
                guardLeaveEntity.setStartDate(startDate);
                guardLeaveEntity.setEndDate(endDate);
                guardLeaveEntity.setDayCount(Integer.parseInt(dayCountString));
                guardLeaveEntity.setReason(reason);
                guardLeaveEntity.setNewRecord(true);
                guardLeaveEntity.setCreateDate(new Date());
                save(guardLeaveEntity);
            } else {
                BsGuardLeaveErrorDto errorDto = new BsGuardLeaveErrorDto();
                errorDto.setEmpNo(empNo);
                errorDto.setEmpName(empName);
                errorDto.setLeaveType(list.get(i).get(3).trim());
                errorDto.setShiftNo(list.get(i).get(4).trim());
                errorDto.setStartDate(startDateString);
                errorDto.setEndDate(endDateString);
                errorDto.setDayCount(dayCountString);
                errorDto.setReason(reason);
                errorDto.setErrorLog(errorLog);
                resultList.add(errorDto);
            }
        }
        return resultList;
    }
}
