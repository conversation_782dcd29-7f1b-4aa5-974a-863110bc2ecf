var dg;
var d;
$(function(){
    dg=$('#dg').datagrid({
        method: "get",
        url:ctx+'/workdetail/list',
        fit : true,
        fitColumns : true,
        border : false,
        idField : 'id',
        striped:true,
        pagination:true,
        rownumbers:true,
        pageNumber:1,
        pageSize : 20,
        pageList : [ 10, 20, 30, 40, 50 ],
        singleSelect:true,
        columns:[[
							                    { field: 'id', title: '資料識別ID  Primary',hidden:true,rowspan:2},
							                    { field: 'empNo', title: '工號', sortable:true,width:500,rowspan:2},
							                    { field: 'empName', title: '姓名', sortable:true,width:500,rowspan:2},
							                    { field: 'postType', title: '崗位類別', sortable:true,width:500,rowspan:2},
							                    { field: '', title: '上班日期', sortable:true,width:500,colspan:31},
							                    { field: 'tempService', title: '臨時勤務日期', sortable:true,width:920,rowspan:2}],							                    
											                    [{ field: 'colL1', title: '1', sortable:true,width:300}
					,
																                    { field: 'colL2', title: '2', sortable:true,width:300}
					,
																                    { field: 'colL3', title: '3', sortable:true,width:300}
					,
																                    { field: 'colL4', title: '4', sortable:true,width:300}
					,
																                    { field: 'colL5', title: '5', sortable:true,width:300}
					,
																                    { field: 'colL6', title: '6', sortable:true,width:300}
					,
																                    { field: 'colL7', title: '7', sortable:true,width:300}
					,
																                    { field: 'colL8', title: '8', sortable:true,width:300}
					,
																                    { field: 'colL9', title: '9', sortable:true,width:300}
					,
                    																{ field: 'colL10', title: '10', sortable:true,width:300}
					,
																					{ field: 'colL11', title: '11', sortable:true,width:300}
					,
																					{ field: 'colL12', title: '12', sortable:true,width:300}
					,
																					{ field: 'colL13', title: '13', sortable:true,width:300}
					,
																					{ field: 'colL14', title: '14', sortable:true,width:300}
					,
																					{ field: 'colL15', title: '15', sortable:true,width:300}
					,
																					{ field: 'colL16', title: '16', sortable:true,width:300}
					,
					{ field: 'colL17', title: '17', sortable:true,width:300}
					,
					{ field: 'colL18', title: '18', sortable:true,width:300}
					,
					{ field: 'colL19', title: '19', sortable:true,width:300}
					,
					{ field: 'colL20', title: '20', sortable:true,width:300}
					,
					{ field: 'colL21', title: '21', sortable:true,width:300}
					,
					{ field: 'colL22', title: '22', sortable:true,width:300}
					,
					{ field: 'colL23', title: '23', sortable:true,width:300}
					,
					{ field: 'colL24', title: '24', sortable:true,width:300}
					,
					{ field: 'colL25', title: '25', sortable:true,width:300}
					,
					{ field: 'colL26', title: '26', sortable:true,width:300}
					,
					{ field: 'colL27', title: '27', sortable:true,width:300}
					,
					{ field: 'colL28', title: '28', sortable:true,width:300}
					,
					{ field: 'colL29', title: '29', sortable:true,width:300}
					,
					{ field: 'colL30', title: '30', sortable:true,width:300}
					,
					{ field: 'colL31', title: '31', sortable:true,width:300}
												        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        //rowTooltip: true,
        toolbar:'#tb'
    });
});

//创建查询对象并查询
function cx(){
    var obj=$("#searchFrom").serializeObject();
    dg.datagrid('load',obj);
}
//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("reset");
}
//导出excel
function exportExcel(){
    var form = document.getElementById("searchFrom");
    searchFrom.action=ctx+'/bsshift/exportExcel';
    form.submit();
}