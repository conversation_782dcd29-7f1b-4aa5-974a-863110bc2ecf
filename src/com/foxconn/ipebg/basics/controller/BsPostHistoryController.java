package com.foxconn.ipebg.basics.controller;

import com.foxconn.ipebg.basics.entity.BsPostHistoryEntity;
import com.foxconn.ipebg.basics.service.BsPostHistoryService;
import com.foxconn.ipebg.common.exception.ExcelException;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.utils.ConvertUtils;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 崗位明細表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-03-07 09:57:22
 */
@Controller
@RequestMapping("bsposthistory")
public class BsPostHistoryController extends BaseController {
    @Autowired
    private BsPostHistoryService postHistoryService;

    /**
      * 方法描述: 列表信息
      * @Author: S6113712
      * @CreateDate:  2019-03-07 09:57:22
      * @Return 列表页面
      **/
    @RequestMapping(method = RequestMethod.GET)
    public String list() {
        //查询列表数据
        return "basics/bsposthistory/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6114893
      * @CreateDate:   2019-09-28 09:57:22
      * @Return
      **/
    @RequestMapping(value = "list", method = RequestMethod.GET)
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
		Page<BsPostHistoryEntity> page = getPage(request);
		List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
		page = postHistoryService.search(page, filters);
        ConvertUtils.convertPropertyToDictLabel(page, "applyType", "guard_aply_type");
        return getEasyUIData(page);
    }

    /**
     * 导出excel
     * @Author: S6114893
     * @CreateDate: 2022-07-21
     * @Return null
     **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        List<BsPostHistoryEntity> list = postHistoryService.search(filters);
        ConvertUtils.convertPropertyToDictLabel(list, "applyType", "guard_aply_type");
        try {
            ExcelUtil.listToExcel(list, BsPostHistoryEntity.excelFieldMap(), "崗位增撤異動明細報表", response);
        } catch (ExcelException e) {
            e.printStackTrace();
        }
    }
}
