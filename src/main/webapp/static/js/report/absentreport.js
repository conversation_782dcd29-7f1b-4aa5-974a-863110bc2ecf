var dg;
var d;
$(function() {
	dg = $('#dg').datagrid({
		method : "get",
		url : ctx + '/absentreport/list',
		// url:'/waterQualityTesting/bsdpt/list',
		fit : true,
		fitColumns : true,
		border : false,
	    idField : 'id',
		striped : true,
		pagination : true,
		rownumbers : true,
		pageNumber : 1,
		pageSize : 20,
		pageList : [ 10, 20, 30, 40, 50 ],
		singleSelect : true,
		sortName : 'issueDate,shiftNo,empNo',
		sortOrder : 'desc,asc,asc',
		columns : [ [
		{field : 'id',title : '資料識別ID  Primary',hidden : true},
		{field : 'empNo',title : '工號',sortable : false,width : 100},
		{field : 'empName',title : '姓名',sortable : false,width : 100},
		{field : 'shiftNo',title : '班別',sortable : false,width : 100},
		{field : 'issueDate',title : '異常日期',sortable : false,width : 100,formatter:formatDate},
		{field : 'issueReason',title : '異常原因',sortable : false,width : 100},
		{field : 'company',title : '公司名稱',sortable : false,width : 100}
		] ],
		enableHeaderClickMenu : true,
		enableHeaderContextMenu : true,
		enableRowContextMenu : false,
		rowTooltip : true,
		toolbar : '#tb'
	});
	$('#dg').datagrid;
});

// 创建查询对象并查询
function cx() {
	var obj = $("#searchFrom").serializeObject();
	dg.datagrid('load', obj);
}
// 表單進度查詢頁面查詢條件重置
function listSearchReset() {
	$("#searchFrom").form("reset");
}
// 导出excel
function exportExcel() {
	var form = document.getElementById("searchFrom");
	searchFrom.action = ctx + '/bssecper/exportExcel';
	form.submit();
}