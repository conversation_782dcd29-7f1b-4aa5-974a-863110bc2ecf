package com.foxconn.ipebg.system.service;

import com.foxconn.ipebg.system.entity.TQhDutyEntity;
import com.foxconn.ipebg.system.dao.TQhDutyDao;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-17 18:41:34
 */
@Service
@Transactional(readOnly=true)
public class  TQhDutyService extends BaseBusinessService<TQhDutyEntity, String>{
    @Autowired
    private TQhDutyDao tQhDutyDao;
    @Override
    public HibernateDao<TQhDutyEntity, String> getEntityDao() {
        return tQhDutyDao;
    }
}
