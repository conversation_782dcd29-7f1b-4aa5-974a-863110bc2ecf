<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>排班群組匹配崗位</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>

<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/bsarrangegrouppost/${action}" method="post">
		<table class="formTable">
		    			<tr>
				<td>id：</td>
				<td>
				    					<input type="hidden" name="id" value="${bsArrangeGroupPost.id}"/>
															<input id="id" name="id" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPost.id }" />
									</td>
			</tr>
						<tr>
				<td>排班群組編碼：</td>
				<td>
				    										<input id="arrangeGroupId" name="arrangeGroupId" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPost.arrangeGroupId }" />
									</td>
			</tr>
						<tr>
				<td>排班群組：</td>
				<td>
				    										<input id="arrangeGroupName" name="arrangeGroupName" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPost.arrangeGroupName }" />
									</td>
			</tr>
						<tr>
				<td>編號：</td>
				<td>
				    										<input id="recno" name="recno" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPost.recno }" />
									</td>
			</tr>
						<tr>
				<td>廠區：</td>
				<td>
				    										<input id="site" name="site" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPost.site }" />
									</td>
			</tr>
						<tr>
				<td>區域：</td>
				<td>
				    										<input id="area" name="area" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPost.area }" />
									</td>
			</tr>
						<tr>
				<td>棟：</td>
				<td>
				    										<input id="block" name="block" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPost.block }" />
									</td>
			</tr>
						<tr>
				<td>層：</td>
				<td>
				    										<input id="floor" name="floor" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPost.floor }" />
									</td>
			</tr>
						<tr>
				<td>方位：</td>
				<td>
				    										<input id="position" name="position" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPost.position }" />
									</td>
			</tr>
						<tr>
				<td>詳細位置：</td>
				<td>
				    										<input id="location" name="location" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPost.location }" />
									</td>
			</tr>
						<tr>
				<td>崗位類別：</td>
				<td>
				    										<input id="postType" name="postType" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPost.postType }" />
									</td>
			</tr>
						<tr>
				<td>班制：</td>
				<td>
				    										<input id="postShift" name="postShift" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPost.postShift }" />
									</td>
			</tr>
						<tr>
				<td>崗位名稱：</td>
				<td>
				    										<input id="postName" name="postName" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPost.postName }" />
									</td>
			</tr>
						<tr>
				<td>人數(不含調休)：</td>
				<td>
				    										<input id="postPerNu" name="postPerNu" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPost.postPerNu }" />
									</td>
			</tr>
						<tr>
				<td>需求時間-開始時間：</td>
				<td>
				    										<input id="postStartDate" name="postStartDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${bsArrangeGroupPost.postStartDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>需求時間-結束時間：</td>
				<td>
				    										<input id="postEndDate" name="postEndDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${bsArrangeGroupPost.postEndDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>崗位級別：</td>
				<td>
				    										<input id="postLevel" name="postLevel" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPost.postLevel }" />
									</td>
			</tr>
						<tr>
				<td>派駐保安公司：</td>
				<td>
				    										<input id="securityCom" name="securityCom" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPost.securityCom }" />
									</td>
			</tr>
						<tr>
				<td>實到崗人數：</td>
				<td>
				    										<input id="postPerNuR" name="postPerNuR" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPost.postPerNuR }" />
									</td>
			</tr>
						<tr>
				<td>生效時間：</td>
				<td>
				    										<input id="postEffectDate" name="postEffectDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${bsArrangeGroupPost.postEffectDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>創建人：</td>
				<td>
				    										<input id="createBy" name="createBy" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPost.createBy }" />
									</td>
			</tr>
						<tr>
				<td>創建時間：</td>
				<td>
				    										<input id="createDate" name="createDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${bsArrangeGroupPost.createDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>更新人：</td>
				<td>
				    										<input id="updateBy" name="updateBy" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPost.updateBy }" />
									</td>
			</tr>
						<tr>
				<td>更新時間：</td>
				<td>
				    										<input id="updateDate" name="updateDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${bsArrangeGroupPost.updateDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>刪除標示：</td>
				<td>
				    										<input id="delFlag" name="delFlag" class="easyui-validatebox" data-options="width: 150" value="${bsArrangeGroupPost.delFlag }" />
									</td>
			</tr>
					</table>
	</form>
  </div>
<script src='${ctx}/static/js/basics/bsarrangegrouppost.js?"+Math.random()"'></script>
<script type="text/javascript">
   //提交表单
$('#mainform').form({
    onSubmit: function(){
        var isValid = $(this).form('validate');
        return isValid;	// 返回false终止表单提交
    },
    success:function(data){ 
        successTip(data,dg,d);
    }
});
</script>
</body>
</html>