package com.foxconn.ipebg.gen.dao;

import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.gen.entity.TableEntity;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 类SysGeneratorDao的功能描述:
 *  代码生成器
 * @auther hxy
 * @date 2017-08-25 16:19:43
 */
@Repository
public class SysGeneratorDao extends HibernateDao<Map<String, Object>, Integer> {
    /**
      * 方法描述:  查詢表結構
      * @Author: S6114648
      * @CreateDate:   2018/7/11  上午 08:53
      * @Return
      **/

    public List<TableEntity> queryList(Map<String, Object> map){
      StringBuffer stringBuffer = new StringBuffer();
      stringBuffer.append("select t.TABLE_NAME as tableName, s.COMMENTS as comments" +
                      " from user_tables t" +
                      " inner join user_tab_comments s" +
                      " on t.TABLE_NAME = s.TABLE_NAME");
        SQLQuery query = createSQLQuery(stringBuffer.toString(),map);
        Integer page = Integer.parseInt(map.get("page").toString());
        Integer pageSize = Integer.parseInt(map.get("pageSize").toString());
        query.setFirstResult((page-1)*pageSize);
        query.setMaxResults(pageSize);
        query.addScalar("tableName");
        query.addScalar("comments");
        List list = query.list();
        List entityList = new ArrayList();
        TableEntity tableEntity = null;
        for (int i = 0; i < list.size(); i++) {
            Object[] objects = (Object[]) list.get(i);
            tableEntity = new TableEntity();
            if(objects[0]!=null){
                tableEntity.setTableName(objects[0].toString());
            }
            if(objects[1]!=null){
                tableEntity.setComments(objects[1].toString());
            }
            entityList.add(tableEntity);
        }
        return entityList;
    }
    /**
      * 方法描述: 查詢表數量
      * @Author: S6114648
      * @CreateDate:   2018/7/11  上午 09:00
      * @Return
      **/

    public int queryTotal(Map<String, Object> map){
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("select count(t.TABLE_NAME) cun" +
                " from user_tables t" +
                " inner join user_tab_comments s" +
                " on t.TABLE_NAME = s.TABLE_NAME");
        SQLQuery query = createSQLQuery(stringBuffer.toString(),map);
        return ((BigDecimal) query.list().get(0)).intValue();
    }
    /**
      * 方法描述:  查詢所有表
      * @Author: S6114648
      * @CreateDate:   2018/7/11  下午 01:32
      * @Return
      **/

    public Map<String, String> queryTable(String tableName){
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("select t.TABLE_NAME as tableName, s.COMMENTS as tableComment,cu.COLUMN_NAME as columnPKey" +
                " from user_tables t" +
                " inner join user_tab_comments s" +
                " on t.TABLE_NAME = s.TABLE_NAME " +
                " left outer join user_cons_columns cu\n" +
                "    on t.TABLE_NAME = cu.TABLE_NAME and cu.POSITION is not null\n" +
                "  left outer join user_constraints au\n" +
                "    on cu.constraint_name = au.constraint_name\n" +
                "   and au.constraint_type = 'P'"+
                " where t.TABLE_NAME=?0");
        SQLQuery query = createSQLQuery(stringBuffer.toString(),tableName);
        query.addScalar("tableName");
        query.addScalar("tableComment");
        query.addScalar("columnPKey");
        query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        return (Map<String, String>) query.list().get(0);
    }
    /**
      * 方法描述: 查詢表列
      * @Author: S6114648
      * @CreateDate:   2018/7/11  下午 01:32
      * @Return
      **/

    public List<Map<String, String>> queryColumns(String tableName){
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("select t.TABLE_NAME as tableName, t.COLUMN_NAME as columnName, t.DATA_TYPE as dataType, s.COMMENTS as columnComment \n" +
                        "  from user_tab_columns t\n" +
                        "  inner join user_col_comments s\n" +
                        "    on t.TABLE_NAME = ?0\n" +
                        "   and t.TABLE_NAME = s.TABLE_NAME and t.COLUMN_NAME=s.COLUMN_NAME");
        SQLQuery query = createSQLQuery(stringBuffer.toString(),tableName);
        query.addScalar("tableName");
        query.addScalar("columnName");
        query.addScalar("dataType");
        query.addScalar("columnComment");
        query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        return query.list();
    }
}
