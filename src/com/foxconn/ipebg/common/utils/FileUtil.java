package com.foxconn.ipebg.common.utils;

import org.apache.log4j.Logger;

import java.io.*;
import java.util.Arrays;


public class FileUtil {
    
    private static Logger logger = Logger.getLogger(FileUtil.class);
    /**
     * 复制获取的文件到目标路径 ChangYao
     * 
     * @param file
     *            源文件
     * @param savePath
     *            目标路径
     */
    public static void copy(File file, String savePath) {
        InputStream in = null;
        OutputStream out = null;
        try {
            in = new BufferedInputStream(new FileInputStream(file), 2048);
            out = new BufferedOutputStream(new FileOutputStream(savePath), 2048);
            byte[] buffer = new byte[2048];
            int len = 0;
            while ((len = in.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != in) {
                try {
                    in.close();
                } catch (IOException e) {
                    logger.error(e);
                }
            }
            if (null != out) {
                try {
                    out.close();
                } catch (IOException e) {
                    logger.error(e);
                }
            }
        }
    }

    /**
     * ChangYao
     * 
     * @param fileDir
     *            存储文件目录
     * @return
     */
    public static String fileDir(String fileDir) {
        File storePath = new File(fileDir);
        if (storePath.exists()) {
            return fileDir;
        } else {
            storePath.mkdirs();
            return fileDir;
        }
    }

    public static void inputstreamtofile(InputStream ins, File file) {
        try {
            OutputStream os = new FileOutputStream(file);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            ins.close();
        } catch (Exception e) {
            logger.error(e);
        }
    }

    public static boolean deleteDir(File dir) {
        if (dir.isDirectory()) {
            String[] children = dir.list();
            for (int i=0; i<children.length; i++) {
                boolean success = deleteDir(new File(dir, children[i]));
                if (!success) {
                    return false;
                }
            }
        }
        // 目录此时为空，可以删除
        return dir.delete();
    }
    
    public static void main(String[] args) {
        System.out.println(Arrays.asList("1,2,3,".split(",")));
    }
}
