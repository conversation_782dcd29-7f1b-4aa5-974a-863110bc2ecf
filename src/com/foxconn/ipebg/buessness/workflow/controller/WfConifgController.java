package com.foxconn.ipebg.buessness.workflow.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.buessness.workflow.entity.WfConifgEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.common.persistence.Page;


/**
 * 流程信息配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-06 11:04:38
 */
@Controller
@RequestMapping("wfconifg")
public class WfConifgController extends BaseController {

    @Autowired
    private WfConifgService wfConifgService;

    /**
      * 方法描述: 列表信息
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:38
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    @RequiresPermissions("workflow:wfconifg:list")
    public String list() {
        //查询列表数据
        return "buessness/workflow/wfconifg/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:38
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    @RequiresPermissions("workflow:wfconifg:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<WfConifgEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        page = wfConifgService.search(page, filters);
        return getEasyUIData(page);
    }
    /**
      * 方法描述: 添加跳轉
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:38
      * @Return
      **/

    @RequiresPermissions("workflow:wfconifg:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("wfConifg", new WfConifgEntity());
        model.addAttribute("action", "create");
        return "buessness/workflow/wfconifg/listForm";
    }
    /**
      * 方法描述: 保存
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:38
      * @Return
      **/

    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("workflow:wfconifg:add")
    @ResponseBody
    public String create(@Valid WfConifgEntity wfConifg, Model model) {
        wfConifgService.save(wfConifg);
        return "success";
    }
    /**
      * 方法描述: 修改跳轉
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:38
      * @Return
      **/

    @RequiresPermissions("workflow:wfconifg:update")
    @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
    public String updateForm(@PathVariable("id") String id, Model model) {
        model.addAttribute("wfConifg", wfConifgService.get(id));
        model.addAttribute("action", "update");
        return "buessness/workflow/wfconifg/listForm";
    }
    /**
      * 方法描述: 修改
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:38
      * @Return
      **/

    @RequestMapping(value="update", method = RequestMethod.POST)
    @RequiresPermissions("workflow:wfconifg:update")
    @ResponseBody
    public String update(@Valid WfConifgEntity wfConifg) {
        wfConifgService.update(wfConifg);
        return "success";
    }

    /**
      * 方法描述: 根據主鍵刪除
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:38
      * @Return
      **/

    @RequestMapping("delete/{id}")
    @RequiresPermissions("workflow:wfconifg:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        wfConifgService.delete(id);
        return "success";
    }

}
