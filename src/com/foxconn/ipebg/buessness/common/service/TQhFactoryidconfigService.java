package com.foxconn.ipebg.buessness.common.service;

import com.foxconn.ipebg.buessness.common.entity.TQhFactoryidconfigEntity;
import com.foxconn.ipebg.buessness.common.dao.TQhFactoryidconfigDao;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;


/**
 * 廠區代碼對應配置表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-08 09:28:36
 */
@Service
@Transactional(readOnly=true)
public class  TQhFactoryidconfigService extends BaseBusinessService<TQhFactoryidconfigEntity, String>{
    @Autowired
    private TQhFactoryidconfigDao tQhFactoryidconfigDao;
    @Override
    public HibernateDao<TQhFactoryidconfigEntity, String> getEntityDao() {
        return tQhFactoryidconfigDao;
    }

    public TQhFactoryidconfigEntity findByFactiryid(String factoryId){
       return tQhFactoryidconfigDao.findUniqueBy("factoryid",factoryId);
    }

    public List<TQhFactoryidconfigEntity> findFactirys(){
        return tQhFactoryidconfigDao.findAll();
    }
}
