package com.foxconn.ipebg.system.utils;

import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;
import org.apache.commons.net.ftp.FTPSClient;
import org.apache.commons.net.ftp.FTPClientConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.commons.io.IOUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.List;

/**
 * Created by xiao on 2018/1/21.
 * 自適應 FTP 和 FTPS（FTP over TLS）
 */
public class FTPUtil {
    private Logger logger = LoggerFactory.getLogger(FTPUtil.class);
    // FTP 根目錄
    private String rootPath;
    // FTP 服務器IP
    private String ip;
    // FTP 服務器端口號
    private int port;
    // FTP 服務器用戶名
    private String user;
    // FTP 服務器密碼
    private String pwd;
    // FTP 客戶端
    private FTPClient ftpClient;
    // 是否是 FTPS
    private boolean isFtps;

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public FTPClient getFtpClient() {
        return ftpClient;
    }

    public void setFtpClient(FTPClient ftpClient) {
        this.ftpClient = ftpClient;
    }

    private FTPUtil() {}

    public FTPUtil(String ip, int port, String user, String pwd, String rootPath) {
        this.ip = ip;
        this.port = port;
        this.pwd = pwd;
        this.rootPath=rootPath;
        this.user=user;
        isFtps = port == 990;
    }

    /**
     * 连接FTP服务器
     *
     * @return
     */
    private boolean connectServer() {
        boolean isSuccess = false;
        if (isFtps) {
            ftpClient = new FTPSClient("TLSv1.2",true);
        } else {
            ftpClient = new FTPClient();
        }
        try {
            ftpClient.connect(ip,port);
            isSuccess = ftpClient.login(user, pwd);
            ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);// 設置文件的傳輸類型為二進制
            ftpClient.setControlEncoding("UTF-8");
            ftpClient.enterLocalPassiveMode();
            int reply=ftpClient.getReplyCode();//獲取應答代碼
            if (!FTPReply.isPositiveCompletion(reply)) {//驗證失敗
                ftpClient.disconnect();
                logger.error("FTP login verification failed");
            }
        } catch (IOException e) {
            logger.error("FTP服务器连接失败", e);
        }
        return isSuccess;
    }

    /**
     * 在ftp服務器上創建並進入子目錄
     * */
    private void preparePath(String remotePath) throws IOException {
        ftpClient.changeWorkingDirectory(rootPath);
        String[] dirs = (remotePath).split("/");
        for (int i = 0; i < dirs.length; i++) {
            ftpClient.mkd(dirs[i]);
            ftpClient.changeWorkingDirectory(dirs[i]);
        }
    }

    /**
     * 上傳文件到 FTP 服務器
     * @param remotePath 文件在 FTP 服務器上的子路徑，可以按日期設置為 yyyy/mm/dd
     * @param fileList 要上傳的文件列表
     * */
    public boolean uploadFile(String remotePath, List<File> fileList) throws IOException {
        boolean uploaded = true;
        FileInputStream fis = null;
        //连接FTP服务器
        if (connectServer()) {
            try {
                // 進入該目錄
                preparePath(remotePath);
                ftpClient.setBufferSize(1024);
                //遍历文件存储
                for (File fileItem : fileList) {
                    //把文件转成文件流
                    fis = new FileInputStream(fileItem);
                    //调用storeFile方法存储
                    ftpClient.storeFile(fileItem.getName(), fis);
                    IOUtils.closeQuietly(fis);
                    fis = null;
                }
            } catch (IOException e) {
                logger.error("上传文件异常", e);
                uploaded = false;
            } finally {
                //关闭连接和文件流
                IOUtils.closeQuietly(fis);
                ftpClient.disconnect();
            }
        }
        return uploaded;
    }

    public boolean uploadFilebyStream(String remotePath, List<InputStream> fileList, List<String> fileNameList) throws IOException {
        boolean uploaded = true;
        //连接FTP服务器
        if (connectServer()) {
            try {
                // 創建並進入目錄
                preparePath(remotePath);
                //遍历文件存储
                for (int i=0;i<fileList.size();i++) {
                    InputStream fileItem = fileList.get(i);
                    //调用storeFile方法存储
                    ftpClient.storeFile(fileNameList.get(i), fileItem);
                    fileItem.close();
                }
            } catch (IOException e) {
                logger.error("上传文件异常", e);
                uploaded = false;
            } finally {
                //关闭连接和文件流
                ftpClient.disconnect();
            }
        }
        return uploaded;
    }

    /**
     * 下載文件
     * @param response response
     * @param filePath FTP文件路徑
     */
    public void downloads(HttpServletResponse response,String filePath) throws IOException {
        InputStream fis = null;
        BufferedInputStream bs = null;
        if (connectServer()) {
            try {
                filePath = new String(filePath.getBytes("UTF-8"), "ISO-8859-1");
                ftpClient.enterLocalPassiveMode();

                //设置linux环境
                FTPClientConfig conf = new FTPClientConfig( FTPClientConfig.SYST_UNIX);
                ftpClient.configure(conf);

                fis = ftpClient.retrieveFileStream(rootPath+filePath);
                bs = new BufferedInputStream(fis);
                OutputStream os = response.getOutputStream();
                BufferedOutputStream bos=new BufferedOutputStream(os);//输出缓冲流
                byte data[]=new byte[4096];//缓冲字节数

                int size=0;
                size=bs.read(data);
                while (size!=-1)
                {
                    bos.write(data,0,size);
                    size=bs.read(data);
                }
                bos.flush();
                bos.close();
            } catch (Exception e) {
                logger.error("上传文件异常", e);
            }finally {
                //关闭连接和文件流
                IOUtils.closeQuietly(fis);
                IOUtils.closeQuietly(bs);
                ftpClient.disconnect();
            }
        }
    }
}