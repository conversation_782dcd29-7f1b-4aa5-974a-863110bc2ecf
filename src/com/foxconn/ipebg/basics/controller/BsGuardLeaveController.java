package com.foxconn.ipebg.basics.controller;

import cn.hutool.core.date.DateUtil;
import com.foxconn.ipebg.basics.entity.*;
import com.foxconn.ipebg.basics.service.BsGuardLeaveService;
import com.foxconn.ipebg.basics.service.BsTrainingPersonService;
import com.foxconn.ipebg.basics.service.PerInfoService;
import com.foxconn.ipebg.basics.vo.BsGuardInfoVO;
import com.foxconn.ipebg.basics.vo.BsGuardLeaveVO;
import com.foxconn.ipebg.common.exception.ExcelException;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.ConvertUtils;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import com.foxconn.ipebg.system.utils.FileDownloadUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("guardLeave")
public class BsGuardLeaveController extends BaseController {

    private static final String TPLFILEPATH = "/static/resources/templet";
    private static final String BATCHIMPORTTPL = "guardLeave.xlsx";

    @Autowired
    private BsGuardLeaveService service;

    @Autowired
    private BsTrainingPersonService bsTrainingPersonService;

    @Autowired
    private PerInfoService perInfoService;

    /**
     * 处理用户请求，返回员工请假列表的页面。
     *
     * @return 返回页面的逻辑视图名，该页面用于展示员工的请假信息列表。
     */
    @RequiresPermissions("basics:guardLeave:list")
    @RequestMapping(method = RequestMethod.GET)
    public String list() {
        // 查询并展示列表数据
        return "basics/guardLeave/list";
    }

    /**
     * 方法描述: 列表数据查询
     *
     * @Author: ********
     * @CreateDate: 2024-05-21
     * @Return
     **/
    @RequiresPermissions("basics:guardLeave:list")
    @RequestMapping(value = "list", method = RequestMethod.GET)
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<BsGuardLeaveEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter
                .buildFromHttpRequest(request);
        page = service.search(page, filters);
        return getEasyUIData(service.voPageFromEntityPage(page));
    }

    @RequestMapping(value = "getGuardInfoByEmpNo", method = RequestMethod.GET)
    @ResponseBody
    public BsGuardInfoVO getGuardInfoByEmpNo(@RequestParam(value = "empNo") String empNo) {
        return service.getGuardInfoByEmpNo(empNo);
    }

    /**
     * 方法描述: 保存
     *
     * @Author: ********
     * @CreateDate: 2024-05-21
     * @Return
     **/
    @RequiresPermissions("basics:guardLeave:add")
    @RequestMapping(value = "create", method = RequestMethod.POST)
    @ResponseBody
    public String create(@Valid BsGuardLeaveEntity guardLeaveEntity,
                         Model model, HttpServletRequest request) {
        if (service.couldSaveOrUpdate(guardLeaveEntity)) {
            guardLeaveEntity.setNewRecord(true);
            guardLeaveEntity.setCreateDate(new Date());
            service.save(guardLeaveEntity);
            return "success";
        } else {
            return "與之前維護的請假資料有衝突";
        }
    }

    /**
     * 方法描述: 添加跳轉
     *
     * @Author: ********
     * @CreateDate: 2024-05-21
     * @Return
     **/
    @RequiresPermissions("basics:guardLeave:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model, HttpServletRequest request) {
        model.addAttribute("model", new BsGuardLeaveEntity());
        model.addAttribute("action", "create");
        return "basics/guardLeave/listForm";
    }

    /**
     * 方法描述: 修改跳轉
     *
     * @Author: ********
     * @CreateDate: 2024-05-21
     * @Return
     **/
    @RequiresPermissions("basics:guardLeave:update")
    @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
    public String updateForm(@PathVariable("id") String id, Model model) {
        model.addAttribute("model", service.get(id));
        model.addAttribute("action", "update");
        model.addAttribute("beginOfToday", DateUtil.beginOfDay(new Date()));
        model.addAttribute("endOfToday", DateUtil.endOfDay(new Date()));
        return "basics/guardLeave/listForm";
    }

    /**
     * 方法描述: 修改
     *
     * @Author: ********
     * @CreateDate: 2024-05-21
     * @Return
     **/

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @RequiresPermissions("basics:guardLeave:update")
    @ResponseBody
    public String update(@Valid BsGuardLeaveEntity guardLeaveEntity,
                         @RequestParam(value = "ids") String ids) {
        guardLeaveEntity.setId(ids);
        if (service.couldSaveOrUpdate(guardLeaveEntity)) {
            guardLeaveEntity.setNewRecord(false);
            guardLeaveEntity.setUpdateDate(new Date());
            service.merge(guardLeaveEntity);
            return "success";
        } else {
            return "與之前維護的請假資料有衝突";
        }
    }

    /**
     * 方法描述: 根據主鍵刪除
     *
     * @Author: ********
     * @CreateDate: 2024-05-21
     * @Return
     **/

    @RequestMapping("delete/{id}")
    @RequiresPermissions("basics:guardLeave:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        service.delete(id);
        return "success";
    }

    /**
     * 导出excel
     * @Author: ********
     * @CreateDate: 2022-07-21
     * @Return null
     **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<PropertyFilter> filters = PropertyFilter
                .buildFromHttpRequest(request);
        List<BsGuardLeaveEntity> list = service.searchOrder(filters, "startDate", false);
        try {
            List<BsGuardLeaveVO> voList = service.voListFromEntityList(list);
            ExcelUtil.listToExcel(voList, BsGuardLeaveEntity.exportFieldMap(), "請假信息明細表", response);
        } catch (ExcelException e) {
            e.printStackTrace();
        }
    }

    /**
     * 導入模板下載
     * @param request
     * @param response
     */
    @RequestMapping(value = "downLoad/batchImportTpl", method = RequestMethod.GET)
    public void batchImportTpl(HttpServletRequest request,
                               HttpServletResponse response) {
        fileDownload(request, response, BATCHIMPORTTPL);
    }

    private void fileDownload(HttpServletRequest request,
                              HttpServletResponse response, String fileName) {
        String path = request.getSession().getServletContext()
                .getRealPath(TPLFILEPATH);
        try {
            FileDownloadUtil.fileDownload(request, response, path, fileName);// 下載點位信息模板
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 批量導入
     *
     * @param file
     *            文檔
     * @param isExcel2003
     *            是否2003
     * @return
     */
    @RequestMapping(value = "upload", method = RequestMethod.POST)
    @ResponseBody
    public String upload(MultipartFile file, boolean isExcel2003, Model model,
                         HttpServletRequest request) {

        if (file.equals("") || file.getSize() <= 0) {
            return Constant.FAIL;
        } else {
            String result = null;
            try {
                result = service.batchImport(
                        file.getInputStream(), isExcel2003);
            } catch (IOException e) {
                e.printStackTrace();
            }
            if (Constant.RESULT.CODE_YES.getValue().equals(result)) {
                return Constant.SUCCESS;
            } else {
                return result;
            }
        }
    }

    /**
     * 方法描述: 下載批量上傳異常信息
     *
     * @Author: ********
     * @CreateDate: 2019-02-13 18:57:46
     * @Return
     **/

    @RequestMapping(value = "downLoad/errorExcel", method = RequestMethod.GET)
// @RequiresPermissions("other:tqhraymachinescrap:add")
    @ResponseBody
    public void errorExcelDownload(HttpServletRequest request,
                                   HttpServletResponse response) throws IOException {
        try {
            List<BsGuardLeaveErrorDto> list = (List<BsGuardLeaveErrorDto>) request.getSession().getAttribute(service.ERRORINFOINDEX);
            // excel的sheetName
            String sheetName = "錯誤日誌";
            // excel要导出的数据
            // 导出
            if (list == null || list.size() == 0) {
                System.out.println("日誌为空");
            }else {
                //将list集合转化为excel
                ExcelUtil.listToExcel(list, BsGuardLeaveErrorDto.getFieldMap(), sheetName, response);
                System.out.println("导出成功~~~~");
            }
        } catch (ExcelException e) {
            e.printStackTrace();
        }
    }
}
