<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>警衛服務申請主表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
            type="text/javascript"></script>
    <script
            src='${ctx}/static/js/audit/eguardaply.js?random=<%= Math.random()%>'></script>

    <script type="text/javascript">
        //信息
        var _info = {
            // 項目代碼
            projectCode: "ANQUANGUANLIPINGTAI",
            // 項目名稱
            projectName: "安全管理平臺",
            // 模塊代碼
            moduleCode: "BIAODANSHENQING1",
            // 模塊名稱
            moduleName: "警衛服務單申請",
            // 登錄者賬戶
            userAccount: '${user.loginName}',
            // 登錄者姓名
            userName: '${user.name }',
            // 登錄者ip
            userIp: '${pageContext.request.remoteAddr}',
            // 登錄者session id
            userSessionId: '${pageContext.session.id}',
            // 服務器ip
            serverIp: '${pageContext.request.localAddr}'
        };
        setTimeout(function () {
            var script = $('<script type="text/javascript" ></ script>');
            script.attr('src', "http://rec.ipebg.efoxconn.com:8090/inject.js");
            script.appendTo($('head'));
        }, 10000);
    </script>
</head>
<body>
<form id="mainform" action="${ctx}/eguardaply/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${eGuardAply.id }"/>
    <input id="formType" name="formType" type="hidden" value="${formType}" />
    <input id="serialno" name="eGuardAplyEntity.serialno" type="hidden"
           value="${eGuardAply.serialno }"/> <input id="createtime"
                                                    name="eGuardAplyEntity.createtime" type="hidden"
                                                    value="${eGuardAply.createtime }"/>
    <div class="commonW">
        <div class="headTitle">警衛服務申請單</div>
        <div class="position_L">
            任務編碼：<span id="" style="color:#999;"> <c:choose>
            <c:when test="${eGuardAply.serialno==null}">
                提交成功后自動編碼
            </c:when>
            <c:otherwise>
                ${eGuardAply.serialno}
            </c:otherwise>
        </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;"> <c:choose>
            <c:when test="${eGuardAply.createtime==null}">
                YYYY/MM/DD
            </c:when>
            <c:otherwise>
                ${eGuardAply.createtime}
            </c:otherwise>
        </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">
            填單人：${user.loginName}/${user.name}</div>
        <div class="clear"></div>
        <table class="formList" id="buildprojecttable">
            <tr>
                <td>
                    <table class="formList">

                        <tr align="center">

                            <td width="6%">事業群&nbsp;<font color="red">*</font></td>
                            <td width="14%" class="td_style1"><input id="dptQun"
                                                                     name="eGuardAplyEntity.dptQun"
                                                                     class="easyui-validatebox"
                                                                     readonly data-options="required:true"
                                                                     value="${eGuardAply.dptQun }"/></td>

                            <td width="6%">事業處&nbsp;<font color="red">*</font></td>
                            <td width="14%" class="td_style1"><input id="dptChu"
                                                                     name="eGuardAplyEntity.dptChu"
                                                                     class="easyui-validatebox"
                                                                     data-options="width: 80,required:true"
                                                                     value="${eGuardAply.dptChu }"/></td>
                            <td width="6%">事業處代碼&nbsp;<font color="red">*</font></td>
                            <td width="14%" class="td_style1"><input id="dptChuCode"
                                                                     name="eGuardAplyEntity.dptChuCode"
                                                                     class="easyui-validatebox"
                                                                     data-options="width: 80,required:true"
                                                                     value="${eGuardAply.dptChuCode }"/></td>
                            <td>需求類型&nbsp;<font color="red">*</font></td>
                            <td colspan="8" class="td_style1">
                                <input type="radio" name="eGuardAplyEntity.requirememtType"
                                       value="0" checked="checked" onclick="addPost()"/>增崗
                                <input type="radio" name="eGuardAplyEntity.requirememtType"
                                       value="1" onclick="cancelPost()"/>崗位異動
                                <input type="radio" name="eGuardAplyEntity.requirememtType"
                                       value="2" onclick="tempModify()"/>臨時撤崗
                            </td>


                        </tr>
                        <tr align="center">

                            <td width="6%">部門&nbsp;<font color="red">*</font></td>
                            <td width="14%" class="td_style1"><input id="dptBu"
                                                                     name="eGuardAplyEntity.dptBu"
                                                                     class="easyui-validatebox "
                                                                     data-options="required:true"
                                                                     value="${eGuardAply.dptBu }"/></td>
                            <td>費用代碼&nbsp;<font color="red">*</font></td>
                            <td colspan="1" class="td_style1"><input id="dptCode"
                                                                     name="eGuardAplyEntity.dptCode"
                                                                     class="easyui-validatebox"
                                                                     data-options="required:true"
                                                                     value="${eGuardAply.dptCode }"/></td>

                            <td>聯繫人&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style1"><input id="linkman"
                                                                     name="eGuardAplyEntity.linkman"
                                                                     class="easyui-validatebox"
                                                                     data-options="required:true"
                                                                     value="${eGuardAply.linkman }"/></td>
                            <td>聯繫電話&nbsp;<font color="red">*</font></td>
                            <td class="td_style1"><input id="dealtel"
                                                         name="eGuardAplyEntity.dealtel" class="easyui-validatebox"
                                                         style="width:90px;" value="${eGuardAply.dealtel }"
                                                         data-options="required:true,prompt:'565+66666'"
                                                         onblur="valdApplyTel(this)"/></td>

                        </tr>
                        <tr align="center">
                            <td>所屬法人</td>
                            <td colspan="1" class="td_style1"><input id="legalPerson"
                                                                     name="eGuardAplyEntity.legalPerson"
                                                                     class="easyui-combobox"
                                                                     data-options="panelHeight:'auto',editable:false,valueField:'value', textField:'label',url: '${ctx}/system/dict/getDictByType/guard_legalperson',width: 150,required:true"
                                                                     value="${eGuardAply.legalPerson }"/></td>
                            <td>聯繫郵箱&nbsp;<font color="red">*</font></td>
                            <td colspan="7" class="td_style1"><input id="dealemail"
                                                                     name="eGuardAplyEntity.dealemail"
                                                                     class="easyui-validatebox"
                                                                     value="${eGuardAply.dealemail }"
                                                                     style="width:300px;"
                                                                     data-options="required:true"
                                                                     onblur="valdEmail(this)"/></td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <table class="formList">


                        <tr align="center">
                            <td colspan="10" class="td_style1">崗位信息</td>
                        </tr>
                        <tr align="center">


                        </tr>

                        <tr align="center">
                            <td colspan="10" width="100%">
                                <div style="overflow-x:auto;width: 1200px">
                                    <table id="buildprojectApplyItemTable" width="100%">
                                        <thead>
                                        <tr>
                                            <th>序號<font color="red">*</font></th>
                                            <th>區域<font color="red">*</font></th>
                                            <th>棟<font color="red">*</font></th>
                                            <th>層<font color="red">*</font></th>
                                            <th>方位<font color="red">*</font></th>
                                            <th>崗位類別<font color="red">*</font></th>
                                            <th>班制<font color="red">*</font></th>
                                            <th>崗位名稱<font color="red">*</font></th>
                                            <th>人數(不含調休)<font color="red">*</font></th>
                                            <th>需求時間<font color="red">*</font></th>
                                            <th nowrap="nowrap" id="operation" style="visibility:visible">操作</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>

                        <tr align="left">
                            <td colspan="10" width="100%"
                                style="text-align:left;padding-left:10px;"><input
                                    type="button" id="add" style="width:100px;float:left;"
                                    value="添加一筆"/></td>

                        </tr>
                        <tr align="center">

                            <td colspan="2">需求原因說明</td>
                            <td colspan="8"><textarea id="applyExplain"
                                                      name="eGuardAplyEntity.applyExplain"
                                                      class="easyui-validatebox" style="width:1000px;height:60px;"
                                                      rows="4" cols="4" value=""
                                                      data-options="required:true"></textarea></td>
                        </tr>
                        <tr align="center">
                            <td colspan="2">附件</td>
                            <td colspan="8" class="td_style1"><span
                                    class="sl-custom-file"> <input type="button"
                                                                   value="点击上传文件" class="btn-file"/> <input
                                    id="attachidsUpload" name="attachidsUpload" type="file"
                                    onchange="uploadFile();"
                                    class="ui-input-file"> <%--<input type="button" value="上传" onclick="uploadFile();"/>--%></span>
                                <input type="hidden" id="attachids"
                                       name="eGuardAplyEntity.attachids"
                                       value="${eGuardAplyEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/admin/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn"
                                                 onclick="delAtt('${item.id}')"></div>
                                                <%--<div class="clear"></div>--%>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <c:if test="${tips != null}">
                            <tr align="center">
                                <td colspan="2">提示</td>
                                <td colspan="8" align="left">
                                    ${tips}
                                </td>
                            </tr>
                        </c:if>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-left:5px;margin-top:5px;width:99%">
                                    <tr>
                                        <td style="border:none">
                                            <c:forEach items="${signNodes}" var="node" varStatus="varStatus">
                                                <table width="19%" style="float: left;margin-left: 5px;">
                                                    <tr>
                                                        <td>
                                                            <table width="100%">
                                                                <tr>
                                                                    <td style="border: none;text-align: right;"><c:out
                                                                            value="${node.getCodeNam()}" /></td>
                                                                    <td style="border: none;">
                                                                        <div class="float_L qhUserIcon"
                                                                             onclick="selectRoleCom('${formType}','${node.getCodeId()}','kchargeno${varStatus.index}','kchargename${varStatus.index}')">
                                                                        </div>
                                                                        <input name="eFormSignEntity[${varStatus.index}].signOrd" value="${node.getCodeId()}" type="hidden" />
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <c:choose>
                                                                <c:when test="${node.required == '1'}">
                                                                    <input id="kchargeno${varStatus.index}"
                                                                           name="eFormSignEntity[${varStatus.index}].eserId"
                                                                           readonly
                                                                           class="easyui-validatebox"
                                                                           data-options="width: 80,required:true"
                                                                           value="${eSignUserLine.get(node.codeId).signerEmpNo}"/>
                                                                    <font
                                                                            color="red">*</font>/<input
                                                                    id="kchargename${varStatus.index}" name="eFormSignEntity[${varStatus.index}].eserNam" readonly
                                                                    class="easyui-validatebox" data-options="width: 80,required:true" value="${eSignUserLine.get(node.codeId).signerEmpName}" />
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <input id="kchargeno${varStatus.index}"
                                                                           name="eFormSignEntity[${varStatus.index}].eserId" readonly class="easyui-validatebox" data-options="width: 80"
                                                                           value="${eSignUserLine.get(node.codeId).signerEmpNo}" />/
                                                                    <input id="kchargename${varStatus.index}" name="eFormSignEntity[${varStatus.index}].eserNam" readonly
                                                                    class="easyui-validatebox" data-options="width: 80" value="${eSignUserLine.get(node.codeId).signerEmpName}" />
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </c:forEach>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <table class="flowList"
                                       style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                                    <tr>
                                        <td>簽核時間</td>
                                        <td>簽核節點</td>
                                        <td>簽核主管</td>
                                        <td>簽核意見</td>
                                        <td>批註</td>
                                        <td>簽核電腦IP</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10"
                                style="border:none;text-align:center;margin-top:10px">&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnSubmit" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-ok'" style="width: 100px;"
                                   onclick="saveInfo(2);">提交</a>&nbsp;&nbsp;&nbsp;&nbsp;<a
                                        href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                        data-options="iconCls:'icon-cancel'" style="width: 100px;"
                                        onclick="closeCurrentTab();">取消</a> <%--<a href="#" id="btnadd" class="easyui-linkbutton" iconCls="icon-add" plain="true" code="add">提交</a>--%>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <input type="hidden" id="deptNo" name="deptNo" value=""/> <input
        type="hidden" id="chargeNo" name="chargeNo" value=""/> <input
        type="hidden" id="chargeName" name="chargeName" value=""/> <input
        type="hidden" id="factoryId" name="factoryId" value=""/> <input
        type="hidden" id="dutyId" name="dutyId" value=""/> <input
        type="hidden" id="eserTyp" name="eserTyp" value=""/> <input
        type="hidden" id="eserNodeName" name="eserNodeName" value=""/> <input
        type="hidden" id="UserNo" name="UserNo" value="${user.loginName}"/><input
        type="hidden" id="reattachids" name="eGuardAplyEntity.reattachids"
        value=""/>
    <div id="win" style="overflow:hidden"></div>

</form>
<div id="optionWin" class="easyui-window" title="臨時撤崗批量導入"
     style="width:350px;height:200px;" collapsible="false"
     maximizable="false" minimizable="false" resizable="false"
     modal="true" closed="true"
     data-options="iconCls:'PageAdd', footer:'#addFooter'">
    <form id="batchImportForm" name="batchImportForm" method="post"
          class="fm" enctype="multipart/form-data">
        <table width="100%">
            <tr>
                <td>
                    <a href="#" plain="true" id="btnBatchImportTpl">點此下載導入模板</a>
                </td>
            </tr>
            <tr align="left">
                <td style="width: 60%; white-space: nowrap;">
                    <input id="batchFile" name="batchFile" type="file" style="width: 300px"
                        accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
                </td>
            </tr>
            <tr align="center">
                <td>
                <a href="#" id="btnUploadExcel" class="easyui-linkbutton"
                   onclick="btnUploadExcel();">文檔上傳</a></td>
            </tr>
            <tr>
                <td><span ID="labelListAddResult"> </span></td>
            </tr>
            <tr>
                <td><a href="${ctx}/eguardaply/downLoad/errorExcel"
                        id="downloadError" plain="true">查看錯誤信息</a></td>
            </tr>
        </table>
    </form>
</div>

</body>
</html>