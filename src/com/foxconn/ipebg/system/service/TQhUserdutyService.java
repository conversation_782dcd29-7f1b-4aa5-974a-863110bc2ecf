package com.foxconn.ipebg.system.service;

import com.foxconn.ipebg.system.entity.AuditConnonUser;
import com.foxconn.ipebg.system.entity.AuditHqUser;
import com.foxconn.ipebg.system.entity.TQhUserdutyEntity;
import com.foxconn.ipebg.system.dao.TQhUserdutyDao;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-17 09:10:19
 */
@Service
@Transactional(readOnly=true)
public class  TQhUserdutyService extends BaseBusinessService<TQhUserdutyEntity, String>{
    @Autowired
    private TQhUserdutyDao tQhUserdutyDao;
    @Override
    public HibernateDao<TQhUserdutyEntity, String> getEntityDao() {
        return tQhUserdutyDao;
    }

    public List<AuditHqUser> findAuditUser(String dutyid,String factoryid){
        List<AuditHqUser> userList = new ArrayList<AuditHqUser>();
        AuditHqUser user = null;

        String hql ="select distinct s.empno, s.empname,s.ismanager,d.remarks from TQhUserdutyEntity d,TQhUserformhsEntity s where d.empno=s.empno and d.factoryid=?0 and d.dutyid=?1";
        List<Object[]> list = this.tQhUserdutyDao.find(hql, factoryid, dutyid);
        if (list.size() > 0) {
            for (Object[] objects : list) {
                user= new AuditHqUser();
                user.setEmpno(objects[0].toString());
                user.setUsername(objects[1].toString());
                user.setIsmanager(objects[2]==null?"無":objects[2].toString());
                user.setRemarks(objects[3]==null?"":objects[3].toString());
                userList.add(user);
            }
        }
        return userList;
    }
}
