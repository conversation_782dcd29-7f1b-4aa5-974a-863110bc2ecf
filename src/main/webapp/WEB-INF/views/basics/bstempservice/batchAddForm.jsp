<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>臨時勤務信息表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
        var _action="${action}";
        var _btsId = "${bsTempService.id}";
        if(_btsId==null){
            _btsId=-1;
        }
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<form id="mainform" action="${ctx}/bstempservice/${action}" method="post">
    <input type="hidden" name="ids" value="${bsTempService.id }" />
    <input type="hidden" name="tempService.delFlag"  value="${bsTempService.delFlag }"/>
    <input type="hidden" name="tempService.createBy" value="${bsTempService.createBy }" />
    <input type="hidden" name="tempService.createDate"  value="<fmt:formatDate value="${bsTempService.createDate}"/>"/>
    <div class="headTitle">臨時勤務信息維護</div>
    <table class="formList">
        <tr align="center">
            <td>事由：</td>
            <td>
                <input id="cause" name="tempService.cause" value="${bsTempService.cause}" class="easyui-validatebox" data-options="required:true"
                       style="width:98%;"/>
            </td>
            <td>地點：</td>
            <td>
                <input id="location" name="tempService.location" value="${bsTempService.location}" class="easyui-validatebox"
                       data-options="required:true" style="width:98%;"/>
            </td>
            <td>用人單位：</td>
            <td>
                <input id="employerUnit" name="tempService.employerUnit" value="${bsTempService.employerUnit}" class="easyui-validatebox"
                       data-options="required:true" style="width:98%;"
                       value="${bsTempService.employerUnit }"/>
            </td>
        </tr>
        <tr align="center">
            <td>開始時間：</td>
            <td>
                <input id="startTime" name="tempService.startTime" value="${bsTempService.startTime}" class="Wdate easyui-validatebox" type="text"
                       data-options="required:true"
                       onclick="var endTime=$dp.$('endTime');WdatePicker({dateFmt:'yyyy-MM-dd HH:00:00',onpicked:function(){endTime.click();timeOnPicked();},maxDate:'#F{$dp.$D(\'endTime\')}'})"
                       style="width:150px;"/>
            </td>
            <td>結束時間：</td>
            <td>
                <input id="endTime" name="tempService.endTime" value="${bsTempService.endTime}" class="Wdate easyui-validatebox" type="text"
                       data-options="required:true"
                        <c:if test="${action == 'update'}"> disabled </c:if>
                       onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:00:00',onpicked:timeOnPicked,minDate:'#F{$dp.$D(\'startTime\')}'})"
                       style="width:150px;"/>
            </td>
            <td>法人：</td>
            <td>
                <input id="legalPerson" name="tempService.legalPerson" value="${bsTempService.legalPerson}" class="easyui-combobox"
                       data-options="required:true,width: 150"/>
            </td>
        </tr>
        <tr align="center">

            <td>人數：</td>
            <td>
                <input id="peopleNumber" name="tempService.peopleNumber" value="${bsTempService.peopleNumber}" class="easyui-numberbox"
                       data-options="required:true,width: 150"/>
            </td>
            <td>費用代碼：</td>
            <td>
                <input id="costCode" name="tempService.costCode" value="${bsTempService.costCode}" class="easyui-validatebox"
                       data-options="required:true,width: 150"/>
            </td>
            <td>臨時勤務費用(元)：</td>
            <td>
                <input id="expenses" name="tempService.expenses" value="${bsTempService.expenses/100.0}" class="easyui-numberbox"
                       data-options="required:true,width: 150"/>
            </td>

        </tr>
    </table>
</form>
<div id="tb" style="height:auto">
    <span>排班人員工號：</span>
    <input id="empSearch" class="easyui-validatebox" data-options="width: 150,height:300" onblur="getPerson()"/>
</div>
<table id="dg"></table>
<div class="headTitle">
    <a href="#" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'" style="width: 100px;"
       onclick="saveInfo();">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
    <a href="javascript:;" id="btnSave" class="easyui-linkbutton" data-options="iconCls:'icon-cancel'"
       style="width: 100px;" onclick="closeCurrentTab();">取消</a>
</div>
</div>
<script src='${ctx}/static/js/basics/bstempservicebatchadd.js?"+Math.random()"'></script>
<script type="text/javascript">

    //提交表单
    $('#mainform').form({
        onSubmit: function () {
            var isValid = $(this).form('validate');
            return isValid;	// 返回false终止表单提交
        },
        success: function (data) {
            successTip(data, dg, d);
        }
    });
</script>
</body>
</html>