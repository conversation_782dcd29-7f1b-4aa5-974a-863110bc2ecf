package com.foxconn.ipebg.system.service;

import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.system.entity.AuditConnonUser;
import com.foxconn.ipebg.system.entity.AuditHqUser;
import com.foxconn.ipebg.system.entity.TQhChargepathEntity;
import com.foxconn.ipebg.system.dao.TQhChargepathDao;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;


/**
 * 簽核路徑表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-11 10:55:00
 */
@Service
@Transactional(readOnly = true)
public class TQhChargepathService extends BaseBusinessService<TQhChargepathEntity, String> {
    @Autowired
    private TQhChargepathDao tQhChargepathDao;

    @Override
    public HibernateDao<TQhChargepathEntity, String> getEntityDao() {
        return tQhChargepathDao;
    }

    public List<AuditConnonUser> findAuditUser(String chargeNo, String chargeName, TQhChargepathEntity entity) {
        List<AuditConnonUser> userList = new ArrayList<AuditConnonUser>();
        AuditConnonUser user = null;
        String hql = "select distinct " + chargeNo + " as empNo," + chargeName + " as userName" + " from TQhChargepathEntity t where t.factoryid=?0 and t.deptno=?1 and t.isvalid=1";
        if(entity.getKchargeno()!=null&& StringUtils.isNotEmpty(entity.getKchargeno())){
            hql+= " and t.kchargeno='"+entity.getKchargeno()+"'";
        }
        if(entity.getBchargeno()!=null&& StringUtils.isNotEmpty(entity.getBchargeno())){
            hql+=" and t.bchargeno='"+entity.getBchargeno()+"'";
        }
        if(entity.getCchargeno()!=null&& StringUtils.isNotEmpty(entity.getCchargeno())){
            hql+=" and t.cchargeno='"+entity.getCchargeno()+"'";
        }
        List<Object[]> list = this.tQhChargepathDao.find(hql, entity.getFactoryid(), entity.getDeptno());
        if (list.size() > 0) {
            for (Object[] objects : list) {
                if (objects != null&&objects[0]!=null) {
                    user = new AuditConnonUser();
                    user.setEmpno(objects[0].toString());
                    user.setUsername(objects[1].toString());
                    userList.add(user);
                }
            }
        }
        return userList;
    }
    /**
      * 方法描述: 簽核路徑置為無效
      * @Author: S6114648
      * @CreateDate:   2018/10/26  下午 01:51
      * @Return
      **/

    public int setInvalid(String id){
        String hql = "update TQhChargepathEntity t set t.isvalid=?0 where t.id='"+id+"'";
        return this.tQhChargepathDao.batchExecute(hql,"0");
    }

    /**
     * 方法描述: 簽核路徑置為有效
     * @Author: S6114648
     * @CreateDate:   2018/10/26  下午 01:51
     * @Return
     **/

    public int setValid(String id){
        String hql = "update TQhChargepathEntity t set t.isvalid=?0 where t.id='"+id+"'";
        return this.tQhChargepathDao.batchExecute(hql,"1");
    }
}
