<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>資訊（非固資類）物品新購及報廢申請</title>
<script type="text/javascript">var ctx = "${pageContext.request.contextPath}";</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
	<form id="searchFrom" action="">
		<div>
			<input type="text" name="filter_EQS_applyno" class="easyui-validatebox" data-options="width:150,prompt: '申請人工號'"/>
			<input type="text" name="filter_EQS_applydeptno" class="easyui-validatebox" data-options="width:150,prompt: '申請人單位代碼'"/>
			<input type="text" name="filter_EQS_serialno" class="easyui-validatebox" data-options="width:150,prompt: '任務編碼'"/>
			<input type="text" name="filter_EQS_makerno" class="easyui-validatebox" data-options="width:150,prompt: '填單人工號'"/>
			<input id="qysjzt" style="width:100px" class="easyui-validatebox" name="filter_EQS_workstatus" />
		    <br/>
			<input type="text" name="filter_GTD_createtime" class="easyui-my97" datefmt="yyyy-MM-dd" data-options="width:150,prompt: '填單开始日期'"/>
			- <input type="text" name="filter_LTD_createtime" class="easyui-my97" datefmt="yyyy-MM-dd" data-options="width:150,prompt: '填單结束日期'"/>
			<input type="text" name="filter_GTD_complettime" class="easyui-my97" datefmt="yyyy-MM-dd" data-options="width:150,prompt: '簽核完成开始日期'"/>
			- <input type="text" name="filter_LTD_complettime" class="easyui-my97" datefmt="yyyy-MM-dd" data-options="width:150,prompt: '簽核完成结束日期'"/>
			<input id="qysqlb" style="width:100px" class="easyui-validatebox" name="filter_EQS_requiretype" />
			<span class="toolbar-item dialog-tool-separator"></span>
			<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
			<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh" onclick="listSearchReset()">重置</a>
			<a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel" onclick="exportExcel()">导出Excel</a>
		</div>
	</form>
</div>
<table id="dg"></table>
<div id="dlg"></div>
<script src="${ctx}/static/js/other/tqhwfgoodsbuyandscrap.js?"+Math.random()></script>
</body>
</html>