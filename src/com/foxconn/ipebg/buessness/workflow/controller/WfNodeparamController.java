package com.foxconn.ipebg.buessness.workflow.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.buessness.workflow.entity.WfNodeparamEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeparamService;
import com.foxconn.ipebg.common.persistence.Page;


/**
 * 流程節點出口信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-06 11:04:39
 */
@Controller
@RequestMapping("wfnodeparam")
public class WfNodeparamController extends BaseController {

    @Autowired
    private WfNodeparamService wfNodeparamService;

    /**
      * 方法描述: 列表信息
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:39
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    @RequiresPermissions("workflow:wfnodeparam:list")
    public String list() {
        //查询列表数据
        return "buessness/workflow/wfnodeparam/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:39
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    @RequiresPermissions("workflow:wfnodeparam:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<WfNodeparamEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        page = wfNodeparamService.search(page, filters);
        return getEasyUIData(page);
    }
    /**
      * 方法描述: 添加跳轉
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:39
      * @Return
      **/

    @RequiresPermissions("workflow:wfnodeparam:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("wfNodeparam", new WfNodeparamEntity());
        model.addAttribute("action", "create");
        return "buessness/workflow/wfnodeparam/listForm";
    }
    /**
      * 方法描述: 保存
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:39
      * @Return
      **/

    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("workflow:wfnodeparam:add")
    @ResponseBody
    public String create(@Valid WfNodeparamEntity wfNodeparam, Model model) {
        wfNodeparamService.save(wfNodeparam);
        return "success";
    }
    /**
      * 方法描述: 修改跳轉
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:39
      * @Return
      **/

    @RequiresPermissions("workflow:wfnodeparam:update")
    @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
    public String updateForm(@PathVariable("id") String id, Model model) {
        model.addAttribute("wfNodeparam", wfNodeparamService.get(id));
        model.addAttribute("action", "update");
        return "buessness/workflow/wfnodeparam/listForm";
    }
    /**
      * 方法描述: 修改
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:39
      * @Return
      **/

    @RequestMapping(value="update", method = RequestMethod.POST)
    @RequiresPermissions("workflow:wfnodeparam:update")
    @ResponseBody
    public String update(@Valid WfNodeparamEntity wfNodeparam) {
        wfNodeparamService.update(wfNodeparam);
        return "success";
    }

    /**
      * 方法描述: 根據主鍵刪除
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:39
      * @Return
      **/

    @RequestMapping("delete/{id}")
    @RequiresPermissions("workflow:wfnodeparam:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        wfNodeparamService.delete(id);
        return "success";
    }

}
