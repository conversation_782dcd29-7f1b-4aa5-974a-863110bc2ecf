var dg;
var d;
$(function(){
    dg=$('#dg').datagrid({
        method: "get",
        url:ctx+'/eguarditemaply/list',
        fit : true,
        fitColumns : true,
        border : false,
        idField : 'postStartDate',
        striped:true,
        pagination:true,
        rownumbers:true,
        pageNumber:1,
        pageSize : 20,
        pageList : [ 10, 20, 30, 40, 50 ],
        singleSelect:true,
        columns:[[
							                    { field: 'postStartDate', title: '需求時間-開始時間',hidden:true},
											                    { field: 'postEndDate', title: '需求時間-結束時間',sortable:true,width:100}
					,
																                    { field: 'postLevel', title: '崗位級別',sortable:true,width:100}
					,
																                    { field: 'securityCom', title: '派駐保安公司',sortable:true,width:100}
					,
																                    { field: 'postPerNuR', title: '實到崗人數',sortable:true,width:100}
					,
																                    { field: 'postEffectDate', title: '生效時間',sortable:true,width:100}
					,
																                    { field: 'createBy', title: '創建人',sortable:true,width:100}
					,
																                    { field: 'createDate', title: '創建時間',sortable:true,width:100}
					,
																                    { field: 'updateBy', title: '更新者',sortable:true,width:100}
					,
																                    { field: 'updateDate', title: '更新時間',sortable:true,width:100}
					,
																                    { field: 'delFlag', title: '刪除標識',sortable:true,width:100}
					,
																                    { field: 'id', title: '主鍵',sortable:true,width:100}
					,
																                    { field: 'serialno', title: '主表任務單號',sortable:true,width:100}
					,
																                    { field: 'recno', title: '編號',sortable:true,width:100}
					,
																                    { field: 'site', title: '廠區',sortable:true,width:100}
					,
																                    { field: 'area', title: '區域',sortable:true,width:100}
					,
																                    { field: 'block', title: '棟',sortable:true,width:100}
					,
																                    { field: 'floor', title: '層',sortable:true,width:100}
					,
																                    { field: 'position', title: '方位',sortable:true,width:100}
					,
																                    { field: 'location', title: '詳細位置',sortable:true,width:100}
					,
																                    { field: 'postType', title: '崗位類別',sortable:true,width:100}
					,
																                    { field: 'postShift', title: '班制',sortable:true,width:100}
					,
																                    { field: 'postName', title: '崗位名稱',sortable:true,width:100}
					,
																                    { field: 'postPerNu', title: '人數(不含調休)',sortable:true,width:100}
												        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        rowTooltip: true,
        toolbar:'#tb'
    });
});

//弹窗增加
function add() {
    d=$("#dlg").dialog({
        title: '添加崗位申請明細表',
        width: 380,
        height: 380,
        href:ctx+'/eguarditemaply/create',
        maximizable:true,
        modal:true,
        buttons:[{
            text:'确认',
            handler:function(){
                $("#mainform").submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}

//删除
function del(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data){
        if (data){
	$.ajax({
                type:'get',
                url:ctx+"/eguarditemaply/delete/"+row.postStartDate,
                success: function(data){
                    successTip(data,dg);
                }
            });
        }
    });
}

//弹窗修改
function upd(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    d=$("#dlg").dialog({
        title: '修改崗位申請明細表',
        width: 380,
        height: 340,
        href:ctx+'/eguarditemaply/update/'+row.postStartDate,
        maximizable:true,
        modal:true,
        buttons:[{
            text:'修改',
            handler:function(){
                $('#mainform').submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}
//创建查询对象并查询
function cx(){
    var obj=$("#searchFrom").serializeObject();
    dg.datagrid('load',obj);
}
//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("reset");
}
//导出excel
function exportExcel(){
    var form = document.getElementById("searchFrom");
    searchFrom.action=ctx+'/eguarditemaply/exportExcel';
    form.submit();
}