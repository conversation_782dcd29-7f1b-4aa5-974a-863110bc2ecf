package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.entity.BsPostEntity;
import com.foxconn.ipebg.basics.entity.BsTraineeEntity;
import com.foxconn.ipebg.basics.dao.BsPostDao;
import com.foxconn.ipebg.buessness.audit.entity.EGuardItemAplyEntity;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.Dict;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WorkFlowEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.system.service.DictService;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static java.lang.String.*;

/**
 * 崗位明細表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-03-07 09:57:22
 */
@Service
@Transactional(readOnly = true)
public class BsPostService extends BaseBusinessService<BsPostEntity, String> {
	@Autowired
	private BsPostDao bsPostDao;

	@Autowired
	private UserServiceUtil serviceUtil;
	@Autowired
	private DictService dictService;

	@Override
	public HibernateDao<BsPostEntity, String> getEntityDao() {
		return bsPostDao;
	}

	public BsPostEntity findByRecno(String recno) {
		return this.bsPostDao.findUniqueBy("recno", recno);
	}

	public List<BsPostEntity> findCancelPost(String serialnoQry) {

		StringBuffer sb = new StringBuffer();
		sb.append(" select * from bs_post t ").append(" WHERE 1=1  ");
        if (!serialnoQry.equals(""))//||!serialnoQry.equals(null)
        {
        	sb.append("  and t.recno like '%"+serialnoQry+"%' ");	
        }
        
		sb.append("ORDER BY recno ");
		@SuppressWarnings("unchecked")
		List<BsPostEntity> cancelPostList = this.bsPostDao
				.createSQLQuery(sb.toString()).addEntity(BsPostEntity.class)
				.list();
		return cancelPostList;

	}

//	/**
//	 * Author: S6114893
//	 * Date: 2019/9/26
//	 * @param recno: 崗位編碼
//	 * @param name: 崗位名稱
//	 * @param businessGroup: 事業群
//	 * @param page: 結果分頁
//	 * orderBy: 排序方式
//	 * */
//	public Page<BsPostEntity> searchPosts(String recno, String name, String businessGroup, Page page) {
//		StringBuffer sb = new StringBuffer();
//		sb.append("select * from BS_POST WHERE POST_PER_NU > 0 AND DEL_FLAG = '0' ");
//		if (recno != null && recno.trim().length() > 0) {
//			sb.append("AND RECNO='"+ recno + "' ");
//		}
//		if (name != null && name.trim().length() > 0) {
//			sb.append("AND POST_NAME like '" + name + "' ");
//		}
//		if (businessGroup != null && businessGroup.trim().length() > 0) {
//			sb.append("AND SERIALNO in (select SERIALNO from E_GUARD_APLY where DPT_QUN = '" + businessGroup + "') ");
//		}
//
//		sb.append("ORDER BY " + StringUtils.join(page.getOrderBy().replaceAll("([A-Z])", ",$1").split(","), "_").toUpperCase() + " " + page.getOrder());
//		sb.append(" LIMIT " + page.getPageSize());
//		sb.append(" OFFSET " + (page.getFirst() - 1));
//		List<BsPostEntity> postList = this.bsPostDao
//				.createSQLQuery(sb.toString()).addEntity(BsPostEntity.class)
//				.list();
//
//		StringBuffer countSQL = new StringBuffer();
//		countSQL.append("select COUNT(*) from BS_POST WHERE POST_PER_NU > 0 AND DEL_FLAG = '0' ");
//		if (recno != null && recno.trim().length() > 0) {
//			countSQL.append("AND RECNO='"+ recno + "' ");
//		}
//		if (name != null && name.trim().length() > 0) {
//			countSQL.append("AND POST_NAME like '" + name + "' ");
//		}
//		if (businessGroup != null && businessGroup.trim().length() > 0) {
//			countSQL.append("AND SERIALNO in (select SERIALNO from E_GUARD_APLY where DPT_QUN = '" + businessGroup + "') ");
//		}
//
//		Integer count = Integer.valueOf(this.bsPostDao.createSQLQuery(countSQL.toString()).uniqueResult().toString());
//
//		page.setTotalCount(count);
//		page.setResult(postList);
//		return page;
//	}

	public int batchDelete(String recno) {
		return bsPostDao.batchExecute(
				"delete from BsPostEntity t where t.recno=?0", recno);
		
	}

	// 批量 軟刪崗位信息
	public String batchDeleteByRecnos(String ids) {

		//List<EGuardItemAplyEntity> list =null;
		if (ids == null) {
			return null;
		}
		List<String> GuardItems = Arrays.asList(ids.split(","));
		for (int i = 0; i < GuardItems.size(); i++) {
			batchDelete(GuardItems.get(i));
		}
		return "success";

	}

	public List<BsPostEntity> findPostList(BsPostEntity post, String securityCom) {

		StringBuffer sb = new StringBuffer();
		sb.append(" select * from V_BS_POST t ").append(" WHERE 1=1  ");
		if (!post.getPostName().equals(""))//||!serialnoQry.equals(null)
		{
			sb.append("  and t.POST_NAME LIKE '%" + post.getPostName() + "%' ");
		}
//		        if (!post.getEmpName().equals(""))//||!serialnoQry.equals(null)
//		        {
//		        	sb.append("  and t.emp_name='"+post.getEmpName()+"' ");	
//		        }
		if (StringUtils.isNotEmpty(securityCom)) {
			sb.append(" and t.security_com in ('" + securityCom + "') ");
		}
		sb.append("ORDER BY POST_NAME ");
		@SuppressWarnings("unchecked")
		List<BsPostEntity> postList = this.bsPostDao
				.createSQLQuery(sb.toString()).addEntity(BsPostEntity.class)
				.list();
		return postList;

	}

	// 批量生效崗位信息
	public int updatePostPerNuByRecno(String recno, int postPerNu) {
		// TODO Auto-generated method stub
		String hql = " update BsPostEntity t  set "
				+ "t.postEffectDate = ?0 , t.postPerNu='" + postPerNu + "' "
				+ "where  t.recno='" + recno + "' ";
		return this.bsPostDao.batchExecute(hql, new Date());
	}

	/**
	 * 崗位異動申請完成後，更新崗位信息
	 * @param itemAplyEntity 崗位異動詳情
	 * @return 變更條數
	 */
	public int updatePostByGuardAplyItem(EGuardItemAplyEntity itemAplyEntity) {
		String hql = "update BsPostEntity t set " +
				"t.area = ?0, " +
				"t.block = ?1, " +
				"t.floor = ?2, " +
				"t.position = ?3, " +
				"t.postType = ?4, " +
				"t.postShift = ?5, " +
				"t.postName = ?6, " +
				"t.postPerNu = ?7, " +
				"t.postStartDate = ?8, " +
				"t.postEndDate = ?9, " +
				"t.postEffectDate = ?10, " +
				"t.updateDate = ?11 " +
				"where t.recno = ?12";
		return bsPostDao.batchExecute(hql, itemAplyEntity.getArea(), itemAplyEntity.getBlock(), itemAplyEntity.getFloor(),
				itemAplyEntity.getPosition(), itemAplyEntity.getPostType(), itemAplyEntity.getPostShift(),
				itemAplyEntity.getPostName(), itemAplyEntity.getPostPerNu(), itemAplyEntity.getPostStartDate(),
				itemAplyEntity.getPostEndDate(), new Date(), new Date(), itemAplyEntity.getRecno());
	}
}
