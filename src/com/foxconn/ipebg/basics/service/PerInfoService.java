package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.entity.BsTraineeEntity;
import com.foxconn.ipebg.basics.entity.PerInfoEntity;
import com.foxconn.ipebg.basics.entity.BsSecPerEntity;
import com.foxconn.ipebg.basics.entity.PerInfoEntity;
import com.foxconn.ipebg.basics.dao.PerInfoDao;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.Dict;
import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WorkFlowEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.system.service.DictService;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 合格人員
 *
 * <AUTHOR>
 * @email <EMAIL> 
 * @date 2019-04-16 10:12:01
 */
@Service
@Transactional(readOnly=true)
public class  PerInfoService extends BaseBusinessService<PerInfoEntity, String>{
    @Autowired
    private PerInfoDao perInfoDao;

    @Autowired
    private UserServiceUtil serviceUtil;
    @Autowired
    private DictService dictService;

    
    public static final String PERINFOERRORDTOS = "perinfoerrordtos";
    
    @Override
    public HibernateDao<PerInfoEntity, String> getEntityDao() {
        return perInfoDao;
    }
   
    
   
    public PerInfoEntity findByEmpNo(String empNo) {
        return this.perInfoDao.findUniqueBy("empNo", empNo);
    }

    /**
     * 符合臨時勤務安排的人員條件：
     * 1、合格人員
     * 2、非警務排班時間（白班、夜班、調休）
     * 3、非臨時勤務排班時間
     * @param params
     * @return
     */
    public List<PerInfoEntity> listTempService(Map<String,Object> params) {

        String hsql="select *" +
                "  from per_info A" +
                " where not exists" +
                " (" +
                "        select B.Emp_No" +
                "          from bs_arrange B" +
                "         where B.Emp_No = A.Emp_No" +
                "           and B.Shift_No in ('A', 'B', 'C')" +
                "           and to_date(to_char(B.shift_date, 'yyyy-MM-dd') || B.start_time," +
                "                       'yyyy-MM-ddhh24:mi') <= to_date('"+params.get("startTime").toString()+"','yyyy-MM-dd hh24:mi:ss')" +
                "           and to_date(to_char(B.shift_date, 'yyyy-MM-dd') || B.End_Time," +
                "                       'yyyy-MM-ddhh24:mi') >= to_date('"+params.get("startTime").toString()+"','yyyy-MM-dd hh24:mi:ss')" +
                "        union all" +
                "        select C.Emp_No" +
                "          from bs_arrange C" +
                "         where C.Emp_No = A.Emp_No" +
                "           and C.Shift_No in ('A', 'B', 'C')" +
                "           and to_date(to_char(C.shift_date, 'yyyy-MM-dd') || C.start_time," +
                "                       'yyyy-MM-ddhh24:mi') <= to_date('"+params.get("endTime").toString()+"','yyyy-MM-dd hh24:mi:ss')" +
                "           and to_date(to_char(C.shift_date, 'yyyy-MM-dd') || C.End_Time," +
                "                       'yyyy-MM-ddhh24:mi') >= to_date('"+params.get("endTime").toString()+"','yyyy-MM-dd hh24:mi:ss')" +
                "        union all" +
                "        select D.Emp_No" +
                "          from bs_arrange D" +
                "         where D.Emp_No = A.Emp_No" +
                "           and D.Shift_No in ('A', 'B', 'C')" +
                "           and to_date(to_char(D.shift_date, 'yyyy-MM-dd') || D.start_time," +
                "                       'yyyy-MM-ddhh24:mi') > to_date('"+params.get("startTime").toString()+"','yyyy-MM-dd hh24:mi:ss')" +
                "           and to_date(to_char(D.shift_date, 'yyyy-MM-dd') || D.End_Time," +
                "                       'yyyy-MM-ddhh24:mi') < to_date('"+params.get("endTime").toString()+"','yyyy-MM-dd hh24:mi:ss')" +
                "        union all" +
                "        select EB.Emp_No" +
                "          from bs_temp_service E,bs_temp_service_preinfo EA,per_info EB" +
                "         where EB.EMP_NO = A.Emp_No" +
                "           and EB.Id=EA.PI_ID" +
                "           and EA.BTS_ID = E.ID" +
                "           and E.START_TIME <= to_date('"+params.get("endTime").toString()+"','yyyy-MM-dd hh24:mi:ss')" +
                "           and E.End_Time >= to_date('"+params.get("endTime").toString()+"','yyyy-MM-dd hh24:mi:ss')" +
                "        " +
                "        union all" +
                "        select FB.Emp_No" +
                "          from bs_temp_service F,bs_temp_service_preinfo FA,per_info FB" +
                "         where FB.EMP_NO = A.Emp_No" +
                "           and FB.Id=FA.PI_ID" +
                "           and FA.BTS_ID = F.ID" +
                "           and F.START_TIME <= to_date('"+params.get("startTime").toString()+"','yyyy-MM-dd hh24:mi:ss')" +
                "           and F.End_Time >= to_date('"+params.get("startTime").toString()+"','yyyy-MM-dd hh24:mi:ss')" +
                "        union all" +
                "        select GB.Emp_No" +
                "          from bs_temp_service G,bs_temp_service_preinfo GA,per_info GB" +
                "         where GB.EMP_NO = A.Emp_No" +
                "           and GB.Id = GA.PI_ID" +
                "           and GA.BTS_ID = G.ID" +
                "           and G.START_TIME > to_date('"+params.get("startTime").toString()+"','yyyy-MM-dd hh24:mi:ss')" +
                "           and G.End_Time < to_date('"+params.get("endTime").toString()+"','yyyy-MM-dd hh24:mi:ss')" +
                "        )";


        return perInfoDao.createSQLQuery(hsql).addEntity(PerInfoEntity.class).list();
    }


// add hyc 2019/09/17
	public List<PerInfoEntity> findPersonList(PerInfoEntity perInfo, String coms) {

		StringBuffer sb = new StringBuffer();
		sb.append(" select * from V_PER_INFO t ").append(" WHERE 1=1  ");
        if (!perInfo.getEmpNo().equals(""))//||!serialnoQry.equals(null)
        {
        	sb.append("  and t.emp_no like '%"+perInfo.getEmpNo()+"%' ");	
        }
        if (!perInfo.getEmpName().equals(""))//||!serialnoQry.equals(null)
        {
        	sb.append("  and t.emp_name like '%"+perInfo.getEmpName()+"%' ");	
        }
        if (StringUtils.isNotEmpty(coms)) {
            sb.append(" and t.company in ('"+coms+"') ");
        }
		sb.append("ORDER BY emp_no ");
		@SuppressWarnings("unchecked")
		List<PerInfoEntity> personList = this.perInfoDao
				.createSQLQuery(sb.toString()).addEntity(PerInfoEntity.class)
				.list();
		return personList;

	}




    
    //0419新增
    
   /* @Override
	public HibernateDao<PerInfoEntity, String> getEntityDao() {
		return perInfoDao;
	}*/
}
