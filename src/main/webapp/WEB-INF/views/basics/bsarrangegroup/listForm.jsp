<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>排班群組基本資料</title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
</script>

<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
	<form id="mainform" action="${ctx}/bsarrangegroup/${action}"
		method="post">
		<table class="formTable">
			<tr>
				<%-- <td>id：</td>
				<td><input type="hidden" name="id" value="${bsArrangeGroup.id}" />
					<input id="id" name="id" class="easyui-validatebox"
					data-options="width: 150" value="${bsArrangeGroup.id }" /></td> --%>
					<td><input id="ids" name="ids" type="hidden"
					value="${bsArrangeGroup.id }" /></td>
			</tr>
			<c:if test="${bsArrangeGroup.id !=null }">
			<tr>
				<td>排班群組編碼：</td>
				<td><input id="arrangeGroupId" name="arrangeGroupId"
					class="easyui-validatebox" data-options="width: 150"
					value="${bsArrangeGroup.arrangeGroupId }" readonly /></td>
			</tr>
			</c:if>
			<tr>
				<td>排班群組名稱：</td>
				<td><input id="arrangeGroupName" name="arrangeGroupName"
					class="easyui-validatebox" data-options="width: 150"
					value="${bsArrangeGroup.arrangeGroupName }" /></td>
			</tr>
			<tr>
				<td>保安公司：</td>
				<td>
					<input id="securityCom" name="securityCom" class="easyui-combobox"
						   data-options="panelHeight:'auto',valueField:'value', textField:'label',value:'${bsArrangeGroup.securityCom}',
	   						url: '${ctx}/system/user/userSecurityCom',width: 150"
						   style="width:150px;" />
				</td>
			</tr>
			<tr>
				<td>創建人：</td>
				<td><input id="createBy" name="createBy" readonly
					class="easyui-validatebox" data-options="width: 150"
					value="${bsArrangeGroup.createBy }" /></td>
			</tr>
			<tr>
				<td>創建時間：</td>
				<td><input id="createDate" name="createDate"
					class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
					data-options="width: 160" disabled="disabled"
					value="<fmt:formatDate value="${bsArrangeGroup.createDate}" pattern="yyyy-MM-dd HH:mm:ss"/>" />
				</td>
			</tr>
			<tr>
				<td>更新人：</td>
				<td><input id="updateBy" name="updateBy" readonly
					class="easyui-validatebox" data-options="width: 150"
					value="${bsArrangeGroup.updateBy }" /></td>
			</tr>
			<tr>
				<td>更新時間：</td>
				<td><input id="updateDate" name="updateDate"
					class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
					data-options="width: 160" disabled="disabled"
					value="<fmt:formatDate value="${bsArrangeGroup.updateDate}" pattern="yyyy-MM-dd HH:mm:ss"/>" />
				</td>
			</tr>
			<%-- <tr>
				<td>刪除標示：</td>
				<td><input id="delFlag" name="delFlag"
					class="easyui-validatebox" data-options="width: 150"
					value="${bsArrangeGroup.delFlag }" /></td>
			</tr> --%>
		</table>
	</form>
	
	<script
		src='${ctx}/static/js/basics/bsarrangegroup.js?"+Math.random()"'></script>
	<script type="text/javascript">
		//提交表单
		$('#mainform').form({
			onSubmit : function() {
				var isValid = $(this).form('validate');
				return isValid; // 返回false终止表单提交
			},
			success : function(data) {
				successTip(data, dg, d);
			}
		});
	</script>
</body>
</html>