var dg;
var d;
$(function() {
	dg = $('#dg').datagrid({
		method : "get",
		url : ctx + '/etestpointaply/list',
		fit : true,
		fitColumns : true,
		border : false,
		idField : 'updateBy',
		striped : true,
		pagination : true,
		rownumbers : true,
		loadFilter : function(data) {
			// 过滤数据
			var value = {
				total : data.total,
				rows : []
			};
			var x = 0;
			for ( var i = 0; i < data.rows.length; i++) {
				// 
				
				if (data.rows[i].applyStat == "N") 
				{
					data.rows[i].applyStat="駁回"	;
				}
				else if (data.rows[i].applyStat == "E")
				{
						data.rows[i].applyStat="簽核完成"	;
				}
				else
				{
						data.rows[i].applyStat="簽核中"	;
				}
				// :1.飲用純淨水檢驗2.直飲水檢驗3.二次供水水質檢驗4.自建飲用水系統水質檢測
				if (data.rows[i].waterTestType == "1") 
				{
					data.rows[i].waterTestType="飲用純淨水檢驗"	;
				}
				else if (data.rows[i].waterTestType == "2")
				{
					data.rows[i].waterTestType="直飲水檢驗"	;
				}
				else if (data.rows[i].waterTestType == "3")
				{
					data.rows[i].waterTestType="二次供水水質檢驗"	;
				}
				else if (data.rows[i].waterTestType == "4")
				{
					data.rows[i].waterTestType="自建飲用水系統水質檢測"	;	
				}
				value.rows[x++] = data.rows[i];
			}
			return value;

		},
		pageNumber : 1,
		pageSize : 20,
		pageList : [ 10, 20, 30, 40, 50 ],
		singleSelect : true,
		columns : [ [ {
			field : 'id',
			title : '主鍵',
			hidden : true,
			width : 100
		},{
			field : 'attachids',
			title : '預算附檔',
			hidden : true,
			width : 100
		}, {
			field : 'serialno',
			title : '任務編號',
			sortable : true,
			width : 100,
			formatter: operation
		}, {
			field : 'makerno',
			title : '填單人工號',
			sortable : true,
			width : 100
		}, {
			field : 'makername',
			title : '填單人名稱',
			sortable : true,
			width : 100
		}, {
			field : 'makerip',
			title : '填單人IP',
			sortable : true,
			width : 100
		}, {
			field : 'createtime',
			title : '填單時間',
			sortable : true,
			width : 100
		}, {
			field : 'dealno',
			title : '承辦人工號',
			sortable : true,
			width : 100
		}, {
			field : 'dealname',
			title : '承辦人',
			sortable : true,
			width : 100
		}, {
			field : 'waterTestType',
			title : '申請點位類別',
			sortable : true,
			width : 100
		}, {
			field : 'applyStat',
			title : '申請單待審核狀態',
			sortable : true,
			width : 100
		}, {
			field : 'signEmpNo',
			title : '待審核人工號',
			sortable : true,
			width : 100
		}, {
			field : 'signEmpNam',
			title : '待審核人姓名',
			sortable : true,
			width : 100
		} ] ],
		enableHeaderClickMenu : true,
		enableHeaderContextMenu : true,
		enableRowContextMenu : false,
		rowTooltip : true,
		toolbar : '#tb'
	});
	// 初始化去除驗證
	$(":input").removeClass("validatebox-invalid");

	// 添加一筆
	$("#add").click(function(){
		 var len = parseInt($("#buildprojectApplyItemTableIndex").val());
	        var b = $("#buildprojectApplyItemTable tr").length;
	$("#buildprojectApplyItemTable").append("<tr align='center' id=Item"+len+">"
	    +"<td>"+b+"</td>"
	    +"<td><input id='itemContent"+len+"' name='eTestItemEntity["+(len-1)+"].itemContent' class='easyui-validatebox' required='true' style='width:150px;;'></td>"
	    +"<td><input id='testTyp"+len+"' name='eTestItemEntity["+(len-1)+"].testTyp'   class=\"easyui-combobox rayOptionitemclass\" data-options=\"panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadTestType("+len+");}\" style='width:150px;'/></td>"
	   
	    +"<td><input id='testItems"+len+"' name='eTestItemEntity["+(len-1)+"].testItems' class=\"easyui-combobox rayOptionitemclass\"  data-tr-index=\""+len+"\" data-options=\"panelHeight:'auto',valueField:'testItems', textField:'testItems',editable:false,onBeforeLoad:function(){loadTestItems("+len+");},onSelect:function(){queryTestCost(this.dataset.trIndex);} \"  style='width:150px;'/></td>"
	   // +"<td><input id='testItems"+len+"'
		// name='eTestItemEntity["+(len-1)+"].testItems' class=\"easyui-combobox
		// rayOptionitemclass\"
		// data-options=\"panelHeight:'auto',valueField:'testItems',
		// textField:'testItems',editable:false,onBeforeLoad:function(){loadTestItems("+len+");},onSelect:function(){queryTestCost("+len+");}
		// \" style='width:150px;'/></td>"
	   

	    
	    +"<td><input id='unitPrice"+len+"' name='eTestItemEntity["+(len-1)+"].unitPrice' class='easyui-validatebox' readonly required='true' style='width:150px;;' /></td>"
	    +"<td><input id='testCount"+len+"' name='eTestItemEntity["+(len-1)+"].testCount'  class='easyui-validatebox' readonly required='true'  style='width:150px;' onclick='selectTestPoint("+len+");' /></td>"
	    +"<td><input id='testCost"+len+"' name='eTestItemEntity["+(len-1)+"].testCost'  class='easyui-validatebox' readonly required='true' style='width:150px;' />"
	    +"<input type='hidden' id='reattachids"+len+"' name='eTestItemEntity["+(len-1)+"].reattachids' class='easyui-validatebox' required='true' style='width:150px;' /></td>"
	    // type='hidden'
	  
	    +"<td><input type='image' src='"+ctx+"/static/images/deleteRow.png' onclick='deltr("+len+");return false;'/></td>"+"</tr>");
	
	 $.parser.parse($("#Item" + len));
     $("#buildprojectApplyItemTableIndex").val(len + 1);
	$("#itemContent"+len).val('');
	$("#testTyp"+len).val('');
	$("#testItems"+len).val('');

	$("#unitPrice"+len).val('');
	$("#testCount"+len).val('');
	$("#testCost"+len).val('');
	$("#reattachids"+len).val('');


	});
	
});
// 初始化結束**********

// 刪除一筆
function deltr(index) {
// debugger;
// if(confirm("確定刪除此筆記錄？")) {
// if ($("#buildprojectApplyItemTable tr").length == 2) {
// alert("至少保留一筆！")
// return;
// }
// }
	$("#Item" + index).remove();
	var len=parseInt($("#buildprojectApplyItemTableIndex").val());
	$("#buildprojectApplyItemTableIndex").val(len-1);
	var b = $("#buildprojectApplyItemTable tr").length;
	for (var i = 1 ; i <= b; i++) {

	$("#buildprojectApplyItemTable").find("tr").eq(i).attr("id","Item"+(i));
    $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(0).html(i);
    
    $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(1).find("input[id^='itemContent']").attr("id","itemContent"+i);
    $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(2).find("input[id^='testTyp']").attr("id","testTyp"+i);
    $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(3).find("input[id^='testItems']").attr("id","testItems"+i);
    $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(4).find("input[id^='unitPrice']").attr("id","unitPrice"+i);
    $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(5).find("input[id^='testCount']").attr("id","testCount"+i);
    $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(6).find("input[id^='testCost']").attr("id","testCost"+i);
    $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(6).find("input[id^='reattachids']").attr("id","reattachids"+i);

    // ***************
    $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(1).find("input[name^='eTestItemEntity']").attr("name","eTestItemEntity["+(i-1) +"].itemContent");
    $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(2).find("input[name^='eTestItemEntity']").attr("name","eTestItemEntity["+(i-1) +"].testTyp");
    $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(3).find("input[name^='eTestItemEntity']").attr("name","eTestItemEntity["+(i-1)+"].testItems");
    $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(4).find("input[name^='eTestItemEntity']").attr("name","eTestItemEntity["+(i-1) +"].unitPrice");
    $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(5).find("input[name^='eTestItemEntity']").attr("name","eTestItemEntity["+(i-1) +"].testCount");
    $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(6).find("input[name^='testCost']").attr("name","eTestItemEntity["+(i-1) +"].testCost");
    $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(6).find("input[name^='reattachids']").attr("name","eTestItemEntity["+(i-1) +"].reattachids");
    
    $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(2).find("input[id^='testTyp']").attr("comboname","eTestItemEntity["+(i-1) +"].testTyp");
    $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(3).find("input[id^='testItems']").attr("comboname","eTestItemEntity["+(i-1) +"].testItems");

    $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(2).find("input[id^='testTyp']").attr("data-options","panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadTestType("+i+");}");
    $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(3).find("input[id^='testItems']").attr("data-tr-index",i);
    // $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(3).find("input[id^='testItems']").attr("data-options","panelHeight:'auto',valueField:'testItems',
	// textField:'testItems',editable:false,onBeforeLoad:function(){loadTestItems("+i+");},onSelect:function(){queryTestCost("+i+");}
	// ");
    
    
    $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(5).find("input[id^='testCount']").attr("onclick","selectTestPoint("+i+");");
      
    $("#buildprojectApplyItemTable").find("tr").eq(i).find("td").eq(7).find("input[type='image']").attr("onclick","deltr("+i+");return false;");

}
	sumCount();
}

function operation(value, row, index) {
return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('詳情',ctx+'/etestpointaply/view/" + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
};
// 弹窗增加
function add() {
	d = $("#dlg").dialog({
		title : '添加飲用水檢測申請主表',
		width : 380,
		height : 380,
		href : ctx + '/etestpointaply/create',
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '确认',
			handler : function() {
				$("#mainform").submit();
			}
		}, {
			text : '取消',
			handler : function() {
				d.panel('close');
			}
		} ]
	});
}

// 删除
function del() {

	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data) {
		if (data) {
			$.ajax({
				type : 'get',
				url : ctx + "/etestpointaply/delete/" + row.updateBy,
				success : function(data) {
					successTip(data, dg);
				}
			});
		}
	});
}

// 弹窗上傳附檔
function uploadFileForm() {
	// console.log("ff333");
	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	if (row.applyStat!="簽核完成")
		{
		alert("未簽核完成的單子不可上傳附檔!");
		return;
		}
	if (row.attachids!=null)
	{
	alert("該單號已經上傳預算核准檔！");
	return;
	}
	d = $("#dlg").dialog({
		title : '飲用水檢測預算核准檔上傳',
		width : 380,
		height : 340,
		href : ctx + '/etestpointaply/uploadFileForm/' + row.id,
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '確定',
			handler : function() {
				$('#mainform').submit();
			}
		}, {
			text : '取消',
			handler : function() {
				d.panel('close');
			}
		} ]
	});
}
// 创建查询对象并查询
function cx() {
	var obj = $("#searchFrom").serializeObject();
	dg.datagrid('load', obj);
}
// 表單進度查詢頁面查詢條件重置
function listSearchReset() {
	$("#searchFrom").form("reset");
}
// 导出excel
function exportExcel() {
	var form = document.getElementById("searchFrom");
	searchFrom.action = ctx + '/etestpointaply/exportExcel';
	form.submit();
}
function saveInfo(flag) {
    if (flag == '2') {
        var isValid = $("#mainform").form('validate');
        if (!isValid) {
            return;
        }
// if(($("#zchargeno").val()==null||$("#zchargeno").val()=='')&&($("#zcchargeno").val()==null||$("#zcchargeno").val()=='')){
// alert("至少簽核至處級主管及以上主管");
// return;
// }
    }
    $.ajax({
        url: ctx+'/etestpointaply/create/' + flag,
        type: 'POST',
        beforeSend: ajaxLoading,
        // dataType: 'json',
        data: $('#mainform').serialize(),
        success: function (data) {
            ajaxLoadEnd();
            successTip(data, dg, d);
            window.parent.mainpage.mainTabs.closeCurrentTab();
        }
    });
}
function auditInfo(flag) {
    if (flag == 'Y') {
       
// if(($("#zchargeno").val()==null||$("#zchargeno").val()=='')&&($("#zcchargeno").val()==null||$("#zcchargeno").val()=='')){
// alert("至少簽核至處級主管及以上主管");
// return;
// }
    }
    else if(flag == 'N') {
    	 var isValid = $("#mainform").form('validate');
         if (!isValid) {
             return;
         }	
    }
    $.ajax({
        url: ctx+'/etestpointaply/auditSave/' + flag,
        type: 'POST',
        beforeSend: ajaxLoading,
        // dataType: 'json',
        data: $('#mainform').serialize(),
        success: function (data) {
            ajaxLoadEnd();
            successTip(data, dg, d);
            window.parent.mainpage.mainTabs.closeCurrentTab();
        }
    });
}
// validatebox驗證擴展
$.extend($.fn.validatebox.defaults.rules, {
    endTime: {
        validator: function (value, param) {
            var startDate = $('#buildcyclebegindate').datebox('getValue');
            var startTmp = new Date(startDate.replace(/-/g, "/"));
            var endTmp = new Date(value.replace(/-/g, "/"));
            return startTmp <= endTmp;
        },
        message: '结束时间要大于开始时间！'
    }
});

// 驗證申請人聯繫電話
function valdApplyTel(obj){
    var phoneL=$.trim($("#dealtel").val()).split("").length;
    var tel = obj.value;
    if(tel!=""){
        var temp = tel;
        var regex  = /^[0-9]{3}\+[0-9]{5}$/;
        var patt1=new RegExp(regex);
        var result = patt1.test(tel);
        if(!result){
            alert("分機格式有誤!");
            obj.value="";
            obj.focus();
            return;
        }
    }
}
// 驗證手機號碼
function checkMobile(obj) {
    var
        re = /^1\d{10}$/

    if (obj.value!="")
    {
        if (re.test(obj.value)) {
            // alert("正确");
        } else {
            alert("手機格式有誤！");
            obj.value="";
            obj.focus();
            return;
        }

    }
}

// 驗證郵箱
function valdEmail(obj){
    var email = obj.value;
    var temp = email;
    if(email=="")return;
    var regex = /\w+([-/+.]\w+)*@(mail.foxconn.com|foxconn.com|fii-foxconn.com)$/;
    var patt1=new RegExp(regex);
    var result = patt1.test(email);
    if(!result){
        alert("郵箱的格式不正確!");
        obj.value="";
        // obj.focus();
        obj.value =temp;
        return;
    };
}
// 驗證數字
function valdMoney(obj){
    var money = obj.value;
    var temp = money;
    if(money=="")return;
    var regex = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
    var patt1=new RegExp(regex);
    var result = patt1.test(money);
    if(!result){
        alert("格式不正確!");
        obj.value="";
        // obj.focus();
        obj.value =temp;
        return;
    };
}
// 根據工號查出當前申請人的信息
function queryUserInfo() {
    var empno = $.trim($("#dealno").val().toUpperCase());
    if (empno != null && empno != "") {
        $.ajax({
            url: ctx+'/system/user/getUserInfo/',
            type: 'POST',
            beforeSend: ajaxLoading,
            // dataType: 'json',
            data: {empno: empno},
            success: function (data) {
                ajaxLoadEnd();
                if (!data) {
                    $.messager.alert("溫馨提示", "工號不存在", "error");
                    $('#dealno').val('');
                    $('#dealname').val('');
                    $('#dptQun').val('');
                    // $('#dptChu').val('');
                    $('#dptBu').val('');
                    // $('#dptKe').val('');
                    $('#dealemail').val('');
                } else {
                    $('#dealname').val(data.name);
                    $('#dptQun').val(data.dptQun);
                    // $('#dptChu').val(data.dptChu);
                    $('#dptBu').val(data.dptBu);
                    // $('#dptKe').val(data.dptKe);
                    $('#dealemail').val(data.email);
                }
            }
        });
    }
}
// 普通文件上傳
function uploadFile() {
    var attachIds = $.trim($("#attachids").val());
    var attachIdsL = attachIds.split(",").length;
    if (attachIdsL >= 6) {
        $.messager.alert("溫馨提示", "上傳附件個數不能超過五個！", "info");
        return;
    }
    var explorer = window.navigator.userAgent;
    if (!!window.ActiveXObject || "ActiveXObject" in window) {
        ajaxLoading();
        $.ajaxFileUpload({  // Jquery插件上传文件
            url: ctx + "/admin/uploadCompatible",
            secureuri: false,// 是否启用安全提交 默认为false
            fileElementId: "attachidsUpload", // type="file"的id
            dataType: "JSON",  // 返回值类型
            success: function (msg, status) {
                msg = jQuery.parseJSON(msg);
                $("#dowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href=' + ctx + '/admin/download/' + msg.id + '>' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delAtt(\'' + msg.id + '\')"></div></div>');
                $("#attachids").val($("#attachids").val() + msg.id + ",");
                ajaxLoadEnd();
            },
            error: function (data, status, e) {
                ajaxLoadEnd();
            }
        });
    }else{
// 创建
        var form_data = new FormData();
        // 获取文件
// var file = document.getElementById('fileToUpload').files[0];
        var file_data = $("#attachidsUpload").prop("files")[0];
        form_data.append("file", file_data);
        form_data.append("path", "upload");
        // 把所以表单信息
        $.ajax({
            type: "POST",
            url: ctx+"/admin/upload",
            beforeSend: ajaxLoading,
           dataType : "json",
            processData: false,  // 注意：让jQuery不要处理数据
            contentType: false,  // 注意：让jQuery不要设置contentType
            data: form_data
        }).success(function (msg) {
            ajaxLoadEnd();
            $("#dowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href='+ctx+'/admin/download/' + msg.id + '>' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delAtt(\'' + msg.id + '\')"></div></div>');
            $("#attachids").val($("#attachids").val() + msg.id + ",");
            // $("#attachids").val(msg.id);
        }).fail(function (msg) {
            ajaxLoadEnd();
        });
    }
}



function delAtt(fileid) {
    $("#" + fileid).remove();
    var kk = $("#attachids").val();
    kk = kk.replace(fileid + ",", "");
    $("#attachids").val(kk);
    ajaxLoading();
    $.post(ctx + '/admin/delete/' + fileid, {}, function (data) {
        successTip(data);
        ajaxLoadEnd();
    }, 'text').error(function() { ajaxLoadEnd(); });
}

function reUploadFile() {
    var attachIds = $.trim($("#reattachids").val());
    var attachIdsL = attachIds.split(",").length;
    if (attachIdsL >= 6) {
        alert("上傳附件個數不能超過五個！");
        return;
    }
    // 创建
    var form_data = new FormData();
    // 获取文件
// var file = document.getElementById('fileToUpload').files[0];
    var file_data = $("#attachidsUpload").prop("files")[0];
    form_data.append("file", file_data);
    form_data.append("path", "test");
    // 把所以表单信息
    $.ajax({
        type: "POST",
        url: ctx+"/admin /upload",
        beforeSend: ajaxLoading,
// dataType : "json",
        processData: false,  // 注意：让jQuery不要处理数据
        contentType: false,  // 注意：让jQuery不要设置contentType
        data: form_data
    }).success(function (msg) {
        ajaxLoadEnd();
        $("#reDowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href='+ctx+'"/admin /download/' + msg.id + '">' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delReAtt(\'' + msg.id + '\')"></div></div>');
        $("#reattachids").val($("#reattachids").val() + msg.id + ",");
        // $("#attachids").val(msg.id);
    }).fail(function (msg) {
    });
}

function delReAtt(fileid) {
    $("#" + fileid).remove();
    var kk = $("#reattachids").val();
    kk = kk.replace(fileid + ",", "");
    $("#reattachids").val(kk);
    $.post(ctx+'/admin /delete/' + fileid, {}, function (data) {
        successTip(data);
    });
}


// 點擊添加一筆檢驗類型
function loadTestType(flag) {
    $.ajax({
        url: ctx+"/system/dict/getDictByType/test_type",
        type: "GET",
        success: function (data) {
            var themecombo2 = [{'label': '請選擇', 'value': ''}];
            for (var i = 0; i < data.length; i++) {
                themecombo2.push({"label": data[i].label, "value": data[i].value});
            }
            
                $("#testTyp" + flag).combobox("loadData", themecombo2);

        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });

}

// 選擇檢測項目

function selectTestPoint(flag) {
    if ($("#unitPrice"+flag).val() == null || $("#unitPrice"+flag).val() == '') {
        // alert("請輸入承辦人工號.");
        $.messager.alert("溫馨提示", "請先選擇檢驗項目", "info");
        return;
    }
    d = $("#win").dialog({
        title: '檢測點位選擇',
        width: 1000,
        height: 800,
        async: false,
        href: ctx + '/pointitem/testPointList',
        maximizable: false,
        modal: true,
        onOpen: function () {
   $('#dutyId').val("55");
   $('#factoryId').val("66");
        },
        buttons: [{
            text: '確定',
            handler: function () {              
// $("#" + colNo).val("");
// $("#" + colName).val("");
            	$("#testCount"+flag).val("");            	
            	$("#reattachids"+flag).val("");
            	$("#testCost"+flag).val("");
                var rows = $('#testPointList').datagrid('getSelections');
                var ids = "";
                for (var i = 0, j = rows.length; i < j; i++) {
                    // ids.push(rows[i].empno + ',' + rows[i].username);
                	ids=ids+rows[i].pointno+',';
                }
                ids=ids.substring(0,ids.length-1);
                $("#testCount"+flag).val(rows.length);            	
            	$("#reattachids"+flag).val(ids);
            	$("#testCost"+flag).val(rows.length * $("#unitPrice"+flag).val());
            	// 點位數量總計
            	sumCount();
            	
                d.panel('close');
            }
        }, {
            text: '置空檢測點位',
            handler: function () {
            	$("#testCount"+flag).val("");            	
            	$("#reattachids"+flag).val("");
            	$("#testCost"+flag).val("");
            	sumCount();
                d.panel('close');
            }
        }]
    });
}

//總計
function sumCount()
{
	 var len = parseInt($("#buildprojectApplyItemTableIndex").val());
	 var testCountSum=0,testCostSum=0;
	  for (var k = 1; k < len; k++) {
		 if ($("#testCount"+k).val() != null || $("#testCount"+k).val() != '')
		{
			  testCountSum=Number($("#testCount"+k).val())+Number(testCountSum);
			  testCostSum=Number($("#testCost"+k).val())+Number(testCostSum);
			  
	    }
		   
      }
	  $("#testCountSum").val(testCountSum);
	  $("#testCostSum").val(testCostSum);
	
}

// 加載項目明細
function loadTestPoint(itemNo) {
   
    d = $("#win").dialog({
        title: '檢測點位明細',
        width: 1000,
        height: 800,
        async: false,
        href: ctx + '/epointtestitem/testPointList',
        maximizable: false,
        modal: true,
        onOpen: function () {
    $('#itemNoDTO').val(itemNo);
 // $('#factoryId').val(itemNo);
        },
        buttons: [{
            text: '關閉',
            handler: function () {              
                d.panel('close');
            }
        }]
    });
}
// 根據檢驗類型和檢驗項目帶出價格
function queryTestCost(flag) {
	// console.log("123");
	var type = $.trim($('input[id="waterTestType"]:checked').val());// +";"+$.trim($("#testItems"+flag).val());
	var testItems =$('#testItems'+flag).combobox('getValue');// $.trim($("#testItems"+flag).val()
																// );
    if (type != null && type != "") {
        $.ajax({
            url: ctx+"/testitem/getTestCost/"+type,
            type: 'POST',
            beforeSend: ajaxLoading,
            // dataType: 'json',
            data: {testItems: testItems},
            success: function (data) {
                ajaxLoadEnd();
                if (!data) {
                    $.messager.alert("溫馨提示", "此檢驗項目不存在！", "error");
                    $("#testCost"+flag).val('');
                } else {
                    // $('#dalname').val(data.empname);
                  
                    $('#unitPrice'+flag).val(data[0].testCost);
                }
            }
            ,error: function (error) {
                alert(error);
            }
        });
    }
}
function loadTestItems(flag) {// "#waterTestType"
	 var type = $.trim($('input[id="waterTestType"]:checked').val());
	 
    $.ajax({
        url: ctx+"/testitem/getTestItemsByType/"+type,
        type: "GET",
        success: function (data) {
            var themecombo2 = [];
            for (var i = 0; i < data.length; i++) {
                themecombo2.push({"testItems": data[i].testItems, "testCost": data[i].testCost});
            }
            
                $("#testItems" + flag).combobox("loadData", themecombo2);
             
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });

}

// 刪除table處表頭之外的所有行
function deleteAllRow(tableName){

    var table = document.getElementById(tableName);
    var len = table.rows.length;
    for(var i = 0;i < len-1;i++){
        table.deleteRow(i+1);
    }
}
// 格式化日期
function crtTimeFtt(val) {
    if (val != null) {
        var date = new Date(val);
        return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
    }
}