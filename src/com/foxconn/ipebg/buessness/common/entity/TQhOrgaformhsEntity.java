package com.foxconn.ipebg.buessness.common.entity;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;


/**
 * 組織基本資料表MHS
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-17 08:06:42
 */
@Entity
@Table(name = "T_QH_ORGAFORMHS")
@DynamicUpdate
@DynamicInsert
public class TQhOrgaformhsEntity extends DataEntity<TQhOrgaformhsEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

                        //單位代碼
            private String deptno;
                                //單位名稱
            private String deptname;
                                //單位層級
            private String deptlevel;
                                //上一級單位代碼
            private String superdeptno;
                                //部門對應的廠區代碼
            private String factoryid;
                                //判斷該組織是否為虛擬組織,Y表示虛擬,2013/10/07;F1650984
            private String yesOrNo;
                                                                                                        //帶版本的流程
            private String workflowcode;
            
                        /**
             * 设置：單位代碼
             */
            public void setDeptno(String deptno) {
                this.deptno = deptno;
            }

            /**
             * 获取：單位代碼
             */
            
            @Column(name = "DEPTNO", nullable = false, length = 20)
            public String getDeptno() {
                return deptno;
            }
                
                        /**
             * 设置：單位名稱
             */
            public void setDeptname(String deptname) {
                this.deptname = deptname;
            }

            /**
             * 获取：單位名稱
             */
            
            @Column(name = "DEPTNAME", nullable = false, length = 20)
            public String getDeptname() {
                return deptname;
            }
                
                        /**
             * 设置：單位層級
             */
            public void setDeptlevel(String deptlevel) {
                this.deptlevel = deptlevel;
            }

            /**
             * 获取：單位層級
             */
            
            @Column(name = "DEPTLEVEL", nullable = false, length = 20)
            public String getDeptlevel() {
                return deptlevel;
            }
                
                        /**
             * 设置：上一級單位代碼
             */
            public void setSuperdeptno(String superdeptno) {
                this.superdeptno = superdeptno;
            }

            /**
             * 获取：上一級單位代碼
             */
            
            @Column(name = "SUPERDEPTNO", nullable = false, length = 20)
            public String getSuperdeptno() {
                return superdeptno;
            }
                
                        /**
             * 设置：部門對應的廠區代碼
             */
            public void setFactoryid(String factoryid) {
                this.factoryid = factoryid;
            }

            /**
             * 获取：部門對應的廠區代碼
             */
            
            @Column(name = "FACTORYID", nullable = false, length = 20)
            public String getFactoryid() {
                return factoryid;
            }
                
                        /**
             * 设置：判斷該組織是否為虛擬組織,Y表示虛擬,2013/10/07;F1650984
             */
            public void setYesOrNo(String yesOrNo) {
                this.yesOrNo = yesOrNo;
            }

            /**
             * 获取：判斷該組織是否為虛擬組織,Y表示虛擬,2013/10/07;F1650984
             */
            
            @Column(name = "YES_OR_NO", nullable = false, length = 20)
            public String getYesOrNo() {
                return yesOrNo;
            }
                
            
            
            
            
            
            
                        /**
             * 设置：帶版本的流程
             */
            public void setWorkflowcode(String workflowcode) {
                this.workflowcode = workflowcode;
            }

            /**
             * 获取：帶版本的流程
             */
            
            @Column(name = "WORKFLOWCODE", nullable = false, length = 20)
            public String getWorkflowcode() {
                return workflowcode;
            }
                
    }
