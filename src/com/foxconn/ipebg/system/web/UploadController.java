package com.foxconn.ipebg.system.web;

import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.DateUtil;
import com.foxconn.ipebg.common.utils.DateUtils;
import com.foxconn.ipebg.common.utils.SpringContextUtil;
import com.foxconn.ipebg.system.entity.TPubFileobjectEntity;
import com.foxconn.ipebg.system.service.DictService;
import com.foxconn.ipebg.system.service.TPubFileobjectService;
import com.foxconn.ipebg.system.utils.FTPUtil;
import com.foxconn.ipebg.system.utils.IPUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.UUID;

/**
 * 登录controller
 *
 * <AUTHOR>
 * @date 2015年1月14日
 */
@Controller
@RequestMapping(value = "{adminPath}")
public class UploadController {
    private Logger logger = LoggerFactory.getLogger(UploadController.class);

    @Autowired
    private TPubFileobjectService fileobjectService;
    @Autowired
    private DictService dictService;

    /**
     * 默认页面
     *
     * @return
     */
    @RequestMapping(value = "uploadIndex", method = RequestMethod.GET)
    public String upload() {
        return "system/upload/index";
    }

    @RequestMapping(value = "uploadAjax", method = RequestMethod.GET)
    public String uploadAjax() {
        return "system/upload/upload";
    }

    @RequestMapping(value = "upload", method = RequestMethod.POST)
    @ResponseBody
    public TPubFileobjectEntity upload(MultipartFile file, String path, HttpServletRequest request) {

        TPubFileobjectEntity entity = new TPubFileobjectEntity();
        String URL= DateUtils.getDate("yyyy/MM/dd");

        String fileName = file.getOriginalFilename();
        //扩展名
        String fileExtensionName = fileName.substring(fileName.lastIndexOf('.') + 1);
        //使用UUID防止文件名重复，覆盖别人的文件
        String uploadFileName = UUID.randomUUID().toString() + "." + fileExtensionName;
        //logger.info("开始上传文件，上传的文件名：{}，上传的路径：{}，新文件名：{} ", fileName, path, uploadFileName);
        logger.info("开始上传文件，上传的文件名：{}，上传的路径：{}，新文件名：{} ", fileName, path);
        //新建文件
        File fileDir = new File(path);
        //判断文件是否存在，不存在就创建一个新的
        if (!fileDir.exists()) {
            //使文件可以改，因为Tomcat发布服务后，文件的权限不一定是可以改的
            fileDir.setWritable(true);
            //使用dirs是为了解决上传的路径中，如果有文件夹的没有创建，其会自动创建文件夹
            fileDir.mkdirs();
        }
        File targetFile = new File(path, uploadFileName);
        try {
            file.transferTo(targetFile);
            //到此为止，文件已经上传服务器成功

            //下一步是把文件上传到FTP服务器,与FTP文件服务器对接
            String ftpIp = dictService.get(2017102503).getValue();
            String ftpUser = dictService.get(2017102501).getValue();
            String ftpPass = dictService.get(2017102502).getValue();
            String rootPath = dictService.get(101002).getValue();
            new FTPUtil(ftpIp,21,ftpUser,ftpPass,rootPath).uploadFile(URL,Lists.newArrayList(targetFile));
            //已将文件上传FTP

            //上传完之后，删除upload下面的文件
            int fileSize = new Long(targetFile.length()).intValue();
            targetFile.delete();
            entity.setName(fileName);
            entity.setUploader(UserUtil.getCurrentUser().getLoginName());
            entity.setUrl("/"+URL+"/"+uploadFileName);
            entity.setIp(IPUtil.getIpAddress(request));
            entity.setType(fileExtensionName);
            entity.setSizez(fileSize);
            entity.setCreatedate(DateUtil.getNowTime("yyyy-MM-dd HH:mm:ss"));
            fileobjectService.save(entity);
        } catch (IOException e) {
            logger.error("上传文件异常", e);
            return null;
        }
        return entity;
    }
    @RequestMapping(value = "uploadCompatible", method = RequestMethod.POST)
    @ResponseBody
    public TPubFileobjectEntity uploadCompatible(MultipartFile attachidsUpload,HttpServletRequest request) {

        TPubFileobjectEntity entity = new TPubFileobjectEntity();
        String URL= DateUtils.getDate("yyyy/MM/dd");

        String fileName = attachidsUpload.getOriginalFilename();
        //扩展名
        String fileExtensionName = fileName.substring(fileName.lastIndexOf('.') + 1);
        //使用UUID防止文件名重复，覆盖别人的文件
        String uploadFileName = UUID.randomUUID().toString() + "." + fileExtensionName;
        logger.info("开始上传文件，上传的文件名：{}，上传的路径：{}，新文件名：{} ", fileName, uploadFileName);
        //新建文件
        String path = "updload1";
        File fileDir = new File(path);
        //判断文件是否存在，不存在就创建一个新的
        if (!fileDir.exists()) {
            //使文件可以改，因为Tomcat发布服务后，文件的权限不一定是可以改的
            fileDir.setWritable(true);
            //使用dirs是为了解决上传的路径中，如果有文件夹的没有创建，其会自动创建文件夹
            fileDir.mkdirs();
        }
        File targetFile = new File(path, uploadFileName);
        try {
            attachidsUpload.transferTo(targetFile);
            //到此为止，文件已经上传服务器成功

            //下一步是把文件上传到FTP服务器,与FTP文件服务器对接
            String ftpIp = dictService.get(2017102503).getValue();
            String ftpUser = dictService.get(2017102501).getValue();
            String ftpPass = dictService.get(2017102502).getValue();
            String rootPath = dictService.get(101002).getValue();
            new FTPUtil(ftpIp,21,ftpUser,ftpPass,rootPath).uploadFile(URL,Lists.newArrayList(targetFile));
            //已将文件上传FTP

            //上传完之后，删除upload下面的文件
            int fileSize = new Long(targetFile.length()).intValue();
            targetFile.delete();
            entity.setName(fileName);
            entity.setUploader(UserUtil.getCurrentUser().getLoginName());
            entity.setUrl("/"+URL+"/"+uploadFileName);
            entity.setIp(IPUtil.getIpAddress(request));
            entity.setType(fileExtensionName);
            entity.setSizez(fileSize);
            entity.setCreatedate(DateUtil.getNowTime("yyyy-MM-dd HH:mm:ss"));
            fileobjectService.save(entity);
        } catch (IOException e) {
            logger.error("上传文件异常", e);
            return null;
        }
        return entity;
    }
//    @RequestMapping("/download/{id}")
//    @ResponseBody
//    public ResponseEntity<byte[]> export(@PathVariable(value = "id") String id) throws IOException {
//        HttpHeaders headers = new HttpHeaders();
//        TPubFileobjectEntity entity = fileobjectService.findById(id);
//        String filePath = entity.getUrl();
//        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
//        headers.setContentDispositionFormData("attachment", URLEncoder.encode(entity.getName(), "UTF-8"));
//        String ftpIp = dictService.get(2017102503).getValue();
//        String ftpUser = dictService.get(2017102501).getValue();
//        String ftpPass = dictService.get(2017102502).getValue();
//        String rootPath = dictService.get(101002).getValue();
//        FTPUtil ftpUtil = new FTPUtil(ftpIp,21,ftpUser,ftpPass,rootPath);
//        return new ResponseEntity<byte[]>(ftpUtil.download(filePath), headers, HttpStatus.CREATED);
//    }
    @RequestMapping("/download/{id}")
    //@ResponseBody
    public void download(@PathVariable(value = "id") String id,HttpServletRequest request,
                         HttpServletResponse response) throws IOException {
        TPubFileobjectEntity entity = fileobjectService.findById(id);
        response.setHeader("Content-Disposition", "attachment; filename=\"" + java.net.URLEncoder.encode(entity.getName(), "UTF-8") + "\"");
        String filePath = entity.getUrl();
        String ftpIp = dictService.get(2017102503).getValue();
        String ftpUser = dictService.get(2017102501).getValue();
        String ftpPass = dictService.get(2017102502).getValue();
        String rootPath = dictService.get(101002).getValue();
        int port = Integer.parseInt(dictService.get(2017102504).getValue());
        FTPUtil ftpUtil = new FTPUtil(ftpIp,port,ftpUser,ftpPass,rootPath);
        ftpUtil.downloads(response,filePath);
    }

    @RequestMapping("/delete/{ids}")
    @ResponseBody
    public String delete(@PathVariable(value = "ids") String ids) throws IOException {
        fileobjectService.delete(ids);
        return Constant.SUCCESS;
    }
}
