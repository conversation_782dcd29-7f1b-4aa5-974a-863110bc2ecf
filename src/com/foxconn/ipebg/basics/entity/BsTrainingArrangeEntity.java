package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 培訓期日排班表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-09-09 14:58:54
 */
@Entity
@Table(name = "BS_TRAINING_ARRANGE")
@DynamicUpdate
@DynamicInsert
public class BsTrainingArrangeEntity extends DataEntity<BsTrainingArrangeEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    //培訓期編碼
    private String trainingPeriodNo;
    //培訓期名稱
    private String trainingPeriodName;
    //排班日期
    private Date arrangeDate;
    //是否稽核：Y-是，N-否
    private String isCheck;
    //排班人數
    private Integer arrangeNum;


    /**
     * 设置：培訓期編碼
     */
    public void setTrainingPeriodNo(String trainingPeriodNo) {
        this.trainingPeriodNo = trainingPeriodNo;
    }

    /**
     * 获取：培訓期編碼
     */

    @Column(name = "TRAINING_PERIOD_NO", nullable = false, length = 20)
    public String getTrainingPeriodNo() {
        return trainingPeriodNo;
    }

    /**
     * 设置：培訓期名稱
     */
    public void setTrainingPeriodName(String trainingPeriodName) {
        this.trainingPeriodName = trainingPeriodName;
    }

    /**
     * 获取：培訓期名稱
     */

    @Column(name = "TRAINING_PERIOD_NAME", nullable = false, length = 20)
    public String getTrainingPeriodName() {
        return trainingPeriodName;
    }

    /**
     * 设置：排班日期
     */
    public void setArrangeDate(Date arrangeDate) {
        this.arrangeDate = arrangeDate;
    }

    /**
     * 获取：排班日期
     */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "ARRANGE_DATE", nullable = false, length = 20)
    public Date getArrangeDate() {
        return arrangeDate;
    }

    /**
     * 设置：是否稽核：Y-是，N-否
     */
    public void setIsCheck(String isCheck) {
        this.isCheck = isCheck;
    }

    /**
     * 获取：是否稽核：Y-是，N-否
     */

    @Column(name = "IS_CHECK", nullable = false, length = 20)
    public String getIsCheck() {
        return isCheck;
    }


    /**
     * 设置：排班人數
     */
    public void setArrangeNum(Integer arrangeNum) {
        this.arrangeNum = arrangeNum;
    }

    /**
     * 获取：排班人數
     */

    @Column(name = "ARRANGE_NUM", nullable = false, length = 20)
    public Integer getArrangeNum() {
        return arrangeNum;
    }

}
