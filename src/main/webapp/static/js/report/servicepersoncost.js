var dg;
var d;
$(function() {
	$("#filter_dptQun").combobox({
		data: distinctArrayByProperty(_bsDptList, 'dptQun'),
		valueField: "dptQun",
		textField: "dptQun",
		editable: false,
		panelHeight: 'auto'
	});

	dg = $('#dg').datagrid({
		method : "get",
		url : ctx + '/servicepersoncost/list',
		// url:'/waterQualityTesting/bsdpt/list',
		fit : true,
		fitColumns : true,
		border : false,
	    idField : 'id',
		striped : true,
		pagination : true,
		rownumbers : true,
		pageNumber : 1,
		pageSize : 20,
		pageList : [ 10, 20, 30, 40, 50 ],
		singleSelect : true,
		columns : [ [
			{field : 'id',title : '資料識別ID  Primary',hidden : true},
			{field : 'empNo',title : '工號',sortable : false,width : 100, rowspan: 2},
			{field : 'empName',title : '姓名',sortable : false,width : 100, rowspan: 2},
			{field : 'postName',title : '崗位名稱',sortable : false,width : 100, rowspan: 2},
			{field : 'shiftName',title : '班別',sortable : false,width : 100, rowspan: 2},
			{field : 'dptQun',title : '事業群',sortable : false,width : 100, rowspan: 2},
			{field : 'legal',title : '法人',sortable : false,width : 100, rowspan: 2},
			{field : 'dptBu',title : '部門',sortable : false,width : 100, rowspan: 2},
			{field : 'costId',title : '費用代碼',sortable : false,width : 100, rowspan: 2},
			{field : 'entryDate',title : '入職日期',sortable : false,width : 100, rowspan: 2},
			{field : 'passDate', title: '合格日期', sortable: false, width: 100, rowspan: 2},
			{title : '支付時期',colspan:2},
			{field : 'dayCount',title : '工作天數',sortable : false,width : 100, rowspan: 2},
			{field : 'normalCost',title : '應發薪資',sortable : false,width : 100, rowspan: 2},
			{field : 'traineeCost',title : '培訓費用',sortable : false,width : 100, rowspan: 2},
			{field : 'overtimeCost',title : '加班費',sortable : false,width : 100, rowspan: 2},
			{field : 'totalCost',title : '實發薪資',sortable : false,width : 100, rowspan: 2}
		],[
			{field : 'periodBeginDate',title : '起',sortable : false,width : 100},
			{field : 'periodEndDate',title : '止',sortable : false,width : 100}
		] ],
		enableHeaderClickMenu : true,
		enableHeaderContextMenu : true,
		enableRowContextMenu : false,
		rowTooltip : true,
		toolbar : '#tb'
	});
	$('#dg').datagrid;
});

// 创建查询对象并查询
function cx() {
	var obj = $("#searchFrom").serializeObject();
	dg.datagrid('load', obj);
}

// 表單進度查詢頁面查詢條件重置
function listSearchReset() {
	$("#searchFrom").form("reset");
}

// 导出excel
function exportExcel() {
	var form = document.getElementById("searchFrom");
	searchFrom.action = ctx + '/servicepersoncost/exportExcel';
	form.submit();
}

// 去重
function distinctArrayByProperty(array, property) {
	var propArray = new Array();
	var resultArray = new Array();
	$.each(array, function (idx, item) {
		if (propArray.indexOf(item[property]) == -1) {
			propArray.push(item[property]);
			resultArray.push(item);
		}
	});
	return resultArray;
}

// 查看匯總
function showSummary() {
	d = $("#dlg").dialog({
		title: '匯總信息查看',
		width: 280,
		height: 350,
		href: ctx + '/servicepersoncost/summary',
		maximizable: true,
		modal: true,
		buttons: [{
			text: '關閉',
			handler: function () {
				d.panel('close');
			}
		}]
	});
}
