<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>排班基本資料表</title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
	type="text/javascript"></script>

	<script type="text/javascript">
        //信息
        var _info = {
            // 項目代碼
            projectCode : "ANQUANGUANLIPINGTAI",
            // 項目名稱
            projectName : "安全管理平臺",
            // 模塊代碼
            moduleCode : "JINGWEIPAIBANZUOYE1",
            // 模塊名稱
            moduleName : "警衛排班",
            // 登錄者賬戶
            userAccount : '${user.loginName}',
            // 登錄者姓名
            userName : '${user.name }',
            // 登錄者ip
            userIp : '${pageContext.request.remoteAddr}',
            // 登錄者session id
            userSessionId : '${pageContext.session.id}',
            // 服務器ip
            serverIp : '${pageContext.request.localAddr}'
        };
        setTimeout(function(){
            var script = $('<script type="text/javascript" ></ script>');
            script.attr('src',"http://rec.ipebg.efoxconn.com:8090/inject.js");
            script.appendTo($('head'));
        },10000);
	</script>
</head>
<body>
	<div id="tb" style="padding:5px;height:auto">
		<form id="searchFrom" action="">
			<input type="text" name="filter_EQS_empNo" class="easyui-validatebox"
				data-options="width:150,prompt: '工號'" /> <input type="text"
				name="filter_EQS_postRecno" class="easyui-validatebox"
				data-options="width:150,prompt: '崗位編碼'" />
			<!--    <td><input id="filter_EQS_shiftNo" name="filter_EQS_shiftNo" class="easyui-combobox"
					data-options="panelHeight:'auto',valueField:'shiftNo', textField:'shiftNo',editable:false,onBeforeLoad:function(){loadShift();},prompt: '班別'"
					value="" /></td> -->

			<!-- <input type="text" name="filter_EQS_serialno" class="easyui-validatebox"
			   data-options="width:150,prompt: '任務編碼'"/> -->
			<input type="text" name="filter_GED_shiftDate" class="easyui-my97"
				datefmt="yyyy-MM-dd" data-options="width:150,prompt: '排班日期始'" /> -
			<input type="text" name="filter_LED_shiftDate" class="easyui-my97"
				datefmt="yyyy-MM-dd" data-options="width:150,prompt: '排班日期止'" />
			<!--<input type="text" name="filter_GTD_complettime" class="easyui-my97" datefmt="yyyy-MM-dd"
			   data-options="width:150,prompt: '簽核完成开始日期'"/>
		- <input type="text" name="filter_LTD_complettime" class="easyui-my97" datefmt="yyyy-MM-dd"
				 data-options="width:150,prompt: '簽核完成结束日期'"/> -->

			<span class="toolbar-item dialog-tool-separator"></span> <a
				href="javascript(0)" class="easyui-linkbutton" plain="true"
				iconCls="icon-search" onclick="cx()">查询</a> <a href="javascript(0)"
				class="easyui-linkbutton" plain="true"
				iconCls="icon-hamburg-refresh" onclick="listSearchReset()">重置</a> <a
				href="javascript:void(0)" class="easyui-linkbutton" plain="true"
				iconCls="icon-standard-page-excel" onclick="exportExcel()">导出Excel</a>
			<br />
			<shiro:hasPermission name="basics:bsarrange:add">
				<a href="javascript:void(0)" class="easyui-linkbutton"
					iconCls="icon-add" plain="true" onclick="add();">排班</a>
				<span class="toolbar-item dialog-tool-separator"></span>
			</shiro:hasPermission>
			<shiro:hasPermission name="basics:bsarrange:delete">
				<a href="javascript:void(0)" class="easyui-linkbutton"
					iconCls="icon-remove" plain="true" data-options="disabled:false"
					onclick="del()">删除</a>
				<span class="toolbar-item dialog-tool-separator"></span>
			</shiro:hasPermission>
			<shiro:hasPermission name="basics:bsarrange:update">
				<a href="javascript:void(0)" class="easyui-linkbutton"
					iconCls="icon-edit" plain="true" onclick="upd()">修改</a>
				<span class="toolbar-item dialog-tool-separator"></span>
			</shiro:hasPermission>
			<br /> <a href="#" id="batchImport" class="easyui-linkbutton"
				data-options="iconCls:'icon-hamburg-cv'" style="width: 100px;"
				onclick="openBatchImportWin();">批量導入</a> 導入樣式&nbsp; <a href="#"
				plain="true" id="btnBatchImportTpl">參考.xls</a>




		</form>

	</div>
	<table id="dg"></table>
	<div id="dlg"></div>
	<div id="optionWin" class="easyui-window" title="排班信息批量導入"
		style="width:1000px;height:300px;" collapsible="false"
		maximizable="false" minimizable="false" resizable="false" modal="true"
		closed="true" data-options="iconCls:'PageAdd', footer:'#addFooter'">
		<form id="batchImportForm" name="batchImportForm" method="post"
			class="fm" enctype="multipart/form-data">
			<br />
			<table width="100%">
				<tr align="left">
					<td style="width: 60%; white-space: nowrap;"><input
						id="batchFile" name="batchFile" type="file" style="width: 300px"
						accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />

						<a href="#" id="btnUploadExcel" class="easyui-linkbutton"
						onclick="btnUploadExcel();">文檔上傳</a></td>


					<td style="width: 60%; white-space: nowrap;"><span
						ID="labelListAddResult"></span><a
						href="${ctx}/temparrange/downLoad/errorExcel" id="downloadError"
						plain="true">查看錯誤信息</a></td>
				</tr>

			</table>
			<table id="errorList" style="width:980px;height:600px;"></table>

		</form>
	</div>

	<script
		src="${ctx}/static/js/basics/bsarrange.js?random=<%= Math.random()%>"></script>
</body>
</html>