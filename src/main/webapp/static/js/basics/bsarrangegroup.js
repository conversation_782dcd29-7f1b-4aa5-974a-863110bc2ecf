var dg;
var d;
$(function(){
    dg=$('#dg').datagrid({
        method: "get",
        url:ctx+'/bsarrangegroup/list',
        fit : true,
    	queryParams:{
            order:'desc',
            sort:'createDate'
       },
        fitColumns : true,
        border : false,
        idField : 'id',
        striped:true,
        pagination:true,
        rownumbers:true,
        pageNumber:1,
        pageSize : 20,
        pageList : [ 10, 20, 30, 40, 50 ],
        singleSelect:true,
        columns:[[
            { field: 'id', title: 'id',hidden:true},
            { field: 'arrangeGroupId', title: '排班群組編碼',sortable:true,width:100},
            { field: 'arrangeGroupName', title: '排班群組',sortable:true,width:100},
            { field: 'securityCom', title: '保安公司',sortable:true,width:100},
            { field: 'createBy', title: '創建人',sortable:true,width:100},
            { field: 'createDate', title: '創建時間',sortable:true,width:100},
            { field: 'updateBy', title: '更新人',sortable:true,width:100},
            { field: 'updateDate', title: '更新時間',sortable:true,width:100}
        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        rowTooltip: true,
        toolbar:'#tb'
    });
    
    $.ajax({
        url: ctx + "/bsarrangegroup/getAllArrangeGroup",
        //dataType:"json",
        type: "GET",
        success: function (data) {
            //绑定第一个下拉框
            $("#filter_EQS_arrangeGroupId").combobox({
                data: data,
                valueField: "arrangeGroupId",
                textField: "arrangeGroupName",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({
                    	arrangeGroupId: '',
                    	arrangeGroupName: '請選擇'
                    });
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });
    
});

//弹窗增加
function add() {
    d=$("#dlg").dialog({
        title: '添加排班群組基本資料',
        width: 380,
        height: 380,
        href:ctx+'/bsarrangegroup/create',
        maximizable:true,
        modal:true,
        buttons:[{
            text:'确认',
            handler:function(){
                var arrangeGroupName=$.trim($("#arrangeGroupName").val());
                var securityCom=$("#securityCom").combobox("getValue");
     			  
   				if (arrangeGroupName == "") {
   					$.messager.alert("溫馨提示", "排班群組名稱不能為空！", "info");
   					return;
   				}
                if (securityCom=="") {
                    $.messager.alert("溫馨提示", "保安公司不能為空！", "info");
                    return;
                }

                $("#mainform").submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}

//删除
function del(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    var para=row.id+","+row.arrangeGroupId;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data){
        if (data){
	$.ajax({
                type:'get',
                url:ctx+"/bsarrangegroup/delete/"+para,
                success: function(data){
                    successTip(data,dg);
                }
            });
        }
    });
}

//弹窗修改
function upd(){
    var row = dg.datagrid('getSelected');
    if(rowIsNull(row)) return;
    d=$("#dlg").dialog({
        title: '修改排班群組基本資料',
        width: 380,
        height: 340,
        href:ctx+'/bsarrangegroup/update/'+row.id,
        maximizable:true,
        modal:true,
        buttons:[{
            text:'修改',
            handler:function(){
                var arrangeGroupName=$.trim($("#arrangeGroupName").val());
            	var securityCom=$("#securityCom").combobox("getValue");
    			  
                if (arrangeGroupName == "") {
                    $.messager.alert("溫馨提示", "排班群組名稱不能為空！", "info");
                    return;
                }

                if (securityCom=="") {
                    $.messager.alert("溫馨提示", "保安公司不能為空！", "info");
                    return;
                }

                $('#mainform').submit();
            }
        },{
            text:'取消',
            handler:function(){
                d.panel('close');
            }
        }]
    });
}
//创建查询对象并查询
function cx(){
    var obj=$("#searchFrom").serializeObject();
    dg.datagrid('load',obj);
}
//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("reset");
}
//导出excel
function exportExcel(){
    var form = document.getElementById("searchFrom");
    searchFrom.action=ctx+'/bsarrangegroup/exportExcel';
    form.submit();
}