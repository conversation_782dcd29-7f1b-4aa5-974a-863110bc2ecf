<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>資訊（非固資類）物品新購及報廢申請</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src='${ctx}/static/js/other/tqhwfgoodsbuyandscrap.js?'+Math.random()></script>
    <style type="text/css">
        .td_style2 {
            text-align: left;
        }
        .td_style3{
            border: none 0px;
        }
    </style>
</head>
<body>
<form id="mainform" action="${ctx}/tqhwfgoodsbuyandscrap/${action}" method="post">
    <div class="commonW">
        <div class="headTitle">資訊（非固資類）物品新購及報廢申請單</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">${tQhWfgoodsbuyandscrap.serialno}</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
            <input class="inputCss" style="width: 100px" value="<fmt:formatDate value='${tQhWfgoodsbuyandscrap.createtime}' pattern='yyyy-MM-dd hh:mm'/>" >
            </span>
        </div>
        <div class="position_R margin_R"> 填單人：${tQhWfgoodsbuyandscrap.makerno}/${tQhWfgoodsbuyandscrap.makername}</div>
        <br>
        <div class="clear"></div>
        <table class="formList" id="buildprojecttable">
            <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td width="4%">申請人工號<font color="red">*</font></td>
                            <td width="6%" class="td_style1">
                                <input id="applyno" name="tQhWfgoodsbuyandscrap.applyno" class="easyui-validatebox inputCss"
                                       data-options="width: 80" value="${tQhWfgoodsbuyandscrap.applyno }" readonly/>
                            </td>
                            <td width="4%">申請人</td>
                            <td width="6%" class="td_style1">
                                <input id="applyname" name="tQhWfgoodsbuyandscrap.applyname"
                                       class="easyui-validatebox inputCss"
                                       data-options="width:80" readonly value="${tQhWfgoodsbuyandscrap.applyname }" readonly/>
                            </td>
                            <td width="4%">單位代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applydeptno" name="tQhWfgoodsbuyandscrap.applydeptno"
                                       class="easyui-validatebox inputCss" data-options="width: 90"
                                       readonly value="${tQhWfgoodsbuyandscrap.applydeptno }" readonly/>
                            </td>
                            <td width="3%">費用代碼</td>
                            <td width="6%" class="td_style1">
                                <input id="applycostno" name="tQhWfgoodsbuyandscrap.applycostno" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${tQhWfgoodsbuyandscrap.applycostno }" readonly/>
                            </td>
                            <td width="4%">所在廠區&nbsp;<font color="red">*</font></td>
                            <td width="7%" class="td_style1">
                                <input id="applyfactoryid" name="tQhWfgoodsbuyandscrap.applyfactoryid" class="easyui-combobox"
                                       panelHeight="auto" value="${tQhWfgoodsbuyandscrap.applyfactoryid }"  disabled
                                       data-options="width: 120"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td>資位</td>
                            <td class="td_style1">
                                <input id="applyleveltype" name="tQhWfgoodsbuyandscrap.applyleveltype" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${tQhWfgoodsbuyandscrap.applyleveltype }" readonly/>
                            </td>
                            <td>管理職</td>
                            <td class="td_style1">
                                <input id="applymanager" name="tQhWfgoodsbuyandscrap.applymanager" class="easyui-validatebox inputCss" data-options="width: 80"
                                       value="${tQhWfgoodsbuyandscrap.applymanager }" readonly/>
                            </td>
                            <td>聯繫郵箱<font color="red">*</font></td>
                            <td colspan="3" class="td_style1">
                                <input id="applyemail" name="tQhWfgoodsbuyandscrap.applyemail" class="easyui-validatebox inputCss"
                                       value="${tQhWfgoodsbuyandscrap.applyemail }" style="width:300px;" readonly/>
                            </td>
                            <td>使用區域<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applyarea" name="tQhWfgoodsbuyandscrap.applyarea" class="easyui-combobox" data-options="width: 70"
                                       value="${tQhWfgoodsbuyandscrap.applyarea }" panelHeight="auto" disabled />&nbsp;/
                                <input id="applybuilding" name="tQhWfgoodsbuyandscrap.applybuilding" class="easyui-combobox" data-options="width: 70"
                                       value="${tQhWfgoodsbuyandscrap.applybuilding }" panelHeight="auto" disabled />
                            </td>
                        </tr>
                        <tr align="center">
                            <td>聯繫分機<font color="red">*</font></td>
                            <td class="td_style1">
                                <input id="applytel" name="tQhWfgoodsbuyandscrap.applytel" class="easyui-validatebox inputCss" style="width:90px;"
                                       value="${tQhWfgoodsbuyandscrap.applytel }" readonly/>
                            </td>
                            <td>單位&nbsp;<font color="red">*</font></td>
                            <td colspan="4" class="td_style1">
                                <input id="applydeptname" name="tQhWfgoodsbuyandscrap.applydeptname" class="easyui-validatebox inputCss" data-options="width: 350"
                                       value="${tQhWfgoodsbuyandscrap.applydeptname }" readonly/>
                            </td>
                            <td>安保區域&nbsp;<font color="red">*</font></td>
                            <td colspan="2" class="td_style2">
                                <div class="securityareaDiv"></div>
                                <input id="securityarea" name="tQhWfgoodsbuyandscrap.securityarea"
                                       type="hidden" class="easyui-validatebox" data-options="width: 100"
                                       value="${tQhWfgoodsbuyandscrap.securityarea }"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>

                    <table class="formList">
                        <tr>
                            <td colspan="10" class="td_style1">申請詳細信息</td>
                        </tr>
                        <tr align="center">
                            <td colspan="3" width="15%">申請類型<font color="red">*</font></td>
                            <td colspan="7" class="td_style2">
                                <div class="requiretypeDiv"></div>
                                <input id="requiretype" name="tQhWfgoodsbuyandscrap.requiretype"
                                       type="hidden" class="easyui-validatebox" data-options="width: 150"
                                       value="${tQhWfgoodsbuyandscrap.requiretype }"/>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="10" style="height: 100%;width: 100%">
                                <div style="overflow-x: auto;width: 100%;">
                                    <c:if test="${zbItemEntity!=null&&zbItemEntity.size()>0}">
                                        <table width="100%" id="zbItemTable">
                                            <tr align="center">
                                                <td width="8%">物品類別</td>
                                                <td width="5%">序號</td>
                                                <td width="20%">物品名稱<font color="red">*</font></td>
                                                <td width="10%">品牌<font color="red">*</font></td>
                                                <td width="15%">規格型號<font color="red">*</font></td>
                                                <td width="15%">編號&序列號<font color="red">*</font></td>
                                                <td width="10%">數量<font color="red">*</font></td>
                                            </tr>
                                            <c:forEach items="${zbItemEntity}" var="zbItem" varStatus="status">
                                                <tr align="center" id="zbItem${status.index+1}">
                                                    <c:if test="${status.index==0}">
                                                        <td rowspan="${zbItemEntity.size()}">周邊設備及配件類</td>
                                                    </c:if>
                                                    <td>${status.index+1}</td>
                                                    <td>
                                                        <input id="zb_goodsname${status.index+1}" name="tQhWfgoodsbuyandscrapzbitems[${status.index}].goodsname"
                                                               class="easyui-combobox" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadzbGoodsname(${status.index+1});},onSelect:function(){onchangezbGoodsname(${status.index+1});}" style="width:80px;"
                                                               value="${zbItem.goodsname}" disabled/>&nbsp;&nbsp;
                                                        <c:if test="${zbItem.othername!=null}">
                                                            <input id="zb_othername${status.index+1}" name="tQhWfgoodsbuyandscrapzbitems[${status.index}].othername"
                                                                   class="easyui-validatebox" data-options="prompt:'選擇其他時必填'" style="width:100px;background-color:#EBEBE4" readonly
                                                                   value="${zbItem.othername}"/>
                                                        </c:if>
                                                        <c:if test="${zbItem.othername==null}">
                                                            <input id="zb_othername${status.index+1}" name="tQhWfgoodsbuyandscrapzbitems[${status.index}].othername"
                                                                   class="easyui-validatebox"  style="width:100px;" disabled
                                                                   value="${zbItem.othername}"/>
                                                        </c:if>
                                                    </td>
                                                    <td>${zbItem.brand}</td>
                                                    <td>${zbItem.specific}</td>
                                                    <td>${zbItem.serialnumber}</td>
                                                    <td>${zbItem.goodsnum}</td>
                                                </tr>
                                            </c:forEach>
                                        </table>
                                    </c:if>
                                    <c:if test="${zaItemEntity!=null&&zaItemEntity.size()>0}">
                                        <table width="100%" id="zaItemTable">
                                            <tr align="center">
                                                <td width="8%" rowspan="${zaItemEntity.size()+1}">資安管制類</td>
                                                <td width="5%">序號</td>
                                                <td width="20%">物品名稱<font color="red">*</font></td>
                                                <td width="10%">品牌<font color="red">*</font></td>
                                                <td width="15%">規格型號<font color="red">*</font></td>
                                                <td width="15%">編號&序列號<font color="red">*</font></td>
                                                <td width="10%">數量<font color="red">*</font></td>
                                            </tr>
                                            <c:forEach items="${zaItemEntity}" var="zaItem" varStatus="status">
                                                <tr align="center" id="zaItem${status.index+1}">
                                                    <td>${status.index+1}</td>
                                                    <td>
                                                        <input id="za_goodsname${status.index+1}" name="tQhWfgoodsbuyandscrapzaitems[${status.index}].goodsname"
                                                               class="easyui-combobox" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadzaGoodsname(${status.index+1});},onSelect:function(){onchangezaGoodsname(${status.index+1});}" style="width:80px;"
                                                               value="${zaItem.goodsname}" disabled/>&nbsp;&nbsp;
                                                        <c:if test="${zaItem.othername!=null}">
                                                            <input id="za_othername${status.index+1}" name="tQhWfgoodsbuyandscrapzaitems[${status.index}].othername"
                                                                   class="easyui-validatebox" data-options="prompt:'選擇其他時必填'" style="width:100px;;background-color:#EBEBE4" readonly
                                                                   value="${zaItem.othername}"/>
                                                        </c:if>
                                                        <c:if test="${zaItem.othername==null}">
                                                            <input id="za_othername${status.index+1}" name="tQhWfgoodsbuyandscrapzaitems[${status.index}].othername"
                                                                   class="easyui-validatebox"  style="width:100px;" disabled
                                                                   value="${zaItem.othername}"/>
                                                        </c:if>
                                                    </td>
                                                    <td>${zaItem.brand}</td>
                                                    <td>${zaItem.specific}</td>
                                                    <td>${zaItem.serialnumber}</td>
                                                    <td>${zaItem.goodsnum}</td>
                                                </tr>
                                            </c:forEach>
                                        </table>
                                    </c:if>
                                </div>
                            </td>
                        </tr>
                        <tr align="center">
                            <td colspan="3">需求說明&nbsp;<font color="red">*</font></td>
                            <td colspan="7" class="td_style1">
                                <textarea id="describtion" name="tQhWfgoodsbuyandscrap.describtion"
                                          class="easyui-validatebox inputCss" style="width:800px;height:80px;" rows="5" cols="6" readonly>${tQhWfgoodsbuyandscrap.describtion}</textarea>
                            </td>
                        </tr>

                        <c:if test="${tQhWfgoodsbuyandscrap.infoassess!=null}">
                        <tr align="center">
                            <td colspan="3">資訊評估&nbsp;<font color="red">*</font></td>
                            <td colspan="7" class="td_style1">
                            <textarea name="tQhWfgoodsbuyandscrap.infoassess" class="easyui-validatebox"
                                      style="width:800px;height:80px;" rows="5" cols="6" readonly>${tQhWfgoodsbuyandscrap.infoassess}</textarea>
                            </td>
                        </tr>
                        </c:if>

                        <c:if test="${tQhWfgoodsbuyandscrap.isoldthings!=null}">
                            <tr align="center">
                                <td colspan="3">是否利舊物品</td>
                                <td colspan="7" class="td_style2">
                                    <div class="isoldthingsDiv"></div>
                                    <input id="isoldthings" name="tQhWfgoodsbuyandscrap.isoldthings"
                                           type="hidden" class="easyui-validatebox" data-options="width: 150"
                                           value="${tQhWfgoodsbuyandscrap.isoldthings }"/>
                                    <input id="isoldthingsAudit" name="tQhWfgoodsbuyandscrap.isoldthings"
                                           type="hidden" class="easyui-validatebox" data-options="width: 150"
                                           value="${tQhWfgoodsbuyandscrap.isoldthings }"/>
                                </td>
                            </tr>
                            <c:if test="${tQhWfgoodsbuyandscrap.isoldthings=='1'}">
                                <tr align="center">
                                    <td colspan="10" width="100%">
                                        <div style="overflow-x: auto;width: 100%;">
                                            <table id="oldItemTable" width="100%">
                                                <tr align="center">
                                                    <td>序號</td>
                                                    <td>物品名稱<font color="red">*</font></td>
                                                    <td>規格型號<font color="red">*</font></td>
                                                    <td>數量<font color="red">*</font></td>
                                                </tr>
                                                <c:if test="${oldItemEntity!=null&&oldItemEntity.size()>0}">
                                                    <c:forEach items="${oldItemEntity}" var="oldItem" varStatus="status">
                                                        <tr align="center" id="oldItem${status.index+1}">
                                                            <td>${status.index+1}</td>
                                                            <td>
                                                                <input id="old_goodsname${status.index+1}" name="tQhWfgoodsbuyandscrapolditems[${status.index}].goodsname"
                                                                       class="easyui-combobox" data-options="panelHeight:'auto',valueField:'value', textField:'label',editable:false,onBeforeLoad:function(){loadoldGoodsname(${status.index+1});},onSelect:function(){onchangeoldGoodsname(${status.index+1});}" style="width:80px;"
                                                                       value="${oldItem.goodsname}" disabled/>&nbsp;&nbsp;
                                                                <c:if test="${oldItem.othername!=null}">
                                                                    <input id="old_othername${status.index+1}" name="tQhWfgoodsbuyandscrapolditems[${status.index}].othername"
                                                                           class="easyui-validatebox" data-options="prompt:'選擇其他時必填'" style="width:100px;;background-color:#EBEBE4" readonly
                                                                           value="${oldItem.othername}"/>
                                                                </c:if>
                                                                <c:if test="${oldItem.othername==null}">
                                                                    <input id="old_othername${status.index+1}" name="tQhWfgoodsbuyandscrapolditems[${status.index}].othername"
                                                                           class="easyui-validatebox"  style="width:100px;" disabled
                                                                           value="${oldItem.othername}"/>
                                                                </c:if>
                                                            </td>
                                                            <td>${oldItem.specific}</td>
                                                            <td>${oldItem.goodsnum}</td>
                                                        </tr>
                                                    </c:forEach>
                                                </c:if>
                                            </table>
                                        </div>
                                    </td>
                                </tr>
                            </c:if>
                        </c:if>

                        <c:if test="${tQhWfgoodsbuyandscrap.isdegauss!=null}">
                            <tr align="center">
                                <td colspan="3">硬盤是否消磁</td>
                                <td colspan="7" class="td_style2">
                                    <div class="isdegaussDiv"></div>
                                    <input id="isdegauss" name="tQhWfgoodsbuyandscrap.isdegauss"
                                           type="hidden" class="easyui-validatebox" data-options="width: 150"
                                           value="${tQhWfgoodsbuyandscrap.isdegauss }"/>
                                    <input id="isdegaussAudit" name="tQhWfgoodsbuyandscrap.isdegauss"
                                           type="hidden" class="easyui-validatebox" data-options="width: 150"
                                           value="${tQhWfgoodsbuyandscrap.isdegauss }"/>
                                </td>
                            </tr>
                        </c:if>

                        <c:if test="${tQhWfgoodsbuyandscrap.isscrap!=null}">
                            <tr align="center">
                                <td colspan="3">是否報廢入庫</td>
                                <td colspan="7" class="td_style2">
                                    <div class="isscrapDiv"></div>
                                    <input id="isscrap" name="tQhWfgoodsbuyandscrap.isscrap"
                                           type="hidden" class="easyui-validatebox" data-options="width: 150"
                                           value="${tQhWfgoodsbuyandscrap.isscrap }"/>
                                    <input id="isscrapAudit" name="tQhWfgoodsbuyandscrap.isscrap"
                                           type="hidden" class="easyui-validatebox" data-options="width: 150"
                                           value="${tQhWfgoodsbuyandscrap.isscrap }"/>
                                </td>
                            </tr>
                        </c:if>
                        <c:if test="${tQhWfgoodsbuyandscrap.attachids!=null}">
                        <tr align="center">
                            <td colspan="3">附件</td>
                            <td colspan="7" class="td_style1">
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}" style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L"><a href="${ctx}/admin/download/${item.id}">${item.name}</a></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        </c:if>

                        <tr align="center">
                            <td colspan="3">備註</td>
                            <td colspan="7" class="td_style2">
                                1、此表僅作為資訊類非固資物品報廢后﹐提交請購需求時的依據﹐簽核完成后請將此表單列印保存，并在電子簽核系統中將此表單作為附件上傳﹐供各級主管審核時評估參考；<br/>
                                2、資安管制類物品的報廢鑒定需簽至資安人員﹐且報廢之實物交由資安課統一回收處理；<br/>
                                3、硬盤請購及電腦報廢之前必須將報廢硬盤先送至資安課消磁處理后方可進行請購或報廢作業。
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)" onclick="showWfImag('${processId}','資訊（非固資類）物品新購及報廢申請流程圖','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:left;">
                                ${chargeNodeInfo}
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align:left;">
                                <iframe id="qianheLogFrame" name="qianheLogFrame"
                                        src="${ctx}/wfcontroller/goChargeLog?serialNo=${tQhWfgoodsbuyandscrap.serialno}"
                                        width="100%"></iframe>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="10" style="text-align:center;padding-left:10px;">
                                <a href="javascript:;" id="btnSave" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-cancel'"
                                   style="width: 100px;" onclick="closeCurrentTab();">關閉</a>&nbsp;&nbsp;&nbsp;&nbsp;
                                <a href="#" id="btnPrint" class="easyui-linkbutton" data-options="iconCls:'icon-print'"
                                   style="width: 100px;" onclick="printWindow('btnClose,btnPrint');">列印</a>

                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
</form>
<div id="dlg"></div>
<input type="hidden" id="nodeName"  value=""/>
<input type="hidden" id="disOrEnabled"  value="disabled"/>
<input type="hidden" id="degaussnumAudit"   value="${tQhWfgoodsbuyandscrap.degaussnum}" />
<input type="hidden" id="scrapnumAudit"   value="${tQhWfgoodsbuyandscrap.scrapnum}" />
</body>
</html>