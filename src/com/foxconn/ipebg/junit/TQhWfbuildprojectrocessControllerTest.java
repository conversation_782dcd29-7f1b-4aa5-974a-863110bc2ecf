package com.foxconn.ipebg.junit;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSONObject;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNodeparamEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WorkFlowEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.common.utils.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;

import java.util.List;

/**
 * TQhWfbuildprojectrocessController Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>10/12/2018</pre>
 */
@ContextConfiguration(locations = {"/applicationContext.xml"})
public class TQhWfbuildprojectrocessControllerTest extends AbstractJUnit4SpringContextTests {


    @Autowired
    private WorkFlowService flowService;

    @Test
    public void testCreate(){

        /*String workFlowId = "dzqh_xinzengxiangmuhuanbaoshouxubanlishenqingdan";

       // TQhWfbuildprojectrocessEntity tQhWfbuildprojectrocess = tQhWfbuildprojectrocessService.findById("321bf69a2ed6482088d176d880c357af");
//        tQhWfbuildprojectrocess.setNewRecord(true);
//        tQhWfbuildprojectrocessService.save(tQhWfbuildprojectrocess);
        //啟動流程
        WorkFlowEntity entity = new WorkFlowEntity();
        entity.setWorkflowId(workFlowId);
        entity.setSerialNo(tQhWfbuildprojectrocess.getSerialno());

        entity.setEmpNo("S6112942");

        List<WfNodeinfoEntity> infoList = wfNodeinfoService.findForUser(workFlowId);
        String taskUser = "";
        for (WfNodeinfoEntity nodeinfoEntity : infoList) {
//            if(StringUtils.isNotBlank(nodeinfoEntity.getColname())) {
                taskUser += nodeinfoEntity.getNodename() + ":" + Reflections.getFieldValue(tQhWfbuildprojectrocess, nodeinfoEntity.getColname()) + ";";
//            }
        }
        entity.setTaskUsers(taskUser);

        List<WfNodeinfoEntity> infoLists = wfNodeinfoService.findForHuiqian(workFlowId);
        String huiqian = "";
        WfNoderuleEntity noderuleEntity = null;
        for (WfNodeinfoEntity list : infoLists) {
            noderuleEntity = noderuleService.findUniqByNodeId(list.getNodeid(),workFlowId);
            huiqian += noderuleEntity.getNodeparamname() + ":" + Reflections.getFieldValue(tQhWfbuildprojectrocess, list.getColname()) + ";";
        }
        entity.setHuiqian(huiqian);*/
//        String processId = flowService.processStart(entity);
//        flowService.createHuiqianTaskInfor(entity,processId,tQhWfbuildprojectrocess);
    }
    @Test
    public void testCompleteTask(){
     //   TQhWfbuildprojectrocessEntity entity=  tQhWfbuildprojectrocessService.get("166df709f8244de18b58369ada176d1a");
      //  System.out.println(entity);
    }
    @Test
    public void queryNodeInfos(){
        List<WfNodeparamEntity> dataList = flowService.queryNodeparam("LKXTOTYK0371805290010");
        JSONUtils.toJSONString(dataList);
    }
}
