var dg;
var d;
$(function() {
	dg = $('#dg').datagrid({
		method : "get",
		url : ctx + '/bsdpt/list',
		// url:'/waterQualityTesting/bsdpt/list',
		fit : true,
		fitColumns : true,
		border : false,
	    idField : 'id',
		striped : true,
		pagination : true,
		rownumbers : true,
		pageNumber : 1,
		pageSize : 20,
		pageList : [ 10, 20, 30, 40, 50 ],
		singleSelect : true,
		columns : [ [

		{
			field : 'id',
			title : '資料識別ID  Primary',
			hidden : true
		}, {
			field : 'dptQun',
			title : '事業群',
			sortable : true,
			width : 100
		}, {
			field : 'dptChu',
			title : '事業處',
			sortable : true,
			width : 100
		}, {
			field : 'dptBu',
			title : '部門名稱',
			sortable : true,
			width : 100
		}, {
			field : 'dptKe',
			title : '單位',
			sortable : true,
			width : 100
		}, {
			field : 'dptId',
			title : '單位代碼',
			sortable : true,
			width : 100
		}, {
			field : 'costId',
			title : '費用代碼',
			sortable : true,
			width : 100
		}, {
			field : 'createBy',
			title : '創建人',
			sortable : true,
			width : 100
		}, {
			field : 'createDate',
			title : '創建時間',
			sortable : true,
			width : 100
		}, {
			field : 'updateBy',
			title : '更新者',
			sortable : true,
			width : 100
		}, {
			field : 'updateDate',
			title : '更新時間',
			sortable : true,
			width : 100
		}, {
			field : 'delFlag',
			title : '刪除標識',
			sortable : true,
			width : 100
		} ] ],
		enableHeaderClickMenu : true,
		enableHeaderContextMenu : true,
		enableRowContextMenu : false,
		rowTooltip : true,
		toolbar : '#tb'
	});
	$('#dg').datagrid
});

// 弹窗增加
function add() {
	d = $("#dlg").dialog({
		title : '添加部門基本資料',
		width : 250,
		height : 270,
		href : ctx + '/bsdpt/create',
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '确认',
			handler : function() {
				$("#mainform").submit();
				$('#dg').datagrid('reload');
				//window.location.reload();
			}
		}, {
			text : '取消',
			handler : function() {
				d.panel('close');
			}
		} ]
	});
}

// 删除
function del() {
	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data) {
		if (data) {
			$.ajax({
				type : 'get',
				url : ctx + "/bsdpt/delete/" + row.id,
				success : function(data) {
					successTip(data, dg);
					 $('#dg').datagrid('reload');
				}
			});
		}
	});
}

// 弹窗修改
function upd() {
	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	d = $("#dlg").dialog({
		title : '修改部門基本資料',
		width : 250,
		height : 270,
		href : ctx + '/bsdpt/update/' + row.id,
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '修改',
			handler : function() {
				$('#mainform').submit();
				$('#dg').datagrid('reload');
				//window.location.reload();
			}
		}, {
			text : '取消',
			handler : function() {
				d.panel('close');
			}
		} ]
	});
}
// 创建查询对象并查询
function cx() {
	var obj = $("#searchFrom").serializeObject();
	dg.datagrid('load', obj);
}
// 表單進度查詢頁面查詢條件重置
function listSearchReset() {
	$("#searchFrom").form("reset");
}
// 导出excel
function exportExcel() {
	var form = document.getElementById("searchFrom");
	searchFrom.action = ctx + '/bsdpt/exportExcel';
	form.submit();
}