package com.foxconn.ipebg.system.entity;

import javax.persistence.*;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * 区域entity
 * <AUTHOR>
 * @date 2015年5月9日 
 */
@Entity
@Table(name = "sys_area_info")
@Cache(usage=CacheConcurrencyStrategy.READ_WRITE)
@DynamicUpdate @DynamicInsert
public class AreaInfo implements java.io.Serializable {

	// Fields
	private static final long serialVersionUID = 1L;
	private Integer id;
	private String areaCode;
	private String areaName;
	private Integer pid;
	private Integer sort;

	// Constructors

	/** default constructor */
	public AreaInfo() {
	}

	/** minimal constructor */
	public AreaInfo(String areaCode, String areaName, Integer pid) {
		this.areaCode = areaCode;
		this.areaName = areaName;
		this.pid = pid;
	}

	/** full constructor */
	public AreaInfo(String areaCode, String areaName, Integer pid, Integer sort) {
		this.areaCode = areaCode;
		this.areaName = areaName;
		this.pid = pid;
		this.sort = sort;
	}

	// Property accessors
	@Id
	@SequenceGenerator(name = "generator", sequenceName = "sys_area_qequence", allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "generator")
	@Column(name = "id", unique = true, nullable = false)
	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "area_code", nullable = false, length = 12)
	public String getAreaCode() {
		return this.areaCode;
	}

	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}

	@Column(name = "area_name", nullable = false, length = 50)
	public String getAreaName() {
		return this.areaName;
	}

	public void setAreaName(String areaName) {
		this.areaName = areaName;
	}

	@Column(name = "pid", nullable = false)
	public Integer getPid() {
		return this.pid;
	}

	public void setPid(Integer pid) {
		this.pid = pid;
	}

	@Column(name = "sort")
	public Integer getSort() {
		return this.sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}

}