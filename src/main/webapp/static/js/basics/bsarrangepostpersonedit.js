var markedPerArr = [];//人員（工號、姓名、可排白班、可排夜班、已排崗位索引、已排班別、已排崗位編號）
var markedPostArr = [];//崗位（崗位編號、崗位名稱、選中班別、白班人員索引、夜班人員索引、白班可排班、夜班可排班）

//用於保存校驗
var overSixOnePerArrCopy = [];
var dayOverTimePerArrCopy = [];
var nightOverTimePerArrCopy = [];
var groupAllPerArrCopy = [];
var arrangedPerArrCopy = [];
var postListCopy = [];
var leavePerArrCopy = [];

$(function () {

    //排班群組
    $('#arrangeGroupId').combogrid({
        data: _groupList,
        panelWidth: 400,
        multiple: false,
        idField: 'arrangeGroupId',
        textField: 'arrangeGroupName',
        fitColumns: true,
        columns: [[
            {field: 'arrangeGroupName', title: '排班群組', width: 100}
        ]],
        onChange: function () {
            initData();
        }
    });

    initSearch();
    initData();
});


var initSearch = function () {
    if (_arrangeGroupId) {
        $("#arrangeGroupId").combogrid("setValue", _arrangeGroupId);
    }
    if (_arrangeDate) {
        $("#arrangeDate").validatebox("setValue", _arrangeDate);
    }else {
        $("#arrangeDate").validatebox("setValue", getDateStr(new Date().getTime(),"day"));
    }
}

function initData() {

    var arrangeGroupId = $("#arrangeGroupId").combogrid("getValue");
    var arrangeDate = $("#arrangeDate").validatebox("getValue");

    if (!arrangeGroupId || !arrangeDate) {
        return;
    }

    var params = {
        arrangeDate: arrangeDate,
        arrangeGroupId: arrangeGroupId
    };
    resetArr();
    $.ajax({
        type: 'post',
        url: ctx + "/bsarrangepostperson/arrangeper/initData",
        beforeSend: ajaxLoading,
        data: params,
        success: function (data) {
            overSixOnePerArrCopy = data.overSixOnePerArr?data.overSixOnePerArr.concat():[];
            dayOverTimePerArrCopy = data.dayOverTimePerArr?data.dayOverTimePerArr.concat():[];
            nightOverTimePerArrCopy = data.nightOverTimePerArr?data.nightOverTimePerArr.concat():[];
            groupAllPerArrCopy = data.groupAllPerArr?data.groupAllPerArr.concat():[];
            arrangedPerArrCopy = data.arrangedPerArr?data.arrangedPerArr.concat():[];
            postListCopy = data.postList?data.postList.concat():[];
            leavePerArrCopy = data.leavePerArr?data.leavePerArr.concat():[];

            ajaxLoadEnd();
            btnShow(arrangeDate);
            //配對
            pair(markePerArr(data),data.postList);
            //自動選擇
            autoChoose();
            //刷新頁面
            reload();
        },
        error: function (e) {
            ajaxLoadEnd();
            parent.$.messager.show({title: "提示", msg: "操作失敗！", position: "bottomRight"});
        }
    });
}

var autoChoose = function () {

    //獲取選中崗位：崗位類別、崗位索引
    var chosedPostIdx = -1;
    var chosedPostShiftNo = 'A';
    for (var i = 0; i < markedPostArr.length; i++) {
        if (markedPostArr[i].choosed) {
            chosedPostIdx = i;
            chosedPostShiftNo = markedPostArr[i].choosed;
            markedPostArr[i].choosed = null;
            break;
        }
    }
    //自動跳轉到下一個可排班崗位
    for (var i = chosedPostIdx + 1; i < markedPostArr.length; i++) {
        if (markedPostArr[i].isDayEnable && chosedPostShiftNo == 'A' && markedPostArr[i].dayPerIdx == null) {
            markedPostArr[i].choosed = chosedPostShiftNo;
            break;
        }
        if (markedPostArr[i].isNightEnable && chosedPostShiftNo == 'B' && markedPostArr[i].nightPerIdx == null) {
            markedPostArr[i].choosed = chosedPostShiftNo;
            break;
        }
    }

}

var btnShow = function (date) {
    var date1 = new Date(date);
    var date2 = getCurrentDay(new Date);
    if (date1 < date2) {
        $("#save").hide();
        $("#saveAndContinue").hide();
        $("#cancel").hide();
    } else {
        $("#save").show();
        $("#saveAndContinue").show();
        $("#cancel").show();
    }
}
var contain = function (arr, empNo) {
    if (arr) {
        for (var i = 0; i < arr.length; i++) {
            if (arr[i].emp_no == empNo) {
                return true;
            }
        }
    }
    return false;
}

var markePerArr = function (data) {
    var arrangedPerArr = data.arrangedPerArr;//已排班人員
    // var groupAllPerArr = addArray(data.groupAllPerArr,arrangedPerArr);//群組所有人
    var groupAllPerArr = uniPersonArray(data.groupAllPerArr, arrangedPerArr);

    var overSixOnePerArr = subArray(distinct(data.overSixOnePerArr), arrangedPerArr);//違反6+1人員
    var dayOverTimePerArr = data.dayOverTimePerArr;//不能上白班人員
    var nightOverTimePerArr = data.nightOverTimePerArr;//不能上夜班人員
    var leavePerArr = data.leavePerArr;//請假人員

    //標記：day-true或false、night-true或false、SHIFT_NO-班別、RECNO-崗位編號
    if (groupAllPerArr) {
        for (var i = 0; i < groupAllPerArr.length; i++) {
            groupAllPerArr[i].day = contain(groupAllPerArrCopy, groupAllPerArr[i].emp_no);
            groupAllPerArr[i].night = contain(groupAllPerArrCopy, groupAllPerArr[i].emp_no);
            debugger
            if (contain(overSixOnePerArr, groupAllPerArr[i].emp_no)) {
                groupAllPerArr[i].day = false;
                groupAllPerArr[i].night = false;
            }
            if (contain(dayOverTimePerArr, groupAllPerArr[i].emp_no)) {
                groupAllPerArr[i].day = false;
            }
            if (contain(nightOverTimePerArr, groupAllPerArr[i].emp_no)) {
                groupAllPerArr[i].night = false;
            }
            if (contain(leavePerArr, groupAllPerArr[i].emp_no)) {
                groupAllPerArr[i].day = false;
                groupAllPerArr[i].night = false;
            }
        }
    }
    return groupAllPerArr;
}

function addArrange(perIdx) {
    //獲取選中崗位：崗位類別、崗位索引
    var chosedPostIdx = -1;
    var chosedPostShiftNo = '';
    for (var i = 0; i < markedPostArr.length; i++) {
        if (markedPostArr[i].choosed) {
            chosedPostIdx = i;
            chosedPostShiftNo = markedPostArr[i].choosed;
            break;
        }
    }

    if (chosedPostIdx >= 0) {
        //所選崗位類別與人員可排班類別一致才能排班
        if ((chosedPostShiftNo == 'A' && !markedPerArr[perIdx].day) || (chosedPostShiftNo == 'B' && !markedPerArr[perIdx].night)) {
            return;
        }
        //人員：崗位編號、班別、崗位索引賦值
        //崗位：白班人員索引或夜班人員索引賦值
        if (chosedPostShiftNo == 'A') {
            markedPostArr[chosedPostIdx].dayPerIdx = perIdx;
            markedPerArr[perIdx].day = false;
            markedPerArr[perIdx].dayRecno = markedPostArr[chosedPostIdx].recno;
            markedPerArr[perIdx].dayPostIdx = chosedPostIdx;
        } else if (chosedPostShiftNo == 'B') {
            markedPostArr[chosedPostIdx].nightPerIdx = perIdx;
            markedPerArr[perIdx].night = false;
            markedPerArr[perIdx].nightRecno = markedPostArr[chosedPostIdx].recno;
            markedPerArr[perIdx].nightPostIdx = chosedPostIdx;
        }
        autoChoose();
        reload();
    }
}

var getDateStr=function(ms,flag){
    if(ms==null || ms==''){
        return '';
    }
    var d=new Date(ms);
    var mm=d.getMonth()+1;
    if(mm<10){
        mm='0'+mm;
    }
    var dd=d.getDate();
    if(dd<10){
        dd='0'+dd;
    }

    if(flag=='day'){
        return d.getFullYear()+'-'+mm+'-'+dd;
    }else if(flag=='sec'){
        var hh=d.getHours();
        if(hh<10){
            hh='0'+hh;
        }
        var mi=d.getMinutes();
        if(mi<10){
            mi='0'+mi;
        }
        var ss=d.getSeconds();
        if(ss<10){
            ss='0'+ss;
        }
        return d.getFullYear()+'-'+mm+'-'+dd+' '+hh+':'+mi+':'+ss;
    }
    return '';
}

function removeArrange(perIdx, shiftNo,postIdx) {
    if(!shiftNo){
        return;
    }

    shiftNo = shiftNo==1?'A':'B';
    //清除已選中
    for (var i = 0; i < markedPostArr.length; i++) {
        if (markedPostArr[i].choosed) {
            markedPostArr[i].choosed=null;
            break;
        }
    }

    if(postIdx || postIdx==0){
        markedPostArr[postIdx].choosed = shiftNo;
    }

    if(perIdx || perIdx==0){
        if (shiftNo == 'A') {
            //崗位：選中崗位類別賦值、人員索引清除
            var postIdex = markedPerArr[perIdx].dayPostIdx;
            if (markedPostArr[postIdex].isDayEnable) {
                markedPostArr[postIdex].choosed = shiftNo;
            }
            markedPostArr[postIdex].dayPerIdx = null;
            markedPerArr[perIdx].day = true && contain(groupAllPerArrCopy, markedPerArr[perIdx].emp_no);
            markedPerArr[perIdx].dayRecno = undefined;
            markedPerArr[perIdx].dayPostIdx = undefined;
        } else if (shiftNo == 'B') {
            var postIdex = markedPerArr[perIdx].nightPostIdx;
            if (markedPostArr[postIdex].isNightEnable) {
                markedPostArr[postIdex].choosed = shiftNo;
            }
            markedPostArr[postIdex].nightPerIdx = null;
            markedPerArr[perIdx].night = true && contain(groupAllPerArrCopy, markedPerArr[perIdx].emp_no);
            markedPerArr[perIdx].nightRecno = undefined;
            markedPerArr[perIdx].nightPostIdx = undefined;
        }
    }
    reload();
}

var getCurrentDay = function (date) {
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var day = date.getDate();
    return new Date(year + '-' + (month<10?'0'+month:month) + '-' + (day<10?'0'+day:day));
}

var distinct = function (arr) {
    var result = [];
    if (arr && arr.length > 0) {
        var temp = {};
        for (var i = 0; i < arr.length; i++) {
            if (!temp[arr[i].emp_no]) {
                temp[arr[i].emp_no] = arr[i].emp_name;
                result.push(arr[i]);
            }
        }
    }
    return result;
}


//返回arr1的子集，該子集中的元素不存在於arr2
var subArray = function (arr1, arr2) {
    var result = [];
    if (arr1 && arr2) {
        for (var i = 0; i < arr1.length; i++) {
            var flag = true;
            for (var j = 0; j < arr2.length; j++) {
                if (arr1[i].emp_no == arr2[j].emp_no) {
                    flag = false;
                    break;
                }
            }
            if (flag) {
                result.push(arr1[i]);
            }
        }
    }

    return result;
}
//arr1和arr2並集
var addArray = function (arr1,arr2) {
    var result = [];
    if (arr1 && arr2) {
        result=arr2.concat(subArray(arr1,arr2));
    }else if( !arr1 && arr2){
        result = arr2;
    }else if(arr1 && !arr2){
        result = arr1;
    }
    return result;
}

var uniPersonArray = function (allPerArr, arrangedPerArr) {
    var result = [];
    for (var i = 0; i < allPerArr.length; i++) {
        var person = allPerArr[i];
        for (var j = 0; j < arrangedPerArr.length; j++) {
            var arrangedPer = arrangedPerArr[j];
            if (person.emp_no == arrangedPer.emp_no) {
                if (arrangedPer.shift_no == 'A') {
                    person.dayRecno = arrangedPer.recno;
                    person.day_post_name = arrangedPer.post_name;
                }
                if (arrangedPer.shift_no == 'B') {
                    person.nightRecno = arrangedPer.recno;
                    person.night_post_name = arrangedPer.post_name;
                }
            }
        }
        result.push(person);
    }
    var others = [];
    var subArr = subArray(arrangedPerArr, allPerArr);
    for (var i = 0; i < subArr.length; i++) {
        var inSub = subArr[i];
        if (contain(others, inSub.emp_no)) {
            var person = firstPersonInArray(others, inSub.emp_no)
            if (inSub.shift_no == 'A') {
                person.dayRecno = inSub.recno;
                person.day_post_name = inSub.post_name;
            } else if (inSub.shift_no == 'B') {
                person.nightRecno = inSub.recno;
                person.night_post_name = inSub.post_name;
            }
        } else {
            var person = {};
            person.emp_no = inSub.emp_no;
            person.emp_name = inSub.emp_name;
            if (inSub.shift_no == 'A') {
                person.dayRecno = inSub.recno;
                person.day_post_name = inSub.post_name;
            } else if (inSub.shift_no = 'B') {
                person.nightRecno = inSub.recno;
                person.night_post_name = inSub.post_name;
            }
            others.push(person);
        }
    }
    if (others.length > 0) {
        result = result.concat(others);
    }
    return result;
}

var firstPersonInArray = function (perArr, empNo) {
    if (empNo) {
        for (var i = 0; i < perArr.length; i++) {
            var person = perArr[i];
            if (person.emp_no == empNo) {
                return person;
            }
        }
    }
    return perArr[0];
}

var pair = function (perArr, postArr) {
    if (!perArr || perArr.length==0  || !postArr || postArr.length==0) {
        return ;
    }
    //崗位人員配對
    markedPostArr = [];
    for (var i = 0; i < postArr.length; i++) {
        var post = postArr[i];
        // 對目前群組內的崗位進行匹配
        if (post.post_per_nu) {
            for (var k = 0; k < post.post_per_nu; k++) {
                var element = {};
                element.recno = post.recno;
                element.post_name = post.post_name;
                element.choosed = null;
                element.dayPerIdx = null;
                element.nightPerIdx = null;
                element.isDayEnable = true;
                element.isNightEnable = post.post_shift == '2班制' ? true : false;

                pairPosition(markedPostArr.length, element, perArr);
                markedPostArr.push(element);
            }
        }
    }
    // 對例外崗位進行匹配（包含崗位人數減少以及撤崗的崗位）
    var postTemp={};//key:崗位編號+班別 value：崗位索引；用處：將相同崗位，按照班別不同排在一起
    for (var i = 0; i < perArr.length; i++) {
        var per=perArr[i];
        if(per.dayPostIdx == undefined && per.dayRecno){
            // 存在這樣的人員：拍過白班，但崗位現在沒了
            var element = {};
            element.recno = per.dayRecno;
            element.post_name = per.day_post_name;
            element.choosed = null;
            element.dayPerIdx = null;
            element.nightPerIdx = null;
            element.isDayEnable = false;
            element.isNightEnable = false;
            pairPosition(markedPostArr.length, element, perArr);
            markedPostArr.push(element);
        }
        if(per.nightPostIdx == undefined && per.nightRecno){
            // 存在這樣的人員：拍過白班，但崗位現在沒了
            var element = {};
            element.recno = per.nightRecno;
            element.post_name = per.night_post_name;
            element.choosed = null;
            element.dayPerIdx = null;
            element.nightPerIdx = null;
            element.isDayEnable = false;
            element.isNightEnable = false;
            pairPosition(markedPostArr.length, element, perArr);
            markedPostArr.push(element);
        }
    }
    markedPerArr = perArr;
}

// 把未配对的人员配对到一个岗位区块上
// postIdx:  崗位序號
// position: 未配对的岗位
// perArray: 人员列表
var pairPosition = function(postIdx, position, perArray) {
    for (var i = 0; i < perArray.length; i++) {
        if (position.dayPerIdx != undefined && position.nightPerIdx != undefined) {
            // 这个岗位白/夜班人员都已确定，不需要继续循环配对了
            return;
        }
        var person = perArray[i];
        if (person.dayPostIdx != undefined && person.nightPostIdx != undefined) {
            // 当前循环的人员白/夜班岗位都已确定，再看下一个人
            continue;
        }
        if (position.dayPerIdx == undefined && person.dayPostIdx == undefined && person.dayRecno == position.recno) {
            // 崗位和人員都沒匹配，且記錄值一致，可以匹配白班
            position.dayPerIdx = i;
            person.dayPostIdx = postIdx;
            person.day = false;
        }
        if (position.nightPerIdx == undefined && person.nightPostIdx == undefined && person.nightRecno == position.recno) {
            // 崗位和人員都沒匹配，且記錄值一致，可以匹配夜班
            position.nightPerIdx = i;
            person.nightPostIdx = postIdx;
            person.night = false;
        }
    }
}

var genPostDIV = function (post, postIdx) {
    var noA=1;//A
    var noB=2;//B
    var emA = post.isDayEnable ? (post.choosed == 'A' ? '<div class="post-enable-a post-choosed" onclick="removeArrange(null,'+noA+','+postIdx+')"></div>' : '<div class="post-enable-a" onclick="removeArrange(null,'+noA+','+postIdx+')"></div>') : '<div class="post-unenable" ></div>';
    var emB = post.isNightEnable ? (post.choosed == 'B' ? '<div class="post-enable-b post-choosed" onclick="removeArrange(null,'+noB+','+postIdx+')"></div>' : '<div class="post-enable-b" onclick="removeArrange(null,'+noB+','+postIdx+')"></div>') : '<div class="post-unenable"></div>';
    for (var i = 0; i < markedPerArr.length; i++) {
        var person = markedPerArr[i];
        var personValid = contain(groupAllPerArrCopy, person.emp_no);
        if (person.dayPostIdx == postIdx) {
            if (post.isDayEnable && post.choosed == 'A' && personValid) {
                emA = '<div class="post-enable-a post-choosed" onclick="removeArrange(' + i + ',' + noA +',' + postIdx + ')" >' + markedPerArr[i].emp_no + '/' + markedPerArr[i].emp_name + '</div>';
            } else if (post.isDayEnable && post.choosed != 'A' && personValid) {
                emA = '<div class="post-enable-a" onclick="removeArrange(' + i + ',' + noA +',' + postIdx + ')" >' + markedPerArr[i].emp_no + '/' + markedPerArr[i].emp_name + '</div>';
            } else {
                emA = '<div class="post-unenable" onclick="removeArrange(' + i + ',' + noA +',' + postIdx + ')" >' + markedPerArr[i].emp_no + '/' + markedPerArr[i].emp_name + '</div>';
            }
        }
        if (person.nightPostIdx == postIdx) {
            if (post.isNightEnable && post.choosed == 'B' && personValid) {
                emB = '<div class="post-enable-b post-choosed"  onclick="removeArrange(' + i + ',' + noB + ',' + postIdx + ')">' + markedPerArr[i].emp_no + '/' + markedPerArr[i].emp_name + '</div>';
            } else if (post.isNightEnable && post.choosed != 'B' && personValid) {
                emB = '<div class="post-enable-b"  onclick="removeArrange(' + i + ',' + noB + ',' + postIdx + ')">' + markedPerArr[i].emp_no + '/' + markedPerArr[i].emp_name + '</div>';
            } else {
                emB = '<div class="post-unenable" onclick="removeArrange(' + i + ',' + noB + ',' + postIdx + ')">' + markedPerArr[i].emp_no + '/' + markedPerArr[i].emp_name + '</div>';
            }
        }
    }

    return emA + emB;
}
var reload = function () {
    //各崗位排班明細
    $("#traineeArr").empty();
    var elements = '';
    for (var i = 0; i < markedPostArr.length; i++) {
        elements += '<div class="post" ><div class="post-title">' + markedPostArr[i].post_name + '</div>' + genPostDIV(markedPostArr[i], i) + '</div>';
    }
    elements += '<div style="clear: both"></div>';//清除浮動標記
    $("#traineeArr").append(elements);


    //可排人員
    $("#no_traineeArr").empty();
    var no_elements = '';
    for (var i = 0; i < markedPerArr.length; i++) {
        if (_allowContinueWork) {
            // 允许连班的情况
            if (markedPerArr[i].day && markedPerArr[i].night) {
                //白晚班都可
                no_elements += '<div class="person-both" onclick="addArrange(' + i + ')">' + markedPerArr[i].emp_no + '/' + markedPerArr[i].emp_name + '</div>';
            } else if (markedPerArr[i].day && !markedPerArr[i].night) {
                //僅白班
                no_elements += '<div class="person-day" onclick="addArrange(' + i + ')">' + markedPerArr[i].emp_no + '/' + markedPerArr[i].emp_name + '</div>';
            } else if (!markedPerArr[i].day && markedPerArr[i].night) {
                //僅晚班
                no_elements += '<div class="person-night" onclick="addArrange(' + i + ')">' + markedPerArr[i].emp_no + '/' + markedPerArr[i].emp_name + '</div>';
            } else if (!markedPerArr[i].day && !markedPerArr[i].night) {
                //白晚班都不可
                no_elements += '<div class="unenable" >' + markedPerArr[i].emp_no + '/' + markedPerArr[i].emp_name + '</div>';
            }
        } else {
            // 不允许连班的情况
            if (markedPerArr[i].dayPostIdx == undefined && markedPerArr[i].nightPostIdx == undefined) {
                // 只有白晚班都未排此人才顯示此人
                if (markedPerArr[i].day && markedPerArr[i].night) {
                    //白晚班都可
                    no_elements += '<div class="person-both" onclick="addArrange(' + i + ')">' + markedPerArr[i].emp_no + '/' + markedPerArr[i].emp_name + '</div>';
                } else if (markedPerArr[i].day && !markedPerArr[i].night) {
                    //僅白班
                    no_elements += '<div class="person-day" onclick="addArrange(' + i + ')">' + markedPerArr[i].emp_no + '/' + markedPerArr[i].emp_name + '</div>';
                } else if (!markedPerArr[i].day && markedPerArr[i].night) {
                    //僅晚班
                    no_elements += '<div class="person-night" onclick="addArrange(' + i + ')">' + markedPerArr[i].emp_no + '/' + markedPerArr[i].emp_name + '</div>';
                } else if (!markedPerArr[i].day && !markedPerArr[i].night) {
                    //白晚班都不可
                    no_elements += '<div class="unenable" >' + markedPerArr[i].emp_no + '/' + markedPerArr[i].emp_name + '</div>';
                }
            }
        }
    }

    no_elements += '<div style="clear: both"></div>';//清除浮動標記
    $("#no_traineeArr").append(no_elements);

}

var reset = function () {
    $("#arrangeGroupId").combogrid("setValue", '');
    $("#arrangeDate").validatebox("setValue", getDateStr(new Date().getTime(),"day"));
    markedPerArr = [];//排班人員
    markedPostArr = [];//排班崗位
    reload();
}

var resetArr = function () {
    markedPerArr = [];//排班人員
    markedPostArr = [];//排班崗位
}


var submit = function (isCloseTab) {


    var arrangeGroupId = $("#arrangeGroupId").combogrid("getValue");
    var arrangeGroupName = $("#arrangeGroupId").combogrid("getText");
    var arrangeDate = $("#arrangeDate").validatebox("getValue");

    if (!arrangeGroupId || !arrangeDate || markedPerArr.length == 0 || markedPostArr.length==0) {
        return;
    }

    var unEnableExist = false;
    var perArr=[];

    for (var i = 0; i < markedPostArr.length; i++) {
        if( markedPostArr[i].dayPerIdx !=null){
            var emp_no = markedPerArr[markedPostArr[i].dayPerIdx].emp_no;
            if(markedPostArr[i].isDayEnable && contain(groupAllPerArrCopy, emp_no)){
                perArr.push(getPer(markedPostArr[i].dayPerIdx, "A",markedPostArr[i].post_name));
            }else{
                unEnableExist=true;
            }
        }
        if( markedPostArr[i].nightPerIdx !=null){
            var emp_no = markedPerArr[markedPostArr[i].nightPerIdx].emp_no;
            if(markedPostArr[i].isNightEnable && contain(groupAllPerArrCopy, emp_no)){
                perArr.push(getPer(markedPostArr[i].nightPerIdx, "B",markedPostArr[i].post_name));
            }else{
                unEnableExist=true;
            }
        }
    }


    if(unEnableExist){
        $("#msg").text("提示：已失效崗位或人員不能保存！");
        return;
    }else{
        $("#msg").text(" ");
    }

    var params={
        validParams : {
            arrangeDate: arrangeDate,
            arrangeGroupId: arrangeGroupId
        },
        validValues:{
            overSixOnePerArr: overSixOnePerArrCopy ,
            dayOverTimePerArr: dayOverTimePerArrCopy ,
            nightOverTimePerArr: nightOverTimePerArrCopy ,
            groupAllPerArr: groupAllPerArrCopy ,
            arrangedPerArr: arrangedPerArrCopy ,
            postList: postListCopy,
            leavePerArr: leavePerArrCopy
        },

        arrangeGroupId:arrangeGroupId,
        arrangeGroupName:arrangeGroupName,
        arrangeDate:arrangeDate,
        perArr:perArr
    };

    $.ajax({
        type: 'post',
        url: ctx + "/bsarrangepostperson/arrangeper/save",
        beforeSend: ajaxLoading,
        data: JSON.stringify(params),
        contentType: "application/json",
        success: function (data) {
            ajaxLoadEnd();
            if (data.success) {
                reset();
                if (isCloseTab) {
                    window.parent.mainpage.mainTabs.closeCurrentTab();
                }

                parent.$.messager.show({title: "提示", msg: data.msg, position: "bottomRight"});
            } else {
                parent.$.messager.show({title: "提示", msg: data.msg, position: "bottomRight"});
            }
        },
        error: function (e) {
            ajaxLoadEnd();
            parent.$.messager.show({title: "提示", msg: "操作失敗！", position: "bottomRight"});
        }
    });
}

var getPer = function(idx, shiftNo,postName){
    var param={};
    param.empNo = markedPerArr[idx]['emp_no'];
    param.empName = markedPerArr[idx]['emp_name'];
    param.shiftNo = shiftNo;
    param.postName = postName;
    if (shiftNo == "A") {
        param.recno = markedPerArr[idx].dayRecno;
    } else if (shiftNo == "B") {
        param.recno = markedPerArr[idx].nightRecno;
    }
    return param;
}

function cancel() {
    window.parent.mainpage.mainTabs.closeCurrentTab();
}

function save() {
    submit(true);
}

function saveAndContinue() {
    submit(false);
}
