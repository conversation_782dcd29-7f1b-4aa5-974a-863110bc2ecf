package com.foxconn.ipebg.audit.service;

import com.foxconn.ipebg.audit.entity.EBsCodeEntity;
import com.foxconn.ipebg.audit.dao.EBsCodeDao;
import com.foxconn.ipebg.basics.dao.ESignUserLineDao;
import com.foxconn.ipebg.basics.dao.ESignUserinfoDao;
import com.foxconn.ipebg.basics.entity.ESignUserLineEntity;
import com.foxconn.ipebg.basics.entity.ESignUserinfoEntity;
import com.foxconn.ipebg.basics.entity.TestitemEntity;
import com.foxconn.ipebg.basics.service.ESignUserLineService;
import com.foxconn.ipebg.basics.service.ESignUserinfoService;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.system.service.DictService;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 代碼基本表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-01-23 09:32:47
 */
@Service
@Transactional(readOnly=true)
public class  EBsCodeService extends BaseBusinessService<EBsCodeEntity, String>{
    @Autowired
    private EBsCodeDao eBsCodeDao;
    @Autowired
    private ESignUserinfoService eSignUserinfoService;
    @Autowired
    private ESignUserLineService eSignUserLineService;
    @Override
    public HibernateDao<EBsCodeEntity, String> getEntityDao() {
        return eBsCodeDao;
    }

    //下拉框
	public List<EBsCodeEntity> getCodeIdByCodeTyp(String codeTyp) {
	      return eBsCodeDao.findAllByOrder("codeTyp",codeTyp,"codeId",true);
	}

	/**
     * 删除签核节点，一并删除节点对应人员以及签核线中人员
     * */
    @Override
    @Transactional(readOnly=false)
    public void delete(String id) {
        // 删除签核节点时，将该节点对应的签核人员设置为无效
        EBsCodeEntity oldEntity = get(id);
        List<PropertyFilter> signerFilters = new ArrayList<PropertyFilter>();
        signerFilters.add(new PropertyFilter("EQS_eserNodeName", oldEntity.getCodeId().toString()));
        signerFilters.add(new PropertyFilter("EQS_eserTyp", oldEntity.getCodeTyp()));
        signerFilters.add(new PropertyFilter("EQS_ynVa", "Y"));
        List<ESignUserinfoEntity> esigners = eSignUserinfoService.search(signerFilters);
        for (ESignUserinfoEntity signer : esigners) {
            signer.setYnVa("N");
            eSignUserinfoService.save(signer);
        }
        // 删除对应签核记录中的人员，避免下次自动带出无效人员
        eSignUserLineService.deleteByFormTypeAndSignOrd(oldEntity.getCodeTyp(), String.valueOf(oldEntity.getCodeId()));
        super.delete(id);
    }

    @Override
    @Transactional(readOnly=false)
    public void update(EBsCodeEntity entity) {
        EBsCodeEntity oldEntity = get(entity.getId());
        if (!oldEntity.getCodeId().equals(entity.getCodeId()) || !oldEntity.getCodeTyp().equals(entity.getCodeTyp())) {
            // 判断到节点位序或节点类别变更，修改对应签核人员
            List<PropertyFilter> signerFilters = new ArrayList<PropertyFilter>();
            signerFilters.add(new PropertyFilter("EQS_eserNodeName", oldEntity.getCodeId().toString()));
            signerFilters.add(new PropertyFilter("EQS_eserTyp", oldEntity.getCodeTyp()));
            signerFilters.add(new PropertyFilter("EQS_ynVa", "Y"));
            List<ESignUserinfoEntity> esigners = eSignUserinfoService.search(signerFilters);
            for (ESignUserinfoEntity signer : esigners) {
                signer.setEserNodeName(String.valueOf(entity.getCodeId()));
                signer.setEserTyp(entity.getCodeTyp());
                eSignUserinfoService.update(signer);
            }
            // 修改签核记录中的顺序
            List<PropertyFilter> lineFilters = new ArrayList<PropertyFilter>();
            lineFilters.add(new PropertyFilter("EQS_formType", oldEntity.getCodeTyp()));
            lineFilters.add(new PropertyFilter("EQS_codeId", oldEntity.getCodeId()));
            List<ESignUserLineEntity> lines = eSignUserLineService.search(lineFilters);
            for (ESignUserLineEntity line : lines) {
                line.setCodeId(entity.getCodeId());
                line.setFormType(entity.getCodeTyp());
                eSignUserLineService.update(line);
            }
        }
        oldEntity.setCodeId(entity.getCodeId());
        oldEntity.setCodeNam(entity.getCodeNam());
        oldEntity.setCodeMemo(entity.getCodeMemo());
        oldEntity.setCodeTyp(entity.getCodeTyp());
        oldEntity.setRequired(entity.getRequired());
        oldEntity.setUpdateDate(new Date());
        super.update(oldEntity);
    }

    public List<EBsCodeEntity> getCodeIdByCodeTyp(final String codeTyp, final String codeMemo) {
        return eBsCodeDao.findAllByOrderCollect(new HashMap<String, Object>(){{
            put("codeTyp", codeTyp);
            put("codeMemo", codeMemo);
        }}, "codeId", true);
    }

    public List<EBsCodeEntity> getCodeTypes() {
        List<EBsCodeEntity> entities = new ArrayList<EBsCodeEntity>();
        List<String> typeArray = eBsCodeDao.createSQLQuery("select distinct code_typ from e_bs_code").list();
        if (typeArray.size() > 0) {
            for (String type :typeArray) {
                EBsCodeEntity entity = new EBsCodeEntity();
                entity.setCodeTyp(type);
                entities.add(entity);
            }
        }
        return entities;
    }

    public EBsCodeEntity getCode(String codeType, Integer codeId) {
        List<EBsCodeEntity> list = eBsCodeDao.find("from EBsCodeEntity t where t.codeTyp=?0 and t.codeId=?1", codeType, codeId);
        return list.get(0);
    }

    /**
     * 獲取簽核節點中特定的某個節點的位序
     * 在系統中加入退回重提功能後，該功能會改變節點次序，所以此方法被淘汰，改為調用`public List<Integer> getConfigedSignOrder(String formType, String configType, String serialno)`
     * @param formType 表單類型
     * @param configType 配置名稱
     * @return 配置的節點數組
     */
    public List<Integer> getConfigedSignOrder(String formType, String configType) {
        String sql = "select a.* " +
                "from e_bs_code a, sys_dict b " +
                "where b.label like '%'||a.code_nam||'%' " +
                "and b.value = ?0 " +
                "and a.code_typ = ?1 " +
                "order by a.code_id ";
        List<EBsCodeEntity> list = (List<EBsCodeEntity>) eBsCodeDao.createSQLQuery(sql, configType, formType).addEntity(EBsCodeEntity.class).list();
        List<Integer> orderList = new ArrayList<Integer>();
        for (EBsCodeEntity code : list) {
            orderList.add(code.getCodeId());
        }
        return orderList;
    }

    /**
     * 獲取指定申請單簽核列表中特定的某個節點的位序
     * @param formType 表單類型
     * @param configType 配置類型
     * @param serialno 表單編號
     * @return
     */
    public List<Integer> getConfigedSignOrder(String formType, String configType, String serialno) {
        String sql = "select a.id, " +
                "(select max(sign_ord) from e_form_sign where apply_id = ?0 and sign_nam = a.code_nam) code_id, " +
                "a.code_nam, a.code_typ, a.code_memo, a.required, a.create_by, a.create_date, a.update_by, a.update_date, a.del_flag " +
                "from e_bs_code a, sys_dict b " +
                "where b.label like '%'||a.code_nam||'%' " +
                "and b.value = ?1 " +
                "and a.code_typ = ?2 " +
                "order by a.code_id ";
        List<EBsCodeEntity> list = (List<EBsCodeEntity>)eBsCodeDao.createSQLQuery(sql, serialno, configType, formType).addEntity(EBsCodeEntity.class).list();
        List<Integer> orderList = new ArrayList<Integer>();
        for (EBsCodeEntity code : list) {
            if (code.getCodeId() != null) {
                orderList.add(code.getCodeId());
            }
        }
        return orderList;
    }
}
