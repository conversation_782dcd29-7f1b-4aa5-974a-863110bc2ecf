package com.foxconn.ipebg.system.dao;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Repository;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import javax.annotation.Resource;

/**
 * Redis 数据源
 * <AUTHOR>
 * @since 2018/11/20
 */
@Repository("redisDao")
public class RedisDao {
	
	private static final Logger log = Logger.getLogger(RedisDao.class);
	
	//@Resource(name = "jedisPool")
	private JedisPool jedisPool;

	public synchronized Jedis getRedisClient() {
		
		try {
			Jedis jedis = jedisPool.getResource();
			return jedis;
		} catch (Exception e) {
			log.error("getRedisClent error", e);
		}
		return null;
	}

	public void returnResource(Jedis jedis) {
		
		close(jedis);
	}

	private void close(Jedis jedis) {
		try {
			if (jedis != null) {
				jedis.close();
			}
		} catch (Exception e) {
			log.error("jedis close error:" + e.getMessage());
			jedis = null;
		}
	}

	public void returnResource(Jedis jedis, boolean broken) {
		
		close(jedis);
	}

}
