package com.foxconn.ipebg.system.entity;

import java.util.List;

/**
 * Company foxconn
 * Copyright (C) 2004-2018 All Rights Reserved.
 */
public class OldMyTask {
    private Integer backCountListCount;
    private Integer backProxyCountListCount;
    private List<TaskList> backCountList;
    private List<TaskList> backProxyCountList;
    private List<TaskList> myTaskProxyCountList;
    private List<TaskList> myTaskCountList;
    private Integer myTaskProxyCountListCount;
    private Integer myTaskCountListCount;

    public Integer getBackCountListCount() {
        return backCountListCount;
    }

    public void setBackCountListCount(Integer backCountListCount) {
        this.backCountListCount = backCountListCount;
    }

    public Integer getBackProxyCountListCount() {
        return backProxyCountListCount;
    }

    public void setBackProxyCountListCount(Integer backProxyCountListCount) {
        this.backProxyCountListCount = backProxyCountListCount;
    }

    public List<TaskList> getBackCountList() {
        return backCountList;
    }

    public void setBackCountList(List<TaskList> backCountList) {
        this.backCountList = backCountList;
    }

    public List<TaskList> getBackProxyCountList() {
        return backProxyCountList;
    }

    public void setBackProxyCountList(List<TaskList> backProxyCountList) {
        this.backProxyCountList = backProxyCountList;
    }

    public List<TaskList> getMyTaskProxyCountList() {
        return myTaskProxyCountList;
    }

    public void setMyTaskProxyCountList(List<TaskList> myTaskProxyCountList) {
        this.myTaskProxyCountList = myTaskProxyCountList;
    }

    public List<TaskList> getMyTaskCountList() {
        return myTaskCountList;
    }

    public void setMyTaskCountList(List<TaskList> myTaskCountList) {
        this.myTaskCountList = myTaskCountList;
    }

    public Integer getMyTaskProxyCountListCount() {
        return myTaskProxyCountListCount;
    }

    public void setMyTaskProxyCountListCount(Integer myTaskProxyCountListCount) {
        this.myTaskProxyCountListCount = myTaskProxyCountListCount;
    }

    public Integer getMyTaskCountListCount() {
        return myTaskCountListCount;
    }

    public void setMyTaskCountListCount(Integer myTaskCountListCount) {
        this.myTaskCountListCount = myTaskCountListCount;
    }
}
