package com.foxconn.ipebg.system.utils;


import org.apache.shiro.authc.UsernamePasswordToken;

/**
 * 扩展添加验证码-继承用户验证类
 * <AUTHOR>
 * @date 2014年12月2日 下午10:45:57
 */
public class UsernamePasswordCaptchaToken extends UsernamePasswordToken {

	private static final long serialVersionUID = 1L;

	private String loginType = "0";// 0为用户密码登录，1为無密碼登录，2手机验证码登录

	private String captcha;

	private String ip;//擴展字段  登錄系統綁定IP校驗

	private String uphoneNum;
	/**
	 * 免密碼登錄驗證碼
	 */
	private String utoken;
	public String getCaptcha() {
		return captcha;
	}

	public void setCaptcha(String captcha) {
		this.captcha = captcha;
	}

	public String getLoginType() {
		return loginType;
	}

	public void setLoginType(String loginType) {
		this.loginType = loginType;
	}

	public String getUphoneNum() {
		return uphoneNum;
	}

	public void setUphoneNum(String uphoneNum) {
		this.uphoneNum = uphoneNum;
	}

	public UsernamePasswordCaptchaToken() {
		super();
	}

	public String getUtoken() {
		return utoken;
	}

	public void setUtoken(String utoken) {
		this.utoken = utoken;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public UsernamePasswordCaptchaToken(String username, char[] password, boolean rememberMe,
										String host, String captcha,
										String loginType, String utoken, String uphoneNum,String ip) {
		super(username, password, rememberMe, host);
		this.captcha = captcha;
		this.loginType = loginType;
		this.uphoneNum = uphoneNum;
		this.ip=ip;
	}

}