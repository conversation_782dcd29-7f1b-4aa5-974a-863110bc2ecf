package com.foxconn.ipebg.buessness.workflow.entity;

import java.io.Serializable;
import java.util.List;

/**
 * 工作流接口返回對應對象
 * <AUTHOR>
 *
 */
public class InterResult implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 操作結果 SUCCESS 成功  , FAIL失敗
	 */
	private String status;
	/**
	 * 操作結果描述
	 */
	private String msg;
	/**
	 * 工單實例id
	 */
	private String processId;
	/**
	 * 實例任務狀態
	 */
	private String running;
	/**
	 * 簽核主管工號
	 */
	private String assignee;
	private String value;
	/**
	 * 任務記錄
	 */
	private List<TaskInfo> taskInfoList;
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getMsg() {
		return msg;
	}
	public void setMsg(String msg) {
		this.msg = msg;
	}
	public String getProcessId() {
		return processId;
	}
	public void setProcessId(String processId) {
		this.processId = processId;
	}
	public String getRunning() {
		return running;
	}
	public void setRunning(String running) {
		this.running = running;
	}
	public String getAssignee() {
		return assignee;
	}
	public void setAssignee(String assignee) {
		this.assignee = assignee;
	}
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}

	public List<TaskInfo> getTaskInfoList() {
		return taskInfoList;
	}

	public void setTaskInfoList(List<TaskInfo> taskInfoList) {
		this.taskInfoList = taskInfoList;
	}
}
