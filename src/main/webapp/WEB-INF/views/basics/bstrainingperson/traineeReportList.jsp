<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>培訓人員明細表</title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
	type="text/javascript"></script>
</head>
<body>
	<div id="tb" style="padding:5px;height:auto">
		<form id="searchFromReport" action="">
			<input type="text" id="trainingPeriodNos" name="trainingPeriodNos"
				class="easyui-combobox" data-options="width:150,prompt: '培訓期編碼 '" />
			<input type="hidden" id="trainingPeriodNosHide"
				name="trainingPeriodNosHide" />

			<!-- <input type="text" name="filter_EQS_trainingPeriodName" class="easyui-validatebox"
			   data-options="width:150,prompt: '培訓期名稱 '"/> -->
			<input type="text" id="empNo" name="empNo" class="easyui-validatebox"
				data-options="width:150,prompt: '工號'" /> <input type="text"
				id="empName" name="empName" class="easyui-validatebox"
				data-options="width:150,prompt: '姓名'" />
				
					    <input type="text" id="result" name="result" class="easyui-validatebox"
			   data-options="width:150,prompt: '考核結果'"/> 
			<!-- 	<input type="text" name="filter_GTD_startDate" class="easyui-my97" datefmt="yyyy-MM-dd"
			   data-options="width:150,prompt: '培訓開始時間 '"/>
		- <input type="text" name="filter_LTD_endDate" class="easyui-my97" datefmt="yyyy-MM-dd"
				 data-options="width:150,prompt: '培訓結束時間 '"/> -->
			<!-- <input id="qysjzt" style="width:100px" class="easyui-validatebox" name="filter_EQS_workstatus"/> -->
			<span class="toolbar-item dialog-tool-separator"></span> <a
				href="javascript(0)" class="easyui-linkbutton" plain="true"
				iconCls="icon-search" onclick="cx()">查询</a> <a href="javascript(0)"
				class="easyui-linkbutton" plain="true"
				iconCls="icon-hamburg-refresh" onclick="listSearchReset()">重置</a>


		</form>

	</div>
	<table id="getPersonList"></table>

	<script type="text/javascript">
		var dataGridPerson;
		var d;
		$(function() {

			//var trainingPeriodNo = $('#trainingPeriodNo').val();
			var empNo = $('#empNo').val();
			var empName = $('#empName').val();
			var param = {
				"empName" : empName,
				"empNo" : empNo
			};

			dataGridPerson = $('#getPersonList').datagrid({
				method : "get",
				fit : true,
				fitColumns : true,
				queryParams : param,
				border : false,
				idField : 'id',
				striped : true,
				pagination : true,
				rownumbers : true,
				pageNumber : 1,
				pageSize : 20,
				pageList : [ 10, 20, 30, 40, 50 ],
				singleSelect : true,
				checkOnSelect : true,
				columns : [ [ {
					field : 'id',
					title : '資料識別ID  Primary',
					hidden : true
				}, {
					field : 'trainingPeriodName',
					title : '培訓期名稱',
					//sortable : true,
					width : 100
				}, {
					field : 'startTime',
					title : '培訓期',
					//sortable : true,
					width : 100/* ,
					formatter : formatDate */
				}, {
					field : 'empNo',
					title : '工號',
					//sortable : true,
					width : 100
				}, {
					field : 'empName',
					title : '姓名',
					//sortable : true,
					width : 100
				}, {
					field : 'psnId',
					title : '出勤天數',
					//sortable : true,
					width : 100
				}, {
					field : 'endTime',
					title : '培訓期天數',
					sortable : true,
					width : 100
				},{
					field : 'company',
					title : '考核結果',
					//sortable : true,
					width : 100
				} ] ]
			});

      $("#result").combobox({
		data:[{'value':'合格','text':'合格'},{'value':'不合格','text':'不合格'}],
		valueField:'value',
		textField:'text',
		panelHeight:'auto',
			loadFilter : function(data) {
							data.unshift({
								value : '',
								text : '請選擇'
							});
							return data;
						}
		});
			var isAll = "Y";
			$.ajax({
				url : ctx + "/bstrainingperiod/getAllTrainingPeriodList/"
						+ isAll,
				//dataType:"json",
				type : "GET",
				success : function(data) {
					//绑定第一个下拉框
					$("#trainingPeriodNos").combobox({
						data : data,
						valueField : "trainingPeriodNo",
						textField : "trainingPeriodName",
						editable : false,
						panelHeight : 400,
						loadFilter : function(data) {
							data.unshift({
								trainingPeriodNo : '',
								trainingPeriodName : '請選擇'
							});
							return data;
						}
					});
				},
				error : function(error) {
					alert("初始化下拉控件失败");
				}
			});
		}

		);
		// 创建查询对象并查询
		function cx() {
			var url = ctx + '/bstrainingperson/getTraineeReportDetailList';
			var obj = $("#searchFromReport").serializeObject();
			dataGridPerson.datagrid({
						url: url,
						queryParams: obj
			});
		}
			
		/* 	${ctx}/bstrainingperson/getTraineeReportDetailList;
			console.log("f88");
	 var trainingPeriodNosHide=$('#trainingPeriodNos').val();
	 $('#trainingPeriodNosHide').val(trainingPeriodNosHide);
	var obj = $("#searchFrom").serializeObject();
	dg.datagrid('load', obj); */

		//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFromReport").form("reset");
}
	</script>
</body>
</html>