package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 崗位人員表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-09-24 14:32:17
 */
@Entity
@Table(name = "BS_ARRANGE_POST_PERSON")
@DynamicUpdate
@DynamicInsert
public class BsArrangePostPersonEntity extends DataEntity<BsArrangePostPersonEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    //崗位編號
    private String recno;
    //崗位名稱
    private String postName;
    //工號
    private String empNo;
    //姓名
    private String empName;
    //班別代碼：A-白班，B-夜班
    private String shiftNo;
    //排班日期
    private Date arrangeDate;
    //排班群組編碼
    private String arrangeGroupId;
    //排班群組
    private String arrangeGroupName;
    @Transient
    protected boolean isResign = false;
    @Transient
    public boolean isResign() {
        return delFlag.equalsIgnoreCase(DataEntity.DEL_FLAG_DELETE);
    }

    public void setResign(boolean resign) {
        isResign = resign;
    }

    /**
     * 设置：崗位編號
     */
    public void setRecno(String recno) {
        this.recno = recno;
    }

    /**
     * 获取：崗位編號
     */

    @Column(name = "RECNO", nullable = false, length = 20)
    public String getRecno() {
        return recno;
    }

    /**
     * 设置：工號
     */
    public void setEmpNo(String empNo) {
        this.empNo = empNo;
    }

    /**
     * 获取：工號
     */

    @Column(name = "EMP_NO", nullable = false, length = 20)
    public String getEmpNo() {
        return empNo;
    }

    /**
     * 设置：姓名
     */
    public void setEmpName(String empName) {
        this.empName = empName;
    }

    /**
     * 获取：姓名
     */

    @Column(name = "EMP_NAME", nullable = false, length = 20)
    public String getEmpName() {
        return empName;
    }

    /**
     * 设置：班別代碼：A-白班，B-夜班
     */
    public void setShiftNo(String shiftNo) {
        this.shiftNo = shiftNo;
    }

    /**
     * 获取：班別代碼：A-白班，B-夜班
     */

    @Column(name = "SHIFT_NO", nullable = false, length = 20)
    public String getShiftNo() {
        return shiftNo;
    }

    /**
     * 设置：排班日期
     */
    public void setArrangeDate(Date arrangeDate) {
        this.arrangeDate = arrangeDate;
    }

    /**
     * 获取：排班日期
     */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "ARRANGE_DATE", nullable = false, length = 20)
    public Date getArrangeDate() {
        return arrangeDate;
    }

    /**
     * 设置：排班群組編碼
     */
    public void setArrangeGroupId(String arrangeGroupId) {
        this.arrangeGroupId = arrangeGroupId;
    }

    /**
     * 获取：排班群組編碼
     */

    @Column(name = "ARRANGE_GROUP_ID", nullable = false, length = 20)
    public String getArrangeGroupId() {
        return arrangeGroupId;
    }


    /**
     * 设置：排班群組
     */
    public void setArrangeGroupName(String arrangeGroupName) {
        this.arrangeGroupName = arrangeGroupName;
    }

    /**
     * 获取：排班群組
     */

    @Column(name = "ARRANGE_GROUP_NAME", nullable = false, length = 20)
    public String getArrangeGroupName() {
        return arrangeGroupName;
    }

    @Column(name = "POST_NAME", nullable = false, length = 20)
    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }
}
