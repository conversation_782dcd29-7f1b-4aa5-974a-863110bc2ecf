package com.foxconn.ipebg.buessness.workflow.service;

import com.foxconn.ipebg.buessness.workflow.entity.WfConifgEntity;
import com.foxconn.ipebg.buessness.workflow.dao.WfConifgDao;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import com.foxconn.ipebg.common.service.BaseService;
import com.foxconn.ipebg.system.entity.Dict;
import com.foxconn.ipebg.system.service.DictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.List;


/**
 * 流程信息配置表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-06 11:04:38
 */
@Service
@Transactional(readOnly=true)
public class  WfConifgService extends BaseBusinessService<WfConifgEntity, String> {
    @Autowired
    private WfConifgDao wfConifgDao;
    @Autowired
    private DictService dictService;
    @Override
    public HibernateDao<WfConifgEntity, String> getEntityDao() {
        return wfConifgDao;
    }

    public WfConifgEntity findUnique(String workflowid){
        Dict dict = dictService.getDictByTypeAndVlaue("workflow_code",workflowid);
        Assert.notNull(dict);
        String version = dict.getLabel();
        return wfConifgDao.findUnique("from WfConifgEntity t where t.workflowid=?0 and t.version=?1",workflowid,version);
    }
}
