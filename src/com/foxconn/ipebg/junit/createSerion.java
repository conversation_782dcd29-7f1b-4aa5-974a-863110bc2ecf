package com.foxconn.ipebg.junit;

import cn.hutool.json.JSONUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhChargelogEntity;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.buessness.common.service.TQhChargelogService;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.common.utils.SpringContextUtil;
import com.foxconn.ipebg.system.entity.*;
import com.foxconn.ipebg.system.service.*;
import com.foxconn.ipebg.system.utils.SendMailUtil;
import com.foxconn.ipebg.system.utils.UsernamePasswordCaptchaToken;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;

import java.util.List;
public class createSerion extends AbstractJUnit4SpringContextTests {
	@Autowired
    private UserServiceUtil userService;

	 @Test
	    public void quartzTest() throws Exception {
	       String headstr="szjc";
	       userService.createSerialno(headstr);
	       System.out.print(userService.createSerialno(headstr));
	    }

}
