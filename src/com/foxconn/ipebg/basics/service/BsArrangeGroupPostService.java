package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.entity.BsArrangeGroupPostEntity;
import com.foxconn.ipebg.basics.entity.BsTraineeEntity;
import com.foxconn.ipebg.basics.dao.BsArrangeGroupPostDao;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.Dict;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WorkFlowEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.system.service.DictService;

import java.util.Date;
import java.util.List;

/**
 * 排班群組匹配崗位
 *
 * <AUTHOR>
 * @email <EMAIL> 
 * @date 2019-09-11 10:28:23
 */
@Service
@Transactional(readOnly=true)
public class  BsArrangeGroupPostService extends BaseBusinessService<BsArrangeGroupPostEntity, String>{
    @Autowired
    private BsArrangeGroupPostDao bsArrangeGroupPostDao;

    @Autowired
    private UserServiceUtil serviceUtil;
    @Autowired
    private DictService dictService;

    @Override
    public HibernateDao<BsArrangeGroupPostEntity, String> getEntityDao() {
        return bsArrangeGroupPostDao;
    }

    public BsArrangeGroupPostEntity findBySerialno(String serialno) {
        return this.bsArrangeGroupPostDao.findUniqueBy("serialno", serialno);
    }
    
	// 通過排班群組編碼查看排班群組是否匹配崗位
	public boolean isExistPostArrangeGroupId(String arrangeGroupId) {
		boolean result = false;
		List<BsArrangeGroupPostEntity> list = bsArrangeGroupPostDao
				.find("from BsArrangeGroupPostEntity t where t.arrangeGroupId=?0 ",//and t.empNo=?1
						
						arrangeGroupId
						);
		if (list.size() > 0) {
			result = true;
		} else {
			result = false;
		}
		return result;
	}
    
}
