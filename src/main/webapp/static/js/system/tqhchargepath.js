var dg;
var d;
$(function () {
    dg = $('#dg').datagrid({
        method: "get",
        url: ctx+'/tqhchargepath/list',
        fit: true,
        fitColumns: true,
        border: false,
        idField: 'id',
        striped: true,
        pagination: true,
        rownumbers: true,
        pageNumber: 1,
        pageSize: 20,
        pageList: [10, 20, 30, 40, 50],
        singleSelect: true,
        columns: [[
            {field: 'id', title: '序號', hidden: true},
            {field: 'factoryid', title: '廠區編碼', align: 'center', sortable: true, width: 100}
            ,
            {field: 'deptno', title: '單位代碼', align: 'center', sortable: true, width: 100}
            ,
            {
                field: 'kchargeno',
                title: '課級主管',
                sortable: true,
                width: 100,
                align: 'center',
                formatter: function (value, row, index) {
                    if (value != null) {
                        return value + "/" + row.kchargename;
                    } else {
                        return "/";
                    }
                }
            }
            ,
            {
                field: 'bchargeno',
                title: '部級主管',
                sortable: true,
                width: 100,
                align: 'center',
                formatter: function (value, row, index) {
                    if (value != null) {
                        return value + "/" + row.bchargename;
                    } else {
                        return "/";
                    }
                }
            }
            ,
            {
                field: 'cchargeno',
                title: '廠級主管',
                sortable: true,
                width: 100,
                align: 'center',
                formatter: function (value, row, index) {
                    if (value != null) {
                        return value + "/" + row.cchargename;
                    } else {
                        return "/";
                    }
                }
            }
            ,
            {
                field: 'zchargeno', title: '製造處級主管', sortable: true, width: 100,
                align: 'center',
                formatter: function (value, row, index) {
                    if (value != null) {
                        return value + "/" + row.zchargename;
                    } else {
                        return "/";
                    }
                }
            }
            ,
            {
                field: 'zcchargeno', title: '製造總處級主管', sortable: true, width: 100, align: 'center',
                formatter: function (value, row, index) {
                    if (value != null) {
                        return value + "/" + row.zcchargename;
                    } else {
                        return "/";
                    }
                }
            }
            ,
            {
                field: 'pcchargeno', title: '產品處級主管', sortable: true, width: 100, align: 'center',
                formatter: function (value, row, index) {
                    if (value != null) {
                        return value + "/" + row.pcchargename;
                    } else {
                        return "/";
                    }
                }
            }
            ,
            {
                field: 'sychargeno', title: '事業群級主管', sortable: true, width: 100, align: 'center',
                formatter: function (value, row, index) {
                    if (value != null) {
                        return value + "/" + row.sychargename;
                    } else {
                        return "/";
                    }
                }
            }
            ,
            {field: 'isvalid', title: '是否有效', sortable: true, width: 50}
            ,
            {field: 'islock', title: '是否鎖定', sortable: true, width: 50}
            ,
            {
                field: 'applyempno', title: '申請人工號', sortable: true, width: 100, align: 'center',
                formatter: function (value, row, index) {
                    if (value != null) {
                        return value + "/" + row.applyusername;
                    } else {
                        return "/";
                    }
                }
            }
            ,
            {field: 'createBy', title: '創建人', sortable: true, width: 50}
            ,
            {
                field: 'createDate',
                title: '創建時間',
                sortable: true,
                width: 100,
                formatter: formatDate
            }
            ,
            {field: 'updateBy', title: '更新者', sortable: true, width: 50}
            ,
            {
                field: 'updateDate',
                title: '更新時間',
                sortable: true,
                width: 100,
                formatter: formatDate
            }
        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        toolbar: '#tb'
    });
});

//弹窗增加
function add() {
    d = $("#dlg").dialog({
        title: '添加簽核路徑表',
        width: 500,
        height: 380,
        href: ctx+'/tqhchargepath/create',
        maximizable: true,
        modal: true,
        buttons: [{
            text: '确认',
            handler: function () {
                $("#mainform").submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//删除
function del() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function (data) {
        if (data) {
            $.ajax({
                type: 'get',
                url: ctx+"/tqhchargepath/delete/" + row.id,
                success: function (data) {
                    successTip(data, dg);
                }
            });
        }
    });
}

//弹窗修改
function upd() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    d = $("#dlg").dialog({
        title: '修改簽核路徑表',
        width: 600,
        height: 400,
        href: ctx+'/tqhchargepath/update/' + row.id,
        maximizable: true,
        modal: true,
        buttons: [{
            text: '修改',
            handler: function () {
                $('#mainform').submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}
//簽核路徑置為無效
function setInvalid() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '置為無效后簽核路徑中將無法顯示？', function (data) {
        if (data) {
            $.ajax({
                type: 'get',
                url: ctx+"/tqhchargepath/setInvalid/" + row.id,
                success: function (data) {
                    successTip(data, dg);
                }
            });
        }
    });
}

function setValid() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '確定置為有效？', function (data) {
        if (data) {
            $.ajax({
                type: 'get',
                url: ctx+"/tqhchargepath/setValid/" + row.id,
                success: function (data) {
                    successTip(data, dg);
                }
            });
        }
    });
}
//创建查询对象并查询
function cx() {
    var obj = $("#searchFrom").serializeObject();
    dg.datagrid('load', obj);
}

function getUserNameByEmpno(obj) {
    if (obj.value != null && obj.value != "") {
        $.post(ctx+'/system/user/getUserInfo/', {
            empno: obj.value
        }, function (data) {
            if (!data) {
                $.messager.alert("溫馨提示", "工號不存在", "error");
            } else {
                if (obj.name == 'creator') {
                    $("#creatorname").val(data.empname);
                } else {
                    $("#" + obj.name.replace("no", "name")).val(data.empname);
                }
            }
        });
    }
}
