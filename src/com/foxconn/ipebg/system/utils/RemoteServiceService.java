package com.foxconn.ipebg.system.utils;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.1.6 in JDK 6
 * Generated source version: 2.1
 */
@WebService(name = "RemoteServiceService", targetNamespace = "http://ws.chn.foxconn.com/")
@XmlSeeAlso({

})
public interface RemoteServiceService {


    /**
     * @param arg0
     * @return returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "getAllMyTasks", targetNamespace = "http://ws.chn.foxconn.com/", className = "com.foxconn.chn.ws.jaxws.GetAllMyTasks")
    @ResponseWrapper(localName = "getAllMyTasksResponse", targetNamespace = "http://ws.chn.foxconn.com/", className = "com.foxconn.chn.ws.jaxws.GetAllMyTasksResponse")
    public String getAllMyTasks(
            @WebParam(name = "arg0", targetNamespace = "")
                    String arg0);

}