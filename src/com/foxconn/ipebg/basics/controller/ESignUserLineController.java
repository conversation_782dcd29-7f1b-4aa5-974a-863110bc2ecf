package com.foxconn.ipebg.basics.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Date;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.basics.entity.ESignUserLineEntity;
import com.foxconn.ipebg.basics.service.ESignUserLineService;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;


/**
 * 默認簽核線記錄表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-09-23 11:36:24
 */
@Controller
@RequestMapping("esignuserline")
public class ESignUserLineController extends BaseController {

    @Autowired
    private ESignUserLineService eSignUserLineService;
   
    /**
      * 方法描述: 列表信息
      * @Author: S6113712
      * @CreateDate:   2019-09-23 11:36:24
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:esignuserline:list")
    public String list() {
        //查询列表数据
        return "basics/esignuserline/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6113712
      * @CreateDate:   2019-09-23 11:36:24
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:esignuserline:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<ESignUserLineEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        page = eSignUserLineService.search(page, filters);
        return getEasyUIData(page);
    }
    /**
     * 方法描述: 保存
     * @Author: S6113712
     * @CreateDate:   2019-09-23 11:36:24
     * @Return
     **/
   
    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("basics:esignuserline:add")
    @ResponseBody
    public String create(@Valid ESignUserLineEntity eSignUserLine, Model model) {
        eSignUserLine.setNewRecord(true);
    	eSignUserLine.setCreateDate(new Date());
        eSignUserLineService.save(eSignUserLine);
        return "success";
    }
    
    /**
      * 方法描述: 添加跳轉
      * @Author: S6113712
      * @CreateDate:   2019-09-23 11:36:24
      * @Return
      **/

    @RequiresPermissions("basics:esignuserline:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("eSignUserLine", new ESignUserLineEntity());
        model.addAttribute("action", "create");
        return "basics/esignuserline/listForm";
    }
    
     /**
     * 方法描述: 修改跳轉
     * @Author: S6113712
     * @CreateDate:   2018-10-06 11:04:39
     * @Return
     **/

   @RequiresPermissions("basics:esignuserline:update")
   @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
   public String updateForm(@PathVariable("id") String id, Model model) {
       model.addAttribute("eSignUserLine", eSignUserLineService.get(id));
       model.addAttribute("action", "update");
      return "basics/esignuserline/listForm";
   }
   
	/**
	 * 方法描述: 修改
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-11 10:55:00
	 * @Return
	 **/

   @RequestMapping(value = "update", method = RequestMethod.POST)
   @RequiresPermissions("basics:esignuserline:update")
   @ResponseBody
   public String update(@Valid ESignUserLineEntity eSignUserLine,@RequestParam(value = "ids") String ids) {
	   eSignUserLine.setId(ids);
	   eSignUserLine.setUpdateDate(new Date());
	   eSignUserLineService.update(eSignUserLine);
	   
       return "success";
   }
    /**
      * 方法描述: 根據主鍵刪除
      * @Author: S6113712
      * @CreateDate:   2019-09-23 11:36:24
      * @Return
      **/

    @RequestMapping("delete/{id}")
    @RequiresPermissions("basics:esignuserline:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        eSignUserLineService.delete(id);
        return "success";
    }
    /**
      * 导出excel
      * @Author: S6113712
      * @CreateDate:   2019-09-23 11:36:24
      * @Return
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {}
}
