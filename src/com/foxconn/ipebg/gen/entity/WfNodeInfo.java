package com.foxconn.ipebg.gen.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class WfNodeInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	private String workFlowId;
	private String nodeId;
	/**
	 * 節點名稱
	 */
	private String nodeName;
	private BigDecimal orderBy;
	/**
	 * 節點簽核類型 0普通簽核 1會簽
	 */
	private String signType;
	/**
	 * 流程節點上簽核人是否必填 （默認N）
	 */
	private String required;
	/**
	 * 表單節點處理人字段
	 */
	private String colName;
	
	/**
	 * 該結點是否可以參與批量簽核
	 */
	private String canBatch;
	private String dynField01,dynField02,dynField03,dynField04,dynField05;
	
	/**
	 * 節點參數列表
	 */
	private List<WfNodeParam> wfNodeParam;
	/**
	 * 會簽節點通過規則.
	 * 當signType=1時，有值
	 */
	private WfNodeRule wfNodeRule;
	
	private WfNodeInfo next;
	public WfNodeInfo getNext() {
		return next;
	}
	public void setNext(WfNodeInfo next) {
		this.next = next;
	}
	public String getWorkFlowId() {
		return workFlowId;
	}
	public void setWorkFlowId(String workFlowId) {
		this.workFlowId = workFlowId;
	}
	public String getNodeId() {
		return nodeId;
	}
	public void setNodeId(String nodeId) {
		this.nodeId = nodeId;
	}
	public String getNodeName() {
		return nodeName;
	}
	public void setNodeName(String nodeName) {
		this.nodeName = nodeName;
	}
	public BigDecimal getOrderBy() {
		return orderBy;
	}
	public void setOrderBy(BigDecimal orderBy) {
		this.orderBy = orderBy;
	}
	public String getSignType() {
		return signType;
	}
	public void setSignType(String signType) {
		this.signType = signType;
	}
	public List<WfNodeParam> getWfNodeParam() {
		return wfNodeParam;
	}
	public void setWfNodeParam(List<WfNodeParam> wfNodeParam) {
		this.wfNodeParam = wfNodeParam;
	}
	public String getRequired() {
		return required;
	}
	public void setRequired(String required) {
		this.required = required;
	}
	public String getColName() {
		return colName;
	}
	public void setColName(String colName) {
		this.colName = colName;
	}
	public WfNodeRule getWfNodeRule() {
		return wfNodeRule;
	}
	public void setWfNodeRule(WfNodeRule wfNodeRule) {
		this.wfNodeRule = wfNodeRule;
	}
	public String getCanBatch() {
		return canBatch;
	}
	public void setCanBatch(String canBatch) {
		this.canBatch = canBatch;
	}
	public String getDynField01() {
		return dynField01;
	}
	public void setDynField01(String dynField01) {
		this.dynField01 = dynField01;
	}
	public String getDynField02() {
		return dynField02;
	}
	public void setDynField02(String dynField02) {
		this.dynField02 = dynField02;
	}
	public String getDynField03() {
		return dynField03;
	}
	public void setDynField03(String dynField03) {
		this.dynField03 = dynField03;
	}
	public String getDynField04() {
		return dynField04;
	}
	public void setDynField04(String dynField04) {
		this.dynField04 = dynField04;
	}
	public String getDynField05() {
		return dynField05;
	}
	public void setDynField05(String dynField05) {
		this.dynField05 = dynField05;
	}
}
