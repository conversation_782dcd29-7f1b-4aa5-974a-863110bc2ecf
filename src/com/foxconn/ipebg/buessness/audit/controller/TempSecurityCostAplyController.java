package com.foxconn.ipebg.buessness.audit.controller;

import com.alibaba.fastjson.JSON;
import com.foxconn.ipebg.audit.entity.EBsCodeEntity;
import com.foxconn.ipebg.audit.entity.EFormSignEntity;
import com.foxconn.ipebg.audit.service.EBsCodeService;
import com.foxconn.ipebg.audit.service.EFormSignService;
import com.foxconn.ipebg.basics.entity.ESignUserLineEntity;
import com.foxconn.ipebg.basics.service.ESignUserLineService;
import com.foxconn.ipebg.buessness.audit.entity.TempSecurityAplyEntity;
import com.foxconn.ipebg.buessness.audit.entity.TempSecurityCostAplyDto;
import com.foxconn.ipebg.buessness.audit.entity.TempSecurityCostAplyEntity;
import com.foxconn.ipebg.buessness.audit.service.TempSecurityAplyService;
import com.foxconn.ipebg.buessness.audit.service.TempSecurityCostAplyService;
import com.foxconn.ipebg.buessness.workflow.entity.Gtasks;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.system.entity.User;
import com.foxconn.ipebg.system.service.DictService;
import com.foxconn.ipebg.system.service.TPubFileobjectService;
import com.foxconn.ipebg.system.service.TPubMailrecordService;
import com.foxconn.ipebg.system.service.UserRoleService;
import com.foxconn.ipebg.system.utils.CommonUtils;
import com.foxconn.ipebg.system.utils.IPUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.*;

/**
 * 臨時性安保服務申請單
 *
 * <AUTHOR>
 * @date 2022-07-06
 */
@Controller
@RequestMapping("tempSecurityCostAply")
public class TempSecurityCostAplyController extends BaseController {
    @Autowired
    private TempSecurityCostAplyService aplyService;
    @Autowired
    private EFormSignService eFormSignService;
    @Autowired
    private ESignUserLineService eSignUserLineService;
    @Autowired
    private EBsCodeService eBsCodeService;
    @Autowired
    private TempSecurityAplyService serviceAplyService;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private TPubMailrecordService tPubMailrecordService;
    @Autowired
    private TPubFileobjectService tPubFileobjectService;
    @Autowired
    private DictService dictService;

    private static final String formType = "LSFY";

    /**
     * 方法描述: 申請單新增
     *
     * @param model 视图模型
     * @param serialno 临时性服务申请单编号
     * @Author: ********
     * @CreateDate: 2022-07-13 10:29:50
     * @Return 页面
     **/
    @RequestMapping(value = "create/{serialno}", method = RequestMethod.GET)
    public String createForm(Model model, @PathVariable("serialno") String serialno) {
        TempSecurityAplyEntity serviceAply = serviceAplyService.findBySerialno(serialno);
        model.addAttribute("action", "create");
        model.addAttribute("serviceAply", serviceAply);
        model.addAttribute("aply", new TempSecurityCostAplyEntity());
        User currentUser = UserUtil.getCurrentUser();
        // 獲取當前登錄用戶已保存的簽核線
        List<ESignUserLineEntity> userLineList = eSignUserLineService.findByEmpNoAndFormType(currentUser.getLoginName(), formType);
        Map<Integer, ESignUserLineEntity> userLineInfo = new HashMap<Integer, ESignUserLineEntity>();
        for (ESignUserLineEntity lineNode: userLineList) {
            userLineInfo.put(lineNode.getCodeId(), lineNode);
        }
        model.addAttribute("eSignUserLine", userLineInfo);
        List<EBsCodeEntity> signNodeList = eBsCodeService.getCodeIdByCodeTyp(formType);
        model.addAttribute("signNodes", signNodeList);
        model.addAttribute("formType", formType);
        return "buessness/audit/tempSecurityCostAply/listForm";
    }

    /**
     * 駁回後的重新提交
     * @param model 視圖
     * @param serialno 申請單號
     * @return
     */
    @RequestMapping(value = "resubmit/{serialno}", method = RequestMethod.GET)
    public String resubmit(Model model, @PathVariable("serialno") String serialno, HttpServletRequest request) {
        model.addAttribute("action", "resubmit");
        TempSecurityCostAplyEntity aplyEntity = aplyService.findBySerialno(serialno);
        TempSecurityAplyEntity serviceAply = serviceAplyService.findBySerialno(aplyEntity.getServiceSerialno());
        model.addAttribute("serviceAply", serviceAply);
        model.addAttribute("aply", aplyEntity);
        User currentUser = UserUtil.getCurrentUser();
        // 獲取當前登錄用戶已保存的簽核線
        List<ESignUserLineEntity> userLineList = eSignUserLineService.findByEmpNoAndFormType(currentUser.getLoginName(), formType);
        Map<Integer, ESignUserLineEntity> userLineInfo = new HashMap<Integer, ESignUserLineEntity>();
        for (ESignUserLineEntity lineNode: userLineList) {
            userLineInfo.put(lineNode.getCodeId(), lineNode);
        }
        model.addAttribute("eSignUserLine", userLineInfo);
        List<EBsCodeEntity> signNodeList = eBsCodeService.getCodeIdByCodeTyp(formType);
        model.addAttribute("signNodes", signNodeList);
        model.addAttribute("formType", formType);
        model.addAttribute("file", tPubFileobjectService.findByIds(aplyEntity.getAttachids(), request.getContextPath()));
        return "buessness/audit/tempSecurityCostAply/listForm";
    }

    /**
     * 方法描述: 保存
     *
     * @Author: ********
     * @CreateDate: 2022-07-13
     * @Return
     **/
    @RequestMapping(value = "create/{flag}", method = RequestMethod.POST)
    @ResponseBody
    public String create(@Valid TempSecurityCostAplyDto dto,
                         HttpServletRequest request,
                         @RequestParam(value = "ids", required = false) String ids,
                         @PathVariable("flag") String flag) {
        TempSecurityCostAplyEntity aplyEntity = dto.getAplyEntity();
        if (ids != null) {
            aplyEntity.setId(ids);
        }

        aplyEntity.setApplyIp(IPUtil.getIpAddress(request));
        aplyEntity.setMakerno(UserUtil.getCurrentUser().getLoginName());
        aplyEntity.setMakername(UserUtil.getCurrentUser().getName());
        // 删除旧签核线
        eSignUserLineService.deleteByEmpNoAndFormType(UserUtil.getCurrentUser().getLoginName(), formType);
        EFormSignEntity[] nodeList = dto.geteFormSignEntity();
        for (int i = 0; i < nodeList.length; i++) {
            EFormSignEntity node = nodeList[i];
            // 保存申请人在每个节点选择的签核者信息
            ESignUserLineEntity eSignUserLineEntity=new ESignUserLineEntity();
            eSignUserLineEntity.setEmpNo(UserUtil.getCurrentUser().getLoginName());
            eSignUserLineEntity.setFormType(formType);
            eSignUserLineEntity.setCodeId(i + 1);
            eSignUserLineEntity.setSignerEmpNo(node.getEserId());
            eSignUserLineEntity.setSignerEmpName(node.getEserNam());
            eSignUserLineEntity.setCreateBy(UserUtil.getCurrentUser().getLoginName());
            eSignUserLineEntity.setCreateDate(new Date());
            eSignUserLineService.save(eSignUserLineEntity);
        }
        // 添加或修改默認簽核線記錄***********************
        String result = aplyService.startProcess(aplyEntity,
                dto.geteFormSignEntity(), CommonUtils.getServerBaseURL(request), formType, flag);
        if (result.equals(Constant.RESULT.CODE_YES.getValue())) {
            return Constant.SUCCESS;
        } else {
            return Constant.FAIL;
        }
    }

    /**
     * 方法描述: 重新提交保存
     *
     * @Author: ********
     * @CreateDate: 2018-12-27 10:29:50
     * @Return
     **/
    @RequestMapping(value = "resubmit/{flag}", method = RequestMethod.POST)
    @ResponseBody
    public String resubmit(@Valid TempSecurityCostAplyDto dto,
                           HttpServletRequest request,
                           @RequestParam(value = "ids", required = false) String ids,
                           @PathVariable("flag") String flag) {
        TempSecurityCostAplyEntity newAply = dto.getAplyEntity();
        TempSecurityCostAplyEntity aplyEntity = aplyService.findBySerialno(newAply.getSerialno());
        aplyEntity.setSecurityCom(newAply.getSecurityCom());
        aplyEntity.setHourNumber(newAply.getHourNumber());
        aplyEntity.setPerNumber(newAply.getPerNumber());
        aplyEntity.setCost(newAply.getCost());
        aplyEntity.setAttachids(newAply.getAttachids());
        aplyService.update(aplyEntity);

        // 删除旧签核线
        eSignUserLineService.deleteByEmpNoAndFormType(UserUtil.getCurrentUser().getLoginName(), formType);
        EFormSignEntity[] nodeList = dto.geteFormSignEntity();
        for (int i = 0; i < nodeList.length; i++) {
            EFormSignEntity node = nodeList[i];
            // 保存申请人在每个节点选择的签核者信息
            ESignUserLineEntity eSignUserLineEntity=new ESignUserLineEntity();
            eSignUserLineEntity.setEmpNo(UserUtil.getCurrentUser().getLoginName());
            eSignUserLineEntity.setFormType(formType);
            eSignUserLineEntity.setCodeId(i + 1);
            eSignUserLineEntity.setSignerEmpNo(node.getEserId());
            eSignUserLineEntity.setSignerEmpName(node.getEserNam());
            eSignUserLineEntity.setCreateBy(UserUtil.getCurrentUser().getLoginName());
            eSignUserLineEntity.setCreateDate(new Date());
            eSignUserLineService.save(eSignUserLineEntity);
        }
        // 添加或修改默認簽核線記錄***********************
        String result = aplyService.reStartProcess(aplyEntity,
                dto.geteFormSignEntity(), CommonUtils.getServerBaseURL(request), formType, flag, IPUtil.getIpAddress(request));
        if (result.equals(Constant.RESULT.CODE_YES.getValue())) {
            return Constant.SUCCESS;
        } else {
            return Constant.FAIL;
        }
    }

    /**
     * 方法描述: 重新提交保存
     *
     * @Author: ********
     * @CreateDate: 2022-12-23
     * @Return
     **/
    @RequestMapping(value = "cancelApply/{serialno}", method = RequestMethod.POST)
    @ResponseBody
    public String cancelApply(HttpServletRequest request,
                              @PathVariable("serialno") String serialno) {
        TempSecurityCostAplyEntity aplyEntity = aplyService.findBySerialno(serialno);
        User currentUser = UserUtil.getCurrentUser();
        if (currentUser == null) return "登錄失效，請重新登錄";
        if (!currentUser.getLoginName().equals(aplyEntity.getMakerno()))
            return "申請人驗證失敗";
        String result = aplyService.cancelApply(aplyEntity, IPUtil.getIpAddress(request));
        if (result.equals(Constant.RESULT.CODE_YES.getValue())) {
            return Constant.SUCCESS;
        } else {
            return Constant.FAIL;
        }
    }

    /**
     * 方法描述: 列表查詢
     *
     * @Author: ********
     * @CreateDate: 2022-07-13
     * @Return
     **/
    @RequestMapping(method = RequestMethod.GET)
    public String list(Model model) {
        // 查询列表数据
        model.addAttribute("legalPersonDicts", JSON.toJSONString(dictService.getDictByType("guard_legalperson")));
        return "buessness/audit/tempSecurityCostAply/list";
    }

    /**
     * 方法描述: 分頁列表查詢
     *
     * @Author: ********
     * @CreateDate: 2022-07-13
     * @Return
     **/
    @RequestMapping(value = "list", method = RequestMethod.GET)
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<TempSecurityCostAplyEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter
                .buildFromHttpRequest(request);
        // 查詢權限begin
        List<Integer> roleList =userRoleService.getRoleIdList(UserUtil.getCurrentUser().getId()) ;
        if (roleList == null || roleList.size() == 0 || !roleList.contains(281)) {
            // 只能查到自己申請或者審核的單字
            List<String> serialnoList = new ArrayList<String>();
            // 查自己申請的單子
            List<PropertyFilter> selfApplyFilters = new ArrayList<PropertyFilter>();
            selfApplyFilters.add(new PropertyFilter("EQS_makerno", UserUtil.getCurrentUser().getLoginName()));
            List<TempSecurityCostAplyEntity> selfApplyList = aplyService.search(selfApplyFilters);
            for (TempSecurityCostAplyEntity aply : selfApplyList) {
                serialnoList.add(aply.getSerialno());
            }
            // 查自己簽核的單子
            List<PropertyFilter> selfSignFilters = new ArrayList<PropertyFilter>();
            selfSignFilters.add(new PropertyFilter("EQS_eserId", UserUtil.getCurrentUser().getLoginName()));
            List<EFormSignEntity> signFormList = eFormSignService.search(selfSignFilters);
            for (EFormSignEntity sign : signFormList) {
                serialnoList.add(sign.getApplyId());
            }
            if (serialnoList.size() == 0) {
                serialnoList.add("");
            }
            filters.add(new PropertyFilter("INS_serialno", serialnoList));
        }
        // 查詢權限end
        page.setOrderBy("createDate");
        page.setOrder("desc");
        page = aplyService.search(page, filters);
        return getEasyUIData(page);
    }

    /**
     * 方法描述: 按臨時性服務單單號查詢結算申請單
     *
     * @Author: ********
     * @CreateDate: 2022-07-15
     * @Return
     **/
    @RequestMapping(value = "listByServiceSerialno", method = RequestMethod.GET)
    @ResponseBody
    public List<TempSecurityCostAplyEntity> listByServiceSerialno(@RequestParam(value = "serviceSerialno", required = true) String serviceSerialno) {
        List<PropertyFilter> filters = new ArrayList<PropertyFilter>();
        // 篩選服務單號
        filters.add(new PropertyFilter("EQS_serviceSerialno", serviceSerialno));
        // 篩選服務單狀態，非駁回
        filters.add(new PropertyFilter("NQS_applyStat", "N"));
        List<TempSecurityCostAplyEntity> result = aplyService.search(filters);
        return result;
    }

    /**
     * 詳細查詢頁面
     *
     * @Author: ********
     * @CreateDate: 2022-07-13
     * @Return
     */
    @RequestMapping(value = "view/{serialno}", method = RequestMethod.GET)
    public String viewForm(@PathVariable("serialno") String serialno,
                           Model model, HttpServletRequest request) {
        TempSecurityCostAplyEntity aplyEntity = aplyService.findBySerialno(serialno);
        TempSecurityAplyEntity serviceAplyEntity = serviceAplyService.findBySerialno(aplyEntity.getServiceSerialno());
        model.addAttribute("aply", aplyEntity);
        model.addAttribute("serviceAply", serviceAplyEntity);
        model.addAttribute("chargeNodeInfo", eFormSignService.getChargeNodeInfo(serialno));
        model.addAttribute("file", tPubFileobjectService.findByIds(aplyEntity.getAttachids(), request.getContextPath()));
        return "buessness/audit/tempSecurityCostAply/listFormView";
    }

    /**
     * 詳細查詢頁面 郵件
     *
     * @Author: ********
     * @CreateDate: 2022-07-13
     * @Return
     */
    @RequestMapping(value = "viewMail/{serialno}", method = RequestMethod.GET)
    public String viewFormMail(@PathVariable("serialno") String serialno,
                               Model model, HttpServletRequest request) {
        TempSecurityCostAplyEntity aplyEntity = aplyService.findBySerialno(serialno);
        TempSecurityAplyEntity serviceAplyEntity = serviceAplyService.findBySerialno(aplyEntity.getServiceSerialno());
        model.addAttribute("aply", aplyEntity);
        model.addAttribute("serviceAply", serviceAplyEntity);
        model.addAttribute("chargeNodeInfo", eFormSignService.getChargeNodeInfo(serialno));
        model.addAttribute("file", tPubFileobjectService.findByIds(aplyEntity.getAttachids(), request.getContextPath()));
        return "buessness/audit/tempSecurityCostAply/listFormView";
    }

    /**
     * 駁回處理頁面
     *
     * @Author: ********
     * @CreateDate: 2022-12-22
     * @Return
     */
    @RequestMapping(value = "rejectHandle/{serialno}", method = RequestMethod.GET)
    public String rejectHandle(@PathVariable("serialno") String serialno,
                               Model model, HttpServletRequest request) {
        TempSecurityCostAplyEntity aplyEntity = aplyService.findBySerialno(serialno);
        TempSecurityAplyEntity serviceAplyEntity = serviceAplyService.findBySerialno(aplyEntity.getServiceSerialno());
        model.addAttribute("aply", aplyEntity);
        model.addAttribute("serviceAply", serviceAplyEntity);
        model.addAttribute("chargeNodeInfo", eFormSignService.getChargeNodeInfo(serialno));
        model.addAttribute("file", tPubFileobjectService.findByIds(aplyEntity.getAttachids(), request.getContextPath()));
        return "buessness/audit/tempSecurityCostAply/listFormRejectHandle";
    }

    /**
     * 審核頁面 郵件
     *
     * @Author: ********
     * @CreateDate: 2022-07-13
     * @Return
     */
    @RequestMapping(value = "auditMail/{serialno}", method = RequestMethod.GET)
    public String auditFormMail(@PathVariable("serialno") String serialno,
                                Model model, HttpServletRequest request) {
        String applyStat = request.getParameter("applyStat");
        String utoken = request.getParameter("utoken");

        EFormSignEntity currentNode = eFormSignService.currentNode(serialno);
        if (currentNode != null) {
            if (currentNode.getSignOrd().toString().equals(applyStat)
                    && currentNode
                    .getSignOrd()
                    .toString()
                    .equals(tPubMailrecordService
                            .findByValidStr(utoken).getOrderstatus())) {

                TempSecurityCostAplyEntity aplyEntity = aplyService
                        .findBySerialno(serialno);
                TempSecurityAplyEntity serviceAplyEntity = serviceAplyService.findBySerialno(aplyEntity.getServiceSerialno());
                model.addAttribute("aply", aplyEntity);
                model.addAttribute("serviceAply", serviceAplyEntity);

                Gtasks nextTask =new Gtasks();
                Gtasks previousTask =new Gtasks();
                List<Gtasks> listTask = eFormSignService.getTaskAllMail(eFormSignService
                        .currentNode(serialno).getEserId(), "", "");
                for (int j = 0; j < listTask.size(); j++) {
                    if (listTask.get(j).getSerialno().equals(serialno)&&(j!=listTask.size()-1) ) {
                        nextTask=  listTask.get(j+1);
                    }
                    if (listTask.get(j).getSerialno().equals(serialno)&&(j!=0) ) {
                        previousTask=listTask.get(j-1);
                    }
                }
                model.addAttribute("nextTask", nextTask);
                model.addAttribute("previousTask", previousTask);

                model.addAttribute("chargeNodeInfo",
                        eFormSignService.getChargeNodeInfo(serialno));
                model.addAttribute("applyStat",
                        eFormSignService.currentNode(serialno).getSignOrd());

                model.addAttribute("formType", formType);

                model.addAttribute("auditType", "auditMail");

                model.addAttribute("file", tPubFileobjectService.findByIds(aplyEntity.getAttachids(), request.getContextPath()));
                return "buessness/audit/tempSecurityCostAply/listFormAudit";
            } else {
                return "system/login";
            }
        } else {
            return "system/login";
        }
    }

    /**
     * 審核頁面
     *
     * @Author: ********
     * @CreateDate: 2022-07-13
     * @Return
     */
    @RequestMapping(value = "audit/{serialno}", method = RequestMethod.GET)
    public String auditForm(@PathVariable("serialno") String serialno,
                            Model model, HttpServletRequest request) {
        TempSecurityCostAplyEntity aplyEntity = aplyService.findBySerialno(serialno);
        TempSecurityAplyEntity serviceAplyEntity = serviceAplyService
                .findBySerialno(aplyEntity.getServiceSerialno());

        model.addAttribute("aply", aplyEntity);
        model.addAttribute("serviceAply", serviceAplyEntity);

        Gtasks nextTask =new Gtasks();
        Gtasks previousTask =new Gtasks();
        List<Gtasks> listTask = eFormSignService.getTaskAll(eFormSignService
                .currentNode(serialno).getEserId(), "", "");
        for (int j = 0; j < listTask.size(); j++) {
            if (listTask.get(j).getSerialno().equals(serialno)&&(j!=listTask.size()-1) ) {
                nextTask=  listTask.get(j+1);
            }
            if (listTask.get(j).getSerialno().equals(serialno)&&(j!=0) ) {
                previousTask=listTask.get(j-1);
            }

        }
        model.addAttribute("nextTask", nextTask);
        model.addAttribute("previousTask", previousTask);

        model.addAttribute("chargeNodeInfo",
                eFormSignService.getChargeNodeInfo(serialno));
        model.addAttribute("applyStat", eFormSignService.currentNode(serialno)
                .getSignOrd());
        // 設置特定節點的序號
        List<Integer> securityComSelectableOrder = eBsCodeService.getConfigedSignOrder(formType, "LSAB_securityCom_selectable", aplyEntity.getSerialno());
        List<Integer> hasCostSelectableOrder = eBsCodeService.getConfigedSignOrder(formType, "LSAB_hasCost_selectable", aplyEntity.getSerialno());
        model.addAttribute("securityComSelectableOrder", securityComSelectableOrder);
        model.addAttribute("hasCostSelectableOrder", hasCostSelectableOrder);
        model.addAttribute("formType", formType);
        model.addAttribute("file", tPubFileobjectService.findByIds(aplyEntity.getAttachids(), request.getContextPath()));
        return "buessness/audit/tempSecurityCostAply/listFormAudit";
    }

    /**
     * 審核保存
     *
     * @Author: ********
     * @CreateDate: 2022-07-13
     * @Return
     */
    @RequestMapping(value = "auditSave/{flag}", method = RequestMethod.POST)
    @ResponseBody
    public String auditInfo(@Valid TempSecurityCostAplyDto dto, Model model,
                            HttpServletRequest request,
                            @RequestParam(value = "ids", required = false) String ids,
                            @PathVariable("flag") String flag) {
        String clientIP = IPUtil.getIpAddress(request);
        String memo = request.getParameter("memo");
        String signOrd = request.getParameter("applyStatRe");
        String result = aplyService.auditProcess(
                dto.getAplyEntity(), flag, signOrd, clientIP, memo, CommonUtils.getServerBaseURL(request));
        if (result.equals(Constant.RESULT.CODE_YES.getValue())) {
            return Constant.SUCCESS;
        } else if (result.equals(Constant.RESULT.CODE_COMMIT.getValue())) {
            return Constant.FAIL_AUDIT_NODE_PROCESSED;
        } else {
            return Constant.FAIL;
        }
    }
}
