package com.foxconn.ipebg.common.utils;

import java.util.*;

/**
 * Company foxconn
 * Copyright (C) 2004-2018 All Rights Reserved.
 */
public class ListUtils {
    public static List removeDuplicate(List list){
        HashSet h = new HashSet<>(list);
        list.clear();;
        list.addAll(h);
        return list;
    }

    public static void removeDuplicateWithOrder(List list) {
        Set set = new HashSet();
        List newList = new ArrayList();
        for (Iterator iter = list.iterator(); iter.hasNext();) {
            Object element = iter.next();
            if (set.add(element)) {
                newList.add(element);
            }
        }
        list.clear();
        list.addAll(newList);
        System.out.println( " remove duplicate " + list);
    }
}
