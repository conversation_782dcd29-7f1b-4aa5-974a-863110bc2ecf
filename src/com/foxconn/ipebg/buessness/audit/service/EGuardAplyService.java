package com.foxconn.ipebg.buessness.audit.service;

import com.foxconn.ipebg.audit.entity.EFormSignEntity;
import com.foxconn.ipebg.audit.service.EBsCodeService;
import com.foxconn.ipebg.audit.service.EFormSignService;

import com.foxconn.ipebg.basics.entity.BsPostEntity;
import com.foxconn.ipebg.basics.entity.BsPostHistoryEntity;
import com.foxconn.ipebg.basics.service.*;
import com.foxconn.ipebg.buessness.audit.dao.EGuardAplyDao;
import com.foxconn.ipebg.buessness.audit.entity.EGuardAplyEntity;
import com.foxconn.ipebg.buessness.audit.entity.EGuardItemAplyEntity;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;

import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import com.foxconn.ipebg.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.utils.Constant;

import com.foxconn.ipebg.system.service.DictService;
import com.foxconn.ipebg.system.service.TPubMailrecordService;
import com.foxconn.ipebg.system.utils.UserUtil;

import java.util.Date;
import java.util.List;

/**
 * 警衛服務申請主表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-02-28 09:55:52
 */
@Service
@Transactional(readOnly = true)
public class EGuardAplyService extends
		BaseBusinessService<EGuardAplyEntity, String> {
	@Autowired
	private EGuardAplyDao eGuardAplyDao;
	@Autowired
	private ESignUserinfoService eSignUserinfoService;
	@Autowired
	private EFormSignService eFormSignService;
	@Autowired
	private EGuardItemAplyService eGuardItemAplyService;
	@Autowired
	private UserServiceUtil serviceUtil;
	@Autowired
	private BsPostService bsPostService;
	@Autowired
	private EBsCodeService eBsCodeService;
	@Autowired
	private BsPostHistoryService postHistoryService;
	@Autowired
	private BsPostPatchService postPatchService;

	@Autowired
	private TPubMailrecordService mailrecordService;

	public static final String EGUARDITEMERRORDTOS = "eguarditemerrordtos";

	@Override
	public HibernateDao<EGuardAplyEntity, String> getEntityDao() {
		return eGuardAplyDao;
	}

	public EGuardAplyEntity findBySerialno(String serialno) {
		return this.eGuardAplyDao.findUniqueBy("serialno", serialno);
	}

	/**
	 * 方法描述: 啟動流程
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019-02-23 13:46:08
	 * @Return
	 **/
	@Transactional
	public String startProcess(EGuardAplyEntity eGuardAply,
			EGuardItemAplyEntity[] eGuardItemAplyEntity, EFormSignEntity[] eFormSignEntities, String formType, String flag, String contextPath) {
		try {
			// 設置流水號
			eGuardAply.setCreatetime(new Date());
			if (flag.equals(Constant.RESULT.CODE_RUNNING.getValue())) {
				if (StringUtils.isEmpty(eGuardAply.getSerialno())
						|| eGuardAply.getSerialno() == null) {
					eGuardAply.setNewRecord(true);
					eGuardAply.setSerialno(serviceUtil.createSerialno("JWFW"));
				} else {
					eGuardAply.setNewRecord(false);
					// 如果是修改先刪除再保存子表信息
					eGuardItemAplyService.batchDelete(eGuardAply.getSerialno());
				}
				eGuardAply.setSignEmpNo(eFormSignEntities[0].getEserId());
				eGuardAply.setSignEmpNam(eFormSignEntities[0].getEserNam());
				eGuardAply.setWorkstatus(Constant.RESULT.CODE_RUNNING
						.getValue());
				this.save(eGuardAply);

				User currentUser = UserUtil.getCurrentUser();
				// 保存子表信息
                //創新崗位
				if (eGuardItemAplyEntity != null
						&& eGuardAply.getRequirememtType().equals("0")) {

					for (int i = 0; i < eGuardItemAplyEntity.length; i++) {
						eGuardItemAplyEntity[i].setSerialno(eGuardAply
								.getSerialno());
						eGuardItemAplyEntity[i].setDelFlag("0");
						eGuardItemAplyEntity[i].setRecno(serviceUtil
								.createSerialno("GWBM"));// 自動生成項目編碼
						if (currentUser != null) {
							eGuardItemAplyEntity[i].setDptId(currentUser.getDelFlag());
						}
						eGuardItemAplyService.save(eGuardItemAplyEntity[i]);
					}
				}
				//變更崗位
				if (eGuardItemAplyEntity != null
						&& eGuardAply.getRequirememtType().equals("1")) {
					for (int i = 0; i < eGuardItemAplyEntity.length; i++) {
						eGuardItemAplyEntity[i].setSerialno(eGuardAply
								.getSerialno());
						eGuardItemAplyEntity[i].setDelFlag("1");// 代表岗位异动申请中
						if (currentUser != null) {
							eGuardItemAplyEntity[i].setDptId(currentUser.getDelFlag());
						}
						eGuardItemAplyService.save(eGuardItemAplyEntity[i]);
					}
				}
				// 臨時增撤崗
				if (eGuardItemAplyEntity != null
						&& eGuardAply.getRequirememtType().equals("2")) {
					for (int i = 0; i < eGuardItemAplyEntity.length; i++) {
						eGuardItemAplyEntity[i].setSerialno(eGuardAply
								.getSerialno());
						eGuardItemAplyEntity[i].setDelFlag("4");
						if (currentUser != null) {
							eGuardItemAplyEntity[i].setDptId(currentUser.getDelFlag());
						}
						eGuardItemAplyService.save(eGuardItemAplyEntity[i]);
					}
				}
				String applyStat = "";//第一個審核節點狀態
				String signEmpNo = "", signEmpNam = "";
				for (int i = 0; i < eFormSignEntities.length; i++) {
					EFormSignEntity node = eFormSignEntities[i];
					if (node.getEserId().isEmpty()) {
						continue;
					}
					if (applyStat == "") {
						applyStat = node.getSignOrd().toString();
						signEmpNo = node.getEserId();
						signEmpNam = node.getEserNam();
					}
					node.setApplyId(eGuardAply.getSerialno());
					node.setEserMail(eSignUserinfoService
							.findByeserIdTypNodeName(node.getEserId(),
									formType, node.getSignOrd().toString())
							.getEserMail());
					node.setCreateDate(new Date());
					node.setSignNam(eBsCodeService.getCode(formType, node.getSignOrd()).getCodeNam());
					eFormSignService.save(node);
				}
				// 初始化主表表單狀態
				auditUpdateGuardAply(eGuardAply.getSerialno(), applyStat,
						signEmpNo, signEmpNam);
				mailrecordService.sendMail(eGuardAply, contextPath, "Y");
			}
		} catch (Exception e) {
			this.logger.info(e.getMessage(), e);
			return Constant.RESULT.CODE_NO.getValue();
		}
		return Constant.RESULT.CODE_YES.getValue();
	}

	@Transactional
	public String auditProcess(EGuardAplyEntity eAplyEntity,
			EGuardItemAplyEntity[] eGuardItemAplyEntity, String flag, String signOrd,
			String clientIP, String memo, String formType, String contextPath) {

		try {
			String minStatWait = "", maxStatWait = "";
			EFormSignEntity currentNode = eFormSignService
					.currentNode(eAplyEntity.getSerialno());
			if (!signOrd.equals(currentNode.getSignOrd().toString())) {
				return Constant.RESULT.CODE_COMMIT.getValue();
			}
			// 同意
			if (flag.equals("Y") && !eAplyEntity.getSerialno().equals(null)) {
				minStatWait = currentNode.getSignOrd().toString();
				maxStatWait = eFormSignService.maxStatWait(eAplyEntity
						.getSerialno());
				if (!minStatWait.equals("")) {
					List<Integer> siteSurveyOrder = eBsCodeService.getConfigedSignOrder(formType, "JWFW_site_survey");
					List<Integer> positionConfirmOrder = eBsCodeService.getConfigedSignOrder(formType, "JWFW_post_confirm");
					if (siteSurveyOrder.contains(Integer.parseInt(minStatWait))) {
						// 現場勘察說明變更
						auditUpdateGuardSafeManagement(
								eAplyEntity.getSerialno(),
								eAplyEntity.getSafeManagement(),
								eAplyEntity.getManpowerDemand(),
								eAplyEntity.getSafetyFacilities());
					}
					if (positionConfirmOrder.contains(Integer.parseInt(minStatWait))) {
						// 變更派駐保安公司
						eGuardItemAplyService.auditUpdateGuardSecurityCom(
								eAplyEntity.getSerialno(),
								eGuardItemAplyEntity[0].getSecurityCom());
					}
					eFormSignService.auditUpdate(eAplyEntity.getSerialno(),
							minStatWait, clientIP, "Y", memo);
					EFormSignEntity nextNode = eFormSignService
							.currentNode(eAplyEntity.getSerialno());
					String signEmpNo = "", signEmpNam = "";

					if (minStatWait.equals(maxStatWait))
						minStatWait = "E";
					if (nextNode != null) {
						signEmpNo = nextNode.getEserId();
						signEmpNam = nextNode.getEserNam();
						minStatWait = nextNode.getSignOrd().toString();
					}
					auditUpdateGuardAply(eAplyEntity.getSerialno(),
							minStatWait, signEmpNo, signEmpNam);
					// 審核結束時把崗位信息批量拋到崗位表里
					if (minStatWait == "E") {
						if (eAplyEntity.getRequirememtType().equals("0")) {
							// 增崗
							// 更新生效時間
							eGuardItemAplyService.auditUpdateGuardPostEffectDate(eAplyEntity.getSerialno(),"2");

							List<EGuardItemAplyEntity> itemEntity = eGuardItemAplyService
									.findItemEntityes(eAplyEntity.getSerialno());
							for (EGuardItemAplyEntity guardItemAplyEntity : itemEntity) {
								BsPostEntity bsPostEntity = new BsPostEntity();
								bsPostEntity.setAssignCreateBy(false);
								bsPostEntity.setSerialno(guardItemAplyEntity
										.getSerialno());
								bsPostEntity.setRecno(guardItemAplyEntity
										.getRecno());
								bsPostEntity.setArea(guardItemAplyEntity
										.getArea());
								bsPostEntity.setBlock(guardItemAplyEntity
										.getBlock());
								bsPostEntity.setFloor(guardItemAplyEntity
										.getFloor());
								bsPostEntity.setPosition(guardItemAplyEntity
										.getPosition());
								bsPostEntity.setLocation(guardItemAplyEntity
										.getLocation());
								bsPostEntity.setPostType(guardItemAplyEntity
										.getPostType());
								bsPostEntity.setPostShift(guardItemAplyEntity
										.getPostShift());
								bsPostEntity.setPostName(guardItemAplyEntity
										.getPostName());
								bsPostEntity
										.setPostStartDate(guardItemAplyEntity
												.getPostStartDate());
								bsPostEntity.setPostEndDate(guardItemAplyEntity
										.getPostEndDate());
								bsPostEntity.setSecurityCom(guardItemAplyEntity
										.getSecurityCom());
								bsPostEntity.setPostPerNu(guardItemAplyEntity
										.getPostPerNu());
								bsPostEntity
										.setPostEffectDate(guardItemAplyEntity
												.getPostEffectDate());
								bsPostEntity.setDptId(guardItemAplyEntity.getDptId());
								bsPostService.save(bsPostEntity);

								BsPostHistoryEntity history = guardItemAplyEntity.getHistoryEntity();
								history.setApplyEmpNo(eAplyEntity.getMakerno());
								history.setApplyEmpName(eAplyEntity.getMakername());
								history.setApplyEmpBu(eAplyEntity.getDptBu());
								history.setApplyType(eAplyEntity.getRequirememtType());
								postHistoryService.save(history);
							}
						} else if (eAplyEntity.getRequirememtType().equals("1")) {
							// 批量變更明細表軟刪除狀態
							eGuardItemAplyService.auditUpdateGuardPostEffectDate(eAplyEntity
									.getSerialno(),"3");
							// 從崗位信息表刪除撤崗信息
							List<EGuardItemAplyEntity> eGuardItemAplyEntityList=eGuardItemAplyService.findItemEntityes(eAplyEntity.getSerialno());
							for (EGuardItemAplyEntity eGuardItemAply : eGuardItemAplyEntityList)
							{
								// 保存修改记录
								BsPostHistoryEntity history = eGuardItemAply.getHistoryEntity();
								history.setApplyEmpNo(eAplyEntity.getMakerno());
								history.setApplyEmpName(eAplyEntity.getMakername());
								history.setApplyEmpBu(eAplyEntity.getDptBu());
								history.setApplyType(eAplyEntity.getRequirememtType());
								BsPostEntity oldPost = bsPostService.findByRecno(eGuardItemAply.getRecno());
								if (oldPost != null) {
									history.setSecurityCom(oldPost.getSecurityCom());
								}
								postHistoryService.save(history);
								//如果撤崗數變更為0 刪除崗位 否則更新崗位
								if (eGuardItemAply.getPostPerNu().toString().equals("0")) {
									// 刪除崗位
									bsPostService.batchDelete(eGuardItemAply.getRecno());
								} else {
									// 更新崗位
									bsPostService.updatePostByGuardAplyItem(eGuardItemAply);
								}
							}
						} else if (eAplyEntity.getRequirememtType().equals("2")) {
							// 临时增撤岗
							// 批量變更明細表軟刪除狀態
							eGuardItemAplyService.auditUpdateGuardPostEffectDate(eAplyEntity
									.getSerialno(),"5");
							List<EGuardItemAplyEntity> eGuardItemAplyEntityList=eGuardItemAplyService.findItemEntityes(eAplyEntity.getSerialno());
							for (EGuardItemAplyEntity item : eGuardItemAplyEntityList) {
								BsPostHistoryEntity history = item.getHistoryEntity();
								history.setApplyEmpNo(eAplyEntity.getMakerno());
								history.setApplyEmpName(eAplyEntity.getMakername());
								history.setApplyEmpBu(eAplyEntity.getDptBu());
								history.setApplyType(eAplyEntity.getRequirememtType());
								BsPostEntity oldPost = bsPostService.findByRecno(item.getRecno());
								if (oldPost != null) {
									history.setSecurityCom(oldPost.getSecurityCom());
								}
								postHistoryService.save(history);
							}
						}
						mailrecordService.sendMail(eAplyEntity, contextPath, "E");
					} else {
						mailrecordService.sendMail(eAplyEntity, contextPath, "Y");
					}
				}
			}
			// 駁回
			else if (flag.equals("N")
					&& !eAplyEntity.getSerialno().equals(null)) {

				minStatWait = currentNode.getSignOrd().toString();
				maxStatWait = eFormSignService.maxStatWait(eAplyEntity
						.getSerialno());
				if (!minStatWait.equals("")) {
					eFormSignService.auditUpdate(eAplyEntity.getSerialno(),
							minStatWait, clientIP, "N", memo);
					String signEmpNo = "", signEmpNam = "";
					eFormSignService.batchDeleteWait(eAplyEntity.getSerialno());
					auditUpdateGuardAply(eAplyEntity.getSerialno(), "N",
							signEmpNo, signEmpNam);
					eGuardItemAplyService
					.delFlagUpdateBySerialno(eAplyEntity
							.getSerialno(),"N");
				}

				mailrecordService.sendMail(eAplyEntity, contextPath, "N");
			}

		} catch (Exception e) {
			this.logger.info(e.getMessage(), e);
			return Constant.RESULT.CODE_NO.getValue();
		}
		return Constant.RESULT.CODE_YES.getValue();

	}

	// 審核變更申請主表審核狀態
	public int auditUpdateGuardAply(String serialno, String applyStat,
			String signEmpNo, String signEmpNam) {

		String hql = "update EGuardAplyEntity t  set " + "t.applyStat = '"
				+ applyStat + "',t.signEmpNo = '" + signEmpNo + "',"
				+ "t.signEmpNam   = '" + signEmpNam + "'  "
				+ "where  t.serialno='" + serialno + "' ";
		return this.eGuardAplyDao.batchExecute(hql);

	}

	// 勘察人員說明
	public int auditUpdateGuardSafeManagement(String serialno,
			String safeManagement, String manpowerDemand,
			String safetyFacilities) {

		String hql = "update EGuardAplyEntity t  set " + "t.safeManagement = '"
				+ safeManagement + "',t.manpowerDemand = '" + manpowerDemand
				+ "'," + "t.safetyFacilities   = '" + safetyFacilities + "'  "
				+ "where  t.serialno='" + serialno + "' ";
		return this.eGuardAplyDao.batchExecute(hql);

	}

	// 更新附件id
	public int uploadFlie(String serialno, String attachids) {

		String hql = "update EGuardAplyEntity t  set " + "t.attachids = '"
				+ attachids + "',t.updateBy = '"
				+ UserUtil.getCurrentUser().getLoginName() + "',"
				+ "t.updateDate   = ?0 " + "where  t.serialno='" + serialno
				+ "' ";
		return this.eGuardAplyDao.batchExecute(hql, new Date());

	}

}
