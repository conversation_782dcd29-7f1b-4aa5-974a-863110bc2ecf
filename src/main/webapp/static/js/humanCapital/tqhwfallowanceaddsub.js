var dg;
var d;
$(function () {
    dg = $('#dg').datagrid({
        method: "get",
        url: ctx + '/tqhwfallowanceaddsub/list',
        fit: true,
        fitColumns: true,
        border: false,
        idField: 'id',
        striped: true,
        pagination: true,
        rownumbers: true,
        pageNumber: 1,
        pageSize: 20,
        pageList: [10, 20, 30, 40, 50],
        singleSelect: true,
        columns: [[
            {field: 'id', title: '主鍵', hidden: true},
            {field: 'serialno', title: '任務編號', sortable: true, width: 150, formatter: operation},
            {field: 'applyType', title: '申請類型', sortable: true, width: 50},
            {field: 'dealno', title: '承辦人', sortable: true, width: 50},
            {field: 'dealdeptno', title: '單位代碼', sortable: true, width: 60},
            {field: 'dealdeptname', title: '單位名稱', sortable: true, width: 200},
            {field: 'createtime', title: '填單時間', sortable: true, width: 100},
            {field: 'workstatus', title: '任務狀態', sortable: true, width: 50},
            {field: 'nodeName', title: '當前簽核節點', sortable: true, width: 100},
            {field: 'auditUser', title: '當前簽核人', sortable: true, width: 80},
            {field: 'complettime', title: '簽核完成時間', sortable: true, width: 100}
        ]],
        enableHeaderClickMenu: true,
        enableHeaderContextMenu: true,
        enableRowContextMenu: false,
        rowTooltip: true,
        toolbar: '#tb'
    });

    //獲取廠區
    $.get(ctx + '/tqhfactoryidconfig/allFactorys/',
        function (result) {
            $("#dealfactoryid").combobox({
                data: result,
                valueField: "factoryid",
                textField: "factoryname",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({factoryid: '', factoryname: '請選擇'});
                    return data;
                }
            });
        });
    //申請類型
    $.ajax({
        url: ctx + "/system/dict/getDictByType/allowance_type",
        type: "GET",
        success: function (data) {
            $("#applyType").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({value: '', label: '請選擇'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });
    $.ajax({
        url: ctx + "/system/dict/getDictByType/audit_status",
        //dataType:"json",
        type: "GET",
        success: function (data) {
            //绑定第一个下拉框
            $("#qysjzt").combobox({
                data: data,
                valueField: "value",
                textField: "label",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                    data.unshift({
                        value: '',
                        label: '請選擇'
                    });
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });
});
//申請內容不能超過200漢字檢測
function LessThan(oTextArea){
    //获得textarea的maxlength属性
    var MaxLength=oTextArea.getAttribute("maxlength");
    var num=MaxLength-oTextArea.value.length;
    if(num==0){
        $('#txtNum').hide();
        $('#txtNum').html("");
    }else{
        $('#txtNum').show();
        $('#txtNum').html("<font font-size='13px' color='red'>还能输入："+num+"字</font>");
    }
    //返回文本框字符个数是否符号要求的boolean值
    return oTextArea.value.length < oTextArea.getAttribute("maxlength");
}
//格式化
function operation(value, row, index) {
    if (row.workstatus == 0) {
        return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('津貼加減項申請單',ctx+'/tqhwfallowanceaddsub/create/" + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
    } else if (row.workstatus == 4) {
        return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('津貼加減項申請單',ctx+'/tqhwfallowanceaddsub/reject/" + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
    }
    return "<a href=\"#\" onclick=\"window.parent.mainpage.mainTabs.addModule('津貼加減項申請單詳情',ctx+'/tqhwfallowanceaddsub/view/" + row.serialno + "','icon-hamburg-basket')\">" + value + "</a>";
};

//添加主管審核
function addHq() {
    var html = "<tr align='center'>"
        + "<td><input name='hchargeno' style='width: 80px' value='' onblur='getUserNameByEmpno(this);'/>/<input id='hchargename' name='hchargename' style='width: 80px'  value='' readonly/>"
        + '<input type="image" src="' + ctx + '/static/images/deleteRow.png" onclick="deleteRow(this);return false;"/></td>'
        + "</tr>";
    $("#hchargenoTable").append(html);
}

//刪除一筆
function deleteRow(obj) {
    debugger;
    if (confirm("確定刪除此筆記錄？")) {
        $(obj).parent().parent().remove();
    }
}

//根據工號查詢用戶名
function getUserNameByEmpno(obj) {
    var empno = $.trim($("#dealno").val());
    if (empno == null && empno == "") {
        return;
    }
    var row = obj.parentNode.parentNode.rowIndex + 1;
    debugger;
    $.ajax({
        url: ctx + '/system/user/getUserInfo',
        type: 'POST',
        beforeSend: ajaxLoading,
        data: {empno: obj.value},
        success: function (data) {
            if (!data) {
                $.messager.alert("溫馨提示", "工號不存在", "error");
                $("#hchargenoTable tr:eq(" + row + ")").find("#hchargename").val("");
            } else {
                $("#hchargenoTable tr:eq(" + row + ")").find("#hchargename").val(data.empname);
                debugger;
            }
            ajaxLoadEnd();
        }
    });
}

//弹窗增加
function saveInfo(flag) {
    if (flag == '2') {
        var isValid = $("#mainform").form('validate');
        if($("#applyType").combobox("getValue")==""){
            $("#applyType").combobox('textbox').focus();
            return;
        }
        if (!valdFile()) {
            return;
        }
        if (!isValid) {
            return;
        }
    }
    $.ajax({
        url: ctx + '/tqhwfallowanceaddsub/create/' + flag,
        type: 'POST',
        beforeSend: ajaxLoading,
        //dataType: 'json',
        data: $('#mainform').serialize(),
        success: function (data) {
            ajaxLoadEnd();
            successTip(data, dg, d);
            window.parent.mainpage.mainTabs.closeCurrentTab();
        }
    });
}
//提交時驗證當申請類別為物品新購時需要上傳附件
function valdFile(){
    var attachIds = $.trim($("#attachids").val());
    if(attachIds==''){
        $.messager.alert("溫馨提示", "請上傳附件", "info");
        return false;
    }
    return true;
}
//删除
function del() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function (data) {
        if (data) {
            $.ajax({
                type: 'get',
                url: ctx + "tqhwfallowanceaddsub/delete/" + row.id,
                success: function (data) {
                    successTip(data, dg);
                }
            });
        }
    });
}

//弹窗修改
function upd() {
    var row = dg.datagrid('getSelected');
    if (rowIsNull(row)) return;
    d = $("#dlg").dialog({
        title: '修改津貼加減項申請單',
        width: 380,
        height: 340,
        href: ctx + 'tqhwfallowanceaddsub/update/' + row.id,
        maximizable: true,
        modal: true,
        buttons: [{
            text: '修改',
            handler: function () {
                $('#mainform').submit();
            }
        }, {
            text: '取消',
            handler: function () {
                d.panel('close');
            }
        }]
    });
}

//创建查询对象并查询
function cx() {
    var obj = $("#searchFrom").serializeObject();
    dg.datagrid('load', obj);
}

//根據工號查出當前申請人的信息
function queryUserInfo() {
    var empno = $.trim($("#dealno").val());
    if (empno == null && empno == "") {
        return;
    }
    $.ajax({
        url: ctx + '/system/user/getUserInfo/',
        type: 'POST',
        beforeSend: ajaxLoading,
        //dataType: 'json',
        data: {empno: empno},
        success: function (data) {
            ajaxLoadEnd();
            if (!data) {
                $.messager.alert("溫馨提示", "工號不存在", "error");
                $('#dealno').val('');
                $('#dealname').val('');
                $('#dealdeptno').val('');
                $('#dealdeptname').val('');
                $('#dealemail').val('');
                $('#dealfactoryid').combobox('setValue', '');
                $('#dealfactoryidForSelect').val('');
                $('#dealtel').val('');
            } else {
                $('#dealname').val(data.empname);
                $('#dealdeptno').val(data.deptno);
                $('#dealdeptname').val(data.deptname);
                $('#dealfactoryid').combobox('setValue', data.factoryid);
                $('#dealfactoryidForSelect').val(data.factoryid);
                $('#dealemail').val(data.email);
                $('#dealtel').val(data.phone);
            }
        }
    });
}

//表單進度查詢頁面查詢條件重置
function listSearchReset() {
    $("#searchFrom").form("reset");
}

//导出excel
function exportExcel() {
    var form = document.getElementById("searchFrom");
    searchFrom.action = ctx + '/tqhwfallowanceaddsub/exportExcel';
    form.submit();
}

//附件上傳
function uploadFile() {
    var attachIds = $.trim($("#attachids").val());
    var attachIdsL = attachIds.split(",").length;
    if (attachIdsL >= 6) {
        alert("上傳附件個數不能超過五個！");
        return;
    }
    // 创建
    var form_data = new FormData();
    // 获取文件
//        var file = document.getElementById('fileToUpload').files[0];
    var file_data = $("#attachidsUpload").prop("files")[0];
    form_data.append("file", file_data);
    form_data.append("path", "test");
    // 把所以表单信息
    $.ajax({
        type: "POST",
        url: ctx + "/watermark/upload",
//            dataType : "json",
        beforeSend: ajaxLoading,
        processData: false,  // 注意：让jQuery不要处理数据
        contentType: false,  // 注意：让jQuery不要设置contentType
        data: form_data
    }).success(function (msg) {
        ajaxLoadEnd();
        $("#dowloadUrl").append('&nbsp;&nbsp;&nbsp;<div class="float_L" id="' + msg.id + '" style="line-height:30px;margin-left:5px;"><div class="float_L"><a href=' + ctx + '"/admin/download/' + msg.id + '">' + msg.name + '</a></div><div class="float_L deleteBtn" onclick="delAtt(\'' + msg.id + '\')"></div></div>');
        $("#attachids").val($("#attachids").val() + msg.id + ",");
        // $("#attachids").val(msg.id);
    }).fail(function (msg) {
    });
}

function delAtt(fileid) {
    $("#" + fileid).remove();
    var kk = $("#attachids").val();
    kk = kk.replace(fileid + ",", "");
    $("#attachids").val(kk);
    $.post(ctx + '/watermark/delete/' + fileid, {}, function (data) {
        console.log(data);
        successTip(data);
    });
}

function printWindow_allowance(buttonNames) {
    var buttonNameArray = buttonNames.split(",");
    for (var i = 0; i < buttonNameArray.length; i++) {
        $("#" + buttonNameArray[i]).css("display", "none");
    }
    // document.getElementsByTagName('body')[0].style.zoom=0.85;
    //添加水印
    watermark({watermark_txt: "CAA太原周邊薪資\nFOXCONN"});
    window.print();
    //隱藏水印
    $('.mask_div').hide();
    for (var i = 0; i < buttonNameArray.length; i++) {
        $("#" + buttonNameArray[i]).css("display", "");
    }
    return false;
}

/**
 * 添加水印
 * @param settings
 */
function watermark(settings) {
    var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串

    //默认设置
    var defaultSettings = {
        watermark_txt: "text",
        watermark_x: 20,//水印起始位置x轴坐标
        watermark_y: 20,//水印起始位置Y轴坐标
        watermark_rows: 0,//水印行数
        watermark_cols: 0,//水印列数
        watermark_x_space: 100,//水印x轴间隔
        watermark_y_space: 50,//水印y轴间隔
        watermark_color: '#000000',//水印字体颜色
        watermark_alpha: 0.3,//水印透明度
        watermark_fontsize: '18px',//水印字体大小
        watermark_font: '微软雅黑',//水印字体
        watermark_width: 150,//水印宽度
        watermark_height: 50,//水印长度
        watermark_angle: 20//水印倾斜度数
    };
    //采用配置项替换默认值，作用类似jquery.extend
    if (arguments.length === 1 && typeof arguments[0] === "object") {
        var src = arguments[0] || {};
        for (key in src) {
            if (src[key] && defaultSettings[key] && src[key] === defaultSettings[key])
                continue;
            else if (src[key])
                defaultSettings[key] = src[key];
        }
    }

    var oTemp = document.createDocumentFragment();

    //获取页面最大宽度
    var page_width = $(".commonW").width();
    //获取页面最大长度
    var page_height = $(".commonW").height() + 20;

    //获取页面最大宽度
    // var page_width = Math.max($(".commonW").scrollWidth, $(".commonW").width());
    //获取页面最大长度
    // var page_height = Math.max($(".commonW").scrollHeight, $(".commonW").height());
    debugger;
    //如果将水印列数设置为0，或水印列数设置过大，超过页面最大宽度，则重新计算水印列数和水印x轴间隔
    if (defaultSettings.watermark_cols == 0 || (parseInt(defaultSettings.watermark_x + defaultSettings.watermark_width * defaultSettings.watermark_cols + defaultSettings.watermark_x_space * (defaultSettings.watermark_cols - 1)) > page_width)) {
        defaultSettings.watermark_cols = parseInt((page_width - defaultSettings.watermark_x + defaultSettings.watermark_x_space) / (defaultSettings.watermark_width + defaultSettings.watermark_x_space));
        defaultSettings.watermark_x_space = parseInt((page_width - defaultSettings.watermark_x - defaultSettings.watermark_width * defaultSettings.watermark_cols) / (defaultSettings.watermark_cols - 1));
    }
    //如果将水印行数设置为0，或水印行数设置过大，超过页面最大长度，则重新计算水印行数和水印y轴间隔
    if (defaultSettings.watermark_rows == 0 || (parseInt(defaultSettings.watermark_y + defaultSettings.watermark_height * defaultSettings.watermark_rows + defaultSettings.watermark_y_space * (defaultSettings.watermark_rows - 1)) > page_height)) {
        defaultSettings.watermark_rows = parseInt((defaultSettings.watermark_y_space + page_height - defaultSettings.watermark_y) / (defaultSettings.watermark_height + defaultSettings.watermark_y_space));
        defaultSettings.watermark_y_space = parseInt(((page_height - defaultSettings.watermark_y) - defaultSettings.watermark_height * defaultSettings.watermark_rows) / (defaultSettings.watermark_rows - 1));
    }
    var x;
    var y;
    for (var i = 0; i < defaultSettings.watermark_rows; i++) {
        y = defaultSettings.watermark_y + (defaultSettings.watermark_y_space + defaultSettings.watermark_height) * i;
        for (var j = 0; j < defaultSettings.watermark_cols; j++) {
            x = defaultSettings.watermark_x + (defaultSettings.watermark_width + defaultSettings.watermark_x_space) * j;

            var mask_div = document.createElement('div');
            mask_div.id = 'mask_div' + i + j;
            mask_div.className = 'mask_div';
            mask_div.appendChild(document.createTextNode(defaultSettings.watermark_txt));
            //设置水印div倾斜显示
            mask_div.style.webkitTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
            mask_div.style.MozTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
            mask_div.style.msTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
            mask_div.style.OTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
            mask_div.style.visibility = "";
            mask_div.style.position = "absolute";
            mask_div.style.left = x + 'px';
            mask_div.style.top = y + 'px';
            mask_div.style.overflow = "hidden";
            mask_div.style.zIndex = "9999";
            if (userAgent.indexOf("Chrome") > -1) {
                mask_div.style.transform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
                mask_div.style.opacity = defaultSettings.watermark_alpha;
            } else if (userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1) {
                mask_div.style.filter = "alpha(opacity=10)";
                mask_div.style.filter = "progid:DXImageTransform.Microsoft.Matrix(M11=" + defaultSettings.watermark_angle + ",M12=" + defaultSettings.watermark_angle + ",M21=" + defaultSettings.watermark_angle + ",M22=" + defaultSettings.watermark_angle + ",SizingMethod='auto expand')";
            }
            // $("#mask_div").css("opacity","0.3");
            mask_div.style.fontSize = defaultSettings.watermark_fontsize;
            mask_div.style.fontFamily = defaultSettings.watermark_font;
            mask_div.style.color = defaultSettings.watermark_color;
            mask_div.style.textAlign = "center";
            mask_div.style.width = defaultSettings.watermark_width + 'px';
            mask_div.style.height = defaultSettings.watermark_height + 'px';
            mask_div.style.display = "block";
            mask_div.style.pointerEvents = 'none';
            oTemp.appendChild(mask_div);
        }
        ;
    }
    ;
    document.body.appendChild(oTemp);
}
