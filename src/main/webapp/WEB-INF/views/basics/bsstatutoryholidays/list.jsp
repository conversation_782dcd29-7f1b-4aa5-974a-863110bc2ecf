<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>法定節假日表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
    <form id="searchFrom" action="">
        <input type="text" name="filter_LIKES_holidayDesc" class="easyui-validatebox"
               data-options="width:150,prompt: '描述'"/>
        <input type="text" name="filter_GED_holidayDate" class="easyui-my97"
               datefmt="yyyy-MM-dd" data-options="width:150,prompt: '起始日期'"/> -
        <input type="text" name="filter_LED_holidayDate" class="easyui-my97"
               datefmt="yyyy-MM-dd" data-options="width:150,prompt: '截止日期'"/>

        <span class="toolbar-item dialog-tool-separator"></span> <a
            href="javascript(0)" class="easyui-linkbutton" plain="true"
            iconCls="icon-search" onclick="cx()">查询</a>
        <a href="javascript(0)"
           class="easyui-linkbutton" plain="true"
           iconCls="icon-hamburg-refresh" onclick="listSearchReset()">重置</a>
        <br/>
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="add();">添加</a>
        <span class="toolbar-item dialog-tool-separator"></span>
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true"
           data-options="disabled:false" onclick="del()">删除</a>
        <span class="toolbar-item dialog-tool-separator"></span>
        <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true" onclick="upd()">修改</a>
        <span class="toolbar-item dialog-tool-separator"></span>

    </form>

</div>
<table id="dg"></table>
<div id="dlg"></div>

<script src="${ctx}/static/js/basics/bsstatutoryholidays.js?random=<%= Math.random()%>"></script>
</body>
</html>