package com.foxconn.ipebg.basics.entity;

import java.io.Serializable;
import java.util.Date;

public class PerInfoErrorDto implements Serializable {
	private static final long serialVersionUID = 1L;
	// 工號
	private String empNo;
	// 姓名
	private String empName;
	// 公司名稱
	private String company;
	// 身份證號
	private String psnId;
	// 合格日期
	private String passDate;
	// 入職日期
	private String entryDate;
	// 失敗原因
	private String errorLog;

	public String getEmpNo() {
		return empNo;
	}
	
	public void setEmpNo(String empNo) {
		this.empNo = empNo;
	}

	public String getEmpName() {
		return empName;
	}

	public void setEmpName(String empName) {
		this.empName = empName;
	}

	public String getCompany() {
		return company;
	}

	public void setCompany(String company) {
		this.company = company;
	}

	public String getPsnId() {
		return psnId;
	}

	public void setPsnId(String psnId) {
		this.psnId = psnId;
	}

	public String getPassDate() {
		return passDate;
	}

	public void setPassDate(String passDate) {
		this.passDate = passDate;
	}

	public String getErrorLog() {
		return errorLog;
	}

	public void setErrorLog(String errorLog) {
		this.errorLog = errorLog;
	}

	public String getEntryDate() {
		return entryDate;
	}

	public void setEntryDate(String entryDate) {
		this.entryDate = entryDate;
	}
}
