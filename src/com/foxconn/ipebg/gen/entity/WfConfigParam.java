package com.foxconn.ipebg.gen.entity;

import java.io.Serializable;

/**
 * 流程級參數配置信息
 * <AUTHOR>
 *
 */
public class WfConfigParam implements Serializable {
	private static final long serialVersionUID = 1L;
	private String workFlowId;
	private String parameName;
	private String paramValue;
	private String paramType;


	public String getWorkFlowId() {
		return workFlowId;
	}
	public void setWorkFlowId(String workFlowId) {
		this.workFlowId = workFlowId;
	}
	public String getParameName() {
		return parameName;
	}
	public void setParameName(String parameName) {
		this.parameName = parameName;
	}
	public String getParamValue() {
		return paramValue;
	}
	public void setParamValue(String paramValue) {
		this.paramValue = paramValue;
	}
	public String getParamType() {
		return paramType;
	}
	public void setParamType(String paramType) {
		this.paramType = paramType;
	}
}
