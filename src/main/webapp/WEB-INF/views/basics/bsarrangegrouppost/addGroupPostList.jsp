<%--suppress ALL --%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title></title>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
	type="text/javascript"></script>
</head>
<body>
	<div>
		<form id="searchFromPerson" action="">
			<input type="text" id="lableNo" name="lableNo"
				class="easyui-validatebox" data-options="width:60" readonly
				value="排班群组:" />
			<!-- 培訓期: -->
			<input id="addArrangeGroupId" style="width:200px"
				class="easyui-validatebox" name="addArrangeGroupId" /><br /> <input
				type="text" id="postName" name="postName" class="easyui-validatebox"
				data-options="width:150,prompt: '崗位名稱'" />
			<!-- <input type="text"
				id="empName" name="empName" class="easyui-validatebox"
				data-options="width:150,prompt: '姓名'" /> -->
			<!-- <input type="text"
				id="trainingPeriodNo" name="trainingPeriodNo"
				class="easyui-validatebox" data-options="width:150,prompt: '培訓期'" /> -->

			<a href="javascript(0)" class="easyui-linkbutton" plain="true"
				iconCls="icon-search" onclick="cx()">查询</a>
		</form>
	</div>

	<form id="mainformAdd" action="" method="post"></form>
	<table id="getPostList"></table>

	<script type="text/javascript">
		var dataGridPerson;
		var d;
		$(function() {

			//var trainingPeriodNo = $('#trainingPeriodNo').val();
			//var empNo = $('#empNo').val();
			var postName = $('#postName').val();
			var param = {
				"postName" : postName
			};
			var isCheckSort = false;
			dataGridPerson = $('#getPostList')
					.datagrid(
							{
								method : "get",
								url : '${ctx}/bspost/getPostList',
								queryParams : param,
								nowrap : false,
								fit : true,
								fitColumns : true,
								border : false,
								striped : true,
								pagination : false,
								remoteSort : false,
								onClickRow : function(index, row) {
									row.num == 1;
								},
								checkOnSelect : true,
								columns : [ [ {
									field : 'itemid',
									title : 'Order ID',
									width : 200,
									checkbox : true
								}, {
									field : 'recno',
									title : '編號',
									hidden : true
								}, {
									field : 'id',
									title : '資料識別ID  Primary',
									hidden : true
								}, {
									field : 'postName',
									title : '崗位名稱',
									sortable : true,
									width : 120
								}, {
									field : 'area',
									title : '區域',
									sortable : true,
									width : 100
								}, {
									field : 'block',
									title : '棟',
									sortable : true,
									width : 100
								}, {
									field : 'floor',
									title : '層',
									sortable : true,
									width : 100
								} , {
									field : 'postPerNu',
									title : '人數（不含調休）',
									sortable : true,
									width : 100
								}] ],
								onClickRow : function(rowIndex, rowData) {
									//            console.log("onClickRow11111111");
									if (!isCheckSort) {
										//sortByClick('getPostList', rowIndex);
										isCheckSort = false;
									}
								},
								onCheck : function(rowIndex, rowData) {
									var isSelected = false;
									var selectItems = dataGridPerson
											.datagrid('getSelections');
									for ( var i = 0; i < selectItems.length; i++) {
										var index = dataGridPerson.datagrid(
												'getRowIndex', selectItems[i]);
										// console.log(rowIndex);
										if (rowIndex == index) {
											isSelected = true;
										}
									}
									var dr = dataGridPerson.datagrid('getRows')[rowIndex];
									if ((undefined == dr["num"] || "" == dr["num"])
											&& isSelected) {
										//sortByClick('getPostList', rowIndex);
										isCheckSort = true;
									} else {
										isCheckSort = false;
									}
								},
								onUncheck : function(rowIndex, rowData) {
									//            console.log("onUncheck111111111111");
									var isSelected = false;
									var selectItems = dataGridPerson
											.datagrid('getSelections');
									for ( var i = 0; i < selectItems.length; i++) {
										var index = dataGridPerson.datagrid(
												'getRowIndex', selectItems[i]);
										// console.log(rowIndex);
										if (rowIndex == index) {
											isSelected = true;
										}
									}
									var dr = dataGridPerson.datagrid('getRows')[rowIndex];
									if ((undefined == dr["num"] || "" != dr["num"])
											&& !isSelected) {
										//sortByClick('getPostList', rowIndex);
										isCheckSort = true;
									} else {
										isCheckSort = false;
									}
								},
								onUncheckAll : function(rowIndex, rowData) {
									var alllRows = dataGridPerson
											.datagrid('getRows');
									for ( var i = 0; i < alllRows.length; i++) {
										alllRows[i]["num"] = "";
										dataGridPerson
												.datagrid('refreshRow', i);
									}
								},
								onSelectAll : function(rowIndex, rowData) {
									var alllRows = dataGridPerson
											.datagrid('getRows');
									for ( var i = 0; i < alllRows.length; i++) {
										alllRows[i]["num"] = i + 1;
										dataGridPerson
												.datagrid('refreshRow', i);
									}
								}
							});

			$.ajax({
				url : ctx + "/bsarrangegroup/getAllArrangeGroup",
				//dataType:"json",
				type : "GET",
				success : function(data) {
					//绑定第一个下拉框
					$("#addArrangeGroupId").combobox({
						data : data,
						valueField : "arrangeGroupId",
						textField : "arrangeGroupName",
						editable : false,
						panelHeight : 400,
						loadFilter : function(data) {
							data.unshift({
								arrangeGroupId : '',
								arrangeGroupName : '請選擇'
							});
							return data;
						}
					});
				},
				error : function(error) {
					alert("初始化下拉控件失败");
				}
			});

		}

		);
		// 创建查询对象并查询
		function cx() {
			//console.log("ff333");
			var obj = $("#searchFromPerson").serializeObject();
			dataGridPerson.datagrid('load', obj);
		}
	</script>

</body>
</html>