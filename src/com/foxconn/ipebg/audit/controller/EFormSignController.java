package com.foxconn.ipebg.audit.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Date;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.utils.ConvertUtils;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.audit.entity.EFormSignEntity;
import com.foxconn.ipebg.audit.service.EFormSignService;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;
import com.foxconn.ipebg.buessness.workflow.entity.Gtasks;

/**
 * 申請表單審核記錄表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-12-27 08:45:32
 */
@Controller
@RequestMapping("eformsign")
public class EFormSignController extends BaseController {

	@Autowired
	private EFormSignService eFormSignService;

	/**
	 * 方法描述: 列表信息
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-12-27 08:45:32
	 * @Return
	 **/

	@RequestMapping(method = RequestMethod.GET)
	// @RequiresPermissions("audit:eformsign:list")
	public String list() {
		// 查询列表数据
		return "audit/eformsign/list";
	}

	/**
	 * 方法描述: 分頁查詢信息
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-12-27 08:45:32
	 * @Return
	 **/

	@RequestMapping(value = "list", method = RequestMethod.GET)
	// @RequiresPermissions("audit:eformsign:list")
	@ResponseBody
	public Map<String, Object> infoList(HttpServletRequest request) {
		Page<EFormSignEntity> page = getPage(request);
		List<PropertyFilter> filters = PropertyFilter
				.buildFromHttpRequest(request);
		page = eFormSignService.search(page, filters);
		return getEasyUIData(page);
	}

	/**
	 * 方法描述: 保存
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-12-27 08:45:32
	 * @Return
	 **/

	@RequestMapping(value = "create", method = RequestMethod.POST)
	@RequiresPermissions("audit:eformsign:add")
	@ResponseBody
	public String create(@Valid EFormSignEntity eFormSign, Model model) {
		eFormSign.setNewRecord(true);
		eFormSign.setCreateDate(new Date());
		eFormSignService.save(eFormSign);
		return "success";
	}

	/**
	 * 方法描述: 添加跳轉
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-12-27 08:45:32
	 * @Return
	 **/

	@RequiresPermissions("audit:eformsign:add")
	@RequestMapping(value = "create", method = RequestMethod.GET)
	public String createForm(Model model) {
		model.addAttribute("eFormSign", new EFormSignEntity());
		model.addAttribute("action", "create");
		return "audit/eformsign/listForm";
	}

	/**
	 * 方法描述: 修改跳轉
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-06 11:04:39
	 * @Return
	 **/

	@RequiresPermissions("audit:eformsign:update")
	@RequestMapping(value = "update/{id}", method = RequestMethod.GET)
	public String updateForm(@PathVariable("id") String id, Model model) {
		model.addAttribute("eFormSign", eFormSignService.get(id));
		model.addAttribute("action", "update");
		return "audit/eformsign/listForm";
	}

	/**
	 * 方法描述: 修改
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-11 10:55:00
	 * @Return
	 **/

	@RequestMapping(value = "update", method = RequestMethod.POST)
	@RequiresPermissions("audit:eformsign:update")
	@ResponseBody
	public String update(@Valid EFormSignEntity eFormSign,
			@RequestParam(value = "ids") String ids) {
		eFormSign.setId(ids);
		eFormSign.setUpdateDate(new Date());
		eFormSignService.update(eFormSign);

		return "success";
	}

	/**
	 * 方法描述: 根據主鍵刪除
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-12-27 08:45:32
	 * @Return
	 **/

	@RequestMapping("delete/{applyId}")
	@RequiresPermissions("audit:eformsign:delete")
	@ResponseBody
	public String delete(@PathVariable("applyId") String applyId) {
		eFormSignService.delete(applyId);
		return "success";
	}

	/**
	 * 导出excel
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-12-27 08:45:32
	 * @Return
	 **/
	@RequestMapping("exportExcel")
	public void exportExcel(HttpServletRequest request,
			HttpServletResponse response) throws Exception {
	}

	/**
	 * 方法描述: 獲取簽核記錄
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019/01/02 上午 08:21
	 * @Return
	 **/
	@RequestMapping(value = "queryChargeLog", method = RequestMethod.GET)
	@ResponseBody
	public Map<String, Object> queryChargeLog(HttpServletRequest request) {
		Page<EFormSignEntity> page = getPage(request);
		page.setOrderBy("eserDate");
		page.setOrder(Page.DESC);
		List<PropertyFilter> filters = PropertyFilter
				.buildFromHttpRequest(request);
		page = queryChargeLog(page, filters);
		return getEasyUIData(page);
	}

	/**
	 * 方法描述: 跳轉到簽核記錄頁面
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018/10/16 上午 10:39
	 * @Return
	 **/

	@RequestMapping(value = "goChargeLog", method = RequestMethod.GET)
	public String goChargeLog(HttpServletRequest request, Model model,
			@RequestParam(value = "serialNo") String serialNo) {
		model.addAttribute("serialno", serialNo);
		return "system/qianheLogNew";
	}

	/**
	 * 方法描述: 獲取簽核記錄
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018/10/16 上午 08:20
	 * @Return
	 **/

	public Page<EFormSignEntity> queryChargeLog(Page<EFormSignEntity> page,
			List<PropertyFilter> filters) {
		Page<EFormSignEntity> pageResult = eFormSignService.search(page,
				filters);
		return pageResult;
	}
	/**
	 * 方法描述: 獲取簽核記錄
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2019/01/02 上午 08:21
	 * @Return
	 **/
	@RequestMapping(value = "queryChargeLogNew", method = RequestMethod.GET)
	@ResponseBody
	public List<EFormSignEntity> queryChargeLogNew(HttpServletRequest request) {
		String serialno =request.getParameter("filter_EQS_applyId");
		List<EFormSignEntity> pageResult = eFormSignService.findBySerialno(serialno);
		return pageResult;
	}
	   /**
     * 方法描述: 待辦頁面
     * @Author: S6113712
	 * @CreateDate: 2018/10/16 上午 08:20
     * @Return
     **/

   @RequestMapping(value = "index", method = RequestMethod.GET)
   public String indexMyDown(HttpServletRequest request) {
      
       return "audit/eformsign/index";
   }
   
   
   
   /**
    * 方法描述: 待辦數據
    *
   * @Author: S6113712
	 * @CreateDate: 2018/10/16 上午 08:20
    * @Return
    **/

   @RequestMapping(value = "listMyTask", method = RequestMethod.GET)
   @ResponseBody
   public Map<String, Object> myInfoList(HttpServletRequest request) {
	   String serialno=request.getParameter( "serialno");
	   String formName=request.getParameter( "formName");
       Page<Gtasks> page = getPage(request);
       page.setOrderBy("createDate");
       page.setOrder("desc");
       List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
       page = eFormSignService.getAllMyTask( page, serialno, formName);
       ConvertUtils.convertPropertyToDictLabel(page,"wfName","form_name");
       for (Gtasks task : page.getResult()) {
       		if (task.getWfName().equals("JWFW")) {
       			task.setWfName("警衛服務申請單");
			}
	   }
       return getEasyUIData(page);
   }
   

   
}
