package com.foxconn.ipebg.buessness.workflow.service;

import com.foxconn.ipebg.buessness.workflow.entity.WfConifgEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.dao.WfNodeinfoDao;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import com.foxconn.ipebg.common.service.BaseService;
import com.foxconn.ipebg.system.dao.DictDao;
import com.foxconn.ipebg.system.entity.Dict;
import com.foxconn.ipebg.system.service.DictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 流程節點配置表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-06 11:04:38
 */
@Service
@Transactional(readOnly=true)
public class  WfNodeinfoService extends BaseBusinessService<WfNodeinfoEntity, String> {
    @Autowired
    private WfNodeinfoDao wfNodeinfoDao;
    @Autowired
    private DictService dictService;
    @Override
    public HibernateDao<WfNodeinfoEntity, String> getEntityDao() {
        return wfNodeinfoDao;
    }
    public List<WfNodeinfoEntity> findByFilters(String workflowid){
        Dict dict = dictService.getDictByTypeAndVlaue("workflow_code",workflowid);
        Assert.notNull(dict);
        String version = dict.getLabel();
        Map param = new HashMap();
        param.put("workflowid",workflowid);
        param.put("version",version);
        return wfNodeinfoDao.findAllByOrderCollect(param,"orderby",true);
    }

    public List<WfNodeinfoEntity> findForUser(String workflowid){
        Dict dict = dictService.getDictByTypeAndVlaue("workflow_code",workflowid);
        Assert.notNull(dict);
        String version = dict.getLabel();
        return wfNodeinfoDao.find("from WfNodeinfoEntity t where t.workflowid=?0 and t.signtype=?1 and t.version=?2 order by t.orderby",workflowid,"0",version);
    }

    public List<WfNodeinfoEntity> findForHuiqian(String workflowid){
        Dict dict = dictService.getDictByTypeAndVlaue("workflow_code",workflowid);
        Assert.notNull(dict);
        String version = dict.getLabel();
        return wfNodeinfoDao.find("from WfNodeinfoEntity t where t.workflowid=?0 and t.signtype=?1 and t.version=?2 order by t.orderby",workflowid,"1",version);
    }

    public List<WfNodeinfoEntity> findAllByOrder(String workflowid,String orderByProperty, boolean isAsc){
        Dict dict = dictService.getDictByTypeAndVlaue("workflow_code",workflowid);
        Assert.notNull(dict);
        String version = dict.getLabel();
        Map param = new HashMap();
        param.put("workflowid",workflowid);
        param.put("version",version);
        return wfNodeinfoDao.findAllByOrderCollect(param,orderByProperty,isAsc);
    }

    public WfNodeinfoEntity findByTaskName(String taskName,String workflowid,String version){
        return wfNodeinfoDao.findUnique("from WfNodeinfoEntity t where t.workflowid=?0 and t.nodename=?1 and t.version=?2",workflowid,taskName,version);
    }

    public List<WfNodeinfoEntity> findByAllFilters(String workflowid){
        Dict dict = dictService.getDictByTypeAndVlaue("workflow_code",workflowid);
        Assert.notNull(dict);
        String version = dict.getLabel();
        Map param = new HashMap();
        param.put("workflowid",workflowid);
        param.put("version",version);
        return wfNodeinfoDao.findAllByOrderCollect(param,"orderby",true);
    }
}
