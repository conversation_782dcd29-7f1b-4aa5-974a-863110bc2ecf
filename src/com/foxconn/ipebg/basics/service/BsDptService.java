package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.entity.BsDptEntity;
import com.foxconn.ipebg.basics.dao.BsDptDao;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;

import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import com.foxconn.ipebg.common.utils.Global;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.system.service.DictService;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 部門基本資料
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-12-21 14:54:46
 */
@Service
@Transactional(readOnly=true)
public class  BsDptService extends BaseBusinessService<BsDptEntity, String>{
    @Autowired
    private BsDptDao bsDptDao;

    @Autowired
    private UserServiceUtil serviceUtil;
    @Autowired
    private DictService dictService;

    @Override
    public HibernateDao<BsDptEntity, String> getEntityDao() {
        return bsDptDao;
    }

    public BsDptEntity findBySerialno(String serialno) {
        return this.bsDptDao.findUniqueBy("serialno", serialno);      
    }
    
    /**
	 * 方法描述: 條用存儲過程，有返回值
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018/12/29 下午 03:33
	 * @param TableName  相關表名
	 * @param FieldName 相關欄位名
	 * @param FirstStr 編號頭字符串           
	 * @param RecLen 序號位數
	 * @Return
	 **/
	public String CreateFormNo(String TableName, String FieldName,
			String FirstStr, int RecLen) {
		 return this.bsDptDao.CreateFormNo(TableName, FieldName, FirstStr, RecLen);
	}

	public List getDptInfoId() {
		String sql = "select t.id,  coalesce(t.dpt_qun, '') || '---' || coalesce(t.dpt_chu, '') || '---' || coalesce(t.dpt_bu, '') || '---' || coalesce(t.dpt_ke, '') dpt_name  from bs_dpt t";
	
		List list = this.bsDptDao.createSQLQuery(sql).list();
		return list;
	}

	public BsDptEntity findById(String id) {
		return this.bsDptDao.findUniqueBy("id", id);  
	}

	/// 獲取所有事業群
	public List<BsDptEntity> getAllBusinessGroup() {
		List<BsDptEntity> bdList=new ArrayList<BsDptEntity>();
		String sql = "select distinct dpt_qun from bs_dpt";
		List<String> list = this.bsDptDao.createSQLQuery(sql).list();
		if (list.size()>0)
		{
			for (int i=0;i<list.size();i++)
			{
				BsDptEntity bde=new BsDptEntity();
				bde.setDptQun(list.get(i).toString());
				bdList.add(bde);
			}
		}
		return bdList;
	}

	/**
	 * @brief 根據部門ID獲取部門事業群
	 * @param dptID 部門ID
	 * @return 部門名稱
	 * Author: ********
	 * Date: 2019/9/26
	 * */
	public String businessGroupByID(String dptID) {
		if (StringUtils.isBlank(dptID)) return null;
		String sql = "select dpt_qun from bs_dpt WHERE id = '" + dptID + "'";
		return this.bsDptDao.createSQLQuery(sql).uniqueResult().toString();
	}

	/**
	 * @brief 根據部門ID獲取部門事業處
	 * @param dptID 部門ID
	 * @return 部門名稱
	 * Author: ********
	 * Date: 2019/9/26
	 * */
	public String businessDivisionByID(String dptID) {
		if (StringUtils.isBlank(dptID)) return null;
		String sql = "select dpt_chu from bs_dpt WHERE id = '" + dptID + "'";
		Object result = this.bsDptDao.createSQLQuery(sql).uniqueResult();
		if (result == null) {
			return null;
		}
		return result.toString();
	}

	public String getJWFWDetailFormTypeForUser(String userID) {
		String remark = "JWFW_DEFAULT";
		if (userID == null) return remark;
		String factory = Global.getConfig("factoryID");
		if (factory.equalsIgnoreCase("TY")) {
			String sql = "select b.dpt_qun " +
					"from sys_user a, " +
					"bs_dpt b " +
					"where a.del_flag = b.id " +
					"and a.login_name = '" + userID + "'";
			Object sqlResult = this.bsDptDao.createSQLQuery(sql).uniqueResult();
			if (sqlResult == null) return remark;
			String qun = sqlResult.toString();
			if (qun != null && qun.length() > 0) {
				if (qun.equalsIgnoreCase("iPEBG")) {
					remark = "JWFW_iPEBG";
				} else if (qun.equalsIgnoreCase("iDPBG")) {
					remark = "JWFW_iDPBG";
				}
			}
		} else if (factory.equalsIgnoreCase("JC")) {
			String sql = "select b.dpt_chu " +
					"from sys_user a, " +
					"bs_dpt b " +
					"where a.del_flag = b.id " +
					"and a.login_name = '" + userID + "'";
			Object sqlResult = this.bsDptDao.createSQLQuery(sql).uniqueResult();
			if (sqlResult == null) return remark;
			String chu = sqlResult.toString();
			if (chu != null && chu.length() > 0) {
				if (chu.equals("iPEBG事業處")) {
					remark = "JWFW_iPEBG";
				} else if (chu.equals("晉城周邊處")) {
					remark = "JWFW_JCZB";
				}
			}
		}
		return remark;
	}
}
