package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.entity.BsServiceCostEntity;
import com.foxconn.ipebg.basics.entity.BsServiceCostTbEntity;
import com.foxconn.ipebg.basics.entity.TempWorkcostDetailEntity;
import com.foxconn.ipebg.basics.dao.TempWorkcostDetailDao;
import com.foxconn.ipebg.basics.vo.BsServiceCostVO;
import com.foxconn.ipebg.basics.vo.TempWorkcostGatherVO;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import com.foxconn.ipebg.system.entity.Permission;
import jxl.format.*;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.Colour;
import jxl.format.VerticalAlignment;
import jxl.write.*;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.hibernate.type.StandardBasicTypes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.system.entity.Dict;

import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.entity.WorkFlowEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfConifgService;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.buessness.workflow.service.WfNoderuleService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.system.service.DictService;

import javax.swing.*;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 警衛費用結賬明細表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-16 10:36:12
 */
@Service
@Transactional(readOnly = true)
public class TempWorkcostDetailService extends BaseBusinessService<TempWorkcostDetailEntity, String> {
    @Autowired
    private TempWorkcostDetailDao tempWorkcostDetailDao;

    @Autowired
    private UserServiceUtil serviceUtil;
    @Autowired
    private DictService dictService;

    @Override
    public HibernateDao<TempWorkcostDetailEntity, String> getEntityDao() {
        return tempWorkcostDetailDao;
    }

    public TempWorkcostDetailEntity findBySerialno(String serialno) {
        return this.tempWorkcostDetailDao.findUniqueBy("serialno", serialno);
    }

    public List<TempWorkcostGatherVO> listGather(Map<String, Object> params) {

        if (params.get("workMonth") == null || StringUtils.isBlank(params.get("workMonth").toString())) {
            return Arrays.asList();
        }
        StringBuffer sql = new StringBuffer();
//        sql.append("select a.bu_nam buNam,");
//        sql.append("       a.legal_id legalId,");
//        sql.append("       a.cost_id costId,");
//        sql.append("       sum(a.ot_cost) otCost,");
//        sql.append("       sum(a.ts_cost) tsCost,");
//        sql.append("       sum(a.total_cost) totalCost,");
//        sql.append("       sum(a.normal_cost) normalCost");
//        sql.append("  from temp_workcost_detail a");
        sql.append("select bu_nam      buNam," +
                "       legal_Id    legalId," +
                "       cost_Id     costId," +
                "       ot_Cost     otCost," +
                "       tsCost      tsCost," +
                "       total_Cost  totalCost," +
                "       normal_cost normalCost" +
                "  from (select c.*, b.*, c.normal_cost + c.ot_cost + b.tsCost total_Cost" +
                "          from (select bu_nam," +
                "                       legal_id," +
                "                       cost_id," +
                "                       sum(normal_cost) normal_cost," +
                "                       sum(ot_cost) ot_cost," +
                "                       work_month" +
                "                  from TEMP_WORKCOST_DETAIL" +
                "                 group by bu_nam, legal_id, cost_id, work_month) c left join " +
                "               (select cost_code," +
                "                       sum(expenses) tsCost," +
                "                       to_char(start_time, 'yyyymm') start_time" +
                "                  from BS_TEMP_SERVICE group by to_char(start_time, 'yyyymm'), cost_code) b" +
                "                    on c.cost_id = b.cost_code and c.work_month = b.start_time) a");


        sql.append(" where a.work_month = '" + params.get("workMonth").toString() + "'");
        if (params.get("buNam") != null && StringUtils.isNotBlank(params.get("buNam").toString())) {
            sql.append(" and a.bu_nam = '" + params.get("buNam").toString() + "'");
        }
        if (params.get("legalId") != null && StringUtils.isNotBlank(params.get("legalId").toString())) {
            sql.append(" and a.legal_id = '" + params.get("legalId").toString() + "'");
        }
        if (params.get("costId") != null && StringUtils.isNotBlank(params.get("costId").toString())) {
            sql.append(" and a.cost_id = '" + params.get("costId").toString() + "'");
        }
//        sql.append(" group by a.bu_nam, a.legal_id, a.cost_id");
//        sql.append(" order by buNam,legalId,costId");
//        List list = this.tempWorkcostDetailDao.createSQLQuery(sql.toString()).addEntity(TempWorkcostDetailEntity.class).list();
        SQLQuery sqlQuery=this.tempWorkcostDetailDao.createSQLQuery(sql.toString());
        sqlQuery.addScalar("buNam",StandardBasicTypes.STRING );
        sqlQuery.addScalar("legalId",StandardBasicTypes.STRING);
        sqlQuery.addScalar("costId",StandardBasicTypes.STRING);
        sqlQuery.addScalar("otCost",StandardBasicTypes.INTEGER);
        sqlQuery.addScalar("tsCost",StandardBasicTypes.INTEGER);
        sqlQuery.addScalar("normalCost",StandardBasicTypes.INTEGER);
        sqlQuery.addScalar("totalCost",StandardBasicTypes.INTEGER);
        sqlQuery.setResultTransformer(Transformers.aliasToBean(TempWorkcostGatherVO.class));

        return sqlQuery.list();

    }

    public void generateExcelHeader(WritableSheet sheet) throws WriteException {
        // 表头样式
        WritableFont headerFont = new WritableFont(WritableFont.ARIAL, 11, WritableFont.BOLD, false, UnderlineStyle.NO_UNDERLINE, Colour.BLACK);
        WritableCellFormat headerFormat = new WritableCellFormat(headerFont);
        headerFormat.setAlignment(Alignment.CENTRE);
        headerFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
        headerFormat.setBorder(Border.ALL, BorderLineStyle.THIN, Colour.BLACK);
        headerFormat.setWrap(true);
        Label index1 = new Label(0, 0, "序號", headerFormat);
        sheet.addCell(index1);
        sheet.mergeCells(0, 0, 0, 1);
        sheet.setColumnView(0, 5);
        Label month1 = new Label(1, 0, "事業群", headerFormat);
        sheet.addCell(month1);
        sheet.mergeCells(1, 0, 1, 1);
        sheet.setColumnView(1, 10);
        Label qun1 = new Label(2, 0, "法人", headerFormat);
        sheet.addCell(qun1);
        sheet.mergeCells(2, 0, 2, 1);
        sheet.setColumnView(2, 15);
        Label faren1 = new Label(3, 0, "費用代碼", headerFormat);
        sheet.addCell(faren1);
        sheet.mergeCells(3, 0, 3, 1);
        sheet.setColumnView(3, 13);
        Label bumen1 = new Label(4, 0, "費用（RMB）", headerFormat);
        sheet.addCell(bumen1);
        sheet.mergeCells(4, 0, 7, 0);
        Label feiyongdaima1 = new Label(4, 1, "常規費用", headerFormat);
        sheet.addCell(feiyongdaima1);
        sheet.setColumnView(4, 13);
        Label gangwei1 = new Label(5, 1, "加班費用", headerFormat);
        sheet.addCell(gangwei1);
        sheet.setColumnView(5, 13);
        Label banbie1 = new Label(6, 1, "臨時勤務費用", headerFormat);
        sheet.addCell(banbie1);
        sheet.setColumnView(6, 13);
        Label shangbanrenyuanxinxi = new Label(7, 1, "合計", headerFormat);
        sheet.addCell(shangbanrenyuanxinxi);
        sheet.setColumnView(7, 13);
    }

    public void fillSheet(WritableSheet sheet, List<TempWorkcostGatherVO> list, Map<String, String> legalMap) throws WriteException {
        // 内容格式
        WritableFont contentFont = new WritableFont(WritableFont.ARIAL, 11, WritableFont.NO_BOLD, false, UnderlineStyle.NO_UNDERLINE, Colour.BLACK);
        WritableCellFormat contentFormat = new WritableCellFormat(contentFont);
        contentFormat.setAlignment(Alignment.CENTRE);
        contentFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
        contentFormat.setBorder(Border.ALL, BorderLineStyle.THIN, Colour.BLACK);
        Integer normalCostAll = 0, otCostAll = 0, tmpCostAll = 0, allCostAll = 0;
        for (int i = 0; i < list.size(); i++) {
            TempWorkcostGatherVO gather = list.get(i);
            Label index1 = new Label(0, 2 + i, (1 + i) + "", contentFormat);
            sheet.addCell(index1);
            Label group = new Label(1, 2 + i, gather.getBuNam(), contentFormat);
            sheet.addCell(group);
            Label legal = new Label(2, 2 + i, legalMap.get(gather.getLegalId()), contentFormat);
            sheet.addCell(legal);
            Label costId = new Label(3, 2 + i, gather.getCostId(), contentFormat);
            sheet.addCell(costId);
            Label normalCost = new Label(4, 2 + i, gather.getNormalCost() / 100.00 + "", contentFormat);
            sheet.addCell(normalCost);
            Label otCost = new Label(5, 2 + i, gather.getOtCost() / 100.00 + "", contentFormat);
            sheet.addCell(otCost);
            Label tmpCost = new Label(6, 2 + i, gather.getTsCost() / 100.00 + "", contentFormat);
            sheet.addCell(tmpCost);
            Label totalCost = new Label(7, 2 + i, (gather.getNormalCost() + gather.getOtCost() + gather.getTsCost()) / 100.00 + "", contentFormat);
            sheet.addCell(totalCost);
            normalCostAll += gather.getNormalCost();
            otCostAll += gather.getOtCost();
            tmpCostAll += gather.getTsCost();
            allCostAll += (gather.getNormalCost() + gather.getOtCost() + gather.getTsCost());
        }
        Label totalTitle = new Label(3, list.size() + 2, "合计:", contentFormat);
        sheet.addCell(totalTitle);
        Label normal = new Label(4, list.size() + 2, normalCostAll / 100.0 + "", contentFormat);
        sheet.addCell(normal);
        Label ot = new Label(5, list.size() + 2, otCostAll / 100.0 + "", contentFormat);
        sheet.addCell(ot);
        Label tmp = new Label(6, list.size() + 2, tmpCostAll / 100.0 + "", contentFormat);
        sheet.addCell(tmp);
        Label all = new Label(7, list.size() + 2, allCostAll / 100.0 + "", contentFormat);
        sheet.addCell(all);
        Label presentTitle = new Label(3, list.size() + 3, "百分比：", contentFormat);
        sheet.addCell(presentTitle);
        Label normalpercent = new Label(4, list.size() + 3, (allCostAll > 0 ? Math.round(normalCostAll * 100.0 / allCostAll) : 0) + "%", contentFormat);
        sheet.addCell(normalpercent);
        Label otpercent = new Label(5, list.size() + 3, (allCostAll > 0 ? Math.round(otCostAll * 100.0 / allCostAll) : 0) + "%", contentFormat);
        sheet.addCell(otpercent);
        Label tmppercent = new Label(6, list.size() + 3, (allCostAll > 0 ? Math.round(tmpCostAll * 100.0 / allCostAll) : 0) + "%", contentFormat);
        sheet.addCell(tmppercent);
        Label allpercent = new Label(7, list.size() + 3, "100%", contentFormat);
        sheet.addCell(allpercent);
    }
}
