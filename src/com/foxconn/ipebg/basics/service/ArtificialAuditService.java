package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.entity.ArtificialAuditEntity;
import com.foxconn.ipebg.basics.entity.ArtificialAuditErrorDto;
import com.foxconn.ipebg.basics.entity.BsArrangeEntity;
import com.foxconn.ipebg.basics.controller.ArtificialAuditController;
import com.foxconn.ipebg.basics.dao.ArtificialAuditDao;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;

import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foxconn.ipebg.common.utils.IdGen;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.Reflections;
import com.foxconn.ipebg.report.service.AbsentReportService;
import com.foxconn.ipebg.system.entity.Dict;

import com.foxconn.ipebg.system.service.DictService;
import com.foxconn.ipebg.system.utils.CommonUtils;
import com.foxconn.ipebg.system.utils.ImportExcelUtil;
import com.foxconn.ipebg.system.utils.MyBeanUtils;
import com.foxconn.ipebg.system.utils.UserUtil;

import java.io.InputStream;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 人工稽核錄入基本資料
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-18 09:09:19
 */
@Service
@Transactional(readOnly=true)
public class ArtificialAuditService extends
		BaseBusinessService<ArtificialAuditEntity, String> {
	
	
	@Autowired
	private ArtificialAuditDao artificialAuditDao;

//	@Autowired
//	private AbsentReportService absentReportService;
//	@Autowired
//	private BsArrangeService bsArrangeService;
//
//	@Autowired
//	private UserServiceUtil serviceUtil;
//	@Autowired
//	private DictService dictService;

	
	public static final String ARTIFICIALAUDITERRORDTOS = "artificialauditerrordtos";
	@Override
	public HibernateDao<ArtificialAuditEntity, String> getEntityDao() {
		return artificialAuditDao;
	}

	/**
	 * 按日期執行異常人員比對程序
	 * @param date 比對日期
	 */
	@Transactional(readOnly=false)
	public void checkAbnormalPerson(final Date date) throws SQLException {
		artificialAuditDao.checkAbnormalPerson(date);
	}

//	// *******************************導入相關方法**********
//	/**
//	 * 批量導入
//	 * 
//	 * @param in
//	 *            excel文件輸入流
//	 * @param isExcel2003
//	 *            是否excel2003
//	 * @return 是否成功
//	 */
//
//	public String batchImport(InputStream in, boolean isExcel2003) {
//		try {
//
//			List<ArtificialAuditErrorDto> artificialauditErrorDtos=null;
//			ImportExcelUtil poi = new ImportExcelUtil();
//			int success = 0, failed = 0;
//			// 讀取上傳的excel file
//			List<List<String>> list = poi.read(in, isExcel2003);
//			// 判断需要的字段在Excel中是否都存在
//			boolean isExist = true;
//			Map<String, String> fieldMap = getFieldMap();
//			Map<String, Integer> fieldIndexMap = new HashMap();
//			List<String> excelFieldList = list.get(0);
//			// 第一列是序號不需要
//			for (int i = 1; i < excelFieldList.size(); i++) {
//				if (!fieldMap.containsKey(excelFieldList.get(i))) {
//					isExist = false;
//					break;
//				}
//				fieldIndexMap.put(fieldMap.get(excelFieldList.get(i)), i);
//			}
//			// 如果有列名不存在，则抛出异常，提示错误
//			if (!isExist) {
//				return "有列名不存在，請對照模板";
//			}
//			//導入數量限制
////			if (list.size()-1 > 10) {
////				return "資料導入不能大於100筆;";
////			}
//			//開始導入
//			 artificialauditErrorDtos = listToArtificialAuditErrorDto(
//					list, 1, fieldIndexMap);
//			
//			if (artificialauditErrorDtos.size()>0)
//			{
//				UserUtil.getSession().setAttribute(ARTIFICIALAUDITERRORDTOS,artificialauditErrorDtos);
//			  return "導入成功"+(list.size()-1-artificialauditErrorDtos.size())+"筆，失敗"+artificialauditErrorDtos.size()+"筆!";	
//			}
//
//
//		} catch (Exception e) {
//			this.logger.info(e.getMessage(), e);
//			return Constant.RESULT.CODE_NO.getValue();
//		}
//		return Constant.RESULT.CODE_YES.getValue();
//	}
//
//	public HashMap<String, String> getFieldMap() {
//		HashMap<String, String> fieldMap = new HashMap<String, String>();
//
//		fieldMap.put("排班日期", "shiftDate");
//		fieldMap.put("工號", "empNo");
//		fieldMap.put("狀態", "abnormalState");
//		fieldMap.put("第一次稽核結果", "abnormalResult1");
//		fieldMap.put("第一次稽核原因", "abnormalCauses1");
//		fieldMap.put("第二次稽核結果", "abnormalResult2");
//		fieldMap.put("第二次稽核原因", "abnormalCauses2");
//
//		return fieldMap;
//	}
//
//	public LinkedHashMap<String, String> getErrorFieldMap() {
//		LinkedHashMap<String, String> fieldMap = new LinkedHashMap<String, String>();
//
//		fieldMap.put("shiftDate", "排班日期");
//		fieldMap.put("empNo", "工號");
//		fieldMap.put("abnormalState", "狀態");
//		fieldMap.put("abnormalResult1", "第一次稽核結果");
//		fieldMap.put("abnormalCauses1", "第一次稽核原因");
//		fieldMap.put("abnormalResult2", "第二次稽核結果");
//		fieldMap.put("abnormalCauses2", "第二次稽核原因");
//
//		fieldMap.put("errorLog", "導入失敗原因");
//
//		return fieldMap;
//	}
//
//	/**
//	 * @param list
//	 *            列表
//	 * @param startIndex
//	 *            開始的行
//	 * @param fieldIndexMap
//	 *            list中的index和类的英文属性的对应关系Map
//	 * @return
//	 * @throws IllegalAccessException
//	 * @throws InstantiationException
//	 */
//	@Transactional
//	public List<ArtificialAuditErrorDto> listToArtificialAuditErrorDto(
//			List<List<String>> list, int startIndex,
//			Map<String, Integer> fieldIndexMap) {
//		// 定义要返回的list
//		List<ArtificialAuditErrorDto> resultList = new ArrayList<ArtificialAuditErrorDto>();
//		// 将sheet转换为list
//		ArtificialAuditErrorDto entity = new ArtificialAuditErrorDto();
//		ArtificialAuditEntity artificialAuditEntity = new ArtificialAuditEntity();
//		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
//		Date shiftDate = new Date();
//		for (int i = startIndex; i < list.size(); i++) {
//			// 新建要转换的对象
//			
//			
//			if (CommonUtils.isValidDate(list.get(i).get(1))) {
//				try {
//					shiftDate=format.parse(list.get(i).get(1));
//				} catch (ParseException e) {
//					// TODO Auto-generated catch block
//					e.printStackTrace();
//				}
//				
//				BsArrangeEntity bsArrangeEntity=bsArrangeService.isExistEmpInfo(list.get(i).get(2),
//						shiftDate);
//                 //驗證人員是否排班
//				if (bsArrangeEntity != null) {
//					
//					//artificialAuditEntity.setNewRecord(false);
//					//artificialAuditEntity.setId(IdGen.uuid());
//					
//					artificialAuditEntity.setShiftDate(shiftDate);
//					artificialAuditEntity.setEmpNo(list.get(i).get(2));
//					artificialAuditEntity.setAbnormalState(list.get(i).get(3));
//					artificialAuditEntity.setAbnormalResult1(list.get(i).get(4));
//					artificialAuditEntity.setAbnormalCauses1(list.get(i).get(5));
//					artificialAuditEntity.setAbnormalResult2(list.get(i).get(6));
//					artificialAuditEntity.setAbnormalCauses2(list.get(i).get(7));
//
//					artificialAuditEntity.setEmpName(bsArrangeEntity.getEmpName());
//					artificialAuditEntity.setPostRecno(bsArrangeEntity.getPostRecno());
//					artificialAuditEntity.setPostName(bsArrangeEntity.getPostName());
//					artificialAuditEntity.setShiftNo(bsArrangeEntity.getShiftNo());
//					artificialAuditEntity.setShiftName(bsArrangeEntity.getShiftName());
//					artificialAuditEntity.setCreateBy(UserUtil.getCurrentUser().getLoginName());
//					artificialAuditEntity.setCreateDate(new Date());
//
//					absentReportService.updateAbnormalStateByEmpNo(list.get(i).get(2),shiftDate, artificialAuditEntity.getAbnormalState());
//					this.save(artificialAuditEntity);
//					//this.save(artificialAuditEntity);
//				}
//				else//人員未排班
//				{
//					 entity.setShiftDate(shiftDate);
//					 entity.setEmpNo(list.get(i).get(2));
//					 entity.setAbnormalState(list.get(i).get(3));
//					 entity.setAbnormalResult1(list.get(i).get(4));
//					 entity.setAbnormalCauses1(list.get(i).get(5));
//					 entity.setAbnormalResult2(list.get(i).get(6));
//					 entity.setAbnormalCauses2(list.get(i).get(7));
//					 entity.setErrorLog("人員未排班！");
//					 resultList.add(entity);
//				}
//			}
//			else
//			{
//				 //entity.setShiftDate(shiftDate);
//				 entity.setEmpNo(list.get(i).get(2));
//				 entity.setAbnormalState(list.get(i).get(3));
//				 entity.setAbnormalResult1(list.get(i).get(4));
//				 entity.setAbnormalCauses1(list.get(i).get(5));
//				 entity.setAbnormalResult2(list.get(i).get(6));
//				 entity.setAbnormalCauses2(list.get(i).get(7));
//				 entity.setErrorLog("日期格式錯誤！");
//				 resultList.add(entity);	
//			}
//			
//			
//		}
//		return resultList;
//	}
//	// *******************************導入相關方法**********

}
