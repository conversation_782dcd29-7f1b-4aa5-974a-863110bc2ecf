<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>警衛人員</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>

    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/bssecper/${action}" method="post">
    <table class="formTable">
        <tr>
            <td>
                <input id="ids" name="ids" type="hidden" value="${perInfo.id }"/>
            </td>
        </tr>
        <tr>
            <td>工號：</td>
            <td>
                <input id="empNo" name="empNo" class="easyui-validatebox" data-options="width: 150"
                       value="${perInfo.empNo }" />
            </td>
        </tr>
        <tr>
            <td>姓名：</td>
            <td>
                <input id="empName" name="empName" class="easyui-validatebox" data-options="width: 150"
                       value="${perInfo.empName }"/>
            </td>
        </tr>
        <tr>
            <td>公司：</td>
            <td>
                <input id="company" name="company" class="easyui-validatebox" data-options="width: 150"
                       value="${perInfo.company }"/>
            </td>
        </tr>
        <tr>
            <td>身份證號：</td>
            <td>
                <input id="psnId" name="psnId" class="easyui-validatebox" data-options="width: 150"
                       value="${perInfo.psnId }"/>
            </td>
        </tr>
        <tr>
            <td>創建人：</td>
            <td>
                <input id="createBy" name="createBy" readonly class="easyui-validatebox" data-options="width: 150"
                       value="${perInfo.createBy }"/>
            </td>
        </tr>
        <tr>
            <td>創建時間：</td>
            <td>
                <input id="createDate" name="createDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
                       data-options="width: 160" disabled="disabled"
                       value="<fmt:formatDate value="${perInfo.createDate}"/>"/>
            </td>
        </tr>
        <tr>
            <td>更新人：</td>
            <td>
                <input id="updateBy" name="updateBy" readonly class="easyui-validatebox" data-options="width: 150"
                       value="${perInfo.updateBy }"/>
            </td>
        </tr>
        <tr>
            <td>更新時間：</td>
            <td>
                <input id="updateDate" name="updateDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
                       data-options="width: 160" disabled="disabled"
                       value="<fmt:formatDate value="${perInfo.updateDate}"/>"/>
            </td>
        </tr>
    </table>
</form>
</div>
<script src='${ctx}/static/js/basics/bssecper.js?"+Math.random()"'></script>
<script type="text/javascript">
    //提交表单
    $('#mainform').form({
        onSubmit: function () {
            var isValid = $(this).form('validate');
            return isValid;	// 返回false终止表单提交
        },
        success: function (data) {
            successTip(data, dg, d);
        }
    });
</script>
</body>
</html>