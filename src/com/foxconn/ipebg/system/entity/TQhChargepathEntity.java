package com.foxconn.ipebg.system.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 簽核路徑表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-11 10:55:00
 */
@Entity
@Table(name = "t_qh_chargepath")
@DynamicUpdate
@DynamicInsert
public class TQhChargepathEntity extends DataEntity<TQhChargepathEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    //單位代碼
    private String deptno;
    //課級主管
    private String kchargeno;
    //課級主管名稱
    private String kchargename;
    //部級主管
    private String bchargeno;
    //部級主管名稱
    private String bchargename;
    //廠級主管
    private String cchargeno;
    //廠級主管名稱
    private String cchargename;
    //製造處級主管
    private String zchargeno;
    //製造處級主管名稱
    private String zchargename;
    //製造總處級主管
    private String zcchargeno;
    //製造總處級主管名稱
    private String zcchargename;
    //產品處級主管
    private String pcchargeno;
    //產品處級主管名稱
    private String pcchargename;
    //事業群級主管
    private String sychargeno;
    //事業群級主管名稱
    private String sychargename;
    //是否有效
    private String isvalid;
    //是否鎖定
    private String islock;
    //創建人工號
    private String creator;
    //創建時間
    private Date createtime;
    //修改人工號
    private String modor;
    //修改時間
    private Date modifytime;
    //申請人工號
    private String applyempno;
    //申請人姓名
    private String applyusername;
    //導入標誌0:剛導入，1:同步后
    private String importflag;
    //$column.comments
    private String factoryid;

    /**
     * 设置：單位代碼
     */
    public void setDeptno(String deptno) {
        this.deptno = deptno;
    }

    /**
     * 获取：單位代碼
     */
    @Column(name = "deptno", nullable = false, length = 20)
    public String getDeptno() {
        return deptno;
    }

    /**
     * 设置：課級主管
     */
    public void setKchargeno(String kchargeno) {
        this.kchargeno = kchargeno;
    }

    /**
     * 获取：課級主管
     */
    @Column(name = "kchargeno", nullable = false, length = 20)
    public String getKchargeno() {
        return kchargeno;
    }

    /**
     * 设置：課級主管名稱
     */
    public void setKchargename(String kchargename) {
        this.kchargename = kchargename;
    }

    /**
     * 获取：課級主管名稱
     */
    @Column(name = "kchargename", nullable = false, length = 20)
    public String getKchargename() {
        return kchargename;
    }

    /**
     * 设置：部級主管
     */
    public void setBchargeno(String bchargeno) {
        this.bchargeno = bchargeno;
    }

    /**
     * 获取：部級主管
     */
    @Column(name = "bchargeno", nullable = false, length = 20)
    public String getBchargeno() {
        return bchargeno;
    }

    /**
     * 设置：部級主管名稱
     */
    public void setBchargename(String bchargename) {
        this.bchargename = bchargename;
    }

    /**
     * 获取：部級主管名稱
     */
    @Column(name = "bchargename", nullable = false, length = 20)
    public String getBchargename() {
        return bchargename;
    }

    /**
     * 设置：廠級主管
     */
    public void setCchargeno(String cchargeno) {
        this.cchargeno = cchargeno;
    }

    /**
     * 获取：廠級主管
     */
    @Column(name = "cchargeno", nullable = false, length = 20)
    public String getCchargeno() {
        return cchargeno;
    }

    /**
     * 设置：廠級主管名稱
     */
    public void setCchargename(String cchargename) {
        this.cchargename = cchargename;
    }

    /**
     * 获取：廠級主管名稱
     */
    @Column(name = "cchargename", nullable = false, length = 20)
    public String getCchargename() {
        return cchargename;
    }

    /**
     * 设置：製造處級主管
     */
    public void setZchargeno(String zchargeno) {
        this.zchargeno = zchargeno;
    }

    /**
     * 获取：製造處級主管
     */
    @Column(name = "zchargeno", nullable = false, length = 20)
    public String getZchargeno() {
        return zchargeno;
    }

    /**
     * 设置：製造處級主管名稱
     */
    public void setZchargename(String zchargename) {
        this.zchargename = zchargename;
    }

    /**
     * 获取：製造處級主管名稱
     */
    @Column(name = "zchargename", nullable = false, length = 20)
    public String getZchargename() {
        return zchargename;
    }

    /**
     * 设置：製造總處級主管
     */
    public void setZcchargeno(String zcchargeno) {
        this.zcchargeno = zcchargeno;
    }

    /**
     * 获取：製造總處級主管
     */
    @Column(name = "zcchargeno", nullable = false, length = 20)
    public String getZcchargeno() {
        return zcchargeno;
    }

    /**
     * 设置：製造總處級主管名稱
     */
    public void setZcchargename(String zcchargename) {
        this.zcchargename = zcchargename;
    }

    /**
     * 获取：製造總處級主管名稱
     */
    @Column(name = "zcchargename", nullable = false, length = 20)
    public String getZcchargename() {
        return zcchargename;
    }

    /**
     * 设置：產品處級主管
     */
    public void setPcchargeno(String pcchargeno) {
        this.pcchargeno = pcchargeno;
    }

    /**
     * 获取：產品處級主管
     */
    @Column(name = "pcchargeno", nullable = false, length = 20)
    public String getPcchargeno() {
        return pcchargeno;
    }

    /**
     * 设置：產品處級主管名稱
     */
    public void setPcchargename(String pcchargename) {
        this.pcchargename = pcchargename;
    }

    /**
     * 获取：產品處級主管名稱
     */
    @Column(name = "pcchargename", nullable = false, length = 20)
    public String getPcchargename() {
        return pcchargename;
    }

    /**
     * 设置：事業群級主管
     */
    public void setSychargeno(String sychargeno) {
        this.sychargeno = sychargeno;
    }

    /**
     * 获取：事業群級主管
     */
    @Column(name = "sychargeno", nullable = false, length = 20)
    public String getSychargeno() {
        return sychargeno;
    }

    /**
     * 设置：事業群級主管名稱
     */
    public void setSychargename(String sychargename) {
        this.sychargename = sychargename;
    }

    /**
     * 获取：事業群級主管名稱
     */
    @Column(name = "sychargename", nullable = false, length = 20)
    public String getSychargename() {
        return sychargename;
    }

    /**
     * 设置：是否有效
     */
    public void setIsvalid(String isvalid) {
        this.isvalid = isvalid;
    }

    /**
     * 获取：是否有效
     */
    @Column(name = "isvalid", nullable = false, length = 20)
    public String getIsvalid() {
        return isvalid;
    }

    /**
     * 设置：是否鎖定
     */
    public void setIslock(String islock) {
        this.islock = islock;
    }

    /**
     * 获取：是否鎖定
     */
    @Column(name = "islock", nullable = false, length = 20)
    public String getIslock() {
        return islock;
    }

    /**
     * 设置：創建人工號
     */
    public void setCreator(String creator) {
        this.creator = creator;
    }

    /**
     * 获取：創建人工號
     */
    @Column(name = "creator", nullable = false, length = 20)
    public String getCreator() {
        return creator;
    }

    /**
     * 设置：創建時間
     */
    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    /**
     * 获取：創建時間
     */
    @Column(name = "createtime", nullable = false, length = 20)
    public Date getCreatetime() {
        return createtime;
    }

    /**
     * 设置：修改人工號
     */
    public void setModor(String modor) {
        this.modor = modor;
    }

    /**
     * 获取：修改人工號
     */
    @Column(name = "modor", nullable = false, length = 20)
    public String getModor() {
        return modor;
    }

    /**
     * 设置：修改時間
     */
    public void setModifytime(Date modifytime) {
        this.modifytime = modifytime;
    }

    /**
     * 获取：修改時間
     */
    @Column(name = "modifytime", nullable = false, length = 20)
    public Date getModifytime() {
        return modifytime;
    }

    /**
     * 设置：申請人工號
     */
    public void setApplyempno(String applyempno) {
        this.applyempno = applyempno;
    }

    /**
     * 获取：申請人工號
     */
    @Column(name = "applyempno", nullable = false, length = 20)
    public String getApplyempno() {
        return applyempno;
    }

    /**
     * 设置：申請人姓名
     */
    public void setApplyusername(String applyusername) {
        this.applyusername = applyusername;
    }

    /**
     * 获取：申請人姓名
     */
    @Column(name = "applyusername", nullable = false, length = 20)
    public String getApplyusername() {
        return applyusername;
    }

    /**
     * 设置：導入標誌0:剛導入，1:同步后
     */
    public void setImportflag(String importflag) {
        this.importflag = importflag;
    }

    /**
     * 获取：導入標誌0:剛導入，1:同步后
     */
    @Column(name = "importflag", nullable = false, length = 20)
    public String getImportflag() {
        return importflag;
    }

    /**
     * 设置：${column.comments}
     */
    public void setFactoryid(String factoryid) {
        this.factoryid = factoryid;
    }

    /**
     * 获取：${column.comments}
     */
    @Column(name = "factoryid", nullable = false, length = 20)
    public String getFactoryid() {
        return factoryid;
    }

}