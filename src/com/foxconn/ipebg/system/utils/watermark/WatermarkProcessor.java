/*
 * Power by www.xiaoi.com
 */
package com.foxconn.ipebg.system.utils.watermark;

import java.io.*;
import java.util.HashMap;
import java.util.Map;

import com.foxconn.ipebg.system.utils.FileExtensionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.model.XWPFHeaderFooterPolicy;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFHeader;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.xmlbeans.XmlObject;
import org.springframework.util.Assert;

import javax.xml.namespace.QName;

/**
 * 中央处理器
 * <AUTHOR> href="mailto:<EMAIL>">eko.zhan</a>
 * @date 2018年9月18日 上午9:40:29
 * @version 1.0
 */
public class WatermarkProcessor {
	private static Map<String, File> map = new HashMap<String, File>();
	private static Map<String, File> mapForExcel = new HashMap<String, File>();
	/**
	 * 图片水印
	 * <AUTHOR> at 2018年9月18日 上午9:48:57
	 * @param file
	 * @param imageFile
	 * @throws WatermarkException
	 */
	public static void process(File file, File imageFile) throws WatermarkException {
		AbstractProcessor processor = null;
		if (FileExtensionUtils.isWord(file.getName())) {
			processor = new WordProcessor(file, imageFile);
		} else if (FileExtensionUtils.isExcel(file.getName())) {
			processor = new ExcelProcessor(file, imageFile);
		} else if (FileExtensionUtils.isPpt(file.getName())) {
			processor = new PowerPointProcessor(file, imageFile);
		} else if (FileExtensionUtils.isPdf(file.getName())) {
			processor = new PdfProcessor(file, imageFile);
		} else if (FileExtensionUtils.isImage(file.getName())) {
			processor = new ImageProcessor(file, imageFile);
		}
		if (processor!=null) {
			processor.process();
		}else {
			throw new WatermarkException("不支持文件格式为 " + FilenameUtils.getExtension(file.getName()) + " 的水印处理");
		}
	}
	/**
	 * 文本水印
	 * <AUTHOR> at 2018年9月18日 上午11:08:08
	 * @param file
	 * @param text
	 * @throws WatermarkException
	 */
	public static void process(File file, String text) throws WatermarkException {
		Assert.hasText(text, "水印文本不能为空");
		//通过 text 生成 Image File
		if (FileExtensionUtils.isExcel(file.getName())||FileExtensionUtils.isPpt(file.getName())){
			if (mapForExcel.get(text) == null) {
				mapForExcel.put(text, FontImageForExcel.createImage(text));
			}
			process(file, mapForExcel.get(text));
		}else {
			if (map.get(text) == null) {
				map.put(text, FontImage.createImage(text));
			}
			process(file, map.get(text));
		}
	}
	public static void main(String[] args) throws WatermarkException {
		process(new File("E:\\Internet_email&WWW.pdf"),"CAA太原周邊薪資");
	}
	
}
