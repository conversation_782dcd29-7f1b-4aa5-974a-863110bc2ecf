package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.dao.BsArrangeListDao;
import com.foxconn.ipebg.basics.entity.BsArrangeListEntity;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(readOnly=true)
public class BsArrangeListService extends BaseBusinessService<BsArrangeListEntity, String> {
    @Autowired
    private BsArrangeListDao arrangeListDao;

    @Override
    public HibernateDao<BsArrangeListEntity, String> getEntityDao() {
        return arrangeListDao;
    }
}
