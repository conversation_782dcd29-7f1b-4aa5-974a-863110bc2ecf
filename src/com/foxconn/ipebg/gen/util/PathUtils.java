package com.foxconn.ipebg.gen.util;

import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.FilenameFilter;
import java.util.HashMap;
import java.util.Map;

public class PathUtils {
	public static String getClassPath(){
		String path = PathUtils.class.getResource("/").getFile();
		String os = System.getProperty("os.name").toUpperCase();
		// 當前操作系統為windows
		if (os.indexOf("WINDOWS") >= 0) {
			path = path.substring(1);
		}
		File f=new File(path);
		return f.getPath()+ System.getProperty("file.separator");
	}	
	public static String getWebInf(){
		String path = getClassPath();
		File f=new File(path);
		return f.getParent()+ System.getProperty("file.separator");
	}
	public static String getContextPath(){
		String path = getWebInf();
		File f=new File(path);
		return f.getParent()+ System.getProperty("file.separator");
	}
	public static void findSystemFiles(String key, Map params){
		if(StringUtils.isEmpty(key)||params==null||params.get(key)==null){
			return ;
		}
		StringBuffer sb=new StringBuffer();
		String configStr=params.get(key).toString();
		String[] fileStrs=configStr.split(",");
		for(String s:fileStrs){
			int len=s.lastIndexOf(System.getProperty("file.separator"));
			if(len==-1){
				len=s.lastIndexOf("/");
			}
			final String filePattern=s.substring(len+1);
			
			String path=s.substring(0,len);
			path =getContextPath()+path;
			File fs=new File(path);
			if(fs.isDirectory()){
				File[] fl = fs.listFiles(new FilenameFilter() {
					private String filterStr = null;
					private void init(){
						filterStr = filePattern;					
						filterStr = filterStr.replace("*", "\\p{Graph}*");					
					}
					public boolean accept(File dir, String name) {
						if(filterStr ==  null){
							init();
						}					
						return name.matches(filterStr);
						
					}
				});			
				for(File f:fl){
					sb.append(f.getPath()).append(",");
				}			
			}
		}		
		params.put(key, sb.toString());
	}
	
	
	public static void main(String[] args){
//		System.out.println(PathUtils.getClassPath());
//		System.out.println(PathUtils.getWebInf());
//		System.out.println(getContextPath());
		Map params=new HashMap<String, String>();
		params.put("config", "WEB-INF/conf/*-action.xml");
		findSystemFiles("config",params);
		System.out.println(params);
		
	}
}
