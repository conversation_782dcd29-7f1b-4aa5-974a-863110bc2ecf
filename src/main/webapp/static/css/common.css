.commonW {
    width: 1200px;
    margin: 0 auto;
    color: #1f1f1f;
}

.headTitle {
    line-height: 60px;
    height: 60px;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    color: #1DB8FF;
}

.clear {
    clear: both;
}

.position_L {
    float: left;
    width: 33%;
    font-size: 14px;
    text-align: left;
}

.position_L1 {
    float: left;
    width: 33%;
    font-size: 14px;
    text-align: center;
}

.position_R {
    float: right;
    width: 33%;
    font-size: 14px;
    text-align: right;
}

.margin_L {
    margin-left: 5px;
}

.margin_R {
    margin-right: 5px;
    font-size: 14px;
}

.formList {
    border-collapse: collapse;
    width: 100%;
}

.formList th {
    height: 30px;
    text-align: center;
    font-size: 14px;
    font-weight: bold;
    border: 1px solid #138CDD;
    vertical-align: middle;
}

.formList td {
    height: 30px;
    word-break: break-all;
    font-size: 12px;
    border: 1px solid #138CDD;
    vertical-align: middle;
}

.td_style1 {
    text-align: left;
    font-weight: bold;
    color: #1a7bc9;
}

.td_styleCenter {
    text-align: center;
    font-weight: bold;
    color: #1a7bc9;
}

.inputCss {
    width: 100%;
    margin: -1px;
    border: 0px;
}

.input {
    width: 99%;
    height: 99%;
    border-top: 0px;
    border-left: 0px;
    border-right: 0px;
    border-bottom: 0px;
}

.flowList {
    border-collapse: collapse;
}

.flowList th {
    height: 30px;
    text-align: center;
    font-size: 12px;
    vertical-align: middle;
}

.flowList td {
    height: 30px;
    text-align: center;
    word-break: break-all;
    font-size: 12px;
    vertical-align: middle;
}

.qhUserIcon {
    background: url('../images/qhUser.png') no-repeat center;
    width: 30px;
    height: 30px;
    border: none;
    cursor: pointer;
}

.float_L {
    float: left;
}

.float_L_file {
    float: left;
}

.deleteBtn {
    background: url('../images/delete.png') no-repeat center;
    width: 30px;
    height: 30px;
    border: none;
    cursor: pointer;
}