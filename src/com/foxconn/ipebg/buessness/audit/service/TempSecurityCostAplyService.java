package com.foxconn.ipebg.buessness.audit.service;

import com.foxconn.ipebg.audit.entity.EFormSignEntity;
import com.foxconn.ipebg.audit.service.EBsCodeService;
import com.foxconn.ipebg.audit.service.EFormSignService;
import com.foxconn.ipebg.basics.service.ESignUserinfoService;
import com.foxconn.ipebg.basics.vo.FinancialVO;
import com.foxconn.ipebg.buessness.audit.dao.TempSecurityCostAplyDao;
import com.foxconn.ipebg.buessness.audit.entity.TempSecurityAplyEntity;
import com.foxconn.ipebg.buessness.audit.entity.TempSecurityCostAplyEntity;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.ConvertUtils;
import com.foxconn.ipebg.common.utils.StringUtils;
import com.foxconn.ipebg.system.dao.UserDao;
import com.foxconn.ipebg.system.entity.User;
import com.foxconn.ipebg.system.service.TPubMailrecordService;
import com.foxconn.ipebg.system.utils.UserUtil;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.hibernate.type.StandardBasicTypes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foxconn.ipebg.common.service.BaseBusinessService;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@Transactional(readOnly = true)
public class TempSecurityCostAplyService extends BaseBusinessService<TempSecurityCostAplyEntity, String> {
    @Autowired
    private TempSecurityCostAplyDao aplyDao;
    @Autowired
    private UserDao userDao;
    @Autowired
    private UserServiceUtil serviceUtil;
    @Autowired
    private ESignUserinfoService eSignUserinfoService;
    @Autowired
    private EBsCodeService eBsCodeService;
    @Autowired
    private EFormSignService eFormSignService;
    @Autowired
    private TPubMailrecordService mailrecordService;

    @Override
    public HibernateDao<TempSecurityCostAplyEntity, String> getEntityDao() {
        return aplyDao;
    }

    public TempSecurityCostAplyEntity findBySerialno(String serialno) {
        return aplyDao.findUniqueBy("serialno", serialno);
    }

    /**
     * 方法描述: 啟動流程
     *
     * @Author: S6114893
     * @CreateDate: 2022-07-07
     * @Return
     **/
    @Transactional
    public String startProcess(TempSecurityCostAplyEntity aplyEntity, EFormSignEntity[] eFormSignEntities, String contextPath, String formType, String flag) {
        try {
            if (flag.equals(Constant.RESULT.CODE_RUNNING.getValue())) {
                // 設置流水號
                if (StringUtils.isEmpty(aplyEntity.getSerialno())) {
                    aplyEntity.setNewRecord(true);
                    aplyEntity.setSerialno(serviceUtil.createSerialno("LSFY"));
                } else {
                    aplyEntity.setNewRecord(false);
                }
                aplyEntity.setSignEmpNo(eFormSignEntities[0].getEserId());
                aplyEntity.setSignEmpNam(eFormSignEntities[0].getEserNam());
                aplyEntity.setWorkstatus(Constant.RESULT.CODE_RUNNING.getValue());
                save(aplyEntity);

                String applyStat = "";//第一個審核節點狀態
                String signEmpNo = "", signEmpNam = "";
                for (int i = 0; i < eFormSignEntities.length; i++) {
                    EFormSignEntity node = eFormSignEntities[i];
                    if (node.getEserId().isEmpty()) {
                        continue;
                    }
                    if (applyStat == "") {
                        applyStat = node.getSignOrd().toString();
                        signEmpNo = node.getEserId();
                        signEmpNam = node.getEserNam();
                    }
                    node.setApplyId(aplyEntity.getSerialno());
                    node.setEserMail(eSignUserinfoService
                            .findByeserIdTypNodeName(node.getEserId(),
                                    formType, node.getSignOrd().toString())
                            .getEserMail());
                    node.setCreateDate(new Date());
                    node.setSignNam(eBsCodeService.getCode(formType, node.getSignOrd()).getCodeNam());
                    eFormSignService.save(node);
                }
                // 初始化主表表單狀態
                auditUpdateAply(aplyEntity.getSerialno(), applyStat,
                        signEmpNo, signEmpNam);
                mailrecordService.sendMail(aplyEntity, "Y", contextPath);
            }
        } catch (Exception e) {
            this.logger.info(e.getMessage(), e);
            return Constant.RESULT.CODE_NO.getValue();
        }
        return Constant.RESULT.CODE_YES.getValue();
    }

    @Transactional
    public String auditProcess(TempSecurityCostAplyEntity aplyEntity, String flag, String signOrd,
                               String clientIP, String memo, String contextPath) {
        try {
            String minStatWait = "", maxStatWait = "";
            EFormSignEntity currentNode = eFormSignService
                    .currentNode(aplyEntity.getSerialno());
            if (!signOrd.equals(currentNode.getSignOrd().toString())) {
                return Constant.RESULT.CODE_NO.getValue();
            }
            if (flag.equals("Y") && StringUtils.isNotEmpty(aplyEntity.getSerialno())) {
                // 同意
                minStatWait = currentNode.getSignOrd().toString();
                maxStatWait = eFormSignService.maxStatWait(aplyEntity.getSerialno());
                if (StringUtils.isNotEmpty(minStatWait)) {
                    eFormSignService.auditUpdate(aplyEntity.getSerialno(),
                            minStatWait, clientIP, "Y", memo);
                    EFormSignEntity nextNode = eFormSignService
                            .currentNode(aplyEntity.getSerialno());
                    String signEmpNo = "", signEmpNam = "";

                    if (minStatWait.equals(maxStatWait))
                        minStatWait = "E";
                    if (nextNode != null) {
                        signEmpNo = nextNode.getEserId();
                        signEmpNam = nextNode.getEserNam();
                        minStatWait = nextNode.getSignOrd().toString();
                    }
                    auditUpdateAply(aplyEntity.getSerialno(),
                            minStatWait, signEmpNo, signEmpNam);
                    if (minStatWait.equals("E")) {
                        mailrecordService.sendMail(aplyEntity, "E", contextPath);
                    } else {
                        mailrecordService.sendMail(aplyEntity, "Y", contextPath);
                    }
                }
            }
            // 駁回
            else if (flag.equals("N")
                    && StringUtils.isNotEmpty(aplyEntity.getSerialno())) {
                minStatWait = currentNode.getSignOrd().toString();
                maxStatWait = eFormSignService.maxStatWait(aplyEntity
                        .getSerialno());
                if (!minStatWait.equals("")) {
                    eFormSignService.auditUpdate(aplyEntity.getSerialno(),
                            minStatWait, clientIP, "N", memo);
                    eFormSignService.batchDeleteWait(aplyEntity.getSerialno());
                    // 駁回時發回申請人重新提交
                    EFormSignEntity node = new EFormSignEntity();
                    node.setApplyId(aplyEntity.getSerialno());
                    node.setEserId(aplyEntity.getMakerno());
                    node.setEserNam(aplyEntity.getMakername());
                    node.setEserMail(userDao.findUniqueBy("loginName", aplyEntity.getMakerno()).getEmail());
                    node.setCreateDate(new Date());
                    node.setSignOrd(currentNode.getSignOrd()+1);
                    node.setSignNam("駁回處理");
                    node.setCreateBy(currentNode.getEserId());
                    node.setCreateDate(new Date());
                    node.setEserStat("W");
                    eFormSignService.save(node);

                    auditUpdateAply(aplyEntity.getSerialno(), node.getSignOrd().toString(),
                            node.getEserId(), node.getEserNam());
                }
                mailrecordService.sendMail(aplyEntity, "N", contextPath);
            }
        } catch (Exception e) {
            this.logger.info(e.getMessage(), e);
            return Constant.RESULT.CODE_NO.getValue();
        }
        return Constant.RESULT.CODE_YES.getValue();
    }

    /**
     * 方法描述: 重新提交保存
     *
     * @Author: S6114893
     * @CreateDate: 2022-12-23
     * @Return
     **/
    @Transactional
    public String reStartProcess(TempSecurityCostAplyEntity aplyEntity, EFormSignEntity[] eFormSignEntities, String contextPath, String formType, String flag, String ip) {
        try {
            if (flag.equals(Constant.RESULT.CODE_RUNNING.getValue())) {
                aplyEntity.setNewRecord(false);
                aplyEntity.setSignEmpNo(eFormSignEntities[0].getEserId());
                aplyEntity.setSignEmpNam(eFormSignEntities[0].getEserNam());
                aplyEntity.setWorkstatus(Constant.RESULT.CODE_RUNNING.getValue());
                save(aplyEntity);

                // 獲取駁回處理節點
                EFormSignEntity rejectHandleNode = eFormSignService.currentNode(aplyEntity.getSerialno());
                rejectHandleNode.setSignNam("重新提交");
                rejectHandleNode.setEserIp(ip);
                rejectHandleNode.setEserStat("Y");
                rejectHandleNode.setEserDate(new Date());
                eFormSignService.save(rejectHandleNode);

                String applyStat = "";//第一個審核節點狀態
                String signEmpNo = "", signEmpNam = "";
                for (int i = 0; i < eFormSignEntities.length; i++) {
                    EFormSignEntity node = eFormSignEntities[i];
                    if (node.getEserId().isEmpty()) {
                        continue;
                    }
                    if (applyStat == "") {
                        applyStat = String.valueOf(rejectHandleNode.getSignOrd()+node.getSignOrd());;
                        signEmpNo = node.getEserId();
                        signEmpNam = node.getEserNam();
                    }
                    node.setApplyId(aplyEntity.getSerialno());
                    node.setEserMail(eSignUserinfoService
                            .findByeserIdTypNodeName(node.getEserId(),
                                    formType, node.getSignOrd().toString())
                            .getEserMail());
                    node.setCreateDate(new Date());
                    node.setSignNam(eBsCodeService.getCode(formType, node.getSignOrd()).getCodeNam());
                    node.setSignOrd(rejectHandleNode.getSignOrd()+node.getSignOrd());
                    eFormSignService.save(node);
                }
                // 初始化主表表單狀態
                auditUpdateAply(aplyEntity.getSerialno(), applyStat,
                        signEmpNo, signEmpNam);
                mailrecordService.sendMail(aplyEntity, "Y", contextPath);
            }
        } catch (Exception e) {
            this.logger.info(e.getMessage(), e);
            return Constant.RESULT.CODE_NO.getValue();
        }
        return Constant.RESULT.CODE_YES.getValue();
    }

    /**
     * 被駁回後，申請人取消申請
     * @param aplyEntity 申請單
     * @param ip 當前操作人IP地址
     * @return
     */
    @Transactional(readOnly = false)
    public String cancelApply(TempSecurityCostAplyEntity aplyEntity, String ip) {
        try {
            // 更新簽核記錄
            EFormSignEntity rejectHandleNode = eFormSignService.currentNode(aplyEntity.getSerialno());
            rejectHandleNode.setSignNam("取消申請");
            rejectHandleNode.setEserIp(ip);
            rejectHandleNode.setEserStat("Y");
            rejectHandleNode.setEserDate(new Date());
            eFormSignService.save(rejectHandleNode);
            // 更新申請單狀態
            auditUpdateAply(aplyEntity.getSerialno(), "C", "", "");
        } catch (Exception e) {
            this.logger.info(e.getMessage(), e);
            return Constant.RESULT.CODE_NO.getValue();
        }
        return Constant.RESULT.CODE_YES.getValue();
    }

    // 審核變更申請主表審核狀態
    public int auditUpdateAply(String serialno, String applyStat,
                               String signEmpNo, String signEmpNam) {
        String hql = "update TempSecurityCostAplyEntity t  set " + "t.applyStat = '"
                + applyStat + "',t.signEmpNo = '" + signEmpNo + "',"
                + "t.signEmpNam   = '" + signEmpNam + "'  "
                + "where  t.serialno='" + serialno + "' ";
        return aplyDao.batchExecute(hql);
    }

    /**
     * 經管報表-臨時性安保服務結算
     * @param params 查詢參數 dptQun legal costId securityCom startDate endDate
     * @return 列表數據
     */
    public List<FinancialVO> tempSecurityCostFinancialList(Map<String, String> params) {
        String securityCom = params.get("securityCom");
        String startDate = params.get("startDate");
        String endDate = params.get("endDate");
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            return Arrays.asList();
        }
        StringBuffer sql = new StringBuffer();
        sql.append("select a.cost_id as costId, round(COALESCE(sum(c.cost), 0), 2) tempCost2 ");
        sql.append("from e_temp_security_aply a, e_temp_security_cost_aply c ");
        sql.append("where a.apply_stat = 'E' ");
        sql.append("and c.apply_stat = 'E' ");
        sql.append("and a.serialno = c.service_serialno ");
        if (StringUtils.isNotBlank(securityCom)) {
            sql.append("and c.security_com = '"+securityCom+"' ");
        }
        User user = UserUtil.getCurrentUser();
        if (user != null) {
            // 按保安公司查询
            String securityComs = user.getSecurityCom().replace(",", "','");
            sql.append(" and c.security_com in ('"+securityComs+"') ");
        }
        sql.append("and a.start_time between to_date('"+startDate+"', 'yyyy-mm-dd') and to_date('"+endDate+"', 'yyyy-mm-dd') ");
        sql.append("group by a.cost_id");

        SQLQuery sqlQuery = this.aplyDao.createSQLQuery(sql.toString());
        sqlQuery.addScalar("costId",StandardBasicTypes.STRING);
        sqlQuery.addScalar("tempCost2",StandardBasicTypes.FLOAT);
        sqlQuery.setResultTransformer(Transformers.aliasToBean(FinancialVO.class));
        List<FinancialVO> resultList = sqlQuery.list();
        return resultList;
    }

    /**
     * 經管報表-臨時性安保服務結算
     * @param params 查詢參數 dptQun legal costId securityCom workMonth
     * @return 列表數據
     */
    public List<FinancialVO> tempSecurityCostFinancialList2(Map<String, String> params) {
        String securityCom = params.get("securityCom");
        String workMonth = params.get("workMonth");
        if (StringUtils.isBlank(workMonth)) {
            return Arrays.asList();
        }
        StringBuffer sql = new StringBuffer();
        sql.append("select a.cost_id as costId, a.legal_person as legal, round(COALESCE(sum(c.cost), 0), 2) tempCost2 ");
        sql.append("from e_temp_security_aply a, e_temp_security_cost_aply c ");
        sql.append("where a.apply_stat = 'E' ");
        sql.append("and c.apply_stat = 'E' ");
        sql.append("and a.serialno = c.service_serialno ");
        if (StringUtils.isNotBlank(securityCom)) {
            sql.append("and c.security_com = '"+securityCom+"' ");
        }
        User user = UserUtil.getCurrentUser();
        if (user != null) {
            // 按保安公司查询
            String securityComs = user.getSecurityCom().replace(",", "','");
            sql.append(" and c.security_com in ('"+securityComs+"') ");
        }
        sql.append("and date_trunc('month', a.start_time) = to_timestamp('"+workMonth+"', 'yyyymm') ");
        sql.append("group by a.cost_id, a.legal_person ");

        SQLQuery sqlQuery = this.aplyDao.createSQLQuery(sql.toString());
        sqlQuery.addScalar("costId",StandardBasicTypes.STRING);
        sqlQuery.addScalar("legal",StandardBasicTypes.STRING);
        sqlQuery.addScalar("tempCost2",StandardBasicTypes.FLOAT);
        sqlQuery.setResultTransformer(Transformers.aliasToBean(FinancialVO.class));
        List<FinancialVO> resultList = sqlQuery.list();
        ConvertUtils.convertPropertyToDictLabel(resultList, "legal", "guard_legalperson");
        return resultList;
    }
}
