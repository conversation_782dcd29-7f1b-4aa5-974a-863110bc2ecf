package com.foxconn.ipebg.common.taglib;

import com.foxconn.ipebg.buessness.common.entity.TQhAllRelationEntity;
import com.foxconn.ipebg.buessness.common.service.TQhAllRelationService;
import com.foxconn.ipebg.buessness.workflow.entity.InterConfig;
import com.foxconn.ipebg.buessness.workflow.entity.InterResult;
import com.foxconn.ipebg.buessness.workflow.entity.WfNodeparamEntity;
import com.foxconn.ipebg.buessness.workflow.service.ProcessService;
import com.foxconn.ipebg.buessness.workflow.service.WorkFlowService;
import com.foxconn.ipebg.common.utils.SpringContextUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import javax.servlet.jsp.JspWriter;
import javax.servlet.jsp.tagext.BodyTagSupport;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class ActionTag extends BodyTagSupport {
    private Logger logger = Logger.getLogger(ActionTag.class);
    private static final long serialVersionUID = 1L;
    private String onclick;
    private String cssClass;
    private String cssStyle;
    private String blankNum;
    private String dataOptions;
    //提交 的工單流水號
    private String serialNo;
    //回調方法
    private String callback;
    //方法執行前的自檢方法
    private String perCall;

    @Override
    public int doStartTag() {
        try {
            StringBuffer sbAll = new StringBuffer();
            JspWriter out = this.pageContext.getOut();
            WebApplicationContext wac = ContextLoader.getCurrentWebApplicationContext();
            WorkFlowService service = (WorkFlowService) wac.getBean("workFlowService");
            List<WfNodeparamEntity> entityList = service.queryNodeparam(serialNo);

            TQhAllRelationService allRelationService = (TQhAllRelationService) wac.getBean("TQhAllRelationService");
            ProcessService processService = (ProcessService) wac.getBean("processService");
            TQhAllRelationEntity relationEntity = allRelationService.queryByEntity(serialNo);
            InterConfig config = new InterConfig();
            config.setProcessId(relationEntity.getProcessid());
            //獲取流程當前節點處理人
            InterResult taskInfo = processService.currentTaskInfo(config);
            if (UserUtil.getCurrentUser().getLoginName().equals(taskInfo.getTaskInfoList().get(0).getAssignee())) {
                for (WfNodeparamEntity entity : entityList) {
                    StringBuffer sb = new StringBuffer(initTag());
                    sb.append(" name=\"mySign\"");
                    sb.append(" onclick=\"").append("audit('").append(serialNo).
                            append("',").append(entity.getIspass());
                    /*if (StringUtils.isNotEmpty(callback)) {
                        sb.append("," + callback);
                    }*/
                    if (StringUtils.isNotEmpty(perCall)) {
                        sb.append("," + perCall);
                    }
                    sb.append(");\">").append(entity.getDescrib())
                            .append("</a>&nbsp;&nbsp;&nbsp;&nbsp;");
                    sbAll.append(sb);
                }
                out.write(sbAll.toString());
            }
        } catch (IOException e) {
            e.printStackTrace();
            logger.info(e.getMessage(), e);
        }
        return 0;
    }

    private String initTag() {
        StringBuffer initStr = new StringBuffer("<a href=\"javascript:void(0);\"");
        if (StringUtils.isNotEmpty(cssClass)) {
            initStr.append(" class=\"").append(cssClass).append("\"");
        }
        if (StringUtils.isNotEmpty(cssStyle)) {
            initStr.append(" style=\"").append(cssStyle).append("\"");
        }
        if (StringUtils.isNotBlank(dataOptions)) {
            initStr.append(" data-options=\"").append(dataOptions).append("\"");
        }
        return initStr.toString();
    }


    public String getOnclick() {
        return onclick;
    }

    public void setOnclick(String onclick) {
        this.onclick = onclick;
    }

    public String getCssClass() {
        return cssClass;
    }

    public void setCssClass(String cssClass) {
        this.cssClass = cssClass;
    }

    public String getCssStyle() {
        return cssStyle;
    }

    public void setCssStyle(String cssStyle) {
        this.cssStyle = cssStyle;
    }

    public String getBlankNum() {
        return blankNum;
    }

    public void setBlankNum(String blankNum) {
        this.blankNum = blankNum;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getCallback() {
        return callback;
    }

    public void setCallback(String callback) {
        this.callback = callback;
    }

    public String getPerCall() {
        return perCall;
    }

    public void setPerCall(String perCall) {
        this.perCall = perCall;
    }

    public String getDataOptions() {
        return dataOptions;
    }

    public void setDataOptions(String dataOptions) {
        this.dataOptions = dataOptions;
    }
}
