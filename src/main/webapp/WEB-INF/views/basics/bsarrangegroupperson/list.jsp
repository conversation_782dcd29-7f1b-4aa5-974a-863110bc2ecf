<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>排班群組匹配合格人員</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
        <form id="searchFrom" action="">
        		<input type="text" id="filter_EQS_arrangeGroupId" name="filter_EQS_arrangeGroupId"  class="easyui-validatebox"
			   data-options="width:150,prompt: '排班群组'"/>
		
		<input type="text" name="filter_LIKES_empNo" class="easyui-validatebox"
			   data-options="width:150,prompt: '工號'"/>
		<input type="text" name="filter_LIKES_empName" class="easyui-validatebox"
			   data-options="width:150,prompt: '姓名'"/>
		
		<span class="toolbar-item dialog-tool-separator"></span>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
		   onclick="listSearchReset()">重置</a>
		<!-- <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel"
		   onclick="exportExcel()">导出Excel</a> -->
		   <br/>
		   	<shiro:hasPermission name="basics:bsarrangegroupperson:add">
	       		<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="add();">添加</a>
	       		<span class="toolbar-item dialog-tool-separator"></span>
	       	</shiro:hasPermission>
            <shiro:hasPermission name="basics:bsarrangegroupperson:delete">
	            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true" data-options="disabled:false" onclick="del()">移除</a>
	        	<span class="toolbar-item dialog-tool-separator"></span>
	        </shiro:hasPermission>
         <%--    <shiro:hasPermission name="basics:bsarrangegroupperson:update">
	            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true" onclick="upd()">修改</a>
	            <span class="toolbar-item dialog-tool-separator"></span>
	        </shiro:hasPermission> --%>

	</form>

  </div>
<table id="dg"></table>
<div id="dlg" style="overflow:hidden"></div>

<script src="${ctx}/static/js/basics/bsarrangegroupperson.js?random=<%= Math.random()%>"></script>
</body>
</html>