package com.foxconn.ipebg.system.utils;

import com.foxconn.ipebg.system.entity.Mail;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.Map;

public class SendMailUtil {
	private Logger logger = Logger.getLogger(SendMailUtil.class);
	public String sendMail(Mail mail){
		Map params = new HashMap();
		params.put("ITEM_TO", mail.getUsermail());//收件人郵件地址
		params.put("ITEM_SUBJECT", VelocityUtil.getMailSubject(mail));//主旨
		params.put("ITEM_CONTENT", VelocityUtil.getMailContent(mail));//內容
		params.put("ITEM_CONTENT_TYPE", "1");//內容發送方式
		params.put("ITEM_AUTH_NO", "sgas");//系統別名
//		params.put("ITEM_AUTH_NO", "noauth-scfhao");//系統別名
		String ret="1";
		try {
			String url = "http://**************:8080/api/SenderProvider/SendMail";//發送郵件接口
		/*	String subjectTemplateName = params.get("SUBJECT_TEMPLATE");
			String contentTemplateName = params.get("CONTENT_TEMPLATE");*/
		/*	params.put("ITEM_SUBJECT", "");
			params.put("ITEM_CONTENT", getContentByTemplate(contentTemplateName, params));
			params.put("ITEM_SEND_EXPECTDATE",DateTool.toLongDate(new Date()));*/
			String resultTemp=postRequest(url, params);
			if(StringUtils.isNotEmpty(resultTemp)) {
//				System.out.println(resultTemp);
				JSONObject jsonObject = new JSONObject(resultTemp);
				String result = jsonObject.getString("IsError");
				System.out.println(result);
				ret = "false".equals(result) ? "0" : "1";
				System.out.println(ret);
			}
		} catch (Exception e) {
			logger.info(e.getMessage(),e);
			return ret;
		}
		return ret;
	}

public String postRequest(String url, Map<String, String> map) {
		HttpClient client = new HttpClient();
		PostMethod method = new PostMethod(url);
		String message="";
		try {
			client.getParams().setContentCharset("utf-8");
			client.getHttpConnectionManager().getParams().setConnectionTimeout(5000);
			method.addParameters(mapToPairs(map));
			client.executeMethod(method);
			int code = method.getStatusCode();
			if(code==200){
				message= method.getResponseBodyAsString();
			}else{
				System.out.println("Request Error "+url+":"+code);
			}
			method.releaseConnection();
		} catch (Exception e) {
			logger.info(e.getMessage(),e);
		}
		return message;
	}

	private static NameValuePair[] mapToPairs(Map<String, String> map) throws UnsupportedEncodingException {
		NameValuePair[] pairArray = new NameValuePair[map.size()];
		int i=0;
		for(Map.Entry<String, String> entry:map.entrySet()){
			pairArray[i++]=new NameValuePair(entry.getKey(), String.valueOf(entry.getValue()));
		}
		return pairArray;
	}
	public static void main(String[] args) throws UnknownHostException {
		Map<String, String> params = new HashMap<String,String>();
		params.put("ITEM_TO","<EMAIL>");//收件人
		//params.put("ITEM_BCC","<EMAIL>");//密送人
		params.put("ITEM_SUBJECT","這是測試郵件");//主旨
		params.put("ITEM_CONTENT","這是測試郵件!");//內容
		params.put("ITEM_CONTENT_TYPE","0");//內容發送方式
		params.put("ITEM_SEND_EXPECTDATE","1231");//發送時間
		params.put("ITEM_AUTH_NO","PJ-18-5400");//系統別名
		params.put("ITEM_CREATOR","H2012095");//創建者

		String url = "http://**************:8080/api/SenderProvider/SendMail";//發送郵件接口
		String resultTemp=new SendMailUtil().postRequest(url, params);
		System.out.println(resultTemp);
//		Map params = new HashMap();
//		params.put("ITEM_TO", mL.getEmail());//收件人郵件地址
//		params.put("ITEM_SUBJECT", mL.getSubject());//主旨
//		params.put("ITEM_CONTENT", mL.getContent());//內容
//		params.put("ITEM_CONTENT_TYPE", "1");//內容發送方式
//		params.put("ITEM_AUTH_NO", "ESIGN_DEV");//系統別名

		/*params.put("ITEM_EMERGENCY","1");//優先級
		params.put("ITEM_AUTH_ID","clssId");//系統別名Id
		params.put("MAILSENDER_ALIAS","郵件來源");//郵件來源
		params.put("CALLBACK_URL","CALLBACK_URL");//郵件來源
		params.put("MessageAttaches","Collection of Message_Attach_View");//Collection of Message_Attach_View
Name	Description	Type	Additional information
ITEM_TO	收件人	string	None.
ITEM_CC	抄送	string	None.
ITEM_BCC	密送	string	None.
ITEM_SUBJECT	主旨	string	None.
ITEM_SEND_EXPECTDATE	期望發送時間	date	None.
ITEM_EMERGENCY	優先級，默認1	integer	None.
ITEM_CREATOR	創建人	string	None.
ITEM_CONTENT	內容	string	None.
ITEM_CONTENT_TYPE	內容發送方式：0：純文本，1：HTML	string	None.
ITEM_AUTH_NO	系統別名 英文	string	None.
ITEM_AUTH_ID		integer	None.
MAILSENDER_ALIAS	郵件來源	string	None.
CALLBACK_URL		string	None.
MessageAttaches		Collection of Message_Attach_View	None.
*/
	}
}
