<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>警衛服務費用結賬明細主表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
        var _bsDptList=${bsDptList};
        var _legalPersonDicts=${legalPersonDicts};
        var _bu, _legalId, _workMonth;
    </script>
    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
    <script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <style type="text/css">
        .grid-colunm {
            width: 100%;
            border: 1px solid #FFFFFF;
            text-align: center;
        }
        .grid-colunm-border {
            width: 100%;
            border-bottom: 1px solid #cccccc;
        }
        .grid-colunm:hover {
            width: 100%;
            text-align: center;
            border: 1px solid #999dff;
            background-color: #e0ecff;
            cursor:pointer;
        }
        .trainee{
            float: left;
            text-align: center;
            border: 1px solid #e0ecff;
            width: 140px;
            padding: 10px 5px;
            border-radius: 5px;
            margin: 5px 2px;
        }
        .resign-trainee{
            float: left;
            text-align: center;
            border: 1px solid #e0ecff;
            background-color: #9d9d9d;
            width: 140px;
            padding: 10px 5px;
            border-radius: 5px;
            margin: 5px 2px;
        }
        .trainee-title{
            text-align: center;
            background-color: #e0ecff;
            padding: 10px 0px;
            color: #783bc7;
            cursor:pointer;
        }
    </style>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
    <form id="searchFrom" action="">
        <input type="text" id="filter_buNam" name="filter_EQS_buNam" class="easyui-combobox" data-options="width:150,prompt: '事業處'"/>
        <input type="text" id="filter_legalId" name="filter_EQS_legalId" class="easyui-combobox" data-options="width:150,prompt: '法人'"/>
        <input type="text" id="filter_workMonth" name="filter_EQS_workMonth" class="easyui-my97" datefmt="yyyy-MM" data-options="width:150,prompt: '月份'"/>

        <span class="toolbar-item dialog-tool-separator"></span>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
        <a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
           onclick="listSearchReset()">重置</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel"
           onclick="exportExcel()">導出Excel</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-sum"
           onclick="showSummary()">查看匯總</a>
    </form>
</div>
<table id="dg"></table>
<div id="modal" class="easyui-window" title="人員明細" data-options="modal:true,closed:true,iconCls:'icon-blank'" style="width:600px;height:500px;padding:10px;">
    <div id="title" class="trainee-title" onclick="closeModel()"></div>
    <div id="personArr">
    </div>
</div>
<div id="dlg"></div>
<script src="${ctx}/static/js/basics/bsservicecost.js?random=<%= Math.random()%>"></script>
</body>
</html>
