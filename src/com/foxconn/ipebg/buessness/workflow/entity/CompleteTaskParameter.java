package com.foxconn.ipebg.buessness.workflow.entity;

/**
 * Company foxconn
 * Copyright (C) 2004-2018 All Rights Reserved.
 */
public class CompleteTaskParameter {
    private String serialno;
    private String taskId;
    private String variables;
    private String workFlowId;
    private String processId;
    private String ip;
    private String attachidsremark;

    public String getSerialno() {
        return serialno;
    }

    public void setSerialno(String serialno) {
        this.serialno = serialno;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getVariables() {
        return variables;
    }

    public void setVariables(String variables) {
        this.variables = variables;
    }

    public String getWorkFlowId() {
        return workFlowId;
    }

    public void setWorkFlowId(String workFlowId) {
        this.workFlowId = workFlowId;
    }

    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getAttachidsremark() {
        return attachidsremark;
    }

    public void setAttachidsremark(String attachidsremark) {
        this.attachidsremark = attachidsremark;
    }
}
