<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>簽名檔</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>

    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/bssignature/${action}" method="post">
    <input type="hidden" name="delFlag"  value="${bsSignature.delFlag }"/>
    <input type="hidden" name="ids" value="${bsSignature.id}"/>
    <table class="formTable">
        <tr>
            <td>工號：</td>
            <td>
                <input id="signatureNo" name="signatureNo" class="easyui-validatebox"
                        <c:if test="${action eq 'update'}">readonly</c:if>
                       data-options="width: 245,required:true" value="${bsSignature.signatureNo }"/>
            </td>
        </tr>
        <tr>
            <td>姓名：</td>
            <td>
                <input id="signatureName" name="signatureName" class="easyui-validatebox" data-options="width: 245"
                       value="${bsSignature.signatureName }"/>
            </td>
        </tr>

        <tr>
            <td>簽名：</td>
            <td>
                <input id="image" type="file" />
                <input id="imageValue" type="hidden" name="image" value="${bsSignature.image }"/>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>
                <img id="showImg" style="max-width:180px;max-height: 180px;" src="${bsSignature.image }"/>
            </td>
        </tr>
        <tr>
            <td>備註：</td>
            <td>
                <input id="signatureDesc" name="signatureDesc" class="easyui-validatebox" data-options="width: 245"
                       value="${bsSignature.signatureDesc }"/>
            </td>
        </tr>

        <tr style="display: none">
            <td>
                <input id="createBy" name="createBy" class="easyui-validatebox" data-options="width: 150" disabled="disabled"
                       value="${bsSignature.createBy }"/>
                <input id="createDate" name="createDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
                       data-options="width: 160" disabled="disabled"
                       value="<fmt:formatDate value="${bsSignature.createDate}"/>"/>
                <input id="updateBy" name="updateBy" class="easyui-validatebox" data-options="width: 150" disabled="disabled"
                       value="${bsSignature.updateBy }"/>
                <input id="updateDate" name="updateDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
                       data-options="width: 160" disabled="disabled"
                       value="<fmt:formatDate value="${bsSignature.updateDate}"/>"/>
            </td>
        </tr>
    </table>
</form>
</div>
<script src='${ctx}/static/js/basics/bssignature.js?"+Math.random()"'></script>
<script type="text/javascript">
    //提交表单
    $('#mainform').form({
        onSubmit: function () {
            var isValid = $(this).form('validate');
            return isValid;	// 返回false终止表单提交
        },
        success: function (data) {
            if(data=='success'){
                successTip(data, dg, d);
            }else {
                parent.$.messager.alert("溫馨提示", data, "error");
            }


        }
    });
</script>
</body>
</html>