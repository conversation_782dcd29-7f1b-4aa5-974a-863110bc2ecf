package com.foxconn.ipebg.report.controller;

import com.alibaba.fastjson.JSON;
import com.foxconn.ipebg.basics.service.BsDptService;
import com.foxconn.ipebg.basics.service.BsServiceDayCostService;
import com.foxconn.ipebg.basics.vo.FinancialVO;
import com.foxconn.ipebg.buessness.audit.service.TempSecurityCostAplyService;
import com.foxconn.ipebg.common.exception.ExcelException;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.utils.ConvertUtils;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.report.entity.ServicePersonCostEntity;
import com.foxconn.ipebg.report.service.ServicePersonCostService;
import com.foxconn.ipebg.system.entity.Dict;
import com.foxconn.ipebg.system.entity.User;
import com.foxconn.ipebg.system.service.DictService;
import com.foxconn.ipebg.system.utils.CustomExcelUtil;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 園區警衛服務器費用報表v3（按警衛人員統計）
 */
@Controller
@RequestMapping("servicepersoncost")
public class ServicePersonCostController extends BaseController {
    @Autowired
    private ServicePersonCostService service;

    @Autowired
    private BsDptService bsDptService;

    @Autowired
    private BsServiceDayCostService bsServiceDayCostService;

    @Autowired
    private TempSecurityCostAplyService tempSecurityCostAplyService;

    /**
     * 方法描述: 列表信息
     * @Author: ********
     * @CreateDate: 2022-12-15
     * @Return
     **/
    @RequestMapping(method = RequestMethod.GET)
    public String list(Model model) {
        model.addAttribute("bsDptList", JSON.toJSONString(bsDptService.getAll()));
        return "report/servicepersoncost";
    }

    /**
     * 方法描述:  分頁查詢信息
     * @Author: ********
     * @CreateDate:   2022-12-15
     * @Return
     **/
    @RequestMapping(value = "list", method = RequestMethod.GET)
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<ServicePersonCostEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        User user = UserUtil.getCurrentUser();
        if (user != null) {
            // 按保安公司查询
            PropertyFilter filter = new PropertyFilter("INS_company", user.getSecurityComs());
            filters.add(filter);
        }
        page.setOrderBy("periodEndDate");
        page.setOrder(Page.DESC);
        page = service.search(page, filters);
        ConvertUtils.convertPropertyToDictLabel(page, "legal", "guard_legalperson");
        return getEasyUIData(page);
    }

    /**
     * 方法描述：查看匯總信息
     * */
    @RequestMapping(value = "summary", method = RequestMethod.GET)
    public String summary() {
        return "report/servicepersoncostsummary";
    }

    @ResponseBody
    @RequestMapping(value = "getSummary", method = RequestMethod.GET)
    public Map<String, Object> getSummary(HttpServletRequest request) {
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        User user = UserUtil.getCurrentUser();
        if (user != null) {
            // 按保安公司查询
            PropertyFilter filter = new PropertyFilter("INS_company", user.getSecurityComs());
            filters.add(filter);
        }
        List<String> properties = Arrays.asList("dayCount", "normalCost", "traineeCost", "overtimeCost", "totalCost");
        List<String> propertyTitles = Arrays.asList("工作天數", "應發薪資", "培訓費用", "加班費", "實發薪資");
        List<Dict> result = service.getSummary(filters, properties, propertyTitles);
        // 添加常規費用欄位
        BigDecimal normalCost = new BigDecimal(result.get(1).getValue());
        BigDecimal traineeCost = new BigDecimal(result.get(2).getValue());
        BigDecimal regularCost = normalCost.add(traineeCost);
        Dict regularDict = new Dict();
        regularDict.setLabel("常規費用");
        regularDict.setValue(regularCost.toString());
        result.add(3, regularDict);
        return getEasyUIData(result);
    }

    /**
     * 导出excel
     * @Author: ********
     * @CreateDate: 2022-12-15
     * @Return
     **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Page<ServicePersonCostEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        User user = UserUtil.getCurrentUser();
        if (user != null) {
            // 按保安公司查询
            PropertyFilter filter = new PropertyFilter("INS_company", user.getSecurityComs());
            filters.add(filter);
        }
        page.setOrderBy("periodEndDate");
        page.setOrder(Page.DESC);
        page.setPageNo(1);
        page.setPageSize(Page.MAXSIZE);
        page = service.search(page, filters);
        ConvertUtils.convertPropertyToDictLabel(page, "legal", "guard_legalperson");
        // 計算總計
        List<String> properties = Arrays.asList("dayCount", "normalCost", "traineeCost", "overtimeCost", "totalCost");
        List<Dict> summaryList = service.getSummary(filters, properties, properties);
        ServicePersonCostEntity summary = new ServicePersonCostEntity();
        for (Dict sumDict : summaryList) {
            if (sumDict.getLabel().equals("dayCount")) {
                summary.setDayCount(Integer.parseInt(sumDict.getValue()));
            } else if (sumDict.getLabel().equals("normalCost")) {
                summary.setNormalCost(Double.parseDouble(sumDict.getValue()));
            } else if (sumDict.getLabel().equals("traineeCost")) {
                summary.setTraineeCost(Double.parseDouble(sumDict.getValue()));
            } else if (sumDict.getLabel().equals("overtimeCost")) {
                summary.setOvertimeCost(Double.parseDouble(sumDict.getValue()));
            } else if (sumDict.getLabel().equals("totalCost")) {
                summary.setTotalCost(Double.parseDouble(sumDict.getValue()));
            }
        }
        summary.setEmpNo("合計");
        page.getResult().add(summary);
        try {
            ExcelUtil.listToExcel(page.getResult(), ServicePersonCostEntity.excelFieldMap(), "園區警衛服務費用結報明細表", response);
        } catch (ExcelException e) {
            e.printStackTrace();
        }
    }

    /**
     * 經管報表v3
     */
    @RequestMapping(value = "financial", method = RequestMethod.GET)
    public String financialPage () {
        return "report/servicepersoncostfinancial";
    }

    /**
     * 經管報表數據v3
     */
    @RequestMapping("financialData")
    @ResponseBody
    public List<FinancialVO> financialData(@RequestParam Map<String, String> params){
        return fixedFincialData(params);
    }

    /**
     * 經管報表數據合成
     * @param params 參數
     * @return
     */
    private List<FinancialVO> fixedFincialData(Map<String, String> params) {
        List<FinancialVO> result = service.financialList(params);
        List<FinancialVO> tempSecurityServiceFinancialList = tempSecurityCostAplyService.tempSecurityCostFinancialList2(params);
        List<String> costIdList = new ArrayList<>();
        for (FinancialVO item : result) {
            costIdList.add(item.getCostId());
        }
        // 添加沒有統計到的臨時安保服務費用統計
        for (FinancialVO item : tempSecurityServiceFinancialList) {
            if (!costIdList.contains(item.getCostId())) {
                result.add(item);
            }
        }
        return result;
    }

    /**
     * 經管報表导出excel 2022-12-29
     * @Author: ********
     **/
    @RequestMapping("exportFinancial")
    public void exportExcel(@RequestParam Map<String, String> params, HttpServletRequest request, HttpServletResponse response) throws Exception {
        CustomExcelUtil excelUtil = null;
        try {
            excelUtil = new CustomExcelUtil(response, null);
            excelUtil.createNewSheet("經管報表", 0);
            bsServiceDayCostService.generateExcelHeader(excelUtil.getCurrentSheet());
            List<FinancialVO> gathers = fixedFincialData(params);
            bsServiceDayCostService.fillSheet(excelUtil.getCurrentSheet(), gathers);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (excelUtil != null) {
                try {
                    excelUtil.closeWorkBook();
                } catch (Exception e) {}
            }
        }
    }
}
