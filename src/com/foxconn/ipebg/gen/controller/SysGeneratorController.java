package com.foxconn.ipebg.gen.controller;

import com.alibaba.fastjson.JSON;
import com.foxconn.ipebg.common.utils.DateUtils;
import com.foxconn.ipebg.common.utils.ReType;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.common.xss.XssHttpServletRequestWrapper;
import com.foxconn.ipebg.gen.entity.TableEntity;
import com.foxconn.ipebg.gen.service.SysGeneratorService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 类SysGeneratorController的功能描述:
 * 代码生成器
 * @auther hxy
 * @date 2017-08-25 16:20:43
 */
@Controller
@RequestMapping("sys/generator")
public class SysGeneratorController extends BaseController {
	@Autowired
	private SysGeneratorService sysGeneratorService;
	@RequestMapping(value = "showTableList")
//	@RequiresPermissions(value = "gen:show")
	public String showRole(Model model) {
		return "/system/gen/tableList";
	}
	/**
	 * 列表
	 */
	@ResponseBody
	@RequestMapping(value = "/list",method = RequestMethod.GET)
	public String list(@RequestParam Map<String, Object> params){
		//查询列表数据
		List<TableEntity> list = sysGeneratorService.queryList(params);
		ReType reType = new ReType(sysGeneratorService.queryTotal(params), list);

		return JSON.toJSONString(reType);
	}
	
	/**
	 * 生成代码
	 */
	@RequestMapping("/code")
	public void code(HttpServletRequest request, HttpServletResponse response) throws IOException {
		String[] tableNames = new String[]{};
		//获取表名，不进行xss过滤
		HttpServletRequest orgRequest = XssHttpServletRequestWrapper.getOrgRequest(request);
		String tables = orgRequest.getParameter("tables");
		tableNames = JSON.parseArray(tables).toArray(tableNames);
		
		byte[] data = sysGeneratorService.generatorCode(tableNames, 1);
		
		response.reset();  
        response.setHeader("Content-Disposition", "attachment; filename=\"waterQualityTesting"+ DateUtils.getDateRandom()+".zip\"");
        response.addHeader("Content-Length", "" + data.length);  
        response.setContentType("application/octet-stream; charset=UTF-8");  
  
        IOUtils.write(data, response.getOutputStream());
	}
}
