package com.foxconn.ipebg.buessness.common.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 生成工單流水功能表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-08 09:28:36
 */
@Entity
@Table(name = "T_QH_CURRENTSERIALNO")
@DynamicUpdate
@DynamicInsert
public class TQhCurrentserialnoEntity extends DataEntity<TQhCurrentserialnoEntity>  implements Serializable {
    private static final long serialVersionUID = 1L;

            //本地廠區代碼
        private String localid;
            //日期
        private String curdate;
            //當前序號
        private Integer curid;

            /**
         * 设置：本地廠區代碼
         */
        public void setLocalid(String localid) {
            this.localid = localid;
        }
    /**
     * 获取：本地廠區代碼
     */
        @Column(name = "LOCALID", nullable = false, length = 20)
        public String getLocalid() {
            return localid;
        }
        
            /**
         * 设置：日期
         */
        public void setCurdate(String curdate) {
            this.curdate = curdate;
        }
    /**
     * 获取：日期
     */
                    @Column(name = "CURDATE", nullable = false, length = 20)
            public String getCurdate() {
                return curdate;
            }
        
            /**
         * 设置：當前序號
         */
        public void setCurid(Integer curid) {
            this.curid = curid;
        }
    /**
     * 获取：當前序號
     */
                    @Column(name = "CURID", nullable = false, length = 20)
            public Integer getCurid() {
                return curid;
            }
        
    }
