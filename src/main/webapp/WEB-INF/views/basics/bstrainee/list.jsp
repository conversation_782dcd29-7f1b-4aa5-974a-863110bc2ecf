<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>培訓人員基本資料表</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div id="tb" style="padding:5px;height:auto">
        <form id="searchFrom" action="">
		<input type="text" name="filter_LIKES_empNo" class="easyui-validatebox"
			   data-options="width:150,prompt: '工號'"/>
			   		<input type="text" name="filter_LIKES_empName" class="easyui-validatebox"
			   data-options="width:150,prompt: '姓名'"/>
			   		<input type="text" name="filter_LIKES_psnId" class="easyui-validatebox"
			   data-options="width:150,prompt: '身份證號'"/>
			   	    <input type="text" id="filter_EQS_status" name="filter_EQS_status" class="easyui-validatebox"
			   data-options="width:150,prompt: '性別'"/> 
			   
			  <!--  <select id="filter_EQS_status" class="easyui-validatebox" name="filter_EQS_status"  
					>
					<option value=""></option>
						<option value="0">男</option>
						<option value="1">女</option>
						

				</select> -->
			   		   		<input type="text" id="filter_EQS_company" name="filter_EQS_company" class="easyui-validatebox"
			   data-options="width:150,prompt: '保安公司'"/>

		<span class="toolbar-item dialog-tool-separator"></span>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
		<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh"
		   onclick="listSearchReset()">重置</a>
	<!-- 	<a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel"
		   onclick="exportExcel()">导出Excel</a> -->
		   <br/>
		   	<shiro:hasPermission name="basics:bstrainee:add">
	       		<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="add();">添加</a>
	       		<span class="toolbar-item dialog-tool-separator"></span>
	       	</shiro:hasPermission>
            <shiro:hasPermission name="basics:bstrainee:delete">
	            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true" data-options="disabled:false" onclick="del()">删除</a>
	        	<span class="toolbar-item dialog-tool-separator"></span>
	        </shiro:hasPermission>
            <shiro:hasPermission name="basics:bstrainee:update">
	            <a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true" onclick="upd()">修改</a>
	            <span class="toolbar-item dialog-tool-separator"></span>
	        </shiro:hasPermission>
	        
	        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <a href="#"
				id="batchImport" class="easyui-linkbutton"
				data-options="iconCls:'icon-hamburg-cv'" style="width: 100px;"
				onclick="openBatchImportWin();">批量導入</a> 導入樣式&nbsp; <a href="#"
				plain="true" id="btnBatchImportTpl">參考.xls</a>

	</form>
		<div id="optionWin" class="easyui-window" title="培訓人員批量導入"
			style="width:800px;height:200px;" collapsible="false"
			maximizable="false" minimizable="false" resizable="false"
			modal="true" closed="true"
			data-options="iconCls:'PageAdd', footer:'#addFooter'">
			<form id="batchImportForm" name="batchImportForm" method="post"
				class="fm" enctype="multipart/form-data">
				<br />
				<table width="100%">
					<tr align="left">
						<td style="width: 60%; white-space: nowrap;"><input
							id="batchFile" name="batchFile" type="file" style="width: 300px"
							accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />

							<a href="#" id="btnUploadExcel" class="easyui-linkbutton"
							onclick="btnUploadExcel();">文檔上傳</a></td>


						<td style="width: 60%; white-space: nowrap;"><span
							ID="labelListAddResult"> </span>
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <a
							href="${ctx}/bstrainee/downLoad/errorExcel"
							id="downloadError" plain="true">查看錯誤信息</a></td>
					</tr>

				</table>
				<!-- <table id="errorList" style="width:980px;height:600px;"></table> -->

			</form>
		</div>

  </div>
<table id="dg"></table>
<div id="dlg"></div>

<script src="${ctx}/static/js/basics/bstrainee.js?random=<%= Math.random()%>"></script>
</body>
</html>