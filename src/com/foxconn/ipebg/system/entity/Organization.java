package com.foxconn.ipebg.system.entity;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;

import org.hibernate.annotations.*;
import org.hibernate.annotations.Cache;

/**
 * 机构entity
 *
 * <AUTHOR>
 * @date 2015年5月9日
 */
@Entity
@Table(name = "sys_organization")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@DynamicUpdate
@DynamicInsert
public class Organization implements java.io.Serializable {

    // Fields

    private static final long serialVersionUID = 1L;
    private Integer id;
    private String orgName;
    private Integer pid;
    private String orgType;
    private Integer orgSort;
    private Integer orgLevel;
    private String orgCode;
    private Integer areaId;

    // Constructors

    /**
     * default constructor
     */
    public Organization() {
    }

    /**
     * minimal constructor
     */
    public Organization(Integer id, String orgName) {
        this.id = id;
        this.orgName = orgName;
    }

    /**
     * full constructor
     */
    public Organization(Integer id, String orgName, Integer pid,
                        String orgType, Integer orgSort, Integer orgLevel) {
        this.id = id;
        this.orgName = orgName;
        this.pid = pid;
        this.orgType = orgType;
        this.orgSort = orgSort;
        this.orgLevel = orgLevel;
    }

    // Property accessors
    @Id
    @SequenceGenerator(name = "generator", sequenceName = "sys_organization_sequence", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "generator")
    @Column(name = "id", unique = true, nullable = false)
    public Integer getId() {
        return this.id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "org_name", nullable = false)
    public String getOrgName() {
        return this.orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    @Column(name = "pid")
    public Integer getPid() {
        return this.pid;
    }

    public void setPid(Integer pid) {
        this.pid = pid;
    }

    @Column(name = "org_type")
    public String getOrgType() {
        return this.orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    @Column(name = "org_sort")
    public Integer getOrgSort() {
        return this.orgSort;
    }

    public void setOrgSort(Integer orgSort) {
        this.orgSort = orgSort;
    }

    @Column(name = "org_level")
    public Integer getOrgLevel() {
        return this.orgLevel;
    }

    public void setOrgLevel(Integer orgLevel) {
        this.orgLevel = orgLevel;
    }

    @Column(name = "org_code")
    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    @Column(name = "area_id")
    public Integer getAreaId() {
        return areaId;
    }

    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } ;
        if (o == null || getClass() != o.getClass()) {
            return false;
        } ;

        Organization person = (Organization) o;

        if (!id.equals(person.id)) {
            return false;
        } ;
        return orgName.equals(person.orgName);

    }

    @Override
    public int hashCode() {
        int result = id.hashCode();
        result = 31 * result + orgName.hashCode();
        return result;
    }

}