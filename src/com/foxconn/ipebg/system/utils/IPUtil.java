package com.foxconn.ipebg.system.utils;

import com.foxconn.ipebg.common.utils.StringUtils;
import org.apache.commons.net.util.SubnetUtils;

import javax.servlet.http.HttpServletRequest;

/**
 * @description 获取客户端ip
 * <AUTHOR>
 * @date 2014年4月3日 下午4:30:27
 */
public class IPUtil {
	public static String getIpAddress(HttpServletRequest request) {
		String ip = request.getHeader("x-forwarded-for");
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getRemoteAddr();
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("http_client_ip");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("HTTP_X_FORWARDED_FOR");
		}
		if (ip != null && ip.indexOf(",") != -1) {
			ip = ip.substring(ip.lastIndexOf(",") + 1, ip.length()).trim();
		}
		if ("0:0:0:0:0:0:0:1".equals(ip)) {
			ip = "127.0.0.1";
		}
		return ip;
	}

	/**
	 * 判断一个IP是否满足规则
	 * @param ip 一个单独的IP地址，如：***********
	 * @param rules 规则，多个规则可用逗号分割，如 ***********,***********/24
	 */
	public static boolean isIPMatchRules(String ip, String rules) {
		if (StringUtils.isEmpty(rules)) {
			return true;
		}
		String[] ruleArray = rules.split(",");
		boolean match = false;
		for (String rule: ruleArray) {
			if (!rule.contains("/") && rule.contains(".")) {
				match = match || rule.equals(ip);
			} else {
				SubnetUtils utils = new SubnetUtils(rule);
				match = match || utils.getInfo().isInRange(ip);
			}
			if (match) {
				break;
			}
		}
		return match;
	}

	public static void main(String[] args) {
		String ip = "*************";
		String[] rules = {
			"**********/16,*********/16",
			"*************",
			"*********/16"
		};
		for (String rule: rules) {
			System.out.println(ip +
					(isIPMatchRules(ip, rule) ? " match ": " not match ") +
					rule);
		}
	}
}
