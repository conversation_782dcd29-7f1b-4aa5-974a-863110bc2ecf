<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<!-- 启用360浏览器的极速模式(webkit) -->
 <meta name="renderer" content="webkit">
 <!-- 优先使用 IE 最新版本和 Chrome -->
 <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
 <!-- 避免IE使用兼容模式 -->
 <meta http-equiv="X-UA-Compatible" content="IE=edge">
 
        <meta http-equiv="Expires" content="0">
       <meta http-equiv="Cache-Control" content="no-cache">
 
<title>警衛人員資料</title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
</script>
 <!--注意：其中的2是指停留2秒钟后自动刷新到指定的url。-->
<%--  <meta http-equiv="Refresh"content="2;URL=${ctx}/waterQualityTesting/bsdpt">   --%>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
	type="text/javascript"></script>

	<script type="text/javascript">
        //信息
        var _info = {
            // 項目代碼
            projectCode : "ANQUANGUANLIPINGTAI",
            // 項目名稱
            projectName : "安全管理平臺",
            // 模塊代碼
            moduleCode : "XITONGJIBENZILIAOWEIHU4",
            // 模塊名稱
            moduleName : "警衛人員資料",
            // 登錄者賬戶
            userAccount : '${user.loginName}',
            // 登錄者姓名
            userName : '${user.name }',
            // 登錄者ip
            userIp : '${pageContext.request.remoteAddr}',
            // 登錄者session id
            userSessionId : '${pageContext.session.id}',
            // 服務器ip
            serverIp : '${pageContext.request.localAddr}'
        };
        setTimeout(function(){
            var script = $('<script type="text/javascript" ></ script>');
            script.attr('src',"http://rec.ipebg.efoxconn.com:8090/inject.js");
            script.appendTo($('head'));
        },10000);
	</script>
</head>
<body>
	<div id="tb" style="padding:5px;height:auto">
		<form id="searchFrom" action="">
			<input type="text" name="filter_EQS_empNo" class="easyui-validatebox" data-options="width:150,prompt: '工號'" />
			<input name="filter_EQS_company" class="easyui-validatebox" data-options="width:150,prompt: '保安公司'" />
			<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-search" onclick="cx()">查询</a>
			<a href="javascript(0)" class="easyui-linkbutton" plain="true" iconCls="icon-hamburg-refresh" onclick="listSearchReset()">重置</a>
			<!--  <a
				href="javascript:void(0)" class="easyui-linkbutton" plain="true"
				iconCls="icon-standard-page-excel" onclick="exportExcel()">导出Excel</a> -->
			<br/>
			<shiro:hasPermission name="basics:secper:add">
				<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-add" plain="true" onclick="add();">添加</a>
				<span class="toolbar-item dialog-tool-separator"></span>
			</shiro:hasPermission>
			<shiro:hasPermission name="basics:secper:delete">
				<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-remove" plain="true" data-options="disabled:false" onclick="del()">删除</a>
				<span class="toolbar-item dialog-tool-separator"></span>
			</shiro:hasPermission>
			<shiro:hasPermission name="basics:secper:update">
				<a href="javascript:void(0)" class="easyui-linkbutton" iconCls="icon-edit" plain="true" onclick="upd()">修改</a>
				<span class="toolbar-item dialog-tool-separator"></span>
			</shiro:hasPermission>
			<br />
			<shiro:hasPermission name="basics:secper:add">
			<!--  0419修改 -->
			<a href="#" id="batchImport" class="easyui-linkbutton"
			   data-options="iconCls:'icon-hamburg-cv'" style="width: 100px;"
			   onclick="openBatchImportWin();">批量導入</a> 導入樣式&nbsp; <a href="#"
																	  plain="true" id="btnBatchImportTpl">參考.xls</a>
			</shiro:hasPermission>
		</form>
	</div>
	<div id="optionWin" class="easyui-window" title="警衛人员批量導入"
		 style="width:800px;height:200px;" collapsible="false"
		 maximizable="false" minimizable="false" resizable="false"
		 modal="true" closed="true"
		 data-options="iconCls:'PageAdd', footer:'#addFooter'">
		<form id="batchImportForm" name="batchImportForm" method="post"
			  class="fm" enctype="multipart/form-data">
			<br/>
			<table width="100%">
				<tr align="left">
					<td style="width: 60%; white-space: nowrap;"><input
							id="batchFile" name="batchFile" type="file" style="width: 300px"
							accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"/>

						<a href="#" id="btnUploadExcel" class="easyui-linkbutton"
						   onclick="btnUploadExcel();">文檔上傳</a></td>


					<td style="width: 60%; white-space: nowrap;"><span
							ID="labelListAddResult"></span><a
							href="${ctx}/bssecper/downLoad/errorExcel" id="downloadError"
							plain="true">查看錯誤信息</a></td>
				</tr>

			</table>
			<!-- <table id="errorList" style="width:980px;height:600px;"></table> -->

		</form>
	</div>
	<table id="dg"></table>
	<div id="dlg"></div>

	<script
		src="${ctx}/static/js/basics/bssecper.js?random=<%= Math.random()%>">
	</script>
</body>
</html>