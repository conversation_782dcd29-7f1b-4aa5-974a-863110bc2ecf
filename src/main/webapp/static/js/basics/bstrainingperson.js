var dg;
var d;
$(function() {
	dg = $('#dg').datagrid({
		method : "get",
		url : ctx + '/bstrainingperson/list',
		fit : true,
		fitColumns : true,
		queryParams:{
            order:'desc',
            sort:'createDate'
       },
		border : false,
		idField : 'id',
		striped : true,
		pagination : true,
		rownumbers : true,
		loadFilter : function(data) {
			// 过滤数据
			var value = {
				total : data.total,
				rows : []
			};
			var x = 0;
			for ( var i = 0; i < data.rows.length; i++) {
				
					data.rows[i].startTime = data.rows[i].startTime+"-"+data.rows[i].endTime+"    "+data.rows[i].startTime1+"-"+data.rows[i].endTime1;
			

				value.rows[x++] = data.rows[i];
			}
			return value;

		},
		pageNumber : 1,
		checkOnSelect : true,
		pageSize : 20,
		pageList : [ 10, 20, 30, 40, 50 ],
		singleSelect : false,
		columns : [ [ {
			field : 'id',
			title : 'Order ID',
			width : 200,
			checkbox : true
		}, {
			field : 'empNo',
			title : '工號',
			sortable : true,
			width : 100
		}, {
			field : 'empName',
			title : '姓名',
			sortable : true,
			width : 100
		}, {
			field : 'psnId',
			title : '身份證號',
			sortable : true,
			width : 100
		}, {
			field : 'company',
			title : '保安公司',
			sortable : true,
			width : 100
		}, {
			field : 'trainingPeriodNo',
			title : '培訓期編碼',
			sortable : true,
			width : 100
		}, {
			field : 'trainingPeriodName',
			title : '培訓期名稱',
			sortable : true,
			width : 100
		}, {
			field : 'startDate',
			title : '培訓開始時間',
			sortable : true,
			width : 100,
			formatter : formatDate
		}, {
			field : 'endDate',
			title : '培訓結束時間',
			sortable : true,
			width : 100,
			formatter : formatDate
		}, {
			field : 'startTime',
			title : '開始時間',
			sortable : true,
			width : 100
		}, /*{
			field : 'endTime',
			title : '結束時間',
			sortable : true,
			width : 100
		}, */{
			field : 'createBy',
			title : '創建人',
			sortable : true,
			width : 100
		}, {
			field : 'createDate',
			title : '創建時間',
			sortable : true,
			width : 100
		}

		] ],
		onCheck : function(rowIndex, rowData) {
			var isSelected = false;
			var selectItems = dg.datagrid('getSelections');
			for ( var i = 0; i < selectItems.length; i++) {
				var index = dg.datagrid('getRowIndex',
						selectItems[i]);
				// console.log(rowIndex);
				if (rowIndex == index) {
					isSelected = true;
				}
			}
			var dr = dg.datagrid('getRows')[rowIndex];
			if ((undefined == dr["num"] || "" == dr["num"])
					&& isSelected) {
				//sortByClick('getPersonList', rowIndex);
				isCheckSort = true;
			} else {
				isCheckSort = false;
			}
		},
		onUncheck : function(rowIndex, rowData) {
			//            console.log("onUncheck111111111111");
			var isSelected = false;
			var selectItems = dg.datagrid('getSelections');
			for ( var i = 0; i < selectItems.length; i++) {
				var index = dg.datagrid('getRowIndex',
						selectItems[i]);
				// console.log(rowIndex);
				if (rowIndex == index) {
					isSelected = true;
				}
			}
			var dr = dg.datagrid('getRows')[rowIndex];
			if ((undefined == dr["num"] || "" != dr["num"])
					&& !isSelected) {
				//sortByClick('getPersonList', rowIndex);
				isCheckSort = true;
			} else {
				isCheckSort = false;
			}
		},
		onUncheckAll : function(rowIndex, rowData) {
			var alllRows = dg.datagrid('getRows');
			for ( var i = 0; i < alllRows.length; i++) {
				alllRows[i]["num"] = "";
				dg.datagrid('refreshRow', i);
			}
		},
		onSelectAll : function(rowIndex, rowData) {
			var alllRows = dg.datagrid('getRows');
			for ( var i = 0; i < alllRows.length; i++) {
				alllRows[i]["num"] = i + 1;
				dg.datagrid('refreshRow', i);
			}
		},
		enableHeaderClickMenu : true,
		enableHeaderContextMenu : true,
		enableRowContextMenu : false,
		rowTooltip : true,
		toolbar : '#tb'
	});
	initCombobox('trainingPeriodNos');
});

//弹窗增加
function add() {
	d = $("#dlg").dialog({
		title : '添加培訓期培訓人員明細基本資料',
		width : 700,
		height : 400,
		closable: false,
		href : ctx + '/bstrainingperson/createNew',
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '分配',
			handler : function() {
				console.log("ff333");
				
				var trainingPeriodNo=$('#trainingPeriodNo').combobox('getValue');
			    if (trainingPeriodNo=="")
	            {
	                	$.messager.alert("溫馨提示", "請選擇培訓期！", "info");
	                	return false;
	             }

				var rows = $('#getPersonList').datagrid('getSelections');
                if (rows.length==0)
                {
                	$.messager.alert("溫馨提示", "請選擇警衛信息！", "info");
                	return false;
                }
				var ids = "";
				for ( var i = 0, j = rows.length; i < j; i++) {
					ids = ids + rows[i].empNo + ',';
				}
				ids = ids.substring(0, ids.length - 1);

				
				
				/*var mainformAdd = document.getElementById("mainformAdd");
				mainformAdd.action = ctx + '/bstrainingperson/create?trainingPeriodNo='+trainingPeriodNo+'&empNos='+ids;
				mainformAdd.submit();
				 //提交表单
				$('#mainformAdd').form({		  
				    success:function(data){ 
				        successTip(data,dataGridPerson);
				    }
				});*/
				
				$.ajax({
					url:ctx + '/bstrainingperson/create?trainingPeriodNo='+trainingPeriodNo+'&empNos='+ids,
					date:'',
					cache: false,
		    		async: false,
					type:'POST',
					dateType:'xml',
					timeout: 100000,
					error:function(xml){
						  // alert('响应失败！');
					   },	
					success:function(data){ 
					        //successTip(data);
						    // alert('添加成功！');
						//cx();
						//var obj1 = $("#searchFrom").serializeObject();
						//dg.datagrid('load', obj1);
						
						$.messager.alert("溫馨提示", "添加成功！", "info");
						window.location.reload();
					    }
				});
				d.panel('close');

			}
		}, {
			text : '取消',
			handler : function() {
				window.location.reload();
				d.panel('close');
			}
		} ]
	});
}
/*function add() {
 d=$("#dlg").dialog({
 title: '添加培訓期培訓人員明細基本資料',
 width: 380,
 height: 380,
 href:ctx+'/bstrainingperson/create',
 maximizable:true,
 modal:true,
 buttons:[{
 text:'确认',
 handler:function(){
 $("#mainform").submit();
 }
 },{
 text:'取消',
 handler:function(){
 d.panel('close');
 }
 }]
 });
 }*/

//删除批量
function del() {
	console.log('888');
	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	
	var rows = dg.datagrid('getSelections');
    if (rows.length==0)
    {
    	$.messager.alert("溫馨提示", "請選擇至少一條記錄！", "info");
    	return false;
    }
	var ids = "";
	for ( var i = 0, j = rows.length; i < j; i++) {
		ids = ids + rows[i].id + ',';
	}
	ids = ids.substring(0, ids.length - 1);


	parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data) {
		if (data) {
			$.ajax({
				type : 'get',
				url : ctx + "/bstrainingperson/delete/" + ids,
				success : function(data) {					
					successTip(data, dg);
					dg.datagrid('reload');
					dg.datagrid('clearSelections');
					//window.location.reload();
				}
			});
		}
	});
}

//弹窗修改
function upd() {
	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	d = $("#dlg").dialog({
		title : '修改培訓期培訓人員明細基本資料',
		width : 380,
		height : 340,
		href : ctx + '/bstrainingperson/update/' + row.id,
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '修改',
			handler : function() {
				$('#mainform').submit();
			}
		}, {
			text : '取消',
			handler : function() {
				d.panel('close');
			}
		} ]
	});
}
//创建查询对象并查询
function cx() {
	console.log("f88");
	 var trainingPeriodNosHide=$('#trainingPeriodNos').val();
	 $('#trainingPeriodNosHide').val(trainingPeriodNosHide);
	var obj = $("#searchFrom").serializeObject();
	dg.datagrid('load', obj);
}
//表單進度查詢頁面查詢條件重置
function listSearchReset() {
	$("#searchFrom").form("reset");
}
//导出excel
function exportExcel() {
	var form = document.getElementById("searchFrom");
	searchFrom.action = ctx + '/bstrainingperson/exportExcel';
	form.submit();
}
/*$(function(){  
    
    //初始化多选复选框  
    initCombobox('xsry','XSRY_CD');//学术荣誉的字典编码是XSRY_CD  
   )*/  
   //参数：id  控件id   code 字典编码  
   function initCombobox(id){  
		
		var isAll="ALL";

               var value = "";  
               //加载下拉框复选框  
               $('#'+id).combobox({  
            	   url: ctx + "/bstrainingperiod/getAllTrainingPeriodList/"+isAll, //后台获取下拉框数据的url  
                   method:'post',  
                   panelHeight:200,//设置为固定高度，combobox出现竖直滚动条  
                   valueField: "trainingPeriodNo",
	               textField: "trainingPeriodName",
                   multiple:true,  
                   formatter: function (row) { //formatter方法就是实现了在每个下拉选项前面增加checkbox框的方法  
                       var opts = $(this).combobox('options');  
                       return '<input type="checkbox" class="combobox-checkbox">' + row[opts.textField]  
                   },  
                   onLoadSuccess: function () {  //下拉框数据加载成功调用  
                       var opts = $(this).combobox('options');  
                       var target = this;  
                       var values = $(target).combobox('getValues');//获取选中的值的values  
                       $.map(values, function (value) {  
                           var el = opts.finder.getEl(target, value);  
                           el.find('input.combobox-checkbox')._propAttr('checked', true);   
                       })  
                   },  
                   onSelect: function (row) { //选中一个选项时调用  
                       var opts = $(this).combobox('options');  
                       //获取选中的值的values  
                       $("#"+id).val($(this).combobox('getValues'));  
                        
                      //设置选中值所对应的复选框为选中状态  
                       var el = opts.finder.getEl(this, row[opts.valueField]);  
                       el.find('input.combobox-checkbox')._propAttr('checked', true);  
                   },  
                   onUnselect: function (row) {//不选中一个选项时调用  
                       var opts = $(this).combobox('options');  
                       //获取选中的值的values  
                       $("#"+id).val($(this).combobox('getValues'));  
                       
                       var el = opts.finder.getEl(this, row[opts.valueField]);  
                       el.find('input.combobox-checkbox')._propAttr('checked', false);  
                   }  
               });  
           }  