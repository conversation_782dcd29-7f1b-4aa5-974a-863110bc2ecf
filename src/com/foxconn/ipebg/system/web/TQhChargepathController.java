package com.foxconn.ipebg.system.web;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;

import com.foxconn.ipebg.common.utils.Constant;
import com.foxconn.ipebg.common.utils.ConvertUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.system.entity.TQhChargepathEntity;
import com.foxconn.ipebg.system.service.TQhChargepathService;
import com.foxconn.ipebg.common.persistence.Page;


/**
 * 簽核路徑表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-11 10:55:00
 */
@Controller
@RequestMapping("tqhchargepath")
public class TQhChargepathController extends BaseController {

    @Autowired
    private TQhChargepathService tQhChargepathService;

    /**
     * 方法描述: 列表信息
     *
     * @Author: S6114648
     * @CreateDate: 2018-10-11 10:55:00
     * @Return
     **/

    @RequestMapping(method = RequestMethod.GET)
    @RequiresPermissions("system:tqhchargepath:list")
    public String list() {
        //查询列表数据
        return "system/tqhchargepath/list";
    }

    /**
     * 方法描述:  分頁查詢信息
     *
     * @Author: S6114648
     * @CreateDate: 2018-10-11 10:55:00
     * @Return
     **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    @RequiresPermissions("system:tqhchargepath:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<TQhChargepathEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        page = tQhChargepathService.search(page, filters);

        //審核狀態替換為字典值
        ConvertUtils.convertPropertyToDictLabel(page,"isvalid","audit_path_isvalid");
        ConvertUtils.convertPropertyToDictLabel(page,"islock","audit_path_islock");
        ConvertUtils.convertFactoryIdToFactoryName(page,"factoryid");
        return getEasyUIData(page);
    }

    /**
     * 方法描述: 添加跳轉
     *
     * @Author: S6114648
     * @CreateDate: 2018-10-11 10:55:00
     * @Return
     **/

    @RequiresPermissions("system:tqhchargepath:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("tQhChargepath", new TQhChargepathEntity());
        model.addAttribute("action", "create");
        return "system/tqhchargepath/listForm";
    }

    /**
     * 方法描述: 保存
     *
     * @Author: S6114648
     * @CreateDate: 2018-10-11 10:55:00
     * @Return
     **/

    @RequestMapping(value = "create", method = RequestMethod.POST)
    @RequiresPermissions("system:tqhchargepath:add")
    @ResponseBody
    public String create(@Valid TQhChargepathEntity tQhChargepath, Model model) {
        tQhChargepath.setNewRecord(true);
        tQhChargepath.setIsvalid("1");
        tQhChargepathService.save(tQhChargepath);
        return "success";
    }

    /**
     * 方法描述: 修改跳轉
     *
     * @Author: S6114648
     * @CreateDate: 2018-10-11 10:55:00
     * @Return
     **/

    @RequiresPermissions("system:tqhchargepath:update")
    @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
    public String updateForm(@PathVariable("id") String id, Model model) {
        model.addAttribute("tQhChargepath", tQhChargepathService.get(id));
        model.addAttribute("action", "update");
        return "system/tqhchargepath/listForm";
    }

    /**
     * 方法描述: 修改
     *
     * @Author: S6114648
     * @CreateDate: 2018-10-11 10:55:00
     * @Return
     **/

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @RequiresPermissions("system:tqhchargepath:update")
    @ResponseBody
    public String update(@Valid TQhChargepathEntity tQhChargepath,@RequestParam(value = "ids") String ids) {
        tQhChargepath.setId(ids);
        tQhChargepathService.update(tQhChargepath);
        return "success";
    }

    /**
     * 方法描述: 根據主鍵刪除
     *
     * @Author: S6114648
     * @CreateDate: 2018-10-11 10:55:00
     * @Return
     **/

    @RequestMapping("delete/{id}")
    @RequiresPermissions("system:tqhchargepath:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        tQhChargepathService.delete(id);
        return "success";
    }
    /**
      * 方法描述: 置為無效
      * @Author: S6114648
      * @CreateDate:   2018/10/26  下午 02:03
      * @Return
      **/

    @RequestMapping("setInvalid/{id}")
    @ResponseBody
    public String setInvalid(@PathVariable("id") String id) {
        tQhChargepathService.setInvalid(id);
        return Constant.SUCCESS;
    }
    /**
     * 方法描述: 置為有效
     * @Author: S6114648
     * @CreateDate:   2018/10/26  下午 02:03
     * @Return
     **/

    @RequestMapping("setValid/{id}")
    @ResponseBody
    public String setValid(@PathVariable("id") String id) {
        tQhChargepathService.setValid(id);
        return Constant.SUCCESS;
    }
}
