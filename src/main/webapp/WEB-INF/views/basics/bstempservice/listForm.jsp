<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>臨時勤務信息表</title>
    <script type="text/javascript">
        var ctx = "${pageContext.request.contextPath}";
    </script>

    <%@ include file="/WEB-INF/views/include/easyui.jsp" %>
</head>
<body>
<form id="mainform" action="${ctx}/bstempservice/${action}" method="post">
    <input type="hidden" name="ids" value="${bsTempService.id }" />
    <input type="hidden" name="delFlag"  value="${bsTempService.delFlag }"/>
    <table class="formTable">
        <tr>
            <td>工號：</td>
            <td>
                <input id="empNo" name="empNo" class="easyui-validatebox" data-options="required:true,width: 150"
                       value="${bsTempService.empNo }"/>
            </td>
        </tr>
        <tr>
            <td>姓名：</td>
            <td>
                <input id="empName" name="empName" class="easyui-validatebox" data-options="required:true,width: 150"
                       value="${bsTempService.empName }"/>
            </td>
        </tr>
        <tr>
            <td>身份證號：</td>
            <td>
                <input id="psnId" name="psnId" class="easyui-validatebox" data-options="required:true,width: 150"
                       value="${bsTempService.psnId }"/>
            </td>
        </tr>
        <tr>
            <td>保安公司：</td>
            <td>
                <input id="company" name="company" class="easyui-validatebox" data-options="required:true,width: 150"
                       value="${bsTempService.company }"/>
            </td>
        </tr>
        <tr>
            <td>事由：</td>
            <td>
                <input id="cause" name="cause" class="easyui-validatebox" data-options="required:true,width: 150"
                       value="${bsTempService.cause }"/>
            </td>
        </tr>
        <tr>
            <td>地點：</td>
            <td>
                <input id="location" name="location" class="easyui-validatebox" data-options="required:true,width: 150"
                       value="${bsTempService.location }"/>
            </td>
        </tr>
        <tr>
            <td>人數：</td>
            <td>
                <input id="peopleNumber" name="peopleNumber" class="easyui-validatebox" data-options="required:true,width: 150"
                       value="${bsTempService.peopleNumber }"/>
            </td>
        </tr>
        <tr>
            <td>開始時間：</td>
            <td>
                <input id="startTime" name="startTime" class="easyui-my97" datefmt="yyyy-MM-dd HH:00:00"
                       data-options="required:true,width: 160"
                       value="<fmt:formatDate value="${bsTempService.startTime}"/>"/>
            </td>
        </tr>
        <tr>
            <td>結束時間：</td>
            <td>
                <input id="endTime" name="endTime" class="easyui-my97" datefmt="yyyy-MM-dd HH:00:00"
                       data-options="required:true,width: 160"
                       value="<fmt:formatDate value="${bsTempService.endTime}"/>"/>
            </td>
        </tr>
        <tr>
            <td>法人：</td>
            <td>
                <input id="legalPerson" name="legalPerson" class="easyui-combobox" data-options="required:true,width: 150"
                       value="${bsTempService.legalPerson }"/>
            </td>
        </tr>
        <tr>
            <td>用人單位：</td>
            <td>
                <input id="employerUnit" name="employerUnit" class="easyui-validatebox" data-options="required:true,width: 150"
                       value="${bsTempService.employerUnit }"/>
            </td>
        </tr>
        <tr>
            <td>費用代碼：</td>
            <td>
                <input id="costCode" name="costCode" class="easyui-validatebox" data-options="required:true,width: 150"
                       value="${bsTempService.costCode }"/>
            </td>
        </tr>
        <tr>
            <td>臨時勤務費用(元)：</td>
            <td>
                <input id="expenses" name="expenses" class="easyui-validatebox" data-options="required:true,width: 150"
                       value="${bsTempService.expenses /100.0}"/>
            </td>
        </tr>
        <tr>
            <td>創建人：</td>
            <td>
                <input id="createBy" name="createBy" class="easyui-validatebox" data-options="required:true,width: 150" readonly
                       value="${bsTempService.createBy }"/>
            </td>
        </tr>
        <tr>
            <td>創建時間：</td>
            <td>
                <input id="createDate" name="createDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
                       data-options="required:true,width: 160" disabled="disabled"
                       value="<fmt:formatDate value="${bsTempService.createDate}"/>"/>
            </td>
        </tr>
        <tr>
            <td>更新人：</td>
            <td>
                <input id="updateBy" name="updateBy" class="easyui-validatebox" data-options="required:true,width: 150" readonly
                       value="${bsTempService.updateBy }"/>
            </td>
        </tr>
        <tr>
            <td>更新時間：</td>
            <td>
                <input id="updateDate" name="updateDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
                       data-options="required:true,width: 160" disabled="disabled"
                       value="<fmt:formatDate value="${bsTempService.updateDate}"/>"/>
            </td>
        </tr>
    </table>
</form>
</div>
<script src='${ctx}/static/js/basics/bstempservice.js?"+Math.random()"'></script>
<script type="text/javascript">
    //提交表单
    $('#mainform').form({
        onSubmit: function () {
            var isValid = $(this).form('validate');
            return isValid;	// 返回false终止表单提交
        },
        success: function (data) {
            successTip(data, dg, d);
        }
    });
</script>
</body>
</html>