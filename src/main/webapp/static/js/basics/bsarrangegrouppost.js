var dg;
var d;
$(function() {
	dg = $('#dg').datagrid({
		method : "get",
		url : ctx + '/bsarrangegrouppost/list',
		fit : true,
		fitColumns : true,
		queryParams:{
            order:'desc',
            sort:'createDate'
       },
		border : false,
		idField : 'id',
		striped : true,
		pagination : true,
		rownumbers : true,
		pageNumber : 1,
		pageSize : 20,
		pageList : [ 10, 20, 30, 40, 50 ],
		singleSelect : false,
		columns : [ [ {
			field : 'id',
			title : 'Order ID',
			width : 200,
			checkbox : true
		}, {
			field : 'arrangeGroupId',
			title : '排班群組編碼',
			hidden : true
		}, {
			field : 'arrangeGroupName',
			title : '排班群組',
			sortable : true,
			width : 100
		}, {
			field : 'recno',
			title : '編號',
			hidden : true
		},  {
			field : 'postName',
			title : '崗位名稱',
			sortable : true,
			width : 100
		}, {
			field : 'area',
			title : '區域',
			sortable : true,
			width : 100
		}, {
			field : 'block',
			title : '棟',
			sortable : true,
			width : 100
		}, {
			field : 'floor',
			title : '層',
			sortable : true,
			width : 100
		},  {
			field : 'createBy',
			title : '創建人',
			sortable : true,
			width : 100
		}, {
			field : 'createDate',
			title : '創建時間',
			sortable : true,
			width : 100
		} ] ],
		onUncheck : function(rowIndex, rowData) {
			//            console.log("onUncheck111111111111");
			var isSelected = false;
			var selectItems = dg.datagrid('getSelections');
			for ( var i = 0; i < selectItems.length; i++) {
				var index = dg.datagrid('getRowIndex',
						selectItems[i]);
				// console.log(rowIndex);
				if (rowIndex == index) {
					isSelected = true;
				}
			}
			var dr = dg.datagrid('getRows')[rowIndex];
			if ((undefined == dr["num"] || "" != dr["num"])
					&& !isSelected) {
				//sortByClick('getPersonList', rowIndex);
				isCheckSort = true;
			} else {
				isCheckSort = false;
			}
		},
		onUncheckAll : function(rowIndex, rowData) {
			var alllRows = dg.datagrid('getRows');
			for ( var i = 0; i < alllRows.length; i++) {
				alllRows[i]["num"] = "";
				dg.datagrid('refreshRow', i);
			}
		},
		onSelectAll : function(rowIndex, rowData) {
			var alllRows = dg.datagrid('getRows');
			for ( var i = 0; i < alllRows.length; i++) {
				alllRows[i]["num"] = i + 1;
				dg.datagrid('refreshRow', i);
			}
		},
		enableHeaderClickMenu : true,
		enableHeaderContextMenu : true,
		enableRowContextMenu : false,
		rowTooltip : true,
		toolbar : '#tb'
	});
	
	 $.ajax({
	        url: ctx + "/bsarrangegroup/getAllArrangeGroup",
	        //dataType:"json",
	        type: "GET",
	        success: function (data) {
	            //绑定第一个下拉框
	            $("#filter_EQS_arrangeGroupId").combobox({
	                data: data,
	                valueField: "arrangeGroupId",
	                textField: "arrangeGroupName",
	                editable: false,
	                panelHeight: 400,
	                loadFilter: function (data) {
	                    data.unshift({
	                    	arrangeGroupId: '',
	                    	arrangeGroupName: '請選擇'
	                    });
	                    return data;
	                }
	            });
	        },
	        error: function (error) {
	            alert("初始化下拉控件失败");
	        }
	    });
	 
});

//弹窗增加
function add() {
	d = $("#dlg").dialog({
		title : '排班群組匹配崗位',
		width : 700,
		height : 400,
		closable: false,
		href : ctx + '/bsarrangegrouppost/create',
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '保存',
			handler : function() {
				console.log("ff333");
				
				var addArrangeGroupId=$('#addArrangeGroupId').combobox('getValue');
			    if (addArrangeGroupId=="")
	            {
	                	$.messager.alert("溫馨提示", "請選擇排班群組！", "info");
	                	return false;
	             }

				var rows = $('#getPostList').datagrid('getSelections');
                if (rows.length==0)
                {
                	$.messager.alert("溫馨提示", "請選擇崗位信息！", "info");
                	return false;
                }
				var ids = "";
				for ( var i = 0, j = rows.length; i < j; i++) {
					ids = ids + rows[i].recno + ',';
				}
				ids = ids.substring(0, ids.length - 1);

				
				
				$.ajax({
					url:ctx + '/bsarrangegrouppost/create?arrangeGroupId='+addArrangeGroupId+'&recnos='+ids,
					date:'',
					cache: false,
		    		async: false,
					type:'POST',
					dateType:'xml',
					timeout: 100000,
					error:function(xml){
						  // alert('响应失败！');
					   },	
					success:function(data){ 
					        //successTip(data);
						    // alert('添加成功！');
						//cx();
						//var obj1 = $("#searchFrom").serializeObject();
						//dg.datagrid('load', obj1);
						
						$.messager.alert("溫馨提示", "添加成功！", "info");
						window.location.reload();
					    }
				});
				d.panel('close');

			}
		}, {
			text : '取消',
			handler : function() {
				window.location.reload();
				d.panel('close');
			}
		} ]
	});
}

//删除
function del() {
	console.log('888');
	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	
	var rows = dg.datagrid('getSelections');
    if (rows.length==0)
    {
    	$.messager.alert("溫馨提示", "請選擇至少一條記錄！", "info");
    	return false;
    }
	var ids = "";
	for ( var i = 0, j = rows.length; i < j; i++) {
		ids = ids + rows[i].id + ',';
	}
	ids = ids.substring(0, ids.length - 1);


	parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data) {
		if (data) {
			$.ajax({
				type : 'get',
				url : ctx + "/bsarrangegrouppost/delete/" + ids,
				success : function(data) {
					successTip(data, dg);
					dg.datagrid('reload');
					dg.datagrid('clearSelections');
				}
			});
		}
	});
}

//弹窗修改
function upd() {
	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	d = $("#dlg").dialog({
		title : '修改排班群組匹配崗位',
		width : 380,
		height : 340,
		href : ctx + '/bsarrangegrouppost/update/' + row.id,
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '修改',
			handler : function() {
				$('#mainform').submit();
			}
		}, {
			text : '取消',
			handler : function() {
				d.panel('close');
			}
		} ]
	});
}
//创建查询对象并查询
function cx() {
	var obj = $("#searchFrom").serializeObject();
	dg.datagrid('load', obj);
}
//表單進度查詢頁面查詢條件重置
function listSearchReset() {
	$("#searchFrom").form("reset");
}
//导出excel
function exportExcel() {
	var form = document.getElementById("searchFrom");
	searchFrom.action = ctx + '/bsarrangegrouppost/exportExcel';
	form.submit();
}