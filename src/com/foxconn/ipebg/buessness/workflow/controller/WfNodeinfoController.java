package com.foxconn.ipebg.buessness.workflow.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.buessness.workflow.entity.WfNodeinfoEntity;
import com.foxconn.ipebg.buessness.workflow.service.WfNodeinfoService;
import com.foxconn.ipebg.common.persistence.Page;


/**
 * 流程節點配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-06 11:04:38
 */
@Controller
@RequestMapping("wfnodeinfo")
public class WfNodeinfoController extends BaseController {

    @Autowired
    private WfNodeinfoService wfNodeinfoService;

    /**
      * 方法描述: 列表信息
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:38
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    @RequiresPermissions("workflow:wfnodeinfo:list")
    public String list() {
        //查询列表数据
        return "buessness/workflow/wfnodeinfo/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:38
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    @RequiresPermissions("workflow:wfnodeinfo:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<WfNodeinfoEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        page = wfNodeinfoService.search(page, filters);
        return getEasyUIData(page);
    }
    /**
      * 方法描述: 添加跳轉
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:38
      * @Return
      **/

    @RequiresPermissions("workflow:wfnodeinfo:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("wfNodeinfo", new WfNodeinfoEntity());
        model.addAttribute("action", "create");
        return "buessness/workflow/wfnodeinfo/listForm";
    }
    /**
      * 方法描述: 保存
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:38
      * @Return
      **/

    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("workflow:wfnodeinfo:add")
    @ResponseBody
    public String create(@Valid WfNodeinfoEntity wfNodeinfo, Model model) {
        wfNodeinfoService.save(wfNodeinfo);
        return "success";
    }
    /**
      * 方法描述: 修改跳轉
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:38
      * @Return
      **/

    @RequiresPermissions("workflow:wfnodeinfo:update")
    @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
    public String updateForm(@PathVariable("id") String id, Model model) {
        model.addAttribute("wfNodeinfo", wfNodeinfoService.get(id));
        model.addAttribute("action", "update");
        return "buessness/workflow/wfnodeinfo/listForm";
    }
    /**
      * 方法描述: 修改
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:38
      * @Return
      **/

    @RequestMapping(value="update", method = RequestMethod.POST)
    @RequiresPermissions("workflow:wfnodeinfo:update")
    @ResponseBody
    public String update(@Valid WfNodeinfoEntity wfNodeinfo) {
        wfNodeinfoService.update(wfNodeinfo);
        return "success";
    }

    /**
      * 方法描述: 根據主鍵刪除
      * @Author: S6114648
      * @CreateDate:   2018-10-06 11:04:38
      * @Return
      **/

    @RequestMapping("delete/{id}")
    @RequiresPermissions("workflow:wfnodeinfo:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        wfNodeinfoService.delete(id);
        return "success";
    }

}
