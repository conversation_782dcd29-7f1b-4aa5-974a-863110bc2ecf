<%--suppress ALL --%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title></title>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
</head>
<body>
<div>
<%--    <input id="roleListSort"
           value="">
    </input>--%>
</div>
<table id="roleListdg4"></table>
<script type="text/javascript">
var dg;
var d;
$(function(){
    var dutyid =$('#dutyId').val();
    var factoryid = $('#factoryId').val();
    var param={"dutyid":dutyid,"factoryid":factoryid};
    var isCheckSort = false;
	dg=$('#roleListdg4').datagrid({
		method: "get",
    	url:'${ctx}/system/auditingNode/getAuditHqUsers',
        queryParams:param,
        nowrap: false,
        fit : true,
        fitColumns : true,
        border : false,
        idField : 'id',
        striped:true,
        pagination:true,
        pageNumber:1,
        pageSize : 20,
        pageList : [ 10, 20, 30, 40, 50 ],
        remoteSort:false,
        singleSelect:false,
        checkOnSelect:true,
        columns:[[
            {field: 'IsEnable', title: '', width: 60, align: 'center',
                formatter: function (value, row, index) {
                    if (index==0) {
                        //如果属性值等于1，则处于选中状态（默认表格中所有单选按钮最多只能有一个值等于1）
                        var s = '<input name="isChecked" type="radio" checked="checked" value="'+row.empno+','+row.username+'"/> ';
                    }
                    else {
                        var s = '<input name="isChecked" type="radio"  value="'+row.empno+','+row.username+'"/> ';
                    }
                    return s;
                }
            },
            {field:'empno',title:'帐号',sortable:true,width:100,align: 'center'},
            {field:'username',title:'主管名稱',sortable:true,width:100,align: 'center'}
        ]]
	});
}

);
</script>
</body>
</html>