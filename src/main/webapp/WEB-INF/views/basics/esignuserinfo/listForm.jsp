<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>簽核者信息表</title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
</script>

<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
	<form id="mainform" action="${ctx}/esignuserinfo/${action}"
		method="post">
		<table class="formTable">
			<tr>

				<input id="ids" name="ids" type="hidden"
					value="${eSignUserinfo.id }" />

			</tr>
			<tr>
				<td>工號：</td>
				<td><input id="eserId" name="eserId" class="easyui-validatebox"
					data-options="width: 200, required:true" value="${eSignUserinfo.eserId }" /></td>
			</tr>
			<tr>
				<td>姓名：</td>
				<td><input id="eserNam" name="eserNam"
					class="easyui-validatebox" data-options="width: 200, required:true"
					value="${eSignUserinfo.eserNam }" /></td>
			</tr>
			<tr>
				<td>性別：</td>
				<td><input id="eserSex" name="eserSex"
					class="easyui-validatebox" data-options="width: 200"
					value="${eSignUserinfo.eserSex }" /></td>
			</tr>
			<tr>
				<td>部門：</td>
				<td><input id="eserDptId" name="eserDptId"
					class="easyui-validatebox" data-options="width: 200"
					value="${eSignUserinfo.eserDptId }" /></td>
			</tr>
			<tr>
				<td>部門名稱：</td>
				<td><input id="eserDptNam" name="eserDptNam"
					class="easyui-validatebox" data-options="width: 200"
					value="${eSignUserinfo.eserDptNam }" /></td>
			</tr>

			<tr>
				<td>Mail地址：</td>
				<td><input id="eserMail" name="eserMail"
					class="easyui-validatebox" data-options="width: 200,required:true"
					value="${eSignUserinfo.eserMail }" /></td>
			</tr>
			<tr>
				<td>簽核者聯系方式：</td>
				<td><input id="eserTel" name="eserTel"
					class="easyui-validatebox" data-options="width: 200, required:true"
					value="${eSignUserinfo.eserTel }" /></td>
			</tr>
			<tr>
				<td>簽核表單類別：</td>
				<td><input id="eserTyp" name="eserTyp"
					class="easyui-combobox" 
					data-options="width: 200,required:true, prompt: '請選擇',onSelect:function(){onchangeType(false);}"
					value="${eSignUserinfo.eserTyp }" /></td>
			</tr>
			<tr>
				<td>簽核節點名稱：</td>
				<td><input id="eserNodeName" name="eserNodeName"
					class="easyui-combobox" data-options="width: 200,required:true"
					value="${eSignUserinfo.eserNodeName }" /></td>
			</tr>
			<tr>
				<td>有效否：</td>
				<td><select id="ynVa" class="easyui-combobox" name="ynVa"
					style="width:200px;">
						<option value="Y">是</option>
						<option value="N"
							<c:if test="${eSignUserinfo.ynVa!='Y'}"> selected="ture" </c:if>>否</option>

				</select></td>

				<%-- <td><input id="ynVa" name="ynVa" class="easyui-validatebox"
					data-options="width: 150" value="${eSignUserinfo.ynVa }" /></td> --%>
			</tr>

			<tr style="display: none">
				<td>
					<input id="createBy" name="createBy"
						   class="easyui-validatebox" data-options="width: 150"
						   value="${eSignUserinfo.createBy }" />
					<input id="createDate" name="createDate"
						   class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
						   data-options="width: 160" disabled="disabled"
						   value="<fmt:formatDate value="${eSignUserinfo.createDate}"/>" />
					<input id="updateBy" name="updateBy"
						   class="easyui-validatebox" data-options="width: 150"
						   value="${eSignUserinfo.updateBy }" />
					<input id="updateDate" name="updateDate"
						   class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
						   data-options="width: 160" disabled="disabled"
						   value="<fmt:formatDate value="${eSignUserinfo.updateDate}"/>" />
				</td>

			</tr>
		</table>
	</form>
	</div>
	<script src='${ctx}/static/js/basics/esignuserinfo.js?'></script>
	<script type="text/javascript">
		//提交表单
		$('#mainform').form({
			onSubmit : function() {
				var isValid = $(this).form('validate');
				return isValid; // 返回false终止表单提交
			},
			success : function(data) {
				successTip(data, dg, d);
			}
		});
	</script>
</body>
</html>