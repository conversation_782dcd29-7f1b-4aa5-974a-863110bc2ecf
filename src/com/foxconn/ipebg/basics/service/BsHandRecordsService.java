package com.foxconn.ipebg.basics.service;

import com.foxconn.ipebg.basics.entity.BsHandRecordsEntity;
import com.foxconn.ipebg.basics.dao.BsHandRecordsDao;
import com.foxconn.ipebg.buessness.common.service.UserServiceUtil;

import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foxconn.ipebg.system.service.DictService;


/**
 * 手持機刷卡記錄
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-12-21 14:54:46
 */
@Service
@Transactional(readOnly=true)
public class  BsHandRecordsService extends BaseBusinessService<BsHandRecordsEntity, String>{
    @Autowired
    private BsHandRecordsDao bsHandRecordsDao;

    @Autowired
    private UserServiceUtil serviceUtil;
    @Autowired
    private DictService dictService;

    @Override
    public HibernateDao<BsHandRecordsEntity, String> getEntityDao() {
        return bsHandRecordsDao;
    }

    public BsHandRecordsEntity findBySerialno(String serialno) {
        return this.bsHandRecordsDao.findUniqueBy("serialno", serialno);      
    }
    
    /**
	 * 方法描述: 條用存儲過程，有返回值
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018/12/29 下午 03:33
	 * @param TableName  相關表名
	 * @param FieldName 相關欄位名
	 * @param FirstStr 編號頭字符串           
	 * @param RecLen 序號位數
	 * @Return
	 **/
	public String CreateFormNo(String TableName, String FieldName,
			String FirstStr, int RecLen) {
		 return this.bsHandRecordsDao.CreateFormNo(TableName, FieldName, FirstStr, RecLen);
	}


	public BsHandRecordsEntity findById(String id) {
		return this.bsHandRecordsDao.findUniqueBy("id", id);  
	}
    
}
