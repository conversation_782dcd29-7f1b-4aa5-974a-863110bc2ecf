package com.foxconn.ipebg.system.entity;

/**
 * Company foxconn
 * Copyright (C) 2004-2018 All Rights Reserved.
 */
public class Mail {
    private String username;  //用戶
    private String ordertype; //表單類型
    private String serialno;  //任務編號

    private String usermail;
    //審核完成
    private String url;  //域名登錄
    private String urlip;//ip登錄
    //駁回
    private String chargerman; //駁回主管
    private String freeloginurl; //免密登錄地址
    private String freeloginurlip;//免密登錄ip地址
    //待審核
    private String dusername; //審核主管
    private String orderstatus;//表單狀態  E審核完成  N駁回   ELSE 審核中 

    private String chargernum; //表單數
    private String systemname; //系統名稱

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getOrdertype() {
        return ordertype;
    }

    public void setOrdertype(String ordertype) {
        this.ordertype = ordertype;
    }

    public String getSerialno() {
        return serialno;
    }

    public void setSerialno(String serialno) {
        this.serialno = serialno;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUrlip() {
        return urlip;
    }

    public void setUrlip(String urlip) {
        this.urlip = urlip;
    }

    public String getChargerman() {
        return chargerman;
    }

    public void setChargerman(String chargerman) {
        this.chargerman = chargerman;
    }

    public String getFreeloginurl() {
        return freeloginurl;
    }

    public void setFreeloginurl(String freeloginurl) {
        this.freeloginurl = freeloginurl;
    }

    public String getFreeloginurlip() {
        return freeloginurlip;
    }

    public void setFreeloginurlip(String freeloginurlip) {
        this.freeloginurlip = freeloginurlip;
    }

    public String getDusername() {
        return dusername;
    }

    public void setDusername(String dusername) {
        this.dusername = dusername;
    }

    public String getOrderstatus() {
        return orderstatus;
    }

    public void setOrderstatus(String orderstatus) {
        this.orderstatus = orderstatus;
    }

    public String getChargernum() {
        return chargernum;
    }

    public void setChargernum(String chargernum) {
        this.chargernum = chargernum;
    }

    public String getSystemname() {
        return systemname;
    }

    public void setSystemname(String systemname) {
        this.systemname = systemname;
    }

    public String getUsermail() {
        return usermail;
    }

    public void setUsermail(String usermail) {
        this.usermail = usermail;
    }
}
