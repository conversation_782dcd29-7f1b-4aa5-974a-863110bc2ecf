package com.foxconn.ipebg.buessness.audit.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.foxconn.ipebg.common.entity.DataEntity;

@Entity
@Table(name = "V_E_TEMP_SECURITY_COST_APLY")
@DynamicUpdate
@DynamicInsert
public class TempSecurityCostAplyEntity extends DataEntity<TempSecurityCostAplyEntity> implements Serializable {
    private static final long serialVersionUID = 1L;
    // 服務單編號
    private String serviceSerialno;
    // 任務編號
    private String serialno;
    // 填單人工號
    private String makerno;
    // 填單人名稱
    private String makername;
    // 簽核完成時間
    private Date complettime;
    // 表單狀態
    private String workstatus;
    // 實際人數
    private Integer perNumber;
    // 服務公司
    private String securityCom;
    // 時數
    private float hourNumber;
    // 費用
    private BigDecimal cost;
    // 申請人IP地址
    private String applyIp;
    // 申請單狀態(E：結案，0：申請，O：，駁回 Q：異常結案)
    private String applyStat;
    // 審核人工號
    private String signEmpNo;
    // 審核人姓名
    private String signEmpNam;
    // 審核時間
    private Date signDate;
    // 申請單位
    private String dpt;
    // 費用代碼
    private String costId;
    // 需求時間段
    private Date startTime;
    private Date endTime;
    // 需求服務地點
    private String servicePlace;
    // 附件Id
    private String attachids;

    private String legalPerson;

    @Column(name = "LEGAL_PERSON", nullable = false, length = 50)
    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    @Column(name = "SERVICE_SERIALNO", nullable = false, length = 20)
    public String getServiceSerialno() {
        return serviceSerialno;
    }

    public void setServiceSerialno(String serviceSerialno) {
        this.serviceSerialno = serviceSerialno;
    }

    @Column(name = "SERIALNO", nullable = false, length = 20)
    public String getSerialno() {
        return serialno;
    }

    public void setSerialno(String serialno) {
        this.serialno = serialno;
    }

    @Column(name = "MAKERNO", nullable = false, length = 20)
    public String getMakerno() {
        return makerno;
    }

    public void setMakerno(String makerno) {
        this.makerno = makerno;
    }

    @Column(name = "MAKERNAME", nullable = false, length = 20)
    public String getMakername() {
        return makername;
    }

    public void setMakername(String makername) {
        this.makername = makername;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "COMPLETTIME", nullable = false, length = 20)
    public Date getComplettime() {
        return complettime;
    }

    public void setComplettime(Date complettime) {
        this.complettime = complettime;
    }

    @Column(name = "WORKSTATUS", nullable = false, length = 20)
    public String getWorkstatus() {
        return workstatus;
    }

    public void setWorkstatus(String workstatus) {
        this.workstatus = workstatus;
    }

    @Column(name = "PER_NUMBER")
    public Integer getPerNumber() {
        return perNumber;
    }

    public void setPerNumber(Integer perNumber) {
        this.perNumber = perNumber;
    }

    @Column(name = "SECURITY_COM", nullable = false, length = 15)
    public String getSecurityCom() {
        return securityCom;
    }

    public void setSecurityCom(String securityCom) {
        this.securityCom = securityCom;
    }

    @Column(name = "HOUR_NUMBER")
    public float getHourNumber() {
        return hourNumber;
    }

    public void setHourNumber(float hourNumber) {
        this.hourNumber = hourNumber;
    }

    @Column(name = "COST", scale = 2)
    public BigDecimal getCost() {
        return cost;
    }

    public void setCost(BigDecimal cost) {
        this.cost = cost;
    }

    @Column(name = "APPLY_IP", nullable = false, length = 20)
    public String getApplyIp() {
        return applyIp;
    }

    public void setApplyIp(String applyIp) {
        this.applyIp = applyIp;
    }

    @Column(name = "APPLY_STAT", nullable = false, length = 20)
    public String getApplyStat() {
        return applyStat;
    }

    public void setApplyStat(String applyStat) {
        this.applyStat = applyStat;
    }

    @Column(name = "SIGN_EMP_NO", nullable = false, length = 20)
    public String getSignEmpNo() {
        return signEmpNo;
    }

    public void setSignEmpNo(String signEmpNo) {
        this.signEmpNo = signEmpNo;
    }

    @Column(name = "SIGN_EMP_NAM", nullable = false, length = 20)
    public String getSignEmpNam() {
        return signEmpNam;
    }

    public void setSignEmpNam(String signEmpNam) {
        this.signEmpNam = signEmpNam;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "SIGN_DATE", nullable = false, length = 20)
    public Date getSignDate() {
        return signDate;
    }

    public void setSignDate(Date signDate) {
        this.signDate = signDate;
    }

    @Column(name = "DPT", nullable = false, length = 50)
    public String getDpt() {
        return dpt;
    }

    public void setDpt(String dpt) {
        this.dpt = dpt;
    }

    @Column(name = "COST_ID", nullable = false, length = 20)
    public String getCostId() {
        return costId;
    }

    public void setCostId(String costId) {
        this.costId = costId;
    }

    @Column(name = "START_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    @Column(name = "END_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    @Column(name = "SERVICE_PLACE", nullable = false, length = 50)
    public String getServicePlace() {
        return servicePlace;
    }

    public void setServicePlace(String servicePlace) {
        this.servicePlace = servicePlace;
    }

    @Column(name = "ATTACHIDS", nullable = false, length = 20)
    public String getAttachids() {
        return attachids;
    }

    public void setAttachids(String attachids) {
        this.attachids = attachids;
    }
}
