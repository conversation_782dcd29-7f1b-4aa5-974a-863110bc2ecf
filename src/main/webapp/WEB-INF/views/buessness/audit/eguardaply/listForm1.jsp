<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>警衛服務申請主表</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/eguardaply/${action}" method="post">
    <input id="ids" name="ids" type="hidden" value="${EGuardAplyEntity.id }"/>
    <input id="serialno" name="serialno" type="hidden" value="${EGuardAplyEntity.serialno }"/>
    <div class="commonW">
    <div class="headTitle">警衛服務申請主表</div>
        <div class="position_L">
            任務編碼：<span style="color:#999;">
                   <c:choose>
                       <c:when test="${EGuardAplyEntity.serialno==null}">
                           提交成功后自動編碼
                       </c:when>
                       <c:otherwise>
                           ${EGuardAplyEntity.serialno}
                       </c:otherwise>
                   </c:choose>
				</span>
        </div>
        <div class="position_L1 margin_L">
            填單時間：<span style="color:#999;">
				    <c:choose>
                        <c:when test="${EGuardAplyEntity.createtime==null}">
                            YYYY/MM/DD
                        </c:when>
                        <c:otherwise>
                            <input style="border:0px" style="width: 120px" readonly="true"
                                   value="<fmt:formatDate value='${EGuardAplyEntity.createtime}' pattern='yyyy-MM-dd HH:mm'/>"/>
                        </c:otherwise>
                    </c:choose>
				</span>
        </div>
        <div class="position_R margin_R">填單人：${user.loginName}/${user.name}</div>
        <div class="clear"></div>
		<table class="formList">
		   <tr>
                <td>
                    <table class="formList">
                     			<tr>
				<td>主鍵：</td>
				<td>
				    					<input type="hidden" name="id" value="${eGuardAply.id}"/>
															<input id="id" name="id" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.id }" />
									</td>
			</tr>
						<tr>
				<td>任務編號：</td>
				<td>
				    										<input id="serialno" name="serialno" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.serialno }" />
									</td>
			</tr>
						<tr>
				<td>填單人工號：</td>
				<td>
				    										<input id="makerno" name="makerno" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.makerno }" />
									</td>
			</tr>
						<tr>
				<td>填單人名稱：</td>
				<td>
				    										<input id="makername" name="makername" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.makername }" />
									</td>
			</tr>
						<tr>
				<td>填單人IP：</td>
				<td>
				    										<input id="makerip" name="makerip" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.makerip }" />
									</td>
			</tr>
						<tr>
				<td>填單時間：</td>
				<td>
				    										<input id="createtime" name="createtime" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${eGuardAply.createtime}"/>" />
									</td>
			</tr>
						<tr>
				<td>簽核完成時間：</td>
				<td>
				    										<input id="complettime" name="complettime" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${eGuardAply.complettime}"/>" />
									</td>
			</tr>
						<tr>
				<td>表單狀態：</td>
				<td>
				    										<input id="workstatus" name="workstatus" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.workstatus }" />
									</td>
			</tr>
						<tr>
				<td>填單人廠區Id：</td>
				<td>
				    										<input id="makerfactoryid" name="makerfactoryid" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.makerfactoryid }" />
									</td>
			</tr>
						<tr>
				<td>承辦人工號：</td>
				<td>
				    										<input id="dealno" name="dealno" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.dealno }" />
									</td>
			</tr>
						<tr>
				<td>承辦人：</td>
				<td>
				    										<input id="dealname" name="dealname" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.dealname }" />
									</td>
			</tr>
						<tr>
				<td>事業群：</td>
				<td>
				    										<input id="dptQun" name="dptQun" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.dptQun }" />
									</td>
			</tr>
						<tr>
				<td>事業處：</td>
				<td>
				    										<input id="dptChu" name="dptChu" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.dptChu }" />
									</td>
			</tr>
						<tr>
				<td>事業處代碼：</td>
				<td>
				    										<input id="dptChuCode" name="dptChuCode" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.dptChuCode }" />
									</td>
			</tr>
						<tr>
				<td>部門：</td>
				<td>
				    										<input id="dptBu" name="dptBu" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.dptBu }" />
									</td>
			</tr>
						<tr>
				<td>費用代碼：</td>
				<td>
				    										<input id="dptCode" name="dptCode" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.dptCode }" />
									</td>
			</tr>
						<tr>
				<td>法人：</td>
				<td>
				    										<input id="legalPerson" name="legalPerson" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.legalPerson }" />
									</td>
			</tr>
						<tr>
				<td>手機：</td>
				<td>
				    										<input id="mobilePhone" name="mobilePhone" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.mobilePhone }" />
									</td>
			</tr>
						<tr>
				<td>承辦人郵箱：</td>
				<td>
				    										<input id="dealemail" name="dealemail" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.dealemail }" />
									</td>
			</tr>
						<tr>
				<td>分機：</td>
				<td>
				    										<input id="dealtel" name="dealtel" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.dealtel }" />
									</td>
			</tr>
						<tr>
				<td>聯繫人：</td>
				<td>
				    										<input id="linkman" name="linkman" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.linkman }" />
									</td>
			</tr>
						<tr>
				<td>需求類型（0.增崗1.撤崗）：</td>
				<td>
				    										<input id="requirememtType" name="requirememtType" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.requirememtType }" />
									</td>
			</tr>
						<tr>
				<td>需求原因說明：</td>
				<td>
				    										<input id="applyExplain" name="applyExplain" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.applyExplain }" />
									</td>
			</tr>
						<tr>
				<td>安全管理必要性：</td>
				<td>
				    										<input id="safeManagement" name="safeManagement" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.safeManagement }" />
									</td>
			</tr>
						<tr>
				<td>人力需求合理性：</td>
				<td>
				    										<input id="manpowerDemand" name="manpowerDemand" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.manpowerDemand }" />
									</td>
			</tr>
						<tr>
				<td>安全硬件設施：</td>
				<td>
				    										<input id="safetyFacilities" name="safetyFacilities" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.safetyFacilities }" />
									</td>
			</tr>
						<tr>
				<td>審核類別：0.新啟用1.重新啟用：</td>
				<td>
				    										<input id="auditType" name="auditType" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.auditType }" />
									</td>
			</tr>
						<tr>
				<td>事業單位支援課課長審核(IPEBG)：</td>
				<td>
				    										<input id="kchargeno1" name="kchargeno1" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargeno1 }" />
									</td>
			</tr>
						<tr>
				<td>事業單位支援課課長審核(IPEBG)：</td>
				<td>
				    										<input id="kchargename1" name="kchargename1" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargename1 }" />
									</td>
			</tr>
						<tr>
				<td>事業單位廠長審核(IPEBG)：</td>
				<td>
				    										<input id="kchargeno2" name="kchargeno2" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargeno2 }" />
									</td>
			</tr>
						<tr>
				<td>事業單位廠長審核(IPEBG)：</td>
				<td>
				    										<input id="kchargename2" name="kchargename2" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargename2 }" />
									</td>
			</tr>
						<tr>
				<td>事業單位產品安全審核(IPEBG)：</td>
				<td>
				    										<input id="kchargeno3" name="kchargeno3" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargeno3 }" />
									</td>
			</tr>
						<tr>
				<td>事業單位產品安全審核(IPEBG)：</td>
				<td>
				    										<input id="kchargename3" name="kchargename3" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargename3 }" />
									</td>
			</tr>
						<tr>
				<td>經管課長審核(IPEBG)：</td>
				<td>
				    										<input id="kchargeno4" name="kchargeno4" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargeno4 }" />
									</td>
			</tr>
						<tr>
				<td>經管課長審核(IPEBG)：</td>
				<td>
				    										<input id="kchargename4" name="kchargename4" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargename4 }" />
									</td>
			</tr>
						<tr>
				<td>經管最高主管核准(IPEBG)：</td>
				<td>
				    										<input id="kchargeno5" name="kchargeno5" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargeno5 }" />
									</td>
			</tr>
						<tr>
				<td>經管最高主管核准(IPEBG)：</td>
				<td>
				    										<input id="kchargename5" name="kchargename5" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargename5 }" />
									</td>
			</tr>
						<tr>
				<td>事業單位處級主管核准(IPEBG)：</td>
				<td>
				    										<input id="kchargeno6" name="kchargeno6" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargeno6 }" />
									</td>
			</tr>
						<tr>
				<td>事業單位處級主管核准(IPEBG)：</td>
				<td>
				    										<input id="kchargename6" name="kchargename6" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargename6 }" />
									</td>
			</tr>
						<tr>
				<td>產品安全部長審核IDPBG（太原物流部、華南檢測中心參照IDPBG）：</td>
				<td>
				    										<input id="kchargeno7" name="kchargeno7" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargeno7 }" />
									</td>
			</tr>
						<tr>
				<td>產品安全部長審核IDPBG（太原物流部、華南檢測中心參照IDPBG）：</td>
				<td>
				    										<input id="kchargename7" name="kchargename7" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargename7 }" />
									</td>
			</tr>
						<tr>
				<td>經管課長審核 IDPBG（太原物流部、華南檢測中心參照IDPBG）：</td>
				<td>
				    										<input id="kchargeno8" name="kchargeno8" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargeno8 }" />
									</td>
			</tr>
						<tr>
				<td>經管課長審核 IDPBG（太原物流部、華南檢測中心參照IDPBG）：</td>
				<td>
				    										<input id="kchargename8" name="kchargename8" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargename8 }" />
									</td>
			</tr>
						<tr>
				<td>產品安全處核准IDPBG（太原物流部、華南檢測中心參照IDPBG）：</td>
				<td>
				    										<input id="kchargeno9" name="kchargeno9" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargeno9 }" />
									</td>
			</tr>
						<tr>
				<td>產品安全處核准IDPBG（太原物流部、華南檢測中心參照IDPBG）：</td>
				<td>
				    										<input id="kchargename9" name="kchargename9" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargename9 }" />
									</td>
			</tr>
						<tr>
				<td>警勤管理部審核  (公共區域)：</td>
				<td>
				    										<input id="kchargeno10" name="kchargeno10" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargeno10 }" />
									</td>
			</tr>
						<tr>
				<td>警勤管理部審核  (公共區域)：</td>
				<td>
				    										<input id="kchargename10" name="kchargename10" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargename10 }" />
									</td>
			</tr>
						<tr>
				<td>太原安全處核准 (公共區域)：</td>
				<td>
				    										<input id="kchargeno11" name="kchargeno11" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargeno11 }" />
									</td>
			</tr>
						<tr>
				<td>太原安全處核准 (公共區域)：</td>
				<td>
				    										<input id="kchargename11" name="kchargename11" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargename11 }" />
									</td>
			</tr>
						<tr>
				<td>太原經管處核准 (公共區域)：</td>
				<td>
				    										<input id="kchargeno12" name="kchargeno12" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargeno12 }" />
									</td>
			</tr>
						<tr>
				<td>太原經管處核准 (公共區域)：</td>
				<td>
				    										<input id="kchargename12" name="kchargename12" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargename12 }" />
									</td>
			</tr>
						<tr>
				<td>太原周邊總處核准(公共區域)：</td>
				<td>
				    										<input id="kchargeno13" name="kchargeno13" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargeno13 }" />
									</td>
			</tr>
						<tr>
				<td>太原周邊總處核准(公共區域)：</td>
				<td>
				    										<input id="kchargename13" name="kchargename13" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargename13 }" />
									</td>
			</tr>
						<tr>
				<td>太原安全處門禁管制課現場勘察：</td>
				<td>
				    										<input id="kchargeno14" name="kchargeno14" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargeno14 }" />
									</td>
			</tr>
						<tr>
				<td>太原安全處門禁管制課現場勘察：</td>
				<td>
				    										<input id="kchargename14" name="kchargename14" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargename14 }" />
									</td>
			</tr>
						<tr>
				<td>太原安全處審核：</td>
				<td>
				    										<input id="kchargeno15" name="kchargeno15" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargeno15 }" />
									</td>
			</tr>
						<tr>
				<td>太原安全處審核：</td>
				<td>
				    										<input id="kchargename15" name="kchargename15" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargename15 }" />
									</td>
			</tr>
						<tr>
				<td>太原安全處人資主管審核：</td>
				<td>
				    										<input id="kchargeno16" name="kchargeno16" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargeno16 }" />
									</td>
			</tr>
						<tr>
				<td>太原安全處人資主管審核：</td>
				<td>
				    										<input id="kchargename16" name="kchargename16" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargename16 }" />
									</td>
			</tr>
						<tr>
				<td>太原經管處審核：</td>
				<td>
				    										<input id="kchargeno17" name="kchargeno17" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargeno17 }" />
									</td>
			</tr>
						<tr>
				<td>太原經管處審核：</td>
				<td>
				    										<input id="kchargename17" name="kchargename17" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargename17 }" />
									</td>
			</tr>
						<tr>
				<td>太原周邊總處核准：</td>
				<td>
				    										<input id="kchargeno18" name="kchargeno18" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargeno18 }" />
									</td>
			</tr>
						<tr>
				<td>太原周邊總處核准：</td>
				<td>
				    										<input id="kchargename18" name="kchargename18" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargename18 }" />
									</td>
			</tr>
						<tr>
				<td>承辦人確認：</td>
				<td>
				    										<input id="kchargeno19" name="kchargeno19" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargeno19 }" />
									</td>
			</tr>
						<tr>
				<td>承辦人確認：</td>
				<td>
				    										<input id="kchargename19" name="kchargename19" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.kchargename19 }" />
									</td>
			</tr>
						<tr>
				<td>申請人IP地址：</td>
				<td>
				    										<input id="applyIp" name="applyIp" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.applyIp }" />
									</td>
			</tr>
						<tr>
				<td>申請單狀態(E：結案，0：申請，O：，駁回 Q：異常結案)：</td>
				<td>
				    										<input id="applyStat" name="applyStat" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.applyStat }" />
									</td>
			</tr>
						<tr>
				<td>審核人工號：</td>
				<td>
				    										<input id="signEmpNo" name="signEmpNo" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.signEmpNo }" />
									</td>
			</tr>
						<tr>
				<td>審核人姓名：</td>
				<td>
				    										<input id="signEmpNam" name="signEmpNam" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.signEmpNam }" />
									</td>
			</tr>
						<tr>
				<td>審核時間：</td>
				<td>
				    										<input id="signDate" name="signDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${eGuardAply.signDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>附件Id：</td>
				<td>
				    										<input id="attachids" name="attachids" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.attachids }" />
									</td>
			</tr>
						<tr>
				<td>補充說明附件Id：</td>
				<td>
				    										<input id="reattachids" name="reattachids" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.reattachids }" />
									</td>
			</tr>
						<tr>
				<td>創建人：</td>
				<td>
				    										<input id="createBy" name="createBy" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.createBy }" />
									</td>
			</tr>
						<tr>
				<td>創建時間：</td>
				<td>
				    										<input id="createDate" name="createDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${eGuardAply.createDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>更新者：</td>
				<td>
				    										<input id="updateBy" name="updateBy" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.updateBy }" />
									</td>
			</tr>
						<tr>
				<td>更新時間：</td>
				<td>
				    										<input id="updateDate" name="updateDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss" data-options="width: 160"  disabled="disabled" value="<fmt:formatDate value="${eGuardAply.updateDate}"/>" />
									</td>
			</tr>
						<tr>
				<td>刪除標識：</td>
				<td>
				    										<input id="delFlag" name="delFlag" class="easyui-validatebox" data-options="width: 150" value="${eGuardAply.delFlag }" />
									</td>
			</tr>
			                    </table>
                </td>
           </tr>
		   <tr>
                <td>
                    <table class="formList">
                        <tr align="center">
                            <td>附件<font color="red">*</font></td>
                            <td colspan="9" class="td_style1"><span
                                    class="sl-custom-file"> <input type="button"
                                                                   value="点击上传文件" class="btn-file"/>
								<input id="attachidsUpload" name="attachidsUpload" type="file"
                                       onchange="uploadFile();" class="ui-input-file"/>
								</span> <input type="hidden" id="attachids"
                                               name="attachids" value="${EGuardAplyEntity.attachids }"/>
                                <div id="dowloadUrl">
                                    <c:forEach items="${file}" varStatus="i" var="item">
                                        <div id="${item.id}"
                                             style="line-height:30px;margin-left:5px;" class="float_L">
                                            <div class="float_L">
                                                <a href="${ctx}/newEsign/download/${item.id}">${item.name}</a>
                                            </div>
                                            <div class="float_L deleteBtn" onclick="delAtt('${item.id}')"></div>
                                        </div>
                                    </c:forEach>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th style="text-align:left;" colspan="10">&nbsp;&nbsp;審核路徑及審核記錄
                                <a href="javascript:void(0)"
                                   onclick="showWfImag('${workFlowId}','警衛服務申請主表','');">點擊查看簽核流程圖</a>
                            </th>
                        </tr>
                    </table>
                </td>
            </tr>
		    <tr>
                <td colspan="10" style="text-align:left;">
                    <table class="flowList" style="margin-top:5px;margin-left:10px;width:75%;margin-bottom:5px;">
                        <tr>
                            <td>簽核時間</td>
                            <td>簽核節點</td>
                            <td>簽核主管</td>
                            <td>簽核意見</td>
                            <td>批註</td>
                            <td>簽核電腦IP</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td colspan="10" style="border:none;text-align:center;margin-top:10px">
                    <a href="javascript:;" id="btnSave" class="easyui-linkbutton" data-options="iconCls:'icon-add'"
                       style="width: 100px;" onclick="saveInfo(0);">保存</a>&nbsp;&nbsp;&nbsp;&nbsp;
                    <a href="javascript:;" id="btnSubmit" class="easyui-linkbutton" data-options="iconCls:'icon-ok'"
                       style="width: 100px;" onclick="saveInfo(2);">提交</a>
                </td>
            </tr>
        </table>
	</div>
	<input type="hidden" id="deptNo" name="deptNo" value=""/>
    <input type="hidden" id="chargeNo" name="chargeNo" value=""/>
    <input type="hidden" id="chargeName" name="chargeName" value=""/>
    <input type="hidden" id="factoryId" name="factoryId" value=""/>
    <input type="hidden" id="dutyId" name="dutyId" value=""/>
    <div id="win"></div>
	</form>
  </div>
<script src='${ctx}/static/js/audit/eguardaply.js?random=<%= Math.random()%>'></script>
</body>
</html>