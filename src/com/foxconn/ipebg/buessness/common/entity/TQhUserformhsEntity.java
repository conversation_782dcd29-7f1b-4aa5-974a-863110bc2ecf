package com.foxconn.ipebg.buessness.common.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.foxconn.ipebg.common.entity.DataEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * 人員基本資料MHS
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-08 09:28:37
 */
@Entity
@Table(name = "T_QH_USERFORMHS")
@DynamicUpdate
@DynamicInsert
public class TQhUserformhsEntity extends DataEntity<TQhUserformhsEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    //資位編碼
    private String leveltype;
    //管理職
    private String ismanager;
    //入廠日期
    private Date indate;
    //在職狀態
    private String idstatus;
    //廠區名稱
    private String factoryname;
    //入集團時間
    private Date ingroup;
    //資位名稱
    private String leveltypename;
    //更新時間
    private Date updatedate;
    //學歷
    private String edupsn;
    //部門名稱
    private String deptname;
    //證件號碼
    private String psnid;
    //員工年資
    private Integer empyear;
    //出生日期 char type
    private String datBirV;
    //入廠日期 char type
    private String datInV;
    //入集團時間 char type
    private String datGroupV;
    //$column.comments
    private String empSex;
    //$column.comments
    private String edtDateV;
    //工號
    private String empno;
    //廠區記錄編碼
    private String factoryid;
    //姓名
    private String empname;
    //性別
    private String sex;
    //出生日期
    private Date birthday;
    //單位代碼
    private String deptno;
    //單位費用代碼
    private String deptcostno;
    //單位編號
    private String insurancedeptno;
    //個人參保電腦號
    private String insurancecomputer;
    //參保時間
    private String insurancetime;

    /**
     * 设置：資位編碼
     */
    public void setLeveltype(String leveltype) {
        this.leveltype = leveltype;
    }

    /**
     * 获取：資位編碼
     */
    @Column(name = "LEVELTYPE", nullable = false, length = 20)
    public String getLeveltype() {
        return leveltype;
    }

    /**
     * 设置：管理職
     */
    public void setIsmanager(String ismanager) {
        this.ismanager = ismanager;
    }

    /**
     * 获取：管理職
     */
    @Column(name = "ISMANAGER", nullable = false, length = 20)
    public String getIsmanager() {
        return ismanager;
    }

    /**
     * 设置：入廠日期
     */
    public void setIndate(Date indate) {
        this.indate = indate;
    }

    /**
     * 获取：入廠日期
     */
    @Column(name = "INDATE", nullable = false, length = 20)
    public Date getIndate() {
        return indate;
    }

    /**
     * 设置：在職狀態
     */
    public void setIdstatus(String idstatus) {
        this.idstatus = idstatus;
    }

    /**
     * 获取：在職狀態
     */
    @Column(name = "IDSTATUS", nullable = false, length = 20)
    public String getIdstatus() {
        return idstatus;
    }

    /**
     * 设置：廠區名稱
     */
    public void setFactoryname(String factoryname) {
        this.factoryname = factoryname;
    }

    /**
     * 获取：廠區名稱
     */
    @Column(name = "FACTORYNAME", nullable = false, length = 20)
    public String getFactoryname() {
        return factoryname;
    }

    /**
     * 设置：入集團時間
     */
    public void setIngroup(Date ingroup) {
        this.ingroup = ingroup;
    }

    /**
     * 获取：入集團時間
     */
    @Column(name = "INGROUP", nullable = false, length = 20)
    public Date getIngroup() {
        return ingroup;
    }

    /**
     * 设置：資位名稱
     */
    public void setLeveltypename(String leveltypename) {
        this.leveltypename = leveltypename;
    }

    /**
     * 获取：資位名稱
     */
    @Column(name = "LEVELTYPENAME", nullable = false, length = 20)
    public String getLeveltypename() {
        return leveltypename;
    }

    /**
     * 设置：更新時間
     */
    public void setUpdatedate(Date updatedate) {
        this.updatedate = updatedate;
    }

    /**
     * 获取：更新時間
     */
    @Column(name = "UPDATEDATE", nullable = false, length = 20)
    public Date getUpdatedate() {
        return updatedate;
    }

    /**
     * 设置：學歷
     */
    public void setEdupsn(String edupsn) {
        this.edupsn = edupsn;
    }

    /**
     * 获取：學歷
     */
    @Column(name = "EDUPSN", nullable = false, length = 20)
    public String getEdupsn() {
        return edupsn;
    }

    /**
     * 设置：部門名稱
     */
    public void setDeptname(String deptname) {
        this.deptname = deptname;
    }

    /**
     * 获取：部門名稱
     */
    @Column(name = "DEPTNAME", nullable = false, length = 20)
    public String getDeptname() {
        return deptname;
    }

    /**
     * 设置：證件號碼
     */
    public void setPsnid(String psnid) {
        this.psnid = psnid;
    }

    /**
     * 获取：證件號碼
     */
    @Column(name = "PSNID", nullable = false, length = 20)
    public String getPsnid() {
        return psnid;
    }

    /**
     * 设置：員工年資
     */
    public void setEmpyear(Integer empyear) {
        this.empyear = empyear;
    }

    /**
     * 获取：員工年資
     */
    @Column(name = "EMPYEAR", nullable = false, length = 20)
    public Integer getEmpyear() {
        return empyear;
    }

    /**
     * 设置：出生日期 char type
     */
    public void setDatBirV(String datBirV) {
        this.datBirV = datBirV;
    }

    /**
     * 获取：出生日期 char type
     */
    @Column(name = "DAT_BIR_V", nullable = false, length = 20)
    public String getDatBirV() {
        return datBirV;
    }

    /**
     * 设置：入廠日期 char type
     */
    public void setDatInV(String datInV) {
        this.datInV = datInV;
    }

    /**
     * 获取：入廠日期 char type
     */
    @Column(name = "DAT_IN_V", nullable = false, length = 20)
    public String getDatInV() {
        return datInV;
    }

    /**
     * 设置：入集團時間 char type
     */
    public void setDatGroupV(String datGroupV) {
        this.datGroupV = datGroupV;
    }

    /**
     * 获取：入集團時間 char type
     */
    @Column(name = "DAT_GROUP_V", nullable = false, length = 20)
    public String getDatGroupV() {
        return datGroupV;
    }

    /**
     * 设置：${column.comments}
     */
    public void setEmpSex(String empSex) {
        this.empSex = empSex;
    }

    /**
     * 获取：${column.comments}
     */
    @Column(name = "EMP_SEX", nullable = false, length = 20)
    public String getEmpSex() {
        return empSex;
    }

    /**
     * 设置：${column.comments}
     */
    public void setEdtDateV(String edtDateV) {
        this.edtDateV = edtDateV;
    }

    /**
     * 获取：${column.comments}
     */
    @Column(name = "EDT_DATE_V", nullable = false, length = 20)
    public String getEdtDateV() {
        return edtDateV;
    }

    /**
     * 设置：工號
     */
    public void setEmpno(String empno) {
        this.empno = empno;
    }

    /**
     * 获取：工號
     */
    @Id
    @Column(name = "EMPNO", nullable = false, length = 20)
    public String getEmpno() {
        return empno;
    }

    /**
     * 设置：廠區記錄編碼
     */
    public void setFactoryid(String factoryid) {
        this.factoryid = factoryid;
    }

    /**
     * 获取：廠區記錄編碼
     */
    @Column(name = "FACTORYID", nullable = false, length = 20)
    public String getFactoryid() {
        return factoryid;
    }

    /**
     * 设置：姓名
     */
    public void setEmpname(String empname) {
        this.empname = empname;
    }

    /**
     * 获取：姓名
     */
    @Column(name = "EMPNAME", nullable = false, length = 20)
    public String getEmpname() {
        return empname;
    }

    /**
     * 设置：性別
     */
    public void setSex(String sex) {
        this.sex = sex;
    }

    /**
     * 获取：性別
     */
    @Column(name = "SEX", nullable = false, length = 20)
    public String getSex() {
        return sex;
    }

    /**
     * 设置：出生日期
     */
    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    /**
     * 获取：出生日期
     */
    @Column(name = "BIRTHDAY", nullable = false, length = 20)
    public Date getBirthday() {
        return birthday;
    }

    /**
     * 设置：單位代碼
     */
    public void setDeptno(String deptno) {
        this.deptno = deptno;
    }

    /**
     * 获取：單位代碼
     */
    @Column(name = "DEPTNO", nullable = false, length = 20)
    public String getDeptno() {
        return deptno;
    }

    /**
     * 设置：單位費用代碼
     */
    public void setDeptcostno(String deptcostno) {
        this.deptcostno = deptcostno;
    }

    /**
     * 获取：單位費用代碼
     */
    @Column(name = "DEPTCOSTNO", nullable = false, length = 20)
    public String getDeptcostno() {
        return deptcostno;
    }

    /**
     * 获取：單位代碼（參保信息)
     */
    @Column(name = "INSURANCEDEPTNO", nullable = false, length = 20)
	public String getInsurancedeptno() {
		return insurancedeptno;
	}
	
	/**
     * 设置：單位代碼（參保信息)
     */
	public void setInsurancedeptno(String insurancedeptno) {
		this.insurancedeptno = insurancedeptno;
	}

	/**
     * 获取：參保電腦號
     */
	@Column(name = "INSURANCECOMPUTER", nullable = false, length = 20)
	public String getInsurancecomputer() {
		return insurancecomputer;
	}

	/**
     * 设置：參保電腦號
     */
	public void setInsurancecomputer(String insurancecomputer) {
		this.insurancecomputer = insurancecomputer;
	}

	/**
     * 获取：參保時間
     */
	@Column(name = "INSURANCETIME", nullable = false, length = 20)
	public String getInsurancetime() {
		return insurancetime;
	}

	/**
     * 设置：參保時間
     */
	public void setInsurancetime(String insurancetime) {
		this.insurancetime = insurancetime;
	}
}
