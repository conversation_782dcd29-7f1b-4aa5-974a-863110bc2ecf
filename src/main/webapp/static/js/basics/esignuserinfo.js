var dg;
var d;
$(function() {
	dg = $('#dg').datagrid({
		method : "get",
		url : ctx + '/esignuserinfo/list',
		fit : true,
		fitColumns : true,
		border : false,
		idField : 'id',
		striped : true,
		pagination : true,
		rownumbers : true,
		pageNumber : 1,
		pageSize : 20,
		pageList : [ 10, 20, 30, 40, 50 ],
		singleSelect : true,
		sortName : 'eserTyp,eserNodeName',
		sortOrder : 'asc,asc',
		columns : [ [ {
			field : 'id',
			title : '資料識別ID  Primary',
			hidden : true
		}, {
			field : 'eserId',
			title : '工號',
			sortable : true,
			width : 100
		}, {
			field : 'eserNam',
			title : '姓名',
			sortable : true,
			width : 100
		}, {
			field : 'eserSex',
			title : '性別',
			sortable : true,
			width : 100
		}, {
			field : 'eserDptId',
			title : '部門',
			sortable : true,
			width : 100
		}, {
			field : 'eserDptNam',
			title : '部門名稱',
			sortable : true,
			width : 100
		}, {
			field : 'eserMail',
			title : 'Mail地址',
			sortable : true,
			width : 100
		}, {
			field : 'eserNodeName',
			title : '簽核節點名稱',
			sortable : true,
			width : 100
		}, {
			field : 'eserTyp',
			title : '簽核表單類別代碼',
			sortable : true,
			width : 100
		}/*, {
			field : 'eserDesc',
			title : '簽核表單名稱',
			sortable : true,
			width : 100
		}*/, {
			field : 'ynVa',
			title : '有效否',
			sortable : true,
			width : 100
		}, {
			field : 'createBy',
			title : '創建人',
			sortable : true,
			width : 100
		}, {
			field : 'createDate',
			title : '創建時間',
			sortable : true,
			width : 100
		}, {
			field : 'updateBy',
			title : '更新者',
			sortable : true,
			width : 100
		}, {
			field : 'updateDate',
			title : '更新時間',
			sortable : true,
			width : 100
		} ] ],
		enableHeaderClickMenu : true,
		enableHeaderContextMenu : true,
		enableRowContextMenu : false,
		rowTooltip : true,
		toolbar : '#tb'
	});

	if (document.getElementById('eserTyp') != null) {
		console.log("asdfafasfdafasf");
		$.ajax({
			url: ctx + "/system/dict/getDictByType/form_name",
			//dataType:"json",
			type: "GET",
			success: function (data) {
				//绑定第一个下拉框
				$("#eserTyp").combobox({
					data: data,
					valueField: "value",
					textField: "label",
					editable: false,
					panelHeight: 400
				});
				onchangeType(true);
			},
			error: function (error) {
				alert("初始化下拉控件失败");
			}
		});
	}

	// 查詢流程類型下拉框
	$("#filterCodeType").combobox({
		url:ctx+'/ebscode/getCodeTypes',
		valueField: "codeTyp",
		textField: "codeTyp",
		editable: false,
		method: 'get',
		panelHeight: 'auto'
	});
});

var dealChange = function(blob, base64) {
    $("#showImg").attr("src",base64);
    $("#imageValue").val(base64);
}

var imageDeal = function(ele, returnBase64) {
    //获取file，转成base64
    var file = ele.files[0]; //取传入的第一个文件
    if(undefined == file) { //如果未找到文件，结束函数，跳出
        return;
    }
    console.log("fileSize" + file.size);
    console.log(file.type);

    var r = new FileReader();
    r.readAsDataURL(file);
    r.onload = function(e) {
        var base64 = e.target.result;
        var bili = 1.5;
        console.log("压缩前：" + base64.length);
        suofang(base64, bili, returnBase64);
    }
}
var suofang = function(base64, bili, callback) {
    console.log("执行缩放程序,bili=" + bili);
    //处理缩放，转格式
    var _img = new Image();
    _img.src = base64;
    _img.onload = function() {
        var _canvas = document.createElement("canvas");
        var w = this.width / bili;
        var h = this.height / bili;
        _canvas.setAttribute("width", w);
        _canvas.setAttribute("height", h);
        _canvas.getContext("2d").drawImage(this, 0, 0, w, h);
        var base64 = _canvas.toDataURL("image/jpeg");
        _canvas.toBlob(function(blob) {
            console.log(blob.size);

            if(blob.size > 1024*1024){
                suofang(base64, bili, callback);
            }else{
                callback(blob, base64);
            }
        }, "image/jpeg");
    }
}


// 弹窗增加
function add() {
	d = $("#dlg").dialog({
		title : '添加簽核者信息表',
		width : 330,
		height : 360,
		href : ctx + '/esignuserinfo/create',
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '确认',
			handler : function() {
				$("#mainform").submit();
			}
		}, {
			text : '取消',
			handler : function() {
				d.panel('close');
			}
		} ]
	});
}
//二級下拉
function onchangeType(init){
    var eserTyp=$('#eserTyp').combobox('getValue');
    debugger
    $.ajax({
        url: ctx+"/ebscode/getCodeId/"+eserTyp,
        type: "GET",
        success: function (data) {
            $("#eserNodeName").combobox({
                data: data,
                valueField: "codeId",
                textField: "codeNam",
                editable: false,
                panelHeight: 400,
                loadFilter: function (data) {
                	if (!init) {
						$('#eserNodeName').combobox('clear');//清空选中项
					}
                    data.unshift({code: '', name: '請選擇'});
                    return data;
                }
            });
        },
        error: function (error) {
            alert("初始化下拉控件失败");
        }
    });
}
// 删除
function del() {
	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	parent.$.messager.confirm('提示', '删除后无法恢复您确定要删除？', function(data) {
		if (data) {
			$.ajax({
				type : 'get',
				url : ctx + "/esignuserinfo/delete/" + row.id,
				success : function(data) {
					successTip(data, dg);
				}
			});
		}
	});
}

// 弹窗修改
function upd() {
	var row = dg.datagrid('getSelected');
	if (rowIsNull(row))
		return;
	d = $("#dlg").dialog({
		title : '修改簽核者信息表',
		width : 330,
		height : 360,
		href : ctx + '/esignuserinfo/update/' + row.id,
		maximizable : true,
		modal : true,
		buttons : [ {
			text : '修改',
			handler : function() {
				$('#mainform').submit();
			}
		}, {
			text : '取消',
			handler : function() {
				d.panel('close');
			}
		} ]
	});
}
// 创建查询对象并查询
function cx() {
	var obj = $("#searchFrom").serializeObject();
	dg.datagrid('load', obj);
}
// 表單進度查詢頁面查詢條件重置
function listSearchReset() {
	$("#searchFrom").form("reset");
}
// 导出excel
function exportExcel() {
	var form = document.getElementById("searchFrom");
	searchFrom.action = ctx + '/esignuserinfo/exportExcel';
	form.submit();
}