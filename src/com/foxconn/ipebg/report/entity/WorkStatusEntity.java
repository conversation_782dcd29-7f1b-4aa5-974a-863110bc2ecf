package com.foxconn.ipebg.report.entity;

        import java.io.Serializable;
        import java.util.Date;
        import javax.persistence.*;

        import com.foxconn.ipebg.common.entity.DataEntity;
        import org.hibernate.annotations.DynamicInsert;
        import org.hibernate.annotations.DynamicUpdate;


/**
 * 班別資料
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-03-07 09:57:22
 */
@Entity
@Table(name = "TEMP_WORKSTATUS_DETAIL")
@DynamicUpdate
@DynamicInsert
public class WorkStatusEntity extends DataEntity<WorkStatusEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

                                    //公司
            private String colL1;
                                //崗
            private String colL2;
                                //崗位
            private String colL3;
                                //編制人數
            private String colL4;
            					//應到人數-白班
            private String colL5;
						//應到人數-夜班
			private String colL6;
			//應到人數-合計
			private String colL7;
			//實到人數-白班
			private String colL8;
			//實到人數-夜班
			private String colL9;
			//實到人數-合計
			private String colL10;
			//調休人數
			private String colL11;
			//缺勤人數
			private String colL12;
			//缺勤比率
			private String colL13;
			//出勤比率
			private String colL14;
			
			
			@Column(name = "L1", nullable = false, length = 200)
			public String getColL1() {
				return colL1;
			}
			/**
			 * @param colL1 the colL1 to set
			 */
			public void setColL1(String colL1) {
				this.colL1 = colL1;
			}
			
			@Column(name = "L2", nullable = false, length = 200)
			public String getColL2() {
				return colL2;
			}
			/**
			 * @param colL2 the colL2 to set
			 */
			public void setColL2(String colL2) {
				this.colL2 = colL2;
			}
			
			@Column(name = "L3", nullable = false, length = 200)
			public String getColL3() {
				return colL3;
			}
			/**
			 * @param colL3 the colL3 to set
			 */
			public void setColL3(String colL3) {
				this.colL3 = colL3;
			}
			
			@Column(name = "L4", nullable = false, length = 200)
			public String getColL4() {
				return colL4;
			}
			/**
			 * @param colL4 the colL4 to set
			 */
			public void setColL4(String colL4) {
				this.colL4 = colL4;
			}
			
			@Column(name = "L5", nullable = false, length = 200)
			public String getColL5() {
				return colL5;
			}
			/**
			 * @param colL5 the colL5 to set
			 */
			public void setColL5(String colL5) {
				this.colL5 = colL5;
			}
			
			@Column(name = "L6", nullable = false, length = 200)
			public String getColL6() {
				return colL6;
			}
			/**
			 * @param colL6 the colL6 to set
			 */
			public void setColL6(String colL6) {
				this.colL6 = colL6;
			}
			
			@Column(name = "L7", nullable = false, length = 200)
			public String getColL7() {
				return colL7;
			}
			/**
			 * @param colL7 the colL7 to set
			 */
			public void setColL7(String colL7) {
				this.colL7 = colL7;
			}

			@Column(name = "L8", nullable = false, length = 200)
			public String getColL8() {
				return colL8;
			}
			/**
			 * @param colL8 the colL8 to set
			 */
			public void setColL8(String colL8) {
				this.colL8 = colL8;
			}
			
			@Column(name = "L9", nullable = false, length = 200)
			public String getColL9() {
				return colL9;
			}
			/**
			 * @param colL9 the colL9 to set
			 */
			public void setColL9(String colL9) {
				this.colL9 = colL9;
			}
			
			@Column(name = "L10", nullable = false, length = 200)
			public String getColL10() {
				return colL10;
			}
			/**
			 * @param colL10 the colL10 to set
			 */
			public void setColL10(String colL10) {
				this.colL10 = colL10;
			}
			
			@Column(name = "L11", nullable = false, length = 200)
			public String getColL11() {
				return colL11;
			}
			/**
			 * @param colL11 the colL11 to set
			 */
			public void setColL11(String colL11) {
				this.colL11 = colL11;
			}
			
			@Column(name = "L12", nullable = false, length = 200)
			public String getColL12() {
				return colL12;
			}
			/**
			 * @param colL12 the colL12 to set
			 */
			public void setColL12(String colL12) {
				this.colL12 = colL12;
			}
			
			@Column(name = "L13", nullable = false, length = 200)
			public String getColL13() {
				return colL13;
			}
			/**
			 * @param colL13 the colL13 to set
			 */
			public void setColL13(String colL13) {
				this.colL13 = colL13;
			}
			
			@Column(name = "L14", nullable = false, length = 200)
			public String getColL14() {
				return colL14;
			}
			/**
			 * @param colL14 the colL14 to set
			 */
			public void setColL14(String colL14) {
				this.colL14 = colL14;
			}
			

                                                                           
            
			
}
