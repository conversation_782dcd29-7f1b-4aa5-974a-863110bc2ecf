package com.foxconn.ipebg.system.dao;

import java.util.List;

import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.system.entity.Permission;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.hibernate.type.StandardBasicTypes;
import org.springframework.stereotype.Repository;


/**
 * 权限DAO
 * <AUTHOR>
 * @date 2015年1月13日
 */
@Repository
public class PermissionDao extends HibernateDao<Permission, Integer> {

	/**
	 * 查询用户拥有的权限
	 * @param userId 用户id
	 * @return 结果集合
	 */
	@SuppressWarnings("unchecked")
	public List<Permission> findPermissions(Integer userId){
		StringBuffer sb=new StringBuffer();
		sb.append("select distinct p.* from sys_permission p ");
		sb.append("INNER JOIN sys_role_permission rp on p.ID=rp.permission_id ");
		sb.append("INNER JOIN sys_role r ON  r.id=rp.role_id ");
		sb.append("INNER JOIN sys_user_role  ur ON ur.role_id =rp.role_id ");
		sb.append("INNER JOIN sys_user u ON u.id = ur.user_id ");
		sb.append("where u.id=?0 order by p.sort");
		SQLQuery sqlQuery=createSQLQuery(sb.toString(), userId);
		sqlQuery.addEntity(Permission.class);
		//sqlQuery.setCacheable(true);
		return sqlQuery.list();
	}
	
	/**
	 * 查询所有的菜单
	 * @param userId
	 * @return 菜单集合
	 */
	@SuppressWarnings("unchecked")
	public List<Permission> findMenus(){
		StringBuffer sb=new StringBuffer();
		sb.append("select p.id id,p.pid pid,p.name as name,p.url url,p.icon icon,p.sort sort,p.description description from sys_permission p ");
		sb.append("where p.type='F' order by p.sort");
		SQLQuery sqlQuery=createSQLQuery(sb.toString());
		sqlQuery.addScalar("id",StandardBasicTypes.INTEGER );
		sqlQuery.addScalar("pid", StandardBasicTypes.INTEGER);
		sqlQuery.addScalar("name",StandardBasicTypes.STRING);
		sqlQuery.addScalar("url",StandardBasicTypes.STRING);
		sqlQuery.addScalar("icon",StandardBasicTypes.STRING);
		sqlQuery.addScalar("sort",StandardBasicTypes.INTEGER);
		sqlQuery.addScalar("description",StandardBasicTypes.STRING);
		sqlQuery.setResultTransformer(Transformers.aliasToBean(Permission.class));
		//sqlQuery.setCacheable(true);
		return sqlQuery.list();
	}
	
	
	/**
	 * 查询用户拥有的菜单权限
	 * @param userId
	 * @return 结果集合
	 */
	@SuppressWarnings("unchecked")
	public List<Permission> findMenus(Integer userId){
		StringBuffer sb=new StringBuffer();
		sb.append("select distinct p.id id,p.pid pid,p.name as name,p.url url,p.icon icon,p.sort sort,p.description description from sys_permission p ");
		sb.append("INNER JOIN sys_role_permission rp on p.id=rp.permission_id ");
		sb.append("INNER JOIN sys_role r ON r.id=rp.role_id ");
		sb.append("INNER JOIN sys_user_role ur ON ur.role_id =rp.role_id ");
		sb.append("INNER JOIN sys_user u ON u.id = ur.user_id ");
		sb.append("where p.type='F' and u.id=?0 order by p.sort");
		SQLQuery sqlQuery=createSQLQuery(sb.toString(), userId);
		sqlQuery.addScalar("id",StandardBasicTypes.INTEGER );
		sqlQuery.addScalar("pid", StandardBasicTypes.INTEGER);
		sqlQuery.addScalar("name",StandardBasicTypes.STRING);
		sqlQuery.addScalar("url",StandardBasicTypes.STRING);
		sqlQuery.addScalar("icon",StandardBasicTypes.STRING);
		sqlQuery.addScalar("sort",StandardBasicTypes.INTEGER);
		sqlQuery.addScalar("description",StandardBasicTypes.STRING);
		sqlQuery.setResultTransformer(Transformers.aliasToBean(Permission.class));
		//sqlQuery.setCacheable(true);
		return sqlQuery.list();
	}
	
	/**
	 * 查询菜单下的操作权限
	 * @param userId
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<Permission> findMenuOperation(Integer pid){
		StringBuffer sb=new StringBuffer();
		sb.append("select p.id id,p.name as name,p.url url,p.perm_code permCode,p.description description from sys_permission p ");
		sb.append("where p.type='O' and p.pid=?0 order by p.sort");
		SQLQuery sqlQuery=createSQLQuery(sb.toString(),pid);
		sqlQuery.addScalar("id",StandardBasicTypes.INTEGER );
		sqlQuery.addScalar("name",StandardBasicTypes.STRING);
		sqlQuery.addScalar("url",StandardBasicTypes.STRING);
		sqlQuery.addScalar("permCode",StandardBasicTypes.STRING);
		sqlQuery.addScalar("description",StandardBasicTypes.STRING);
		sqlQuery.setResultTransformer(Transformers.aliasToBean(Permission.class));
		//sqlQuery.setCacheable(true);
		return sqlQuery.list();
	}
}
