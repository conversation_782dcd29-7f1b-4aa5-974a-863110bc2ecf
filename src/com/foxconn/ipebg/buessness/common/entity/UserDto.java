package com.foxconn.ipebg.buessness.common.entity;

import javax.persistence.Column;
import javax.persistence.Id;
import java.sql.Timestamp;
import java.util.Date;

/**
 * Company foxconn
 * Copyright (C) 2004-2018 All Rights Reserved.
 */
public class UserDto {

    //資位編碼
    private String leveltype;
    //管理職
    private String ismanager;
    //入廠日期
    private Date indate;
    //在職狀態
    private String idstatus;
    //廠區名稱
    private String factoryname;
    //入集團時間
    private Date ingroup;
    //資位名稱
    private String leveltypename;
    //更新時間
    private Date updatedate;
    //學歷
    private String edupsn;
    //部門名稱
    private String deptname;
    //證件號碼
    private String psnid;
    //員工年資
    private Integer empyear;
    //出生日期 char type
    private String datBirV;
    //入廠日期 char type
    private String datInV;
    //入集團時間 char type
    private String datGroupV;
    //$column.comments
    private String empSex;
    //$column.comments
    private String edtDateV;
    //工號
    private String empno;
    //廠區記錄編碼
    private String factoryid;
    //姓名
    private String empname;
    //性別
    private String sex;
    //單位代碼
    private String deptno;
    //參保單位編號
    private String insurancedeptno;
    //參保電腦號
    private String insurancecomputer;
    //參保時間
    private Date insurancetime;
    //單位費用代碼
    private String deptcostno;
    


	// 事業群
 	private String dptQun;
 	// 事業處
 	private String dptChu;
 	// 部門名稱
 	private String dptBu;
 	// 課級單位名稱
 	private String dptKe;
 	// 單位代碼
 	private String dptId;
 	// 費用代碼
 	private String costId;
 	
    private String loginName;
    private String name;
    private String password;
    private String plainPassword;
    private String salt;
    private Timestamp birthday;
    private Short gender;
    private String email;
    private String phone;
    private String icon;
    private Timestamp createDate;
    private String state;
    private String description;
    private Integer loginCount;
    private Timestamp previousVisit;
    private Timestamp lastVisit;
    private String delFlag;

    public String getLeveltype() {
        return leveltype;
    }

    public void setLeveltype(String leveltype) {
        this.leveltype = leveltype;
    }

    public String getIsmanager() {
        return ismanager;
    }

    public void setIsmanager(String ismanager) {
        this.ismanager = ismanager;
    }

    public Date getIndate() {
        return indate;
    }

    public void setIndate(Date indate) {
        this.indate = indate;
    }

    public String getIdstatus() {
        return idstatus;
    }

    public void setIdstatus(String idstatus) {
        this.idstatus = idstatus;
    }

    public String getFactoryname() {
        return factoryname;
    }

    public void setFactoryname(String factoryname) {
        this.factoryname = factoryname;
    }

    public Date getIngroup() {
        return ingroup;
    }

    public void setIngroup(Date ingroup) {
        this.ingroup = ingroup;
    }

    public String getLeveltypename() {
        return leveltypename;
    }

    public void setLeveltypename(String leveltypename) {
        this.leveltypename = leveltypename;
    }

    public Date getUpdatedate() {
        return updatedate;
    }

    public void setUpdatedate(Date updatedate) {
        this.updatedate = updatedate;
    }

    public String getEdupsn() {
        return edupsn;
    }

    public void setEdupsn(String edupsn) {
        this.edupsn = edupsn;
    }

    public String getDeptname() {
        return deptname;
    }

    public void setDeptname(String deptname) {
        this.deptname = deptname;
    }

    public String getPsnid() {
        return psnid;
    }

    public void setPsnid(String psnid) {
        this.psnid = psnid;
    }

    public Integer getEmpyear() {
        return empyear;
    }

    public void setEmpyear(Integer empyear) {
        this.empyear = empyear;
    }

    public String getDatBirV() {
        return datBirV;
    }

    public void setDatBirV(String datBirV) {
        this.datBirV = datBirV;
    }

    public String getDatInV() {
        return datInV;
    }

    public void setDatInV(String datInV) {
        this.datInV = datInV;
    }

    public String getDatGroupV() {
        return datGroupV;
    }

    public void setDatGroupV(String datGroupV) {
        this.datGroupV = datGroupV;
    }

    public String getEmpSex() {
        return empSex;
    }

    public void setEmpSex(String empSex) {
        this.empSex = empSex;
    }

    public String getEdtDateV() {
        return edtDateV;
    }

    public void setEdtDateV(String edtDateV) {
        this.edtDateV = edtDateV;
    }

    public String getEmpno() {
        return empno;
    }

    public void setEmpno(String empno) {
        this.empno = empno;
    }

    public String getFactoryid() {
        return factoryid;
    }

    public void setFactoryid(String factoryid) {
        this.factoryid = factoryid;
    }

    public String getEmpname() {
        return empname;
    }

    public void setEmpname(String empname) {
        this.empname = empname;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getDeptno() {
        return deptno;
    }

    public void setDeptno(String deptno) {
        this.deptno = deptno;
    }

    public String getDeptcostno() {
        return deptcostno;
    }

    public void setDeptcostno(String deptcostno) {
        this.deptcostno = deptcostno;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPlainPassword() {
        return plainPassword;
    }

    public void setPlainPassword(String plainPassword) {
        this.plainPassword = plainPassword;
    }

    public String getSalt() {
        return salt;
    }

    public void setSalt(String salt) {
        this.salt = salt;
    }

    public Timestamp getBirthday() {
        return birthday;
    }

    public void setBirthday(Timestamp birthday) {
        this.birthday = birthday;
    }

    public Short getGender() {
        return gender;
    }

    public void setGender(Short gender) {
        this.gender = gender;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getLoginCount() {
        return loginCount;
    }

    public void setLoginCount(Integer loginCount) {
        this.loginCount = loginCount;
    }

    public Timestamp getPreviousVisit() {
        return previousVisit;
    }

    public void setPreviousVisit(Timestamp previousVisit) {
        this.previousVisit = previousVisit;
    }

    public Timestamp getLastVisit() {
        return lastVisit;
    }

    public void setLastVisit(Timestamp lastVisit) {
        this.lastVisit = lastVisit;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

	public String getInsurancedeptno() {
		return insurancedeptno;
	}

	public void setInsurancedeptno(String insurancedeptno) {
		this.insurancedeptno = insurancedeptno;
	}

	public String getInsurancecomputer() {
		return insurancecomputer;
	}

	public void setInsurancecomputer(String insurancecomputer) {
		this.insurancecomputer = insurancecomputer;
	}

	public Date getInsurancetime() {
		return insurancetime;
	}

	public void setInsurancetime(Date insurancetime) {
		this.insurancetime = insurancetime;
	}
	 public String getDptQun() {
			return dptQun;
		}

		public void setDptQun(String dptQun) {
			this.dptQun = dptQun;
		}

		public String getDptChu() {
			return dptChu;
		}

		public void setDptChu(String dptChu) {
			this.dptChu = dptChu;
		}

		public String getDptBu() {
			return dptBu;
		}

		public void setDptBu(String dptBu) {
			this.dptBu = dptBu;
		}

		public String getDptKe() {
			return dptKe;
		}

		public void setDptKe(String dptKe) {
			this.dptKe = dptKe;
		}

		public String getDptId() {
			return dptId;
		}

		public void setDptId(String dptId) {
			this.dptId = dptId;
		}

		public String getCostId() {
			return costId;
		}

		public void setCostId(String costId) {
			this.costId = costId;
		}
    
}
