<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>合格人員</title>
<script type="text/javascript">
    var ctx = "${pageContext.request.contextPath}";
</script>

<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
</head>
<body>
<form id="mainform" action="${ctx}/perinfo/${action}" method="post">
	<table class="formTable">
		<tr>
			<!-- <td>資料識別ID  Primary：</td> -->
			<td>

				<input id="ids" name="ids" type="hidden" value="${perInfo.id }"/>
				<%-- <input id="id" name="id" class="easyui-validatebox" data-options="width: 150" value="${perInfo.id }" /> --%>
			</td>
		</tr>
		<%-- <tr>
<td>硬卡號：</td>
<td>
                                            <input id="cardId" name="cardId" class="easyui-validatebox" data-options="width: 150" value="${perInfo.cardId }" />
                    </td>
</tr> --%>


		<tr>
			<td>工號：</td>
			<td>
				<input id="empNo" name="empNo" class="easyui-validatebox" data-options="width: 150"
					   value="${perInfo.empNo }" onblur="queryEmpInfo(this);"/>
			</td>
		</tr>
		<tr>
			<td>姓名：</td>
			<td>
				<input id="empName" name="empName" class="easyui-validatebox" data-options="width: 150"
					   value="${perInfo.empName }" readonly="readonly"/>
			</td>
		</tr>


		<tr>
			<td>公司：</td>
			<td>
				<input id="company" name="company" class="easyui-validatebox" data-options="width: 150"
					   value="${perInfo.company }" readonly="readonly"/>
				<%-- <select name = "company"  class="easyui-combobox" value="${perInfo.company }">  --%>
				<!-- <option>保安公司</option>
                <option>橫博</option>
                <option>大地</option>


                </select> -->


			</td>
		</tr>
		<tr>
			<td>身份證號：</td>
			<td>
				<input id="psnId" name="psnId" class="easyui-validatebox" data-options="width: 150"
					   value="${perInfo.psnId }" readonly="readonly"/>
			</td>
		</tr>
		<tr>
			<td>入職日期：</td>
			<td>
				<input id="entryDate" name="entryDate" class="easyui-my97"
					   datefmt="yyyy-MM-dd" data-options="width: 150"
					   value="<fmt:formatDate value="${perInfo.entryDate}"/>"/>
			</td>
		</tr>
		<tr>
			<td>考試合格日期：</td>
			<td>
				<input id="passDate" name="passDate" class="easyui-my97"
					   datefmt="yyyy-MM-dd" data-options="width: 150"
					   value="<fmt:formatDate value="${perInfo.passDate}"/>"/>
			</td>
		</tr>

		<tr style="display: none">
			<td>
				<input id="createBy" name="createBy" readonly class="easyui-validatebox" data-options="width: 150"
					   value="${perInfo.createBy }"/>
				<input id="createDate" name="createDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
					   data-options="width: 160" disabled="disabled"
					   value="<fmt:formatDate value="${perInfo.createDate}"/>"/>
				<input id="updateBy" name="updateBy" readonly class="easyui-validatebox" data-options="width: 150"
					   value="${perInfo.updateBy }"/>
				<input id="updateDate" name="updateDate" class="easyui-my97" datefmt="yyyy-MM-dd HH:mm:ss"
					   data-options="width: 160" disabled="disabled"
					   value="<fmt:formatDate value="${perInfo.updateDate}"/>"/>
			</td>
		</tr>
		<%-- <tr>
<td>刪除標示：</td>
<td>
                                            <input id="delFlag" name="delFlag" class="easyui-validatebox" data-options="width: 150" value="${perInfo.delFlag }" />
                    </td>
</tr> --%>
	</table>
</form>
<script src='${ctx}/static/js/basics/perinfo.js?"+Math.random()"'></script>
<script type="text/javascript">
   //提交表单
$('#mainform').form({
    onSubmit: function(){
        var isValid = $(this).form('validate');
        return isValid;	// 返回false终止表单提交
    },
    success:function(data){ 
        successTip(data,dg,d);
    }
});
</script>
</body>
</html>