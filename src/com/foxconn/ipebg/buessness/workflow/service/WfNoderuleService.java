package com.foxconn.ipebg.buessness.workflow.service;

import com.foxconn.ipebg.buessness.workflow.entity.WfNoderuleEntity;
import com.foxconn.ipebg.buessness.workflow.dao.WfNoderuleDao;
import com.foxconn.ipebg.common.persistence.HibernateDao;
import com.foxconn.ipebg.common.service.BaseBusinessService;
import com.foxconn.ipebg.common.service.BaseService;
import com.foxconn.ipebg.system.entity.Dict;
import com.foxconn.ipebg.system.service.DictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;


/**
 * 流程中會簽節點流程規則
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018-10-06 11:04:39
 */
@Service
@Transactional(readOnly=true)
public class  WfNoderuleService extends BaseBusinessService<WfNoderuleEntity, String> {
    @Autowired
    private WfNoderuleDao wfNoderuleDao;
    @Autowired
    private DictService dictService;
    @Override
    public HibernateDao<WfNoderuleEntity, String> getEntityDao() {
        return wfNoderuleDao;
    }

    public WfNoderuleEntity findUniqByNodeId(String nodeid,String workflowId){
        Dict dict = dictService.getDictByTypeAndVlaue("workflow_code",workflowId);
        Assert.notNull(dict);
        String version = dict.getLabel();
        return wfNoderuleDao.findUnique("from WfNoderuleEntity t where t.nodeid=?0 and t.version=?1",nodeid,version);
    }
}
