package com.foxconn.ipebg.system.utils;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.foxconn.ipebg.system.entity.OldMyTask;

import java.net.MalformedURLException;
import java.net.URL;

import javax.xml.namespace.QName;
import javax.xml.ws.Service;

/**
 * 通过客户端编程的方式调用Webservice服务
 *
 */
public class WebServiceClient {

    public static void main(String[] args) throws Exception {
        URL wsdlUrl = new URL("http://localhost:8083/RemoteServicePort?wsdl");
        Service s = Service.create(wsdlUrl, new QName("http://ws.chn.foxconn.com/","RemoteServiceService"));
        RemoteServiceService hs = s.getPort(new QName("http://ws.chn.foxconn.com/","RemoteServicePort"), RemoteServiceService.class);
        String ret = hs.getAllMyTasks("AD");
        System.out.println(ret);
        System.out.println(JSONUtil.toJsonStr(ret));
        OldMyTask tasks = JSONUtil.toBean(ret,OldMyTask.class);
        System.out.println(JSONUtil.toJsonStr(tasks));
    }
}