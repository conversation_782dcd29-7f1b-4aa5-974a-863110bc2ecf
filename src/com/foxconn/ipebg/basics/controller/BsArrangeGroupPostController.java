package com.foxconn.ipebg.basics.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Date;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.basics.entity.BsArrangeGroupPostEntity;
import com.foxconn.ipebg.basics.entity.BsTraineeEntity;
import com.foxconn.ipebg.basics.entity.BsTrainingPeriodEntity;
import com.foxconn.ipebg.basics.service.BsArrangeGroupPostService;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;
import com.foxconn.ipebg.system.entity.User;


/**
 * 排班群組匹配崗位
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-09-11 10:28:23
 */
@Controller
@RequestMapping("bsarrangegrouppost")
public class BsArrangeGroupPostController extends BaseController {

    @Autowired
    private BsArrangeGroupPostService bsArrangeGroupPostService;
   
    /**
      * 方法描述: 列表信息
      * @Author: S6113712
      * @CreateDate:   2019-09-11 10:28:23
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("basics:bsarrangegrouppost:list")
    public String list() {
        //查询列表数据
        return "basics/bsarrangegrouppost/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6113712
      * @CreateDate:   2019-09-11 10:28:23
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("basics:bsarrangegrouppost:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<BsArrangeGroupPostEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        User user = UserUtil.getCurrentUser();
        if (user != null) {
            // 按保安公司查询
            PropertyFilter filter = new PropertyFilter("INS_securityCom", user.getSecurityComs());
            filters.add(filter);
        }
//        page.setOrderBy("createDate");
//  		page.setOrder("desc");
        page = bsArrangeGroupPostService.search(page, filters);
        return getEasyUIData(page);
    }
    /**
     * 方法描述: 保存
     * @Author: S6113712
     * @CreateDate:   2019-09-11 10:28:23
     * @Return
     **/
   
    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("basics:bsarrangegrouppost:add")
    @ResponseBody
    public String create(HttpServletRequest request, Model model) {
    	

    	
		String arrangeGroupId = request.getParameter("arrangeGroupId");
		String recnos = request.getParameter("recnos");

		String[] recnoArray = recnos.split(",");

		for (int i = 0; i < recnoArray.length; i++) {
			BsArrangeGroupPostEntity bsArrangeGroupPost = new BsArrangeGroupPostEntity();

			bsArrangeGroupPost.setRecno(recnoArray[i]);
			bsArrangeGroupPost.setArrangeGroupId(arrangeGroupId);
			bsArrangeGroupPost.setNewRecord(true);
			bsArrangeGroupPost.setCreateDate(new Date());
			bsArrangeGroupPostService.save(bsArrangeGroupPost);
		}
		return "success";
    
       
    }
    
    /**
      * 方法描述: 添加跳轉
      * @Author: S6113712
      * @CreateDate:   2019-09-11 10:28:23
      * @Return
      **/

    @RequiresPermissions("basics:bsarrangegrouppost:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
//        model.addAttribute("bsArrangeGroupPost", new BsArrangeGroupPostEntity());
//        model.addAttribute("action", "create");
        return "basics/bsarrangegrouppost/addGroupPostList";
    }
   
     /**
     * 方法描述: 修改跳轉
     * @Author: S6113712
     * @CreateDate:   2018-10-06 11:04:39
     * @Return
     **/

   @RequiresPermissions("basics:bsarrangegrouppost:update")
   @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
   public String updateForm(@PathVariable("id") String id, Model model) {
       model.addAttribute("bsArrangeGroupPost", bsArrangeGroupPostService.get(id));
       model.addAttribute("action", "update");
      return "basics/bsarrangegrouppost/listForm";
   }
   
	/**
	 * 方法描述: 修改
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-11 10:55:00
	 * @Return
	 **/

   @RequestMapping(value = "update", method = RequestMethod.POST)
   @RequiresPermissions("basics:bsarrangegrouppost:update")
   @ResponseBody
   public String update(@Valid BsArrangeGroupPostEntity bsArrangeGroupPost,@RequestParam(value = "ids") String ids) {
	   bsArrangeGroupPost.setId(ids);
	   bsArrangeGroupPost.setUpdateDate(new Date());
	   bsArrangeGroupPostService.update(bsArrangeGroupPost);
	   
       return "success";
   }
    /**
      * 方法描述: 根據主鍵刪除
      * @Author: S6113712
      * @CreateDate:   2019-09-11 10:28:23
      * @Return
      **/

    @RequestMapping("delete/{ids}")
    @RequiresPermissions("basics:bsarrangegrouppost:delete")
    @ResponseBody
    public String delete(@PathVariable("ids") String ids) {
    	String[] idArray =ids.split(",");
    	for (int i=0;i<idArray.length;i++)
    	{
    		bsArrangeGroupPostService.delete(idArray[i]);
    	}    
        return "success";
    }
    /**
      * 导出excel
      * @Author: S6113712
      * @CreateDate:   2019-09-11 10:28:23
      * @Return
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {}
}
