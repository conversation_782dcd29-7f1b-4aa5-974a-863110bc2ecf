package com.foxconn.ipebg.buessness.audit.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Date;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import com.foxconn.ipebg.common.persistence.Page;
import com.foxconn.ipebg.common.persistence.PropertyFilter;
import com.foxconn.ipebg.common.web.BaseController;
import com.foxconn.ipebg.buessness.audit.entity.EGuardItemAplyEntity;
import com.foxconn.ipebg.buessness.audit.service.EGuardItemAplyService;
import com.foxconn.ipebg.system.utils.ExcelUtil;
import com.foxconn.ipebg.system.utils.UserUtil;
import com.foxconn.ipebg.buessness.common.service.TQhUserformhsService;


/**
 * 崗位申請明細表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-02-23 16:41:23
 */
@Controller
@RequestMapping("eguarditemaply")
public class EGuardItemAplyController extends BaseController {

    @Autowired
    private EGuardItemAplyService eGuardItemAplyService;
   
    /**
      * 方法描述: 列表信息
      * @Author: S6113712
      * @CreateDate:   2019-02-23 16:41:23
      * @Return
      **/

    @RequestMapping(method = RequestMethod.GET)
    //@RequiresPermissions("audit:eguarditemaply:list")
    public String list() {
        //查询列表数据
        return "audit/eguarditemaply/list";
    }

    /**
      * 方法描述:  分頁查詢信息
      * @Author: S6113712
      * @CreateDate:   2019-02-23 16:41:23
      * @Return
      **/

    @RequestMapping(value = "list", method = RequestMethod.GET)
    //@RequiresPermissions("audit:eguarditemaply:list")
    @ResponseBody
    public Map<String, Object> infoList(HttpServletRequest request) {
        Page<EGuardItemAplyEntity> page = getPage(request);
        List<PropertyFilter> filters = PropertyFilter.buildFromHttpRequest(request);
        page = eGuardItemAplyService.search(page, filters);
        return getEasyUIData(page);
    }
    /**
     * 方法描述: 保存
     * @Author: S6113712
     * @CreateDate:   2019-02-23 16:41:23
     * @Return
     **/
   
    @RequestMapping(value="create", method = RequestMethod.POST)
    @RequiresPermissions("audit:eguarditemaply:add")
    @ResponseBody
    public String create(@Valid EGuardItemAplyEntity eGuardItemAply, Model model) {
        eGuardItemAply.setNewRecord(true);
    	eGuardItemAply.setCreateDate(new Date());
        eGuardItemAplyService.save(eGuardItemAply);
        return "success";
    }
    
    /**
      * 方法描述: 添加跳轉
      * @Author: S6113712
      * @CreateDate:   2019-02-23 16:41:23
      * @Return
      **/

    @RequiresPermissions("audit:eguarditemaply:add")
    @RequestMapping(value = "create", method = RequestMethod.GET)
    public String createForm(Model model) {
        model.addAttribute("eGuardItemAply", new EGuardItemAplyEntity());
        model.addAttribute("action", "create");
        return "audit/eguarditemaply/listForm";
    }
    
     /**
     * 方法描述: 修改跳轉
     * @Author: S6113712
     * @CreateDate:   2018-10-06 11:04:39
     * @Return
     **/

   @RequiresPermissions("audit:eguarditemaply:update")
   @RequestMapping(value = "update/{id}", method = RequestMethod.GET)
   public String updateForm(@PathVariable("id") String id, Model model) {
       model.addAttribute("eGuardItemAply", eGuardItemAplyService.get(id));
       model.addAttribute("action", "update");
      return "audit/eguarditemaply/listForm";
   }
   
	/**
	 * 方法描述: 修改
	 * 
	 * @Author: S6113712
	 * @CreateDate: 2018-10-11 10:55:00
	 * @Return
	 **/

   @RequestMapping(value = "update", method = RequestMethod.POST)
   @RequiresPermissions("audit:eguarditemaply:update")
   @ResponseBody
   public String update(@Valid EGuardItemAplyEntity eGuardItemAply,@RequestParam(value = "ids") String ids) {
	   eGuardItemAply.setId(ids);
	   eGuardItemAply.setUpdateDate(new Date());
	   eGuardItemAplyService.update(eGuardItemAply);
	   
       return "success";
   }
    /**
      * 方法描述: 根據主鍵刪除
      * @Author: S6113712
      * @CreateDate:   2019-02-23 16:41:23
      * @Return
      **/

    @RequestMapping("delete/{id}")
    @RequiresPermissions("audit:eguarditemaply:delete")
    @ResponseBody
    public String delete(@PathVariable("id") String id) {
        eGuardItemAplyService.delete(id);
        return "success";
    }
    /**
      * 导出excel
      * @Author: S6113712
      * @CreateDate:   2019-02-23 16:41:23
      * @Return
      **/
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {}
    
    /**
     * 驗證崗位名稱唯一性
     * @Author: S6113712
     * @CreateDate:   2019-10-23 16:41:23
     * @Return
     **/
    
    @RequestMapping(value = "isExistPostName", method = RequestMethod.POST)
    @ResponseBody
	public String isExistPostName(HttpServletRequest request ) {
    	String postName=request.getParameter("postName");
    	String recno = request.getParameter("recno");
    	EGuardItemAplyEntity eGuardItemAplyEntity=new EGuardItemAplyEntity();
    	eGuardItemAplyEntity.setPostName(postName);
    	eGuardItemAplyEntity.setRecno(recno);
    	if (eGuardItemAplyService.isExistPostName(eGuardItemAplyEntity))
		{
    		return "yes";
		}else
		{
			return "no";
		}
	}
}
