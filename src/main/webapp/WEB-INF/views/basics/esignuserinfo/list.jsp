<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>簽核者信息表</title>
<script type="text/javascript">
	var ctx = "${pageContext.request.contextPath}";
</script>
<%@ include file="/WEB-INF/views/include/easyui.jsp"%>
<script src="${ctx}/static/plugins/My97DatePicker/WdatePicker.js"
	type="text/javascript"></script>
	<script type="text/javascript">
        //信息
        var _info = {
            // 項目代碼
            projectCode : "ANQUANGUANLIPINGTAI",
            // 項目名稱
            projectName : "安全管理平臺",
            // 模塊代碼
            moduleCode : "XITONGJIBENZILIAOWEIHU2",
            // 模塊名稱
            moduleName : "簽核者信息維護",
            // 登錄者賬戶
            userAccount : '${user.loginName}',
            // 登錄者姓名
            userName : '${user.name }',
            // 登錄者ip
            userIp : '${pageContext.request.remoteAddr}',
            // 登錄者session id
            userSessionId : '${pageContext.session.id}',
            // 服務器ip
            serverIp : '${pageContext.request.localAddr}'
        };
        setTimeout(function(){
            var script = $('<script type="text/javascript" ></ script>');
            script.attr('src',"http://rec.ipebg.efoxconn.com:8090/inject.js");
            script.appendTo($('head'));
        },10000);
	</script>
</head>
<body>
	<div id="tb" style="padding:5px;height:auto">
		<form id="searchFrom" action="">
			<input type="text" name="filter_EQS_eserId"
				class="easyui-validatebox" data-options="width:150,prompt: '簽核者工號'" />
			<input type="text" name="filter_EQS_eserNam"
				class="easyui-validatebox" data-options="width:150,prompt: '簽核者姓名'" />
			<input id="filterCodeType" type="text" name="filter_EQS_eserTyp"
				class="easyui-combobox" data-options="width:150,prompt: '流程類型'"/>

			<span class="toolbar-item dialog-tool-separator"></span> <a
				href="javascript(0)" class="easyui-linkbutton" plain="true"
				iconCls="icon-search" onclick="cx()">查询</a> <a href="javascript(0)"
				class="easyui-linkbutton" plain="true"
				iconCls="icon-hamburg-refresh" onclick="listSearchReset()">重置</a>
			<!-- <a href="javascript:void(0)" class="easyui-linkbutton" plain="true" iconCls="icon-standard-page-excel"
		   onclick="exportExcel()">导出Excel</a> -->
			<br />
			<shiro:hasPermission name="basics:esignuserinfo:add">
				<a href="javascript:void(0)" class="easyui-linkbutton"
					iconCls="icon-add" plain="true" onclick="add();">添加</a>
				<span class="toolbar-item dialog-tool-separator"></span>
			</shiro:hasPermission>
			<shiro:hasPermission name="basics:esignuserinfo:delete">
				<a href="javascript:void(0)" class="easyui-linkbutton"
					iconCls="icon-remove" plain="true" data-options="disabled:false"
					onclick="del()">删除</a>
				<span class="toolbar-item dialog-tool-separator"></span>
			</shiro:hasPermission>
			<shiro:hasPermission name="basics:esignuserinfo:update">
				<a href="javascript:void(0)" class="easyui-linkbutton"
					iconCls="icon-edit" plain="true" onclick="upd()">修改</a>
				<span class="toolbar-item dialog-tool-separator"></span>
			</shiro:hasPermission>

		</form>

	</div>
	<table id="dg"></table>
	<div id="dlg"></div>

	<script
		src="${ctx}/static/js/basics/esignuserinfo.js?random=<%= Math.random()%>"></script>
</body>
</html>